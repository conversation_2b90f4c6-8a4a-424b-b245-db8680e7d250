- Nginx
    - [Nginx的安全措施.md](Nginx/Nginx%E7%9A%84%E5%AE%89%E5%85%A8%E6%8E%AA%E6%96%BD.md)
- ✅ SpringBoot
    - [Jackson使用记录](%E2%9C%85%20SpringBoot/Jackson%E4%BD%BF%E7%94%A8%E8%AE%B0%E5%BD%95.md)
    - [Readme](%E2%9C%85%20SpringBoot/README.md)
    - [Spring bean初始化过程原理分析](%E2%9C%85%20SpringBoot/Spring%20Bean%E5%88%9D%E5%A7%8B%E5%8C%96%E8%BF%87%E7%A8%8B%E5%8E%9F%E7%90%86%E5%88%86%E6%9E%90.md)
    - [Spring state machine（状态机）入门](%E2%9C%85%20SpringBoot/Spring%20State%20Machine%EF%BC%88%E7%8A%B6%E6%80%81%E6%9C%BA%EF%BC%89%E5%85%A5%E9%97%A8.md)
    - [Springboot 接口使用date类型接收时间参数](%E2%9C%85%20SpringBoot/SpringBoot%20%E6%8E%A5%E5%8F%A3%E4%BD%BF%E7%94%A8Date%E7%B1%BB%E5%9E%8B%E6%8E%A5%E6%94%B6%E6%97%B6%E9%97%B4%E5%8F%82%E6%95%B0.md)
    - [✅ spring boot 整合quartz](%E2%9C%85%20SpringBoot/%E2%9C%85%20Spring%20boot%20%E6%95%B4%E5%90%88Quartz.md)
    - [✅ springboot tomcat undertow 最多能处理多少请求](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20Tomcat%20undertow%20%E6%9C%80%E5%A4%9A%E8%83%BD%E5%A4%84%E7%90%86%E5%A4%9A%E5%B0%91%E8%AF%B7%E6%B1%82.md)
    - [✅ springboot http接口数据加密解密](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20http%E6%8E%A5%E5%8F%A3%E6%95%B0%E6%8D%AE%E5%8A%A0%E5%AF%86%E8%A7%A3%E5%AF%86.md)
    - [✅ springboot validation使用](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20validation%E4%BD%BF%E7%94%A8.md)
    - [✅ springboot 使用 druid 连接池](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20%E4%BD%BF%E7%94%A8%20Druid%20%E8%BF%9E%E6%8E%A5%E6%B1%A0.md)
    - [✅ springboot 全局异常捕获](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20%E5%85%A8%E5%B1%80%E5%BC%82%E5%B8%B8%E6%8D%95%E8%8E%B7.md)
    - [✅ springboot 整合 jwt 使用](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20%E6%95%B4%E5%90%88%20JWT%20%E4%BD%BF%E7%94%A8.md)
    - [✅ springboot 过滤器、拦截器、监听器使用与原理](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20%E8%BF%87%E6%BB%A4%E5%99%A8%E3%80%81%E6%8B%A6%E6%88%AA%E5%99%A8%E3%80%81%E7%9B%91%E5%90%AC%E5%99%A8%E4%BD%BF%E7%94%A8%E4%B8%8E%E5%8E%9F%E7%90%86.md)
    - [✅ springboot 集成 flyway 数据库版本](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%20%E9%9B%86%E6%88%90%20flyway%20%E6%95%B0%E6%8D%AE%E5%BA%93%E7%89%88%E6%9C%AC.md)
    - [✅ springboot整合单机锁、各种分布式锁实现](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%E6%95%B4%E5%90%88%E5%8D%95%E6%9C%BA%E9%94%81%E3%80%81%E5%90%84%E7%A7%8D%E5%88%86%E5%B8%83%E5%BC%8F%E9%94%81%E5%AE%9E%E7%8E%B0.md)
    - [✅ springboot自动配置原理](%E2%9C%85%20SpringBoot/%E2%9C%85%20SpringBoot%E8%87%AA%E5%8A%A8%E9%85%8D%E7%BD%AE%E5%8E%9F%E7%90%86.md)
    - [✅ springboot整合websocket](%E2%9C%85%20SpringBoot/%E2%9C%85%20Springboot%E6%95%B4%E5%90%88WebSocket.md)
    - 响应式编程WebFlux
        - [Flux怎么遍历](%E2%9C%85%20SpringBoot/%E5%93%8D%E5%BA%94%E5%BC%8F%E7%BC%96%E7%A8%8BWebFlux/Flux%E6%80%8E%E4%B9%88%E9%81%8D%E5%8E%86.md)
    - 第三方工具包
        - [Springboot整合drools使用](%E2%9C%85%20SpringBoot/%E7%AC%AC%E4%B8%89%E6%96%B9%E5%B7%A5%E5%85%B7%E5%8C%85/SpringBoot%E6%95%B4%E5%90%88drools%E4%BD%BF%E7%94%A8.md)
        - [✅ springboot整合easyexcel使用](%E2%9C%85%20SpringBoot/%E7%AC%AC%E4%B8%89%E6%96%B9%E5%B7%A5%E5%85%B7%E5%8C%85/%E2%9C%85%20SpringBoot%E6%95%B4%E5%90%88EasyExcel%E4%BD%BF%E7%94%A8.md)
    - sentinel
        - [降级熔断](%E2%9C%85%20SpringCloudAlibaba/sentinel/%E9%99%8D%E7%BA%A7%E7%86%94%E6%96%AD.md)
    - gateway
        - [Gateway路由原理分析](%E2%9C%85%20SpringCloudAlibaba/gateway/Gateway%E8%B7%AF%E7%94%B1%E5%8E%9F%E7%90%86%E5%88%86%E6%9E%90.md)
        - [✅ gateway 使用nacos实现动态路由](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E4%BD%BF%E7%94%A8nacos%E5%AE%9E%E7%8E%B0%E5%8A%A8%E6%80%81%E8%B7%AF%E7%94%B1.md)
        - [✅ gateway 内置全局过滤器 globalfilter](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E5%86%85%E7%BD%AE%E5%85%A8%E5%B1%80%E8%BF%87%E6%BB%A4%E5%99%A8%20GlobalFilter.md)
        - [✅ gateway 内置路由断言predicate使用](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E5%86%85%E7%BD%AE%E8%B7%AF%E7%94%B1%E6%96%AD%E8%A8%80Predicate%E4%BD%BF%E7%94%A8.md)
        - [✅ gateway 整合sentinel实现网关限流+规则持久化nacos](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E6%95%B4%E5%90%88Sentinel%E5%AE%9E%E7%8E%B0%E7%BD%91%E5%85%B3%E9%99%90%E6%B5%81%2B%E8%A7%84%E5%88%99%E6%8C%81%E4%B9%85%E5%8C%96Nacos.md)
        - [✅ gateway 超时配置和 metadata 使用整理](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E8%B6%85%E6%97%B6%E9%85%8D%E7%BD%AE%E5%92%8C%20metadata%20%E4%BD%BF%E7%94%A8%E6%95%B4%E7%90%86.md)
        - [✅ gateway 集成 redis 实现限流](%E2%9C%85%20SpringCloudAlibaba/gateway/%E2%9C%85%20Gateway%20%E9%9B%86%E6%88%90%20redis%20%E5%AE%9E%E7%8E%B0%E9%99%90%E6%B5%81.md)
    - 优化配置
        - [Cloud一些优化配置](%E2%9C%85%20SpringCloudAlibaba/%E4%BC%98%E5%8C%96%E9%85%8D%E7%BD%AE/cloud%E4%B8%80%E4%BA%9B%E4%BC%98%E5%8C%96%E9%85%8D%E7%BD%AE.md)
    - stream
        - [Springcloudstream4_x](%E2%9C%85%20SpringCloudAlibaba/stream/SpringCloudStream4_x.md)
    - Nacos
        - [Nacos 2.4.3 升级2.5.0](%E2%9C%85%20SpringCloudAlibaba/Nacos/Nacos%202.4.3%20%E5%8D%87%E7%BA%A72.5.0.md)
        - [Nacos手动下线服务实现](%E2%9C%85%20SpringCloudAlibaba/Nacos/Nacos%E6%89%8B%E5%8A%A8%E4%B8%8B%E7%BA%BF%E6%9C%8D%E5%8A%A1%E5%AE%9E%E7%8E%B0.md)
        - [Nacos节点异常问题](%E2%9C%85%20SpringCloudAlibaba/Nacos/Nacos%E8%8A%82%E7%82%B9%E5%BC%82%E5%B8%B8%E9%97%AE%E9%A2%98.md)
        - [Nacos配置中心和注册中心](%E2%9C%85%20SpringCloudAlibaba/Nacos/Nacos%E9%85%8D%E7%BD%AE%E4%B8%AD%E5%BF%83%E5%92%8C%E6%B3%A8%E5%86%8C%E4%B8%AD%E5%BF%83.md)
- ✅ Netty+WebSocket
    - [Netty 服务 ws 注册到 nacos，动态上下线结合 gateway 转发 ws](%E2%9C%85%20Netty%2BWebSocket/Netty%20%E6%9C%8D%E5%8A%A1%20ws%20%E6%B3%A8%E5%86%8C%E5%88%B0%20Nacos%EF%BC%8C%E5%8A%A8%E6%80%81%E4%B8%8A%E4%B8%8B%E7%BA%BF%E7%BB%93%E5%90%88%20Gateway%20%E8%BD%AC%E5%8F%91%20ws.md)
    - [Netty实现连接鉴权和获取客户端ip](%E2%9C%85%20Netty%2BWebSocket/Netty%E5%AE%9E%E7%8E%B0%E8%BF%9E%E6%8E%A5%E9%89%B4%E6%9D%83%E5%92%8C%E8%8E%B7%E5%8F%96%E5%AE%A2%E6%88%B7%E7%AB%AFIP.md)
    - [Websocket集群使用](%E2%9C%85%20Netty%2BWebSocket/WebSocket%E9%9B%86%E7%BE%A4%E4%BD%BF%E7%94%A8.md)
- ✅ 有趣的东西
    - [Gitbook搭建](%E2%9C%85%20%E6%9C%89%E8%B6%A3%E7%9A%84%E4%B8%9C%E8%A5%BF/GitBook%E6%90%AD%E5%BB%BA.md)
    - [Wsl](%E2%9C%85%20%E6%9C%89%E8%B6%A3%E7%9A%84%E4%B8%9C%E8%A5%BF/WSL.md)
    - [推荐工具网站](%E2%9C%85%20%E6%9C%89%E8%B6%A3%E7%9A%84%E4%B8%9C%E8%A5%BF/%E6%8E%A8%E8%8D%90%E5%B7%A5%E5%85%B7%E7%BD%91%E7%AB%99.md)
    - [链接](%E2%9C%85%20%E6%9C%89%E8%B6%A3%E7%9A%84%E4%B8%9C%E8%A5%BF/%E9%93%BE%E6%8E%A5.md)
- ✅ SpringBoot Starter
    - [👌🏻oss对象存储, 自定义springboot starter实现](%E2%9C%85%20SpringBoot%20Starter/%F0%9F%91%8C%F0%9F%8F%BBOSS%E5%AF%B9%E8%B1%A1%E5%AD%98%E5%82%A8%2C%20%E8%87%AA%E5%AE%9A%E4%B9%89SpringBoot%20Starter%E5%AE%9E%E7%8E%B0.md)
    - [👌🏻基于 easyexcel 的高效大数据导出方案](%E2%9C%85%20SpringBoot%20Starter/%F0%9F%91%8C%F0%9F%8F%BB%E5%9F%BA%E4%BA%8E%20EasyExcel%20%E7%9A%84%E9%AB%98%E6%95%88%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA%E6%96%B9%E6%A1%88.md)
    - [👌🏻基于 mybatis 和 spring cloud gateway 的 url 加密解密解决方案](%E2%9C%85%20SpringBoot%20Starter/%F0%9F%91%8C%F0%9F%8F%BB%E5%9F%BA%E4%BA%8E%20MyBatis%20%E5%92%8C%20Spring%20Cloud%20Gateway%20%E7%9A%84%20URL%20%E5%8A%A0%E5%AF%86%E8%A7%A3%E5%AF%86%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md)
- 遇到问题
    - [1026. sharding-jdbc创建表语句失败](%E9%81%87%E5%88%B0%E9%97%AE%E9%A2%98/1026.%20Sharding-jdbc%E5%88%9B%E5%BB%BA%E8%A1%A8%E8%AF%AD%E5%8F%A5%E5%A4%B1%E8%B4%A5.md)
    - [1026. 包装类-拆箱,装箱,如何理解](%E9%81%87%E5%88%B0%E9%97%AE%E9%A2%98/1026.%20%E5%8C%85%E8%A3%85%E7%B1%BB-%E6%8B%86%E7%AE%B1%2C%E8%A3%85%E7%AE%B1%2C%E5%A6%82%E4%BD%95%E7%90%86%E8%A7%A3.md)
    - [接口超时](%E9%81%87%E5%88%B0%E9%97%AE%E9%A2%98/%E6%8E%A5%E5%8F%A3%E8%B6%85%E6%97%B6.md)
- OpenWRT
    - [Docker安装openwrt](OpenWRT/Docker%E5%AE%89%E8%A3%85Openwrt.md)
    - [防火墙](OpenWRT/%E9%98%B2%E7%81%AB%E5%A2%99.md)
- Mac
    - [Brew源](Mac/brew%E6%BA%90.md)
    - [Mac 切换jdk版本](Mac/mac%20%E5%88%87%E6%8D%A2jdk%E7%89%88%E6%9C%AC.md)
    - [安装brew](Mac/%E5%AE%89%E8%A3%85brew.md)
- AI大模型
    - [Funasr](AI%E5%A4%A7%E6%A8%A1%E5%9E%8B/FunASR.md)
    - [Readme](AI%E5%A4%A7%E6%A8%A1%E5%9E%8B/README.md)
    - [Cursor重置设备id方法](AI%E5%A4%A7%E6%A8%A1%E5%9E%8B/cursor%E9%87%8D%E7%BD%AE%E8%AE%BE%E5%A4%87id%E6%96%B9%E6%B3%95.md)
    - [Open-webui](AI%E5%A4%A7%E6%A8%A1%E5%9E%8B/open-webui.md)
    - TTS
        - [Kokorotts](AI%E5%A4%A7%E6%A8%A1%E5%9E%8B/TTS/KokoroTTS.md)
- ✳️ 架构
    - [Mq的问题处理](%E2%9C%B3%EF%B8%8F%20%E6%9E%B6%E6%9E%84/MQ%E7%9A%84%E9%97%AE%E9%A2%98%E5%A4%84%E7%90%86.md)
    - [✅1026. 缓存一致性问题](%E2%9C%B3%EF%B8%8F%20%E6%9E%B6%E6%9E%84/%E2%9C%851026.%20%E7%BC%93%E5%AD%98%E4%B8%80%E8%87%B4%E6%80%A7%E9%97%AE%E9%A2%98.md)
    - [分布式锁的实现方式](%E2%9C%B3%EF%B8%8F%20%E6%9E%B6%E6%9E%84/%E5%88%86%E5%B8%83%E5%BC%8F%E9%94%81%E7%9A%84%E5%AE%9E%E7%8E%B0%E6%96%B9%E5%BC%8F.md)
        - Kafka消费者
            - [✅ kafka rebalance策略](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E6%B6%88%E8%B4%B9%E8%80%85/%E2%9C%85%20Kafka%20Rebalance%E7%AD%96%E7%95%A5.md)
            - [✅ kafka消费json序列化、反序列化](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E6%B6%88%E8%B4%B9%E8%80%85/%E2%9C%85%20Kafka%E6%B6%88%E8%B4%B9Json%E5%BA%8F%E5%88%97%E5%8C%96%E3%80%81%E5%8F%8D%E5%BA%8F%E5%88%97%E5%8C%96.md)
            - [✅ kafka消费消息过滤器](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E6%B6%88%E8%B4%B9%E8%80%85/%E2%9C%85%20Kafka%E6%B6%88%E8%B4%B9%E6%B6%88%E6%81%AF%E8%BF%87%E6%BB%A4%E5%99%A8.md)
        - 进阶
            - [Kafka面试题](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/%E8%BF%9B%E9%98%B6/Kafka%E9%9D%A2%E8%AF%95%E9%A2%98.md)
            - [​✅ kafka如何保证消息可靠性](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/%E8%BF%9B%E9%98%B6/%E2%80%8B%E2%9C%85%20Kafka%E5%A6%82%E4%BD%95%E4%BF%9D%E8%AF%81%E6%B6%88%E6%81%AF%E5%8F%AF%E9%9D%A0%E6%80%A7.md)
        - ​Kafka概述
            - [Kafka基本概念](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/%E2%80%8BKafka%E6%A6%82%E8%BF%B0/Kafka%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5.md)
        - Kafka部署
            - [Centos 安装kafka集群](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E9%83%A8%E7%BD%B2/Centos%20%E5%AE%89%E8%A3%85kafka%E9%9B%86%E7%BE%A4.md)
            - [​✅ kafka配置参数详解](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E9%83%A8%E7%BD%B2/%E2%80%8B%E2%9C%85%20Kafka%E9%85%8D%E7%BD%AE%E5%8F%82%E6%95%B0%E8%AF%A6%E8%A7%A3.md)
            - [✅ kafka docker-compose部署](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E9%83%A8%E7%BD%B2/%E2%9C%85%20Kafka%20Docker-compose%E9%83%A8%E7%BD%B2.md)
        - Kafka生产者
            - [✅ kafka @sendto注解使用](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E7%94%9F%E4%BA%A7%E8%80%85/%E2%9C%85%20Kafka%20%40SendTo%E6%B3%A8%E8%A7%A3%E4%BD%BF%E7%94%A8.md)
            - [✅ kafka消息类型](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E7%94%9F%E4%BA%A7%E8%80%85/%E2%9C%85%20Kafka%E6%B6%88%E6%81%AF%E7%B1%BB%E5%9E%8B.md)
            - [✅ spring boot 整合kafka](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E7%94%9F%E4%BA%A7%E8%80%85/%E2%9C%85%20Spring%20boot%20%E6%95%B4%E5%90%88kafka.md)
            - [✅ springboot replyingkafkatemplate 请求等待回复](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E7%94%9F%E4%BA%A7%E8%80%85/%E2%9C%85%20Springboot%20ReplyingKafkaTemplate%20%E8%AF%B7%E6%B1%82%E7%AD%89%E5%BE%85%E5%9B%9E%E5%A4%8D.md)
        - Kafka架构设计
            - [Kafka消息存储原理](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Kafka/Kafka%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1/Kafka%E6%B6%88%E6%81%AF%E5%AD%98%E5%82%A8%E5%8E%9F%E7%90%86.md)
    - ✅ RocketMQ
        - [Rocketmq5.x 消息发送解析](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/RocketMQ5.x%20%E6%B6%88%E6%81%AF%E5%8F%91%E9%80%81%E8%A7%A3%E6%9E%90.md)
        - [Rocketmq消费问题](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/RocketMQ%E6%B6%88%E8%B4%B9%E9%97%AE%E9%A2%98.md)
        - [Rocketmq生产环境实践](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/RocketMQ%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83%E5%AE%9E%E8%B7%B5.md)
        - [Rocketmq问题](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/RocketMQ%E9%97%AE%E9%A2%98.md)
        - [Rocketmq顺序消息消费延迟问题](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/RocketMQ%E9%A1%BA%E5%BA%8F%E6%B6%88%E6%81%AF%E6%B6%88%E8%B4%B9%E5%BB%B6%E8%BF%9F%E9%97%AE%E9%A2%98.md)
        - [✅ rocketmq消息发送和消费实践](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%9C%85%20RocketMQ%E6%B6%88%E6%81%AF%E5%8F%91%E9%80%81%E5%92%8C%E6%B6%88%E8%B4%B9%E5%AE%9E%E8%B7%B5.md)
        - [✅ rocketmq集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%9C%85%20RocketMQ%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
        - [✅ rocketmq默认端口](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%9C%85%20RocketMQ%E9%BB%98%E8%AE%A4%E7%AB%AF%E5%8F%A3.md)
        - [✅ springboot整合rocketmq分析](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%9C%85%20Springboot%E6%95%B4%E5%90%88RocketMQ%E5%88%86%E6%9E%90.md)
        - 部署
            - [Docker部署rocketmq5.x control集群](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/Docker%E9%83%A8%E7%BD%B2Rocketmq5.x%20control%E9%9B%86%E7%BE%A4.md)
            - [Rocketmq-dledger集群生产搭建497 ](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ-DLedger%E9%9B%86%E7%BE%A4%E7%94%9F%E4%BA%A7%E6%90%AD%E5%BB%BA497%20.md)
            - [Rocketmq-dledger集群生产搭建497-host](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ-DLedger%E9%9B%86%E7%BE%A4%E7%94%9F%E4%BA%A7%E6%90%AD%E5%BB%BA497-host.md)
            - [Rocketmq-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
            - [Rocketmq5.1.4 control-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ5.1.4%20Control-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
            - [Rocketmq5.x control-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ5.x%20Control-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
            - [Rocketmq5.x-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ5.x-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
            - [Rocketmq连接ak sk鉴权](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/RocketMQ%E8%BF%9E%E6%8E%A5ak%20sk%E9%89%B4%E6%9D%83.md)
            - [Rocketmq-exporter 监控部署](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/rocketmq-exporter%20%E7%9B%91%E6%8E%A7%E9%83%A8%E7%BD%B2.md)
            - [✅ rocketmq-dledger集群搭建 ](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%E2%9C%85%20RocketMQ-DLedger%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA%20.md)
            - [✅ rocketmq-dledger集群搭建2 ](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%E2%9C%85%20RocketMQ-DLedger%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA2%20.md)
            - [✅ rocketmq-dledger集群搭建497 ](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%E2%9C%85%20RocketMQ-DLedger%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA497%20.md)
            - [✅ rocketmq5.2.0 control-多 master 多 salve-异步复制-集群搭建 (copy)](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%E2%9C%85%20RocketMQ5.2.0%20Control-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA%20%28copy%29.md)
            - [✅ rocketmq5.2.0 control-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%E2%9C%85%20RocketMQ5.2.0%20Control-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
            - [👌🏻 ✅ rocketmq5.3.0 control-多 master 多 salve-异步复制-集群搭建](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E9%83%A8%E7%BD%B2/%F0%9F%91%8C%F0%9F%8F%BB%20%E2%9C%85%20RocketMQ5.3.0%20Control-%E5%A4%9A%20Master%20%E5%A4%9A%20Salve-%E5%BC%82%E6%AD%A5%E5%A4%8D%E5%88%B6-%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
        - 性能
            - [4.x和5.x版本磁盘使用差异](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/4.x%E5%92%8C5.x%E7%89%88%E6%9C%AC%E7%A3%81%E7%9B%98%E4%BD%BF%E7%94%A8%E5%B7%AE%E5%BC%82.md)
            - [Io高](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/IO%E9%AB%98.md)
            - [Rocketmq 消费者被kill之后, 快速进行重平衡, 实现高可用](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/RocketMQ%20%E6%B6%88%E8%B4%B9%E8%80%85%E8%A2%ABkill%E4%B9%8B%E5%90%8E%2C%20%E5%BF%AB%E9%80%9F%E8%BF%9B%E8%A1%8C%E9%87%8D%E5%B9%B3%E8%A1%A1%2C%20%E5%AE%9E%E7%8E%B0%E9%AB%98%E5%8F%AF%E7%94%A8.md)
            - [Rocketmq内存设置多少合适](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/RocketMQ%E5%86%85%E5%AD%98%E8%AE%BE%E7%BD%AE%E5%A4%9A%E5%B0%91%E5%90%88%E9%80%82.md)
            - [Rocketmq如何降低内存使用](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/RocketMQ%E5%A6%82%E4%BD%95%E9%99%8D%E4%BD%8E%E5%86%85%E5%AD%98%E4%BD%BF%E7%94%A8.md)
            - [Rocketmq性能压测](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/RocketMQ%E6%80%A7%E8%83%BD%E5%8E%8B%E6%B5%8B.md)
            - [Rocketmq生产压测](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%80%A7%E8%83%BD/RocketMQ%E7%94%9F%E4%BA%A7%E5%8E%8B%E6%B5%8B.md)
        - 架构设计
            - [✅ rocketmq通讯机制](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1/%E2%80%8B%E2%9C%85%20RocketMQ%E9%80%9A%E8%AE%AF%E6%9C%BA%E5%88%B6.md)
            - [✅ rocketmq消息存储](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E6%9E%B6%E6%9E%84%E8%AE%BE%E8%AE%A1/%E2%9C%85%20RocketMQ%E6%B6%88%E6%81%AF%E5%AD%98%E5%82%A8.md)
        - 概念和特性
            - [✅ rocketmq特性](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%80%8B%E6%A6%82%E5%BF%B5%E5%92%8C%E7%89%B9%E6%80%A7/%E2%80%8B%E2%9C%85%20RocketMQ%E7%89%B9%E6%80%A7.md)
            - [✅ rocketmq基本概念](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%80%8B%E6%A6%82%E5%BF%B5%E5%92%8C%E7%89%B9%E6%80%A7/%E2%9C%85%20RocketMQ%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5.md)
            - [✅ rocketmq概念入门](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20RocketMQ/%E2%80%8B%E6%A6%82%E5%BF%B5%E5%92%8C%E7%89%B9%E6%80%A7/%E2%9C%85%20RocketMQ%E6%A6%82%E5%BF%B5%E5%85%A5%E9%97%A8.md)
    - RabbitMQ
        - [Rabbitmq 延时队列遇到的坑](%E2%9C%85%20%E6%B6%88%E6%81%AF%E4%B8%AD%E9%97%B4%E4%BB%B6/RabbitMQ/rabbitmq%20%E5%BB%B6%E6%97%B6%E9%98%9F%E5%88%97%E9%81%87%E5%88%B0%E7%9A%84%E5%9D%91.md)
- ✅ MyBatis
    - [Mybatis](%E2%9C%85%20MyBatis/Mybatis.md)
    - [Mybatis遇到的异常](%E2%9C%85%20MyBatis/Mybatis%E9%81%87%E5%88%B0%E7%9A%84%E5%BC%82%E5%B8%B8.md)
    - [✅ mybatis 一级缓存、二级缓存](%E2%9C%85%20MyBatis/%E2%9C%85%20MyBatis%20%E4%B8%80%E7%BA%A7%E7%BC%93%E5%AD%98%E3%80%81%E4%BA%8C%E7%BA%A7%E7%BC%93%E5%AD%98.md)
- ✅ Java相关知识
    - [Readme](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/README.md)
        - 八股文
            - [Jvm调优工具和参数](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/JVM%E8%B0%83%E4%BC%98%E5%B7%A5%E5%85%B7%E5%92%8C%E5%8F%82%E6%95%B0.md)
            - [✅ jvm内存结构](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20JVM%E5%86%85%E5%AD%98%E7%BB%93%E6%9E%84.md)
            - [✅ java内存模型](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20Java%E5%86%85%E5%AD%98%E6%A8%A1%E5%9E%8B.md)
            - [✅ 垃圾回收器](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20%E5%9E%83%E5%9C%BE%E5%9B%9E%E6%94%B6%E5%99%A8.md)
            - [✅ 垃圾回收算法](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20%E5%9E%83%E5%9C%BE%E5%9B%9E%E6%94%B6%E7%AE%97%E6%B3%95.md)
            - [✅ 引用类型](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20%E5%BC%95%E7%94%A8%E7%B1%BB%E5%9E%8B.md)
            - [✅ 类加载机制](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%85%AB%E8%82%A1%E6%96%87/%E2%9C%85%20%E7%B1%BB%E5%8A%A0%E8%BD%BD%E6%9C%BA%E5%88%B6.md)
        - 实践
            - [✅ jvm oom实践分析](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%AE%9E%E8%B7%B5/%E2%9C%85%20JVM%20OOM%E5%AE%9E%E8%B7%B5%E5%88%86%E6%9E%90.md)
            - [✅ 包装类的内存占用分析](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%20%E2%9C%85%20JVM/%E5%AE%9E%E8%B7%B5/%E2%9C%85%20%E5%8C%85%E8%A3%85%E7%B1%BB%E7%9A%84%E5%86%85%E5%AD%98%E5%8D%A0%E7%94%A8%E5%88%86%E6%9E%90.md)
    - ✳️ Java基础
        - [Jdk8语法使用](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/JDK8%E8%AF%AD%E6%B3%95%E4%BD%BF%E7%94%A8.md)
        - [Jackson](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/Jackson.md)
        - [Java基础](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/Java%E5%9F%BA%E7%A1%80.md)
        - [三目运算空指针问题](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/%E4%B8%89%E7%9B%AE%E8%BF%90%E7%AE%97%E7%A9%BA%E6%8C%87%E9%92%88%E9%97%AE%E9%A2%98.md)
            - HashMap
                - [Hashmap扩容机制](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/%F0%9F%93%82%20Java%E9%9B%86%E5%90%88/HashMap/HashMap%E6%89%A9%E5%AE%B9%E6%9C%BA%E5%88%B6.md)
                - [✅ hashmap头插法和尾插法](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%B3%EF%B8%8F%20Java%E5%9F%BA%E7%A1%80/%F0%9F%93%82%20Java%E9%9B%86%E5%90%88/HashMap/%E2%9C%85%20HashMap%E5%A4%B4%E6%8F%92%E6%B3%95%E5%92%8C%E5%B0%BE%E6%8F%92%E6%B3%95.md)
    - ✅ Java并发编程
        - [0210. java并发编程](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/0210.%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B.md)
        - [✅ final关键字](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/%E2%9C%85%20final%E5%85%B3%E9%94%AE%E5%AD%97.md)
        - [✅ synchronized关键字](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/%E2%9C%85%20synchronized%E5%85%B3%E9%94%AE%E5%AD%97.md)
        - [✅ volatile关键字](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/%E2%9C%85%20volatile%E5%85%B3%E9%94%AE%E5%AD%97.md)
        - JUC同步工具类
            - [✅ threadlocal用过吗](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/JUC%E5%90%8C%E6%AD%A5%E5%B7%A5%E5%85%B7%E7%B1%BB/%E2%9C%85%20ThreadLocal%E7%94%A8%E8%BF%87%E5%90%97.md)
        - ✅ 线程和线程池
            - [✅ jdk线程池](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/%E2%9C%85%20%E7%BA%BF%E7%A8%8B%E5%92%8C%E7%BA%BF%E7%A8%8B%E6%B1%A0/%E2%9C%85%20JDK%E7%BA%BF%E7%A8%8B%E6%B1%A0.md)
            - [创建线程的方式](%E2%9C%85%20Java%E7%9B%B8%E5%85%B3%E7%9F%A5%E8%AF%86/%E2%9C%85%20Java%E5%B9%B6%E5%8F%91%E7%BC%96%E7%A8%8B/%E2%9C%85%20%E7%BA%BF%E7%A8%8B%E5%92%8C%E7%BA%BF%E7%A8%8B%E6%B1%A0/%E5%88%9B%E5%BB%BA%E7%BA%BF%E7%A8%8B%E7%9A%84%E6%96%B9%E5%BC%8F.md)
    - ✅ Sharding-JDBC
        - [Sharding-jdbc5.2分片算法运用](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Sharding-JDBC/Sharding-JDBC5.2%E5%88%86%E7%89%87%E7%AE%97%E6%B3%95%E8%BF%90%E7%94%A8.md)
        - ES集群安装和配置
            - [Elkf](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E9%9B%86%E7%BE%A4%E5%AE%89%E8%A3%85%E5%92%8C%E9%85%8D%E7%BD%AE/ELKF.md)
            - [✅ es集群集群安装](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E9%9B%86%E7%BE%A4%E5%AE%89%E8%A3%85%E5%92%8C%E9%85%8D%E7%BD%AE/%E2%9C%85%20ES%E9%9B%86%E7%BE%A4%E9%9B%86%E7%BE%A4%E5%AE%89%E8%A3%85.md)
        - ES数据操作
            - [✅ es bulk批量操作方式](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E6%95%B0%E6%8D%AE%E6%93%8D%E4%BD%9C/%E2%9C%85%20ES%20Bulk%E6%89%B9%E9%87%8F%E6%93%8D%E4%BD%9C%E6%96%B9%E5%BC%8F.md)
            - [✅ es 三种分页查询方法](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E6%95%B0%E6%8D%AE%E6%93%8D%E4%BD%9C/%E2%9C%85%20ES%20%E4%B8%89%E7%A7%8D%E5%88%86%E9%A1%B5%E6%9F%A5%E8%AF%A2%E6%96%B9%E6%B3%95.md)
            - [✅ es​ 基本curd语法入门](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E6%95%B0%E6%8D%AE%E6%93%8D%E4%BD%9C/%E2%9C%85%20ES%E2%80%8B%20%E5%9F%BA%E6%9C%ACCURD%E8%AF%AD%E6%B3%95%E5%85%A5%E9%97%A8.md)
            - [✅ springboot整合es操作](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E6%95%B0%E6%8D%AE%E6%93%8D%E4%BD%9C/%E2%9C%85%20Springboot%E6%95%B4%E5%90%88ES%E6%93%8D%E4%BD%9C.md)
        - ES基本概念
            - [Es 倒排索引理解](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5/ES%20%E5%80%92%E6%8E%92%E7%B4%A2%E5%BC%95%E7%90%86%E8%A7%A3.md)
            - [Es名词](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5/ES%E5%90%8D%E8%AF%8D.md)
            - [​搜索引擎基础概念及原理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5/%E2%80%8B%E6%90%9C%E7%B4%A2%E5%BC%95%E6%93%8E%E5%9F%BA%E7%A1%80%E6%A6%82%E5%BF%B5%E5%8F%8A%E5%8E%9F%E7%90%86.md)
            - [✅ es索引管理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Elasticsearch/%E2%80%8BES%E5%9F%BA%E6%9C%AC%E6%A6%82%E5%BF%B5/%E2%9C%85%20ES%E7%B4%A2%E5%BC%95%E7%AE%A1%E7%90%86.md)
        - 进阶
            - [Redistemplate序列化问题](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E8%BF%9B%E9%98%B6/RedisTemplate%E5%BA%8F%E5%88%97%E5%8C%96%E9%97%AE%E9%A2%98.md)
            - [✅ redis布隆过滤器](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E8%BF%9B%E9%98%B6/%E2%9C%85%20Redis%E5%B8%83%E9%9A%86%E8%BF%87%E6%BB%A4%E5%99%A8.md)
            - [✅ redis缓存存在的问题](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E8%BF%9B%E9%98%B6/%E2%9C%85%20Redis%E7%BC%93%E5%AD%98%E5%AD%98%E5%9C%A8%E7%9A%84%E9%97%AE%E9%A2%98.md)
        - 底层结构
            - [✅ redis string底层结构](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E5%BA%95%E5%B1%82%E7%BB%93%E6%9E%84/%E2%9C%85%20Redis%20string%E5%BA%95%E5%B1%82%E7%BB%93%E6%9E%84.md)
        - 部署
            - [Redis6 主节点新增从节点](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%83%A8%E7%BD%B2/Redis6%20%E4%B8%BB%E8%8A%82%E7%82%B9%E6%96%B0%E5%A2%9E%E4%BB%8E%E8%8A%82%E7%82%B9.md)
            - [Redis6.2.6部署](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%83%A8%E7%BD%B2/Redis6.2.6%E9%83%A8%E7%BD%B2.md)
        - 基础
            - [✅ redis数据类型和使用场景](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E5%9F%BA%E7%A1%80/%E2%9C%85%20Redis%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E5%92%8C%E4%BD%BF%E7%94%A8%E5%9C%BA%E6%99%AF.md)
        - 其他
            - [0. redis笔记](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E5%85%B6%E4%BB%96/0.%20Redis%E7%AC%94%E8%AE%B0.md)
            - [1. springboot redis双机房写入](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E5%85%B6%E4%BB%96/1.%20springboot%20redis%E5%8F%8C%E6%9C%BA%E6%88%BF%E5%86%99%E5%85%A5.md)
        - 问题
            - [Unrecoverable error corrupted cluster config file.](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%97%AE%E9%A2%98/Unrecoverable%20error%20corrupted%20cluster%20config%20file..md)
        - 高可用
            - [Redis集群](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%AB%98%E5%8F%AF%E7%94%A8/Redis%E9%9B%86%E7%BE%A4.md)
            - [✅ redis持久化机制](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%AB%98%E5%8F%AF%E7%94%A8/%E2%9C%85%20Redis%E6%8C%81%E4%B9%85%E5%8C%96%E6%9C%BA%E5%88%B6.md)
            - [主从复制原理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%AB%98%E5%8F%AF%E7%94%A8/%E4%B8%BB%E4%BB%8E%E5%A4%8D%E5%88%B6%E5%8E%9F%E7%90%86.md)
            - [哨兵集群实现原理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20Redis/%E9%AB%98%E5%8F%AF%E7%94%A8/%E5%93%A8%E5%85%B5%E9%9B%86%E7%BE%A4%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86.md)
    - ✅ MySQL
        - [Mysql笔记](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/MySQL%E7%AC%94%E8%AE%B0.md)
        - ✅ 事务
            - [✅ mvcc是什么](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E4%BA%8B%E5%8A%A1/%E2%9C%85%20MVCC%E6%98%AF%E4%BB%80%E4%B9%88.md)
            - [✅ 事务实现原理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E4%BA%8B%E5%8A%A1/%E2%9C%85%20%E4%BA%8B%E5%8A%A1%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86.md)
            - [✅ 事务隔离级别](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E4%BA%8B%E5%8A%A1/%E2%9C%85%20%E4%BA%8B%E5%8A%A1%E9%9A%94%E7%A6%BB%E7%BA%A7%E5%88%AB.md)
            - [✅事务四大特性acid](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E4%BA%8B%E5%8A%A1/%E2%9C%85%E4%BA%8B%E5%8A%A1%E5%9B%9B%E5%A4%A7%E7%89%B9%E6%80%A7ACID.md)
        - ✅ 索引
            - [✅ 索引explain分析](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E7%B4%A2%E5%BC%95/%E2%9C%85%20%E7%B4%A2%E5%BC%95explain%E5%88%86%E6%9E%90.md)
            - [✅ 索引使用和类型分类](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E7%B4%A2%E5%BC%95/%E2%9C%85%20%E7%B4%A2%E5%BC%95%E4%BD%BF%E7%94%A8%E5%92%8C%E7%B1%BB%E5%9E%8B%E5%88%86%E7%B1%BB.md)
            - [✅ 索引原理分析](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E7%B4%A2%E5%BC%95/%E2%9C%85%20%E7%B4%A2%E5%BC%95%E5%8E%9F%E7%90%86%E5%88%86%E6%9E%90.md)
            - [✅ 索引失效场景分析](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E7%B4%A2%E5%BC%95/%E2%9C%85%20%E7%B4%A2%E5%BC%95%E5%A4%B1%E6%95%88%E5%9C%BA%E6%99%AF%E5%88%86%E6%9E%90.md)
            - [✅ 索引运用和概念](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E7%B4%A2%E5%BC%95/%E2%9C%85%20%E7%B4%A2%E5%BC%95%E8%BF%90%E7%94%A8%E5%92%8C%E6%A6%82%E5%BF%B5.md)
        - ✅ 锁
            - [✅ mysql innodb死锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%20InnoDB%E6%AD%BB%E9%94%81.md)
            - [✅ mysql乐观锁和悲观锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%E4%B9%90%E8%A7%82%E9%94%81%E5%92%8C%E6%82%B2%E8%A7%82%E9%94%81.md)
            - [✅ mysql共享锁和排他锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%E5%85%B1%E4%BA%AB%E9%94%81%E5%92%8C%E6%8E%92%E4%BB%96%E9%94%81.md)
            - [✅ mysql行锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%E8%A1%8C%E9%94%81.md)
            - [✅ mysql表锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%E8%A1%A8%E9%94%81.md)
            - [✅ mysql锁](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E9%94%81/%E2%9C%85%20MySQL%E9%94%81.md)
        - 部署
            - [Mysql5.7部署](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E9%83%A8%E7%BD%B2/MySQL5.7%E9%83%A8%E7%BD%B2.md)
        - ✅ 日志
            - [✅ binary log 二进制日志](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E6%97%A5%E5%BF%97/%E2%9C%85%20Binary%20log%20%E4%BA%8C%E8%BF%9B%E5%88%B6%E6%97%A5%E5%BF%97.md)
            - [✅ redo log 重做日志](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E6%97%A5%E5%BF%97/%E2%9C%85%20redo%20log%20%E9%87%8D%E5%81%9A%E6%97%A5%E5%BF%97.md)
            - [✅ undo log 回滚日志](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E6%97%A5%E5%BF%97/%E2%9C%85%20undo%20log%20%E5%9B%9E%E6%BB%9A%E6%97%A5%E5%BF%97.md)
            - [✅ 一些其他日志](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20%E6%97%A5%E5%BF%97/%E2%9C%85%20%E4%B8%80%E4%BA%9B%E5%85%B6%E4%BB%96%E6%97%A5%E5%BF%97.md)
        - 优化
            - [Mysql配置查询sql](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E4%BC%98%E5%8C%96/MySQL%E9%85%8D%E7%BD%AE%E6%9F%A5%E8%AF%A2sql.md)
            - [✅ mysql问题排查相关sql](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E4%BC%98%E5%8C%96/%E2%9C%85%20MySQL%E9%97%AE%E9%A2%98%E6%8E%92%E6%9F%A5%E7%9B%B8%E5%85%B3sql.md)
        - SQL运用
            - [Sql语句类型](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/SQL%E8%BF%90%E7%94%A8/SQL%E8%AF%AD%E5%8F%A5%E7%B1%BB%E5%9E%8B.md)
            - [存储过程和触发器](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/SQL%E8%BF%90%E7%94%A8/%E5%AD%98%E5%82%A8%E8%BF%87%E7%A8%8B%E5%92%8C%E8%A7%A6%E5%8F%91%E5%99%A8.md)
            - [百万数据，分页查询sql优化](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/SQL%E8%BF%90%E7%94%A8/%E7%99%BE%E4%B8%87%E6%95%B0%E6%8D%AE%EF%BC%8C%E5%88%86%E9%A1%B5%E6%9F%A5%E8%AF%A2SQL%E4%BC%98%E5%8C%96.md)
        - 运维
            - [Mysql5.7同步失败处理](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E8%BF%90%E7%BB%B4/Mysql5.7%E5%90%8C%E6%AD%A5%E5%A4%B1%E8%B4%A5%E5%A4%84%E7%90%86.md)
        - 问题
            - [Mysql生产遇到的问题](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E9%97%AE%E9%A2%98/MySQL%E7%94%9F%E4%BA%A7%E9%81%87%E5%88%B0%E7%9A%84%E9%97%AE%E9%A2%98.md)
            - [Mysql隐式类型转换问题](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E9%97%AE%E9%A2%98/Mysql%E9%9A%90%E5%BC%8F%E7%B1%BB%E5%9E%8B%E8%BD%AC%E6%8D%A2%E9%97%AE%E9%A2%98.md)
        - ✅ InnoDB
            - [✅ innodb和myisam的区别](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20InnoDB/%E2%9C%85%20InnoDB%E5%92%8CMyISAM%E7%9A%84%E5%8C%BA%E5%88%AB.md)
            - [✅ mysql innodb内存结构](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E2%9C%85%20InnoDB/%E2%9C%85%20MySQL%20InnoDB%E5%86%85%E5%AD%98%E7%BB%93%E6%9E%84.md)
        - 扩展
            - [✅ canal监听表数据变化](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E6%89%A9%E5%B1%95/%E2%9C%85%20Canal%E7%9B%91%E5%90%AC%E8%A1%A8%E6%95%B0%E6%8D%AE%E5%8F%98%E5%8C%96.md)
            - [✅ 性能测试工具-sysbench](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E5%AD%98%E5%82%A8%E4%B8%AD%E9%97%B4%E4%BB%B6/%E2%9C%85%20MySQL/%E6%89%A9%E5%B1%95/%E2%9C%85%20%E6%80%A7%E8%83%BD%E6%B5%8B%E8%AF%95%E5%B7%A5%E5%85%B7-sysbench.md)
- ✅ 设计模式design pattern
    - [✅ 设计模式脑图](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F%E8%84%91%E5%9B%BE.md)
    - [设计原则](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99.md)
    - [设计模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8F.md)
    - 行为型模式
        - [✅ 模板方法模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%A1%8C%E4%B8%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E6%A8%A1%E6%9D%BF%E6%96%B9%E6%B3%95%E6%A8%A1%E5%BC%8F.md)
        - [✅ 策略模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%A1%8C%E4%B8%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E7%AD%96%E7%95%A5%E6%A8%A1%E5%BC%8F.md)
        - [✅ 观察者模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%A1%8C%E4%B8%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E8%A7%82%E5%AF%9F%E8%80%85%E6%A8%A1%E5%BC%8F.md)
        - [✅ 责任链模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E8%A1%8C%E4%B8%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E8%B4%A3%E4%BB%BB%E9%93%BE%E6%A8%A1%E5%BC%8F.md)
    - 结构型模式
        - [✅ 适配器模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E7%BB%93%E6%9E%84%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E9%80%82%E9%85%8D%E5%99%A8%E6%A8%A1%E5%BC%8F.md)
        - [门面模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E7%BB%93%E6%9E%84%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E9%97%A8%E9%9D%A2%E6%A8%A1%E5%BC%8F.md)
    - 创建型模式
        - [✅ 单例模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E5%88%9B%E5%BB%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E5%8D%95%E4%BE%8B%E6%A8%A1%E5%BC%8F.md)
        - [✅ 建造者模式](%E2%9C%85%20%E8%AE%BE%E8%AE%A1%E6%A8%A1%E5%BC%8Fdesign%20pattern/%E5%88%9B%E5%BB%BA%E5%9E%8B%E6%A8%A1%E5%BC%8F/%E2%9C%85%20%E5%BB%BA%E9%80%A0%E8%80%85%E6%A8%A1%E5%BC%8F.md)
- ✅ Dubbo
    - [Dubbo实际项目使用注意点](%E2%9C%85%20Dubbo/Dubbo%E5%AE%9E%E9%99%85%E9%A1%B9%E7%9B%AE%E4%BD%BF%E7%94%A8%E6%B3%A8%E6%84%8F%E7%82%B9.md)
    - [Dubbo应用级注册](%E2%9C%85%20Dubbo/Dubbo%E5%BA%94%E7%94%A8%E7%BA%A7%E6%B3%A8%E5%86%8C.md)
    - [Dubbo接口测试工具](%E2%9C%85%20Dubbo/Dubbo%E6%8E%A5%E5%8F%A3%E6%B5%8B%E8%AF%95%E5%B7%A5%E5%85%B7.md)
    - [Dubbo特性和用法](%E2%9C%85%20Dubbo/Dubbo%E7%89%B9%E6%80%A7%E5%92%8C%E7%94%A8%E6%B3%95.md)
    - [Readme](%E2%9C%85%20Dubbo/README.md)
    - [✅ dubbo 多注册中心-异地双活](%E2%9C%85%20Dubbo/%E2%9C%85%20Dubbo%20%E5%A4%9A%E6%B3%A8%E5%86%8C%E4%B8%AD%E5%BF%83-%E5%BC%82%E5%9C%B0%E5%8F%8C%E6%B4%BB.md)
        - obsidian-proxy-github
            - [Readme](.obsidian/plugins/obsidian-proxy-github/README.md)
    - 域名
        - [域名](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E5%9F%9F%E5%90%8D/%E5%9F%9F%E5%90%8D.md)
    - ✳️ 容器
        - [Docker 迁移根目录](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/Docker%20%E8%BF%81%E7%A7%BB%E6%A0%B9%E7%9B%AE%E5%BD%95.md)
        - [K8s](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/K8S.md)
        - [K8s集群搭建](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/K8S%E9%9B%86%E7%BE%A4%E6%90%AD%E5%BB%BA.md)
        - [基于docker搭建私有镜像仓库](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/%E5%9F%BA%E4%BA%8EDocker%E6%90%AD%E5%BB%BA%E7%A7%81%E6%9C%89%E9%95%9C%E5%83%8F%E4%BB%93%E5%BA%93.md)
        - [安装node-exporter](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/%E5%AE%89%E8%A3%85node-exporter.md)
        - docker
            - [Docker命令使用](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/docker/Docker%E5%91%BD%E4%BB%A4%E4%BD%BF%E7%94%A8.md)
            - [Docker概念](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/docker/docker%E6%A6%82%E5%BF%B5.md)
            - [Harbor镜像仓库搭建](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/docker/harbor%E9%95%9C%E5%83%8F%E4%BB%93%E5%BA%93%E6%90%AD%E5%BB%BA.md)
            - [Linux离线安装docker](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E5%AE%B9%E5%99%A8/docker/linux%E7%A6%BB%E7%BA%BF%E5%AE%89%E8%A3%85docker.md)
    - ✳️ Linux
        - [Centos时间同步](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/Centos%E6%97%B6%E9%97%B4%E5%90%8C%E6%AD%A5.md)
        - [Jdk21 jvm信息查询命令](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/JDK21%20JVM%E4%BF%A1%E6%81%AF%E6%9F%A5%E8%AF%A2%E5%91%BD%E4%BB%A4.md)
        - [Linux命令记录](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/Linux%E5%91%BD%E4%BB%A4%E8%AE%B0%E5%BD%95.md)
        - [Linux服务器屏蔽国外ip方法](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/Linux%E6%9C%8D%E5%8A%A1%E5%99%A8%E5%B1%8F%E8%94%BD%E5%9B%BD%E5%A4%96IP%E6%96%B9%E6%B3%95.md)
        - [Shell脚本](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/Shell%E8%84%9A%E6%9C%AC.md)
        - [Tcpdump命令使用](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/tcpdump%E5%91%BD%E4%BB%A4%E4%BD%BF%E7%94%A8.md)
        - [Vpn](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/vpn.md)
        - [Vsftp](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/vsftp.md)
        - [排查问题命令](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/%E6%8E%92%E6%9F%A5%E9%97%AE%E9%A2%98%E5%91%BD%E4%BB%A4.md)
        - [日志查询命令技巧](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Linux/%E6%97%A5%E5%BF%97%E6%9F%A5%E8%AF%A2%E5%91%BD%E4%BB%A4%E6%8A%80%E5%B7%A7.md)
    - Windows
        - [Windows一些命令](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/Windows/Windows%E4%B8%80%E4%BA%9B%E5%91%BD%E4%BB%A4.md)
    - ✅ 其他
        - [✅ 自动化运维工具ansible](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20%E5%85%B6%E4%BB%96/%E2%9C%85%20%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4%E5%B7%A5%E5%85%B7ansible.md)
    - ✳️ 监控
        - [Nginx-exporter](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E7%9B%91%E6%8E%A7/nginx-exporter.md)
        - [✅ spring cloud gateway使用grafana监控](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E7%9B%91%E6%8E%A7/%E2%9C%85%20Spring%20Cloud%20Gateway%E4%BD%BF%E7%94%A8Grafana%E7%9B%91%E6%8E%A7.md)
        - [监控springboot服务](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20%E7%9B%91%E6%8E%A7/%E7%9B%91%E6%8E%A7SpringBoot%E6%9C%8D%E5%8A%A1.md)
    - ✅ Docker
        - [Docker removal in progress](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker%20Removal%20In%20Progress.md)
        - [Docker swarm 集群环境搭建](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker%20Swarm%20%E9%9B%86%E7%BE%A4%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA.md)
        - [Docker-compose笔记](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker-compose%E7%AC%94%E8%AE%B0.md)
        - [Dockerhub镜像网站](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Dockerhub%E9%95%9C%E5%83%8F%E7%BD%91%E7%AB%99.md)
        - [Docker定时清理无用null的镜像](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker%E5%AE%9A%E6%97%B6%E6%B8%85%E7%90%86%E6%97%A0%E7%94%A8null%E7%9A%84%E9%95%9C%E5%83%8F.md)
        - [Docker镜像](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker%E9%95%9C%E5%83%8F.md)
        - [Docker限制容器cpu](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Docker%E9%99%90%E5%88%B6%E5%AE%B9%E5%99%A8cpu.md)
        - [Loki采集docker日志](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Loki%E9%87%87%E9%9B%86Docker%E6%97%A5%E5%BF%97.md)
        - [Portainer使用](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/Portainer%E4%BD%BF%E7%94%A8.md)
        - [Docker-composes部署nacos集群](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/docker-composes%E9%83%A8%E7%BD%B2nacos%E9%9B%86%E7%BE%A4.md)
        - [Docker-composes部署xxljob集群](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/docker-composes%E9%83%A8%E7%BD%B2xxljob%E9%9B%86%E7%BE%A4.md)
        - [Docker-compose安装](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/docker-compose%E5%AE%89%E8%A3%85.md)
        - [Docker容器日志占用磁盘空间过大问题](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/docker%E5%AE%B9%E5%99%A8%E6%97%A5%E5%BF%97%E5%8D%A0%E7%94%A8%E7%A3%81%E7%9B%98%E7%A9%BA%E9%97%B4%E8%BF%87%E5%A4%A7%E9%97%AE%E9%A2%98.md)
        - [Promtail采集指定日志文件](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/promtail%E9%87%87%E9%9B%86%E6%8C%87%E5%AE%9A%E6%97%A5%E5%BF%97%E6%96%87%E4%BB%B6.md)
        - [Tengine](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/tengine.md)
        - [Watchtower自动更新docker容器](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/watchtower%E8%87%AA%E5%8A%A8%E6%9B%B4%E6%96%B0Docker%E5%AE%B9%E5%99%A8.md)
        - [✅ docker-compose部署rocketmq集群](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E2%9C%85%20Docker-compose%E9%83%A8%E7%BD%B2RocketMQ%E9%9B%86%E7%BE%A4.md)
        - [✅ docker容器访问宿主机本地ip](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E2%9C%85%20Docker%E5%AE%B9%E5%99%A8%E8%AE%BF%E9%97%AE%E5%AE%BF%E4%B8%BB%E6%9C%BA%E6%9C%AC%E5%9C%B0ip.md)
        - [✅ docker部署prometheus+grafana](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E2%9C%85%20Docker%E9%83%A8%E7%BD%B2Prometheus%2BGrafana.md)
        - [✅ docker-compose 部署 springboot 服务](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E2%9C%85%20docker-compose%20%E9%83%A8%E7%BD%B2%20SpringBoot%20%E6%9C%8D%E5%8A%A1.md)
        - [完全卸载docker](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E5%AE%8C%E5%85%A8%E5%8D%B8%E8%BD%BDdocker.md)
        - [离线安装docker](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%E7%A6%BB%E7%BA%BF%E5%AE%89%E8%A3%85Docker.md)
        - [👌🏻 离线安装docker服务](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%85%20Docker/%F0%9F%91%8C%F0%9F%8F%BB%20%E7%A6%BB%E7%BA%BF%E5%AE%89%E8%A3%85Docker%E6%9C%8D%E5%8A%A1.md)
    - ✳️ Jenkins
        - [✅ jenkins部署springboot项目](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Jenkins/%E2%9C%85%20Jenkins%E9%83%A8%E7%BD%B2Springboot%E9%A1%B9%E7%9B%AE.md)
        - [✅ linux部署jenkins](%E2%9C%85%20%E8%BF%90%E7%BB%B4%E7%9B%B8%E5%85%B3/%E2%9C%B3%EF%B8%8F%20Jenkins/%E2%9C%85%20Linux%E9%83%A8%E7%BD%B2Jenkins.md)
    - ✅ Minio
        - [Mc](%E2%9C%85%20%E5%AF%B9%E8%B1%A1%E5%AD%98%E5%82%A8/%E2%9C%85%20Minio/MC.md)
        - [Minio部署](%E2%9C%85%20%E5%AF%B9%E8%B1%A1%E5%AD%98%E5%82%A8/%E2%9C%85%20Minio/Minio%E9%83%A8%E7%BD%B2.md)
- ✳️ 开发工具
    - [Typero激活方法](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Typero%E6%BF%80%E6%B4%BB%E6%96%B9%E6%B3%95.md)
    - [开发会用到的一些网站](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/%E5%BC%80%E5%8F%91%E4%BC%9A%E7%94%A8%E5%88%B0%E7%9A%84%E4%B8%80%E4%BA%9B%E7%BD%91%E7%AB%99.md)
    - [👌🏻idea最新版2024.2激活方法](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/%F0%9F%91%8C%F0%9F%8F%BBIDEA%E6%9C%80%E6%96%B0%E7%89%882024.2%E6%BF%80%E6%B4%BB%E6%96%B9%E6%B3%95.md)
    - Wireshark
        - [抓包技巧](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Wireshark/%E6%8A%93%E5%8C%85%E6%8A%80%E5%B7%A7.md)
    - arthas
        - [Arthas 常用命令](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/arthas/arthas%20%E5%B8%B8%E7%94%A8%E5%91%BD%E4%BB%A4.md)
    - Maven
        - [✅ maven pom依赖管理](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Maven/%E2%9C%85%20Maven%20Pom%E4%BE%9D%E8%B5%96%E7%AE%A1%E7%90%86.md)
        - [✅ maven 构建生命周期](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Maven/%E2%9C%85%20Maven%20%E6%9E%84%E5%BB%BA%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.md)
        - [✅ maven打包命令package汇总](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Maven/%E2%9C%85%20Maven%E6%89%93%E5%8C%85%E5%91%BD%E4%BB%A4package%E6%B1%87%E6%80%BB.md)
        - [发布自定义jar包到中央仓库](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Maven/%E5%8F%91%E5%B8%83%E8%87%AA%E5%AE%9A%E4%B9%89jar%E5%8C%85%E5%88%B0%E4%B8%AD%E5%A4%AE%E4%BB%93%E5%BA%93.md)
    - Git
        - [Git中不小心误删drop commit如何恢复](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Git/git%E4%B8%AD%E4%B8%8D%E5%B0%8F%E5%BF%83%E8%AF%AF%E5%88%A0drop%20commit%E5%A6%82%E4%BD%95%E6%81%A2%E5%A4%8D.md)
        - [✅ git 常用命令](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Git/%E2%9C%85%20Git%20%E5%B8%B8%E7%94%A8%E5%91%BD%E4%BB%A4.md)
        - [✅ git配置多个ssh-key](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Git/%E2%9C%85%20Git%E9%85%8D%E7%BD%AE%E5%A4%9A%E4%B8%AASSH-Key.md)
        - [代码同时推送到github和gitee](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Git/%E4%BB%A3%E7%A0%81%E5%90%8C%E6%97%B6%E6%8E%A8%E9%80%81%E5%88%B0github%E5%92%8Cgitee.md)
        - [使用git遇到的问题](%E2%9C%B3%EF%B8%8F%20%E5%BC%80%E5%8F%91%E5%B7%A5%E5%85%B7/Git/%E4%BD%BF%E7%94%A8git%E9%81%87%E5%88%B0%E7%9A%84%E9%97%AE%E9%A2%98.md)
    - Spring
        - [Spring](%E2%9C%85%20Spring/Spring/Spring.md)
        - Spring事务
            - [✅ sping事务实现和原理](%E2%9C%85%20Spring/Spring/Spring%E4%BA%8B%E5%8A%A1/%E2%9C%85%20Sping%E4%BA%8B%E5%8A%A1%E5%AE%9E%E7%8E%B0%E5%92%8C%E5%8E%9F%E7%90%86.md)
            - [✅ spring事务传播行为和隔离级别](%E2%9C%85%20Spring/Spring/Spring%E4%BA%8B%E5%8A%A1/%E2%9C%85%20Spring%E4%BA%8B%E5%8A%A1%E4%BC%A0%E6%92%AD%E8%A1%8C%E4%B8%BA%E5%92%8C%E9%9A%94%E7%A6%BB%E7%BA%A7%E5%88%AB.md)
            - [✅ spring事务失效场景](%E2%9C%85%20Spring/Spring/Spring%E4%BA%8B%E5%8A%A1/%E2%9C%85%20Spring%E4%BA%8B%E5%8A%A1%E5%A4%B1%E6%95%88%E5%9C%BA%E6%99%AF.md)
        - IOC和DI
            - [✅ springboot& spring扩展点实践](%E2%9C%85%20Spring/Spring/IOC%E5%92%8CDI/%E2%9C%85%20SpringBoot%26%20Spring%E6%89%A9%E5%B1%95%E7%82%B9%E5%AE%9E%E8%B7%B5.md)
        - Bean
            - [✅ spring bean的作用域](%E2%9C%85%20Spring/Spring/Bean/%E2%9C%85%20Spring%20Bean%E7%9A%84%E4%BD%9C%E7%94%A8%E5%9F%9F.md)
            - [✅ spring 创建bean的方式](%E2%9C%85%20Spring/Spring/Bean/%E2%9C%85%20Spring%20%E5%88%9B%E5%BB%BABean%E7%9A%84%E6%96%B9%E5%BC%8F.md)
            - [✅ spring循环依赖分析](%E2%9C%85%20Spring/Spring/Bean/%E2%9C%85%20Spring%E5%BE%AA%E7%8E%AF%E4%BE%9D%E8%B5%96%E5%88%86%E6%9E%90.md)
            - [如何手动刷新bean](%E2%9C%85%20Spring/Spring/Bean/%E5%A6%82%E4%BD%95%E6%89%8B%E5%8A%A8%E5%88%B7%E6%96%B0bean.md)
    - SpringMVC
        - [Springmvc](%E2%9C%85%20Spring/SpringMVC/SpringMVC.md)
        - [Springmvc请求源码分析](%E2%9C%85%20Spring/SpringMVC/SpringMVC%E8%AF%B7%E6%B1%82%E6%BA%90%E7%A0%81%E5%88%86%E6%9E%90.md)
        - [✅ springmvc请求处理过程](%E2%9C%85%20Spring/SpringMVC/%E2%9C%85%20SpringMVC%E8%AF%B7%E6%B1%82%E5%A4%84%E7%90%86%E8%BF%87%E7%A8%8B.md)
- ✅ SpringBoot3.x
    - [Springboot3.x集成问题](%E2%9C%85%20SpringBoot3.x/SpringBoot3.x%E9%9B%86%E6%88%90%E9%97%AE%E9%A2%98.md)
- 安全
    - [Oauth2](%E5%AE%89%E5%85%A8/Oauth2.md)
- ✅ 性能优化
    - [Cpu占用过高分析](%E2%9C%85%20%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/CPU%E5%8D%A0%E7%94%A8%E8%BF%87%E9%AB%98%E5%88%86%E6%9E%90.md)
    - [Oom异常时jvm优雅退出](%E2%9C%85%20%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/OOM%E5%BC%82%E5%B8%B8%E6%97%B6JVM%E4%BC%98%E9%9B%85%E9%80%80%E5%87%BA.md)
    - [Oom时执行shell脚本并钉钉告警](%E2%9C%85%20%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/OOM%E6%97%B6%E6%89%A7%E8%A1%8Cshell%E8%84%9A%E6%9C%AC%E5%B9%B6%E9%92%89%E9%92%89%E5%91%8A%E8%AD%A6.md)
    - [Redis单节点流量高, cpu单核使用率高的问题](%E2%9C%85%20%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96/Redis%E5%8D%95%E8%8A%82%E7%82%B9%E6%B5%81%E9%87%8F%E9%AB%98%2C%20CPU%E5%8D%95%E6%A0%B8%E4%BD%BF%E7%94%A8%E7%8E%87%E9%AB%98%E7%9A%84%E9%97%AE%E9%A2%98.md)
- ✅ 数据结构与算法
    - [算法与数据结构](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E7%AE%97%E6%B3%95%E4%B8%8E%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84.md)
    - 排序算法
        - [归并排序（merge sort）](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E6%8E%92%E5%BA%8F%E7%AE%97%E6%B3%95/%E5%BD%92%E5%B9%B6%E6%8E%92%E5%BA%8F%EF%BC%88Merge%20Sort%EF%BC%89.md)
        - [快速排序（quick sort）](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E6%8E%92%E5%BA%8F%E7%AE%97%E6%B3%95/%E5%BF%AB%E9%80%9F%E6%8E%92%E5%BA%8F%EF%BC%88Quick%20Sort%EF%BC%89.md)
    - 数据结构
        - [红黑树](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84/%E7%BA%A2%E9%BB%91%E6%A0%91.md)
    - 算法
        - [负载均衡-根据权重随机分配算法](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E7%AE%97%E6%B3%95/%E8%B4%9F%E8%BD%BD%E5%9D%87%E8%A1%A1-%E6%A0%B9%E6%8D%AE%E6%9D%83%E9%87%8D%E9%9A%8F%E6%9C%BA%E5%88%86%E9%85%8D%E7%AE%97%E6%B3%95.md)
        - [随机权重算法](%E2%9C%85%20%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84%E4%B8%8E%E7%AE%97%E6%B3%95/%E7%AE%97%E6%B3%95/%E9%9A%8F%E6%9C%BA%E6%9D%83%E9%87%8D%E7%AE%97%E6%B3%95.md)
- 工具Utils
    - [Quartz工具类](%E5%B7%A5%E5%85%B7Utils/Quartz%E5%B7%A5%E5%85%B7%E7%B1%BB.md)
    - [Redisutils](%E5%B7%A5%E5%85%B7Utils/RedisUtils.md)
    - [统一返回值](%E5%B7%A5%E5%85%B7Utils/%E7%BB%9F%E4%B8%80%E8%BF%94%E5%9B%9E%E5%80%BC.md)
- ✳️ 扩展
    - [✅ jsch实现sftp连接池](%E2%9C%B3%EF%B8%8F%20%E6%89%A9%E5%B1%95/%E2%9C%85%20jsch%E5%AE%9E%E7%8E%B0sftp%E8%BF%9E%E6%8E%A5%E6%B1%A0.md)
- 微服务
    - [Dubbo](%E5%BE%AE%E6%9C%8D%E5%8A%A1/Dubbo.md)
    - [Zookeeper](%E5%BE%AE%E6%9C%8D%E5%8A%A1/Zookeeper.md)
- ✅ NodeJs
    - [Node版本切换](%E2%9C%85%20NodeJs/Node%E7%89%88%E6%9C%AC%E5%88%87%E6%8D%A2.md)
    - Log4j2
        - [No root logger was configured, creating default error-level root logger with console appender](%E6%97%A5%E5%BF%97%E6%A1%86%E6%9E%B6/Log4j2/No%20Root%20logger%20was%20configured%2C%20creating%20default%20ERROR-level%20Root%20logger%20with%20Console%20appender.md)