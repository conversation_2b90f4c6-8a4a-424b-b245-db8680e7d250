# 流程图
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681224472121-561fc8c7-c67e-4fa5-81bf-a0d167599214.png#averageHue=%23868685&clientId=ud8428f9f-d49d-4&from=paste&height=593&id=u81ca0243&originHeight=1186&originWidth=2524&originalType=binary&ratio=2&rotation=0&showTitle=false&size=491871&status=done&style=none&taskId=u78304e09-26b4-4f2a-8aa2-66bc4230e15&title=&width=1262)
# 说明

1. http请求到DispatcherServlet前端控制器。
2. DispatcherServlet前端控制器，请求处理器映射器（HandlerMapping）返回一个执行器链（HandlerExecutionChain）。
3.  DispatcherServlet前端控制器将执行器链发送给处理器适配器（HandlerAdapter ）。
4. 处理器适配器通过执行器链查找具体的控制器（Handler）。
5. 控制器执行相应处理方法，给处理器适配器返回一个ModelAndView对象。
6. 处理器适配器把ModelAndView对象返回给前端控制器。
7. 前端控制器收到ModelAndView对象后，请求视图解析器进行解析。
8. 视图解析器根据视图返回相应的视图，并返回前端控制器。
9. 前端控制器把视图和Model进行渲染，返回给客户端。
