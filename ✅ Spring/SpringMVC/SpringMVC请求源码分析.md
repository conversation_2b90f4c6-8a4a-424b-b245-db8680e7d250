# 1. 如何执行接口, 找到具体方法? 
## 1.1 找处理器映射器, 返回处理器执行链: HandlerExecutionChain

- mappedHandler = **getHandler**(processedRequest);
### 源码
```java
/** List of HandlerMappings used by this servlet. 处理器映射器列表 */
@Nullable
private List<HandlerMapping> handlerMappings;

protected HandlerExecutionChain getHandler(HttpServletRequest request) throws Exception {
    if (this.handlerMappings != null) {
        // 遍历, 有个不为空就返回
        for (HandlerMapping mapping : this.handlerMappings) {
            // getHandler 接口实现方法
            HandlerExecutionChain handler = mapping.getHandler(request);
            if (handler != null) {
                return handler;
            }
        }
    }
    return null;
}
```
### HandlerMapping 接口
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611820501842-a2ca5c04-df5a-466d-8170-deb30ae7a160.png#averageHue=%233f4751&height=210&id=HKznk&originHeight=210&originWidth=808&originalType=binary&ratio=1&rotation=0&showTitle=false&size=36462&status=done&style=none&title=&width=808)
### 默认的 handlerMappings
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611820804095-57fb6cf2-2d3f-4b41-934b-38ac79ff945b.png#averageHue=%233e464f&height=173&id=hnaJt&originHeight=173&originWidth=539&originalType=binary&ratio=1&rotation=0&showTitle=false&size=17402&status=done&style=none&title=&width=539)
### 遍历获取处理器 mapping.getHandler(request)
#### 过程
org.springframework.web.servlet.**DispatcherServlet**#**doDispatch**
org.springframework.web.servlet.**DispatcherServlet**#**getHandler**
**- org.springframework.web.servlet.handler.AbstractHandlerMapping#getHandler**

   - org.springframework.web.servlet.mvc.method.**RequestMappingInfoHandlerMapping#getHandlerInternal  子类**
      - org.springframework.web.servlet.handler.**AbstractHandlerMethodMapping#getHandlerInternal   父抽象类**

                   org.springframework.web.servlet.handler.**AbstractHandlerMethodMapping**#**lookupHandlerMethod 返回 HandlerMethod **

   - ** **org.springframework.web.servlet.handler.**AbstractHandlerMapping**#**getHandlerExecutionChain  返回 **HandlerExecutionChain

#### 源码
```java

// org.springframework.web.servlet.handler.AbstractHandlerMapping#getHandler
public final HandlerExecutionChain getHandler(HttpServletRequest request) throws Exception {
    // 获取处理器 
	Object handler = getHandlerInternal(request);
	....
    // 获取处理器执行链
    HandlerExecutionChain executionChain = getHandlerExecutionChain(handler, request);
    
    // 添加一个跨域的CorsInterceptor
    ...
    
    return executionChain;
}

// org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping#getHandlerInternal
protected HandlerMethod getHandlerInternal(HttpServletRequest request) throws Exception {
    request.removeAttribute(PRODUCIBLE_MEDIA_TYPES_ATTRIBUTE);
    try {
        // 父抽象类方法
        return super.getHandlerInternal(request);
    }
    finally {
        ProducesRequestCondition.clearMediaTypesAttribute(request);
    }
}

// org.springframework.web.servlet.handler.AbstractHandlerMethodMapping#getHandlerInternal
@Override
protected HandlerMethod getHandlerInternal(HttpServletRequest request) throws Exception {
    // 根据request获取请求地址 url, 内部 String url = request.getRequestURI();
    String lookupPath = getUrlPathHelper().getLookupPathForRequest(request);
    // uri保存在request域
    request.setAttribute(LOOKUP_PATH, lookupPath);
    // 加锁
    this.mappingRegistry.acquireReadLock();
    try {
        // 核心方法, 获取具体方法
        HandlerMethod handlerMethod = lookupHandlerMethod(lookupPath, request);
        return (handlerMethod != null ? handlerMethod.createWithResolvedBean() : null);
    }
    finally {
        this.mappingRegistry.releaseReadLock();
    }
}

// 查找当前请求的最佳匹配处理程序方法。 如果找到多个匹配项，则选择最佳匹配项
// org.springframework.web.servlet.handler.AbstractHandlerMethodMapping#lookupHandlerMethod
protected HandlerMethod lookupHandlerMethod(String lookupPath, HttpServletRequest request) throws Exception {
 	List<Match> matches = new ArrayList<>();
    // 从uri注册表找匹配, this.urlLookup.get(urlPath);  
	List<T> directPathMatches = this.mappingRegistry.getMappingsByUrl(lookupPath);
    if (directPathMatches != null) {
        // 添加到matches
        addMatchingMappings(directPathMatches, matches, request);
    }
    if (matches.isEmpty()) {
        // No choice but to go through all mappings...
        addMatchingMappings(this.mappingRegistry.getMappings().keySet(), matches, request);
    }
    if (!matches.isEmpty()) {
        // 先取出第一个
        Match bestMatch = matches.get(0);
        if (matches.size() > 1) { 
            // 多个 找最佳匹配
         	...
            
        }
    	request.setAttribute(BEST_MATCHING_HANDLER_ATTRIBUTE, bestMatch.handlerMethod);
        
        // request.setAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, lookupPath);
        handleMatch(bestMatch.mapping, lookupPath, request);
        return bestMatch.handlerMethod;
    }
}

// 最后封装处理器执行链 HandlerExecutionChain
// org.springframework.web.servlet.handler.AbstractHandlerMapping#getHandlerExecutionChain
protected HandlerExecutionChain getHandlerExecutionChain(Object handler, HttpServletRequest request) {
    HandlerExecutionChain chain = (handler instanceof HandlerExecutionChain ?
                                   (HandlerExecutionChain) handler : new HandlerExecutionChain(handler));
	// 从request域取, 
    String lookupPath = this.urlPathHelper.getLookupPathForRequest(request, LOOKUP_PATH);
    // 拦截器
    for (HandlerInterceptor interceptor : this.adaptedInterceptors) {
        if (interceptor instanceof MappedInterceptor) {
            MappedInterceptor mappedInterceptor = (MappedInterceptor) interceptor;
            if (mappedInterceptor.matches(lookupPath, this.pathMatcher)) {
                chain.addInterceptor(mappedInterceptor.getInterceptor());
            }
        }
        else {
            chain.addInterceptor(interceptor);
        }
    }
    return chain;
}
```
#### 映射注册表
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611823937686-f6e428f1-05c9-47c4-b63d-c6ef07232e50.png#averageHue=%233e454d&height=200&id=OHC8l&originHeight=200&originWidth=972&originalType=binary&ratio=1&rotation=0&showTitle=false&size=28370&status=done&style=none&title=&width=972)
#### 所有映射和处理程序方法
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611824737645-33fd513a-bb2e-4f3a-a961-f7fa92ce529c.png#averageHue=%233e434a&height=381&id=l5IST&originHeight=381&originWidth=847&originalType=binary&ratio=1&rotation=0&showTitle=false&size=61862&status=done&style=none&title=&width=847)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611824446891-4ad5a236-adf6-43ed-a53d-60dbb02728e9.png#averageHue=%232e2c2b&height=295&id=b4I8L&originHeight=295&originWidth=630&originalType=binary&ratio=1&rotation=0&showTitle=false&size=30532&status=done&style=none&title=&width=630)
#### 处理器执行链
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611825839856-5ecbcc78-f5f0-4a76-8e95-55335e15e90c.png#averageHue=%233d4349&height=402&id=ZMXtc&originHeight=402&originWidth=1138&originalType=binary&ratio=1&rotation=0&showTitle=false&size=71186&status=done&style=none&title=&width=1138)
## 1.2. 找处理器适配器,

```java
// 找处理器适配器
HandlerAdapter ha = getHandlerAdapter(mappedHandler.getHandler());

```
org.springframework.web.servlet.**DispatcherServlet**#**doDispatch**
**    **org.springframework.web.servlet.DispatcherServlet#**getHandlerAdapter  返回 **HandlerAdapter

### **getHandlerAdapter 一个遍历**
```java
private List<HandlerAdapter> handlerAdapters;

protected HandlerAdapter getHandlerAdapter(Object handler) throws ServletException {
    if (this.handlerAdapters != null) {
        for (HandlerAdapter adapter : this.handlerAdapters) {
            // 直接HandlerAdapter的实现类的supports方法, 看是否满足, 满足返回适配器
            if (adapter.supports(handler)) {
                return adapter;
            }
        }
    }
    throw new ServletException("No adapter for handler [" + handler +
                               "]: The DispatcherServlet configuration needs to include a HandlerAdapter that supports this handler");
}
```
 自带的
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611826856174-29fea3b2-76fe-497d-ab3a-c9610ab51ffb.png#averageHue=%23404955&height=119&id=cG5u0&originHeight=119&originWidth=383&originalType=binary&ratio=1&rotation=0&showTitle=false&size=9682&status=done&style=none&title=&width=383)
接口
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611826907178-d773330d-eab6-432e-a550-e1819256131b.png#averageHue=%233d4246&height=81&id=C6Qed&originHeight=81&originWidth=495&originalType=binary&ratio=1&rotation=0&showTitle=false&size=7659&status=done&style=none&title=&width=495)
@RequestMapping的适配器
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611827096953-7d7cbf98-2ce1-4979-a23e-b41dc3cf1a1c.png#averageHue=%233e444b&height=259&id=cEA0E&originHeight=259&originWidth=658&originalType=binary&ratio=1&rotation=0&showTitle=false&size=34239&status=done&style=none&title=&width=658)
## 1.3. 执行目标处理器方法, 返回 ModelAndView

```java
ModelAndView mv = null;
// 执行方法
mv = ha.handle(processedRequest, response, mappedHandler.getHandler());

```
### 执行过程
org.springframework.web.servlet.**DispatcherServlet**#**doDispatch**
    org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter#handleInternal    返回抽象方法handleInternal
        org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter#handleInternal   实现抽象类的方法
            org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter#invokeHandlerMethod 
   org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod#invokeAndHandle
org.springframework.web.method.support.InvocableHandlerMethod#invokeForRequest
     org.springframework.web.method.support.InvocableHandlerMethod#getMethodArgumentValues  返回参数数组
  org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite#handleReturnValue 处理返回值

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611827562336-70660488-ae6c-43e6-b0f3-b1fb7d4acba9.png#averageHue=%232d2c2b&height=469&id=npY5I&originHeight=469&originWidth=1029&originalType=binary&ratio=1&rotation=0&showTitle=false&size=88392&status=done&style=none&title=&width=1029)

### handleInternal

```java
// 内部执行
// org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter#handleInternal
@Override
protected ModelAndView handleInternal(HttpServletRequest request,
                                      HttpServletResponse response, HandlerMethod handlerMethod) throws Exception {
    ModelAndView mav;
    checkRequest(request);

    // 同步方法
    // Execute invokeHandlerMethod in synchronized block if required.
    if (this.synchronizeOnSession) {
     	...
    }
    else {
        // No synchronization on session demanded at all...
        // 真正执行的地方
        mav = invokeHandlerMethod(request, response, handlerMethod);
    }
    
    ...
        
    return mav;
}
```
### invokeHandlerMethod

```java

// org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter#invokeHandlerMethod
protected ModelAndView invokeHandlerMethod(HttpServletRequest request,
    HttpServletResponse response, HandlerMethod handlerMethod) throws Exception {

    ServletWebRequest webRequest = new ServletWebRequest(request, response);
    try {
       // 初始化
        ...

        invocableMethod.invokeAndHandle(webRequest, mavContainer);
        if (asyncManager.isConcurrentHandlingStarted()) {
            return null;
        }

        return getModelAndView(mavContainer, modelFactory, webRequest);
    }
    finally {
        webRequest.requestCompleted();
    }
}
```
### invokeAndHandle

```java

// org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod#invokeAndHandle
public void invokeAndHandle(ServletWebRequest webRequest, ModelAndViewContainer mavContainer,
    Object... providedArgs) throws Exception {
	// 返回值
    Object returnValue = invokeForRequest(webRequest, mavContainer, providedArgs);
    // 响应码
    setResponseStatus(webRequest);

    if (returnValue == null) {
        if (isRequestNotModified(webRequest) || getResponseStatus() != null || mavContainer.isRequestHandled()) {
            disableContentCachingIfNecessary(webRequest);
            mavContainer.setRequestHandled(true);
            return;
        }
    }
    // 错误信息
    else if (StringUtils.hasText(getResponseStatusReason())) {
        mavContainer.setRequestHandled(true);
        return;
    }

    mavContainer.setRequestHandled(false);
    Assert.state(this.returnValueHandlers != null, "No return value handlers");
    try {
        // 返回值处理器
        this.returnValueHandlers.handleReturnValue(
            returnValue, getReturnValueType(returnValue), mavContainer, webRequest);
    }
    catch (Exception ex) {
        if (logger.isTraceEnabled()) {
            logger.trace(formatErrorForReturnValue(returnValue), ex);
        }
        throw ex;
    }
}


```

### invokeForRequest

```java
// 调用方法
// org.springframework.web.method.support.InvocableHandlerMethod#invokeForRequest
@Nullable
public Object invokeForRequest(NativeWebRequest request, @Nullable ModelAndViewContainer mavContainer,
                               Object... providedArgs) throws Exception {
	
    // 获取当前请求的方法参数值，检查提供的参数值并返回配置的参数解析器。结果数组将传递到doInvoke 
    Object[] args = getMethodArgumentValues(request, mavContainer, providedArgs);
    if (logger.isTraceEnabled()) {
        logger.trace("Arguments: " + Arrays.toString(args));
    }
    // 执行方法
    return doInvoke(args);
}



```
### getMethodArgumentValues 获取参数

```java
// org.springframework.web.method.support.InvocableHandlerMethod#getMethodArgumentValues
// 获取参数值
protected Object[] getMethodArgumentValues(NativeWebRequest request, @Nullable ModelAndViewContainer mavContainer,
			Object... providedArgs) throws Exception {
 
    MethodParameter[] parameters = getMethodParameters();
    Object[] args = new Object[parameters.length];
    for (int i = 0; i < parameters.length; i++) {
		MethodParameter parameter = parameters[i];
        parameter.initParameterNameDiscovery(this.parameterNameDiscoverer);
        args[i] = findProvidedArgument(parameter, providedArgs);
        if (args[i] != null) {
            continue;
        }
        // 寻找支持处理当前参数的参数解析器, 遍历所有参数解析器, 有一个满足就返回, 并将参数解析器缓存
        if (!this.resolvers.supportsParameter(parameter)) {
            throw new IllegalStateException(formatArgumentError(parameter, "No suitable resolver"));
        }
        try {
            找到合适的参数解析器处理参数, 返回参数值
            args[i] = this.resolvers.resolveArgument(parameter, mavContainer, request, this.dataBinderFactory);
        }
    }
    ...
    return args;
}
```
#### 参数解析器
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611885396206-3d34acd5-1031-4e1d-ba98-2159189e8a65.png#averageHue=%233d4145&height=66&id=ZrGyl&originHeight=66&originWidth=764&originalType=binary&ratio=1&rotation=0&showTitle=false&size=8368&status=done&style=none&title=&width=764)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611884684326-3e5216f4-c607-409e-9394-76788829825d.png#averageHue=%233d4348&height=520&id=XOewX&originHeight=520&originWidth=535&originalType=binary&ratio=1&rotation=0&showTitle=false&size=58895&status=done&style=none&title=&width=535)

### handleReturnValue 处理返回值

```java

// 遍历已注册的HandlerMethodReturnValueHandlers并调用支持它的HandlerMethodReturnValueHandlers 。
// org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite#handleReturnValue
@Override
public void handleReturnValue(@Nullable Object returnValue, MethodParameter returnType,
                              ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws Exception {
	
    HandlerMethodReturnValueHandler handler = selectHandler(returnValue, returnType);
    if (handler == null) {
        throw new IllegalArgumentException("Unknown return value type: " + returnType.getParameterType().getName());
    }
    // 找到后 执行处理返回值
    handler.handleReturnValue(returnValue, returnType, mavContainer, webRequest);
}

private final List<HandlerMethodReturnValueHandler> returnValueHandlers = new ArrayList<>();

@Nullable
private HandlerMethodReturnValueHandler selectHandler(@Nullable Object value, MethodParameter returnType) {
    boolean isAsyncValue = isAsyncReturnValue(value, returnType);
    for (HandlerMethodReturnValueHandler handler : this.returnValueHandlers) {
        if (isAsyncValue && !(handler instanceof AsyncHandlerMethodReturnValueHandler)) {
            continue;
        }
        // 遍历 寻找执行处理的返回值处理器
        if (handler.supportsReturnType(returnType)) {
            return handler;
        }
    }
    return null;
}
```
#### 返回值处理器
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611886185384-a492e7c2-066f-4c77-bc8e-ddb4bc5971b5.png#averageHue=%233e4245&height=69&id=tsunw&originHeight=69&originWidth=674&originalType=binary&ratio=1&rotation=0&showTitle=false&size=7998&status=done&style=none&title=&width=674)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611885677779-13497a3a-3385-4d61-b09b-3eecfbbcbcbb.png#averageHue=%233d4248&height=384&id=di4Jk&originHeight=384&originWidth=665&originalType=binary&ratio=1&rotation=0&showTitle=false&size=42256&status=done&style=none&title=&width=665)
#### 过程

- 1、返回值处理器判断是否支持这种类型返回值 supportsReturnType
- 2、返回值处理器调用 handleReturnValue 进行处理
- 3、RequestResponseBodyMethodProcessor 可以处理返回值标了@ResponseBody 注解的。
   - 1.  利用 MessageConverters 进行处理 将数据写为json
      - 1、内容协商（浏览器默认会以请求头的方式告诉服务器他能接受什么样的内容类型）
      - 2、服务器最终根据自己自身的能力，决定服务器能生产出什么样内容类型的数据，
      - 3、SpringMVC会挨个遍历所有容器底层的 HttpMessageConverter ，看谁能处理？
         - 1、得到MappingJackson2HttpMessageConverter可以将对象写为json
         - 2、利用MappingJackson2HttpMessageConverter将对象转为json再写出去。









