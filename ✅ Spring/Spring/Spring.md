![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1613485253977-710a7b1c-e0f0-4f5d-9a21-2a14d115faea.png#averageHue=%2384b761&height=382&id=owHUC&originHeight=763&originWidth=1048&originalType=binary&ratio=1&rotation=0&showTitle=false&size=409425&status=done&style=none&title=&width=524)
# 1. 什么是Spring

- 轻量级, 一站式的解决方案, 控制反转ioc, 面向切面aop
- 

# 2. IOC
## 2.1 概念

- 控制反转 Inversion of Control
- **把对象的创建, 初始化, 销毁交给spring, 程序员不在new, 只要注入Spring创建好的对象**
- 目的: Spring来管理对象
- 优点: 单例,  降低耦合
## 2.2 IOC核心容器
[https://juejin.cn/post/6844904127051513864#heading-13](https://juejin.cn/post/6844904127051513864#heading-13)
### BeanFactory
> Bean 工厂是工厂模式的一个实现，提供了控制反转功能，用来把应用的配置和依赖从真正的应用代码中分离。
> 最常用的就是org.springframework.beans.factory.xml.XmlBeanFactory ，它根据XML文件中的定义加载beans。
> 该容器从XML 文件读取配置元数据并用它去创建一个完全配置的系统或应用。


> 是Spring里面最底层的接口，包含了各种Bean的定义，读取bean配置文档，管理bean的加载、实例化，控制bean的生命周期，维护bean之间的依赖关系。

- BeanFactroy采用的是**延迟加载**形式来注入Bean的，使用Bean时才对该Bean进行加载实例化。如果有属性没注入会报错
   - 加载配置文件，解析成 **BeanDefinition **放在 Map 里。
   - 调用 getBean 的时候，从 BeanDefinition 所属的 Map 里，拿出 Class 对象进行实例化，同时，如果有依赖关系，将递归调用 getBean 方法 —— 完成依赖注入。

### ApplicationContext 

- ApplicationContext，它是在容器启动时，**一次性创建了所有的Bean。启动时检测所有依赖注入**


![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411746722-836a00fd-7779-4da9-8ba3-8ce96e33081d.png#averageHue=%23ebe0b6&height=703&id=xpgQL&originHeight=703&originWidth=1170&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1170)
## 2.3 IOC初始化流程
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411746702-b649574a-5004-4a49-b476-5c0b90fc91d8.png#averageHue=%23f2f2f2&height=118&id=rrwAh&originHeight=118&originWidth=932&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=932)
## 2.4 DI 依赖注入

- 对象之间的关联关系如何绑定
### 构造器注入
> 构造器依赖注入通过容器触发一个类的构造器来实现的，该类有一系列参数，**每个参数代表一个对其他类的依赖**。


```xml
<bean id="userDao4Oracle" class="com.tgb.spring.dao.UserDao4OracleImpl"/>               
<bean id="userManager" class="com.tgb.spring.manager.UserManagerImpl">    
  <!-- (1)userManager使用了userDao，Ioc是自动创建相应的UserDao实现，都是由容器管理-->    
  <!-- (2)在UserManager中提供构造函数，让spring将UserDao实现注入（DI）过来 -->    
  <!-- (3)让spring管理我们对象的创建和依赖关系，必须将依赖关系配置到spring的核心配置文件中 -->       
  <constructor-arg ref="userDao4Oracle"/>    
</bean>
```

### setter方法注入
> Setter方法注入是容器通过调用无参构造器或无参static工厂 方法实例化bean之后，调用该bean的setter方法，即实现了基于setter的依赖注入。



```xml
<bean id="userDao4Oracle" class="com.tgb.spring.dao.UserDao4OracleImpl"/>           
<bean id="userManager" class="com.tgb.spring.manager.UserManagerImpl">    
  <!-- (1)userManager使用了userDao，Ioc是自动创建相应的UserDao实现，都是由容器管理-->    
  <!-- (2)在UserManager中提供构造函数，让spring将UserDao实现注入（DI）过来 -->    
  <!-- (3)让spring管理我们对象的创建和依赖关系，必须将依赖关系配置到spring的核心配置文件中 -->     
  <property name="userDao" ref="userDao4Oracle"></property>    
</bean>
```

### 接口注入
> 由于在灵活性和易用性比较差，现在从Spring4开始已被废弃。


# 3. Spring Bean
## 3.0 Bean的生命周期
[https://juejin.cn/post/6844904127051513864#heading-27](https://juejin.cn/post/6844904127051513864#heading-27)

```java
一个bean从创建到销毁的过程.
1. 创建bean实例
2. 给bean设置属性值, 其他bean的引用
3. 将 Bean 实 例 传 递 给 Bean 后 置 处 理 器 的 postProcessBeforeInitialization 方法 
4. 调用 Bean 的初始化方法(init-method)  @PostConstruct 
5. 将 Bean 实 例 传 递 给 Bean 后 置 处 理 器 的 postProcessAfterInitialization 方法 
6. bean可以用了
7. 当容器关闭时, 调用 Bean 的销毁方法(destroy-method) @PreDestory
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1613489146970-8aff689b-35e3-4960-bfc9-495e1a7af351.png#averageHue=%23f0f0f0&height=583&id=vw0FO&originHeight=583&originWidth=1139&originalType=binary&ratio=1&rotation=0&showTitle=false&size=255282&status=done&style=none&title=&width=1139)
## 3.1 Spring Bean 循环依赖

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411746830-a107eb65-e39c-49ee-9218-63400849c929.png#averageHue=%23faf7f1&height=200&id=FSp5N&originHeight=249&originWidth=308&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=248)

```
n个类互相引用, 互相注入
```

## 3.2 三级缓存

## 3.3 bean的自动装配

```
Spring自动管理bean之间的依赖关系, 程序员自动引用即可

@Autowired  
<context:annotation-config />
使用@Autowired注解自动装配的过程是怎样的？
  1. 如果查询结果刚好为一个，就将该bean装配给@Autowired指定的数据；
  2. 如果查询的结果不止一个，那么@Autowired会根据名称来查找；
  3. 如果上述查找的结果为空，那么会抛出异常。解决方法时，使用required=false。
```
### 5种自动装配

- no：默认的方式是不进行自动装配的，通过手工设置ref属性来进行装配bean。
- byName：通过bean的名称进行自动装配，如果一个bean的 property 与另一bean 的name 相同，就进行自动装配。
- byType：通过参数的数据类型（类）进行自动装配。
- constructor：利用构造函数进行装配，并且构造函数的参数通过byType进行装配。
- autodetect：自动探测，如果有构造方法，通过 construct的方式自动装配，否则使用 byType的方式自动装配。
## 3.4 Spring框架中的单例bean是线程安全的吗？

- 不是，Spring框架中的**单例bean不是线程安全的。**
- 作用域默认**singleton**
- **作用域改成prototype就是线程安全，每次使用bean都是 new Bean();**
- **无状态可以保证线程安全**（dao），有状态不行（model）
   - **有状态就是有数据存储功能。**
   - **无状态就是不会保存数据。**
## 3.5 Spring如何处理线程并发问题？
> 只有无状态的Bean才可以在多线程环境下共享, **singleton**

- **ThreadLocal**和**线程同步机制**都是为了**解决多线程中相同变量的访问冲突问题**。
   - 同步机制， 时间换空间，变量只有一份， **加锁**
   - ThreadLocal， 空间换时间，**每个线程都有一份独立变量副本**
      - ThreadLocal提供了线程安全的共享对象
# 4. AOP
[https://juejin.cn/post/6844904127051513864#heading-47](https://juejin.cn/post/6844904127051513864#heading-47)

## 4.1 概念

- 面向切面编程(Aspect-Oriented Programming)
-  将程序中的交叉业务逻辑（比如安全，日志，事务等），封装成一个切面，然后注入到目标对象（具体业务逻辑）中去
## 4.2 实现

- 静态代理: AspectJ  
   - 客户端 -> 代理 -> 目标
   - 代理和目标实现同一个接口, 代理增强目标, 客户端只能调用代理
- 动态代理Spring AOP  
   - 通过反射
   -  JDK动态代理:** 只提供接口代理**, **不支持类**
      - 目标接口, 代理handler类实现InvocationHandler接口, 重写invoke法
      - Proxy代理类对象在程序运行时由JVM根据反射机制动态生成的
      -  jdk运行期间，动态创建class字节码并加载到JVM。
-  CGLIB动态代理  
   - CGLIB（Code Generation Library）
   - **通过继承**
   - 是一个代码生成的类库，可以**在运行时动态的生成指定类的一个子类对象**，并覆盖其中特定方法并添加增强代码，从而实现AOP。

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411746756-a2caac12-9236-401b-881f-42d7b679fc8d.png#averageHue=%23fbfaf8&height=296&id=huYNh&originHeight=416&originWidth=1799&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1280)
## 4.3 名称解释

```java
切面Aspect
 - 公共的代码, 日志, 事务, 权限认证等
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411746687-3db55f5d-3107-4764-bfbb-90ed8f499fd8.png#averageHue=%23fcfbfb&height=553&id=j1Cyo&originHeight=553&originWidth=937&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=937)
## 4.4 通知类型Advice


```basic
前置通知（Before）：在目标方法被调用之前调用通知功能；
后置通知（After）：在目标方法完成之后调用通知，此时不会关心方法的输出是什么；
返回通知（After-returning ）：在目标方法成功执行之后调用通知；
异常通知（After-throwing）：在目标方法抛出异常后调用通知；
环绕通知（Around）：通知包裹了被通知的方法，在被通知的方法调用之前和调用之后执行自定义的行为。
```

## 4.5 代码实现
### aop执行顺序
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611737656255-71577402-7c25-41c0-9eba-00f43b4ba119.png#averageHue=%232b2b2b&height=179&id=LkEIl&originHeight=179&originWidth=921&originalType=binary&ratio=1&rotation=0&showTitle=false&size=38058&status=done&style=none&title=&width=921)

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611737932910-101c5195-048e-408f-96ac-cc5701e5dc0f.png#averageHue=%232b2a2a&height=152&id=IT28T&originHeight=152&originWidth=990&originalType=binary&ratio=1&rotation=0&showTitle=false&size=39699&status=done&style=none&title=&width=990)

# 5. Spring的事务管理
## 5.1 事务如何配置

- 编程式: 代码侵入
- 声明式: 注解@Transactional,   xml



## 5.2 事务传播机制

- 事务的传播性一般用在事务嵌套的场景，比如一个事务方法里面调用了另外一个事务方法，那么两个方法是各自作为独立的方法提交还是内层的事务合并到外层的事务一起提交，这就是需要事务传播机制的配置来确定怎么样执行。
- **Propagation - 传播**
- **@Transactional(propagation=Propagation.REQUIRED)  默认**

```java
org.springframework.transaction.TransactionDefinition   接口

0. PROPAGATION_REQUIRED
 - REQUIRED必须, 
 - Spring默认
 - 如果外层有事务，则当前事务加入到外层事务，一块提交，一块回滚。如果外层没有事务，新建一个事务执行
   - 当前位置是否存在事务(包括前后, 方法被调用), 存在就加入, 没有就新建一个事务.
 - Support a current transaction; create a new one if none exists.
 - int PROPAGATION_REQUIRED = 0;
 
1. PROPAGATION_SUPPORT
 - 如果外层有事务，则加入外层事务，如果外层没有事务，则直接使用非事务方式执行。完全依赖外层的事务
   - 当前位置有事务就加入事务, 没有就不执行事务.
 - Support a current transaction; execute non-transactionally if none exists.
 - int PROPAGATION_SUPPORTS = 1;

2. PROPAGATION_MANDATORY
 - 强制性 MANDATORY
 - 与NEVER相反，如果外层没有事务，则抛出异常
   - 当前位置必须要有事务, 否则就抛出异常.
 - Support a current transaction; throw an exception if no current transaction exists.
 - int PROPAGATION_MANDATORY = 2;

3. PROPAGATION_REQUES_NEW
 - 每次都会新开启一个事务，同时把外层事务挂起，当当前事务执行完毕，恢复上层事务的执行。如果外层没有事务，执行当前新开启的事务即可
   - 先创建新事务, 当前位置(前后)有事务存在先暂停.
 - Create a new transaction, suspending the current transaction if one exists.
 int PROPAGATION_REQUIRES_NEW = 3;

4. PROPAGATION_NOT_SUPPORT
 - 该传播机制不支持事务，如果外层存在事务则挂起，执行完当前代码，则恢复外层事务，无论是否异常都不会回滚当前的代码
   - 不支持事务.
 - Do not support a current transaction; rather always execute non-transactionally.
 - int PROPAGATION_NOT_SUPPORTED = 4;
 
5. PROPAGATION_NEVER
 - 不支持外层事务，即如果外层有事务就抛出异常
 - Do not support a current transaction; throw an exception if a current transaction.
 - int PROPAGATION_NEVER = 5;

7. PROPAGATION_NESTED
 - 嵌套 NESTED
 - 可以保存状态保存点，当前事务回滚到某一个点，从而避免所有的嵌套事务都回滚，即各自回滚各自的，如果子事务没有把异常吃掉，基本还是会引起全部回滚的。
 - Execute within a nested transaction if a current transaction exists, behave like {@link #PROPAGATION_REQUIRED} otherwise. 
 - int PROPAGATION_NESTED = 6;

```



## 5.3 隔离级别

- **@Transactional(isolation = Isolation.READ_UNCOMMITTED)**
### _ISOLATION_READ_UNCOMMITTED 读未提交_

- int _ISOLATION_READ_UNCOMMITTED _= 1; 
- 表示可能**发生脏读，复的读取和幻像读取**。
- 此级别允许在提交该行中的任何更改之前，由一个事务更改的行被另一事务读取（“脏读”）。 如果任何更改被回滚，则第二个事务将检索到无效的行。
- Spring默认级别
### _ISOLATION_READ_COMMITTED 读已提交_

- int _ISOLATION_READ_COMMITTED _= 2;
- 指示**防止脏读**； 可能**会发生不可重复的读取和幻像读取**。
- 此级别仅禁止事务读取其中包含**未提交**的更改的行。
### _ISOLATION_REPEATABLE_READ 可重复读_

- int _ISOLATION_REPEATABLE_READ _= 4;
- 指示**防止脏读和不可重复读**； 可能会**发生幻像读取**。
- 此级别禁止事务读取其中未提交更改的行，并且还禁止以下情况：
   - 一个事务读取一行，第二个事务更改该行，第一个事务重新读取该行，**第二次获得不同的值**（“不可重复读取”）。
### _ISOLATION_SERIALIZABLE 序列化_

- int _ISOLATION_SERIALIZABLE _= 8;
- 表示**防止脏读，不可重复读和幻像读**.
- 


## 5.4 事务失效的场景

```java
1. 数据库引擎不支持事务, MyISAM不支持, innoDB支持
2. 对象没有被Spring容器管理, @Service注解没加
3. 方法不是public
4. 自身调用, 

@Service
public class BizServiceImpl implements BizService {    
    //@Transactional   如果这里没有加事务，那么抛出异常后，保存方法不会回滚
    public void save(Order order) {
        saveOrder(order);//调用了当前类的方法        
        throw new NullPointerException();
	}    
    @Transactional    
    public void saveOrder(Order order) {        
        // save order        
        throw new NullPointerException();//保存方法内部的异常也不会回滚 
    }
}

5. 数据源没有配置事务管理器 
@Bean
public PlatformTransactionManager transactionManager(DataSource dataSource) {
    return new DataSourceTransactionManager(dataSource);
}

6. 隔离级别不支持事务, Propagation.NOT_SUPPORTED
7. 异常被吃了, try-catch, 抛出异常才会回滚 throw, throws
8. 异常类型错误: 只有error和RuntimeException, 事务才生效, 受检异常无效
 - throw new RuntimeException(“xxxxxxxxxxxx”); 事务回滚
 - throw new Exception(“xxxxxxxxxxxx”); 事务没有回滚
 
```


# 6. Spring注解

- xml配置 **<context:annotation-config/> **开启注解


```java
@Autowired   只按照byType注入 类名    
	required=true 依赖对象必须存在
@Qualifier("userDao")  按照名称（byName）来装 
@Resource  名称， 找不到名称，就通过类型


```


# 7. 用了哪些设计模式

- 工厂模式 
   - IOC **BeanFactory  简单工厂模式，用来创建对象的实例。**
- 代理模式 
   - AOP **JDK的动态代理和CGLIB字节码生成技术  **
- 单例模式 
   - **bean **IOC容器里只会有一个bean
- 模板方法
   - 解决重复代码问题
   - **RestTemplate, JmsTemplate, JpaTemplate  **
- 观察者模式  
   - 定义对象键一种一对多的依赖关系，**当一个对象的状态发生改变时，所有依赖于它的对象都会得到通知被制动更新  **
   - 如Spring中listener的实现--**ApplicationListener**

