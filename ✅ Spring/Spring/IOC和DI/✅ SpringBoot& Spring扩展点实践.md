# @Bean initMethod+destroyMethod
```java
public class TestBean {

    private String testId;

    private String testName;

    public String getTestId() {
        return testId;
    }

    public void setTestId(String testId) {
        this.testId = testId;
    }

    public String getTestName() {
        return testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }

    public String print() {
        return IdUtil.fastSimpleUUID();
    }

    public void initMethod() {
        System.out.println("initMethod");
    }

    public void destroyMethod() {
        System.out.println("destroyMethod");
    }

}
```
```java
@Configuration
public class TestConfig {

    @Bean(initMethod = "initMethod", destroyMethod = "destroyMethod")
    public TestBean testBean() {

        return new TestBean();
    }

    @PostConstruct
    public void postConstruct() {

        System.out.println("postConstruct");
    }
}
```
## 说明

- initMethod  在Bean创建完成之后执行，init方法需要写在bean类内

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680354053181-004a8c94-fcf5-443d-92dd-8dde9ae8456b.png#averageHue=%231f2124&clientId=u6c146043-6ae0-4&from=paste&height=117&id=u631afeaa&originHeight=234&originWidth=2154&originalType=binary&ratio=2&rotation=0&showTitle=false&size=41099&status=done&style=none&taskId=uabac5bc4-9769-4e6e-acef-fd98dc4a59c&title=&width=1077)

- destroyMethod 在Spring Bean容器销毁后执行

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680354088713-f19ca759-6279-4d77-82e5-ce48fc828cb8.png#averageHue=%23202124&clientId=u6c146043-6ae0-4&from=paste&height=450&id=ue2cc0c99&originHeight=900&originWidth=2232&originalType=binary&ratio=2&rotation=0&showTitle=false&size=229750&status=done&style=none&taskId=uddf5dece-5906-4dce-bc18-e9f799d1750&title=&width=1116)
# @PostConstruct
> 在Bean创建完成之后执行其内的带PostConstruct注解的方法

## 加载顺序

- 根据Bean的全限定名字母顺序排序加载。
- 可使用@Order(n)，修改Bean的加载顺序，n越大优先级越小。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680354758644-bcf10fa1-6b78-471c-9aca-31929584514e.png#averageHue=%23202226&clientId=u6c146043-6ae0-4&from=paste&height=274&id=uaff818c3&originHeight=548&originWidth=2244&originalType=binary&ratio=2&rotation=0&showTitle=false&size=135766&status=done&style=none&taskId=u775d6722-282f-4645-b927-1118b1a5a9e&title=&width=1122)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680354792840-14ae6530-cd8d-4b7e-a967-31baa4649533.png#averageHue=%231f2327&clientId=u6c146043-6ae0-4&from=paste&height=514&id=uc0d35408&originHeight=1028&originWidth=2190&originalType=binary&ratio=2&rotation=0&showTitle=false&size=170184&status=done&style=none&taskId=u3441e529-da89-429d-a90a-458c10d2f4b&title=&width=1095)

- bean内有多个PostConstruct方法 按代码顺序执行。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680354715926-0bebea57-1f19-43e2-8aab-00a3e529df62.png#averageHue=%23202226&clientId=u6c146043-6ae0-4&from=paste&height=757&id=u397bea26&originHeight=1514&originWidth=2244&originalType=binary&ratio=2&rotation=0&showTitle=false&size=183630&status=done&style=none&taskId=u10699706-43fb-43b5-b89b-9b9660a199c&title=&width=1122)
# InitializingBean 接口
> bean创建完成后执行afterPropertiesSet方法。

```java
@Component
public class InitializingBeanDemo implements InitializingBean {
    /**
     * 在包含它的BeanFactory设置了所有bean属性并满足了BeanFactoryAware、ApplicationContextAware等之后，由它调用。
     * 此方法允许bean实例在设置完所有bean属性后执行其总体配置验证和最终初始化。
     * @throws Exception in the event of misconfiguration (such as failure to set an
     *                   essential property) or if initialization fails for any other reason
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        System.out.println("InitializingBeanDemo afterPropertiesSet");
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680355279826-9644d857-f1b5-4862-aaf0-3c04c39b8e0e.png#averageHue=%2321262a&clientId=u6c146043-6ae0-4&from=paste&height=69&id=u15483a95&originHeight=138&originWidth=2134&originalType=binary&ratio=2&rotation=0&showTitle=false&size=31693&status=done&style=none&taskId=ub9509864-4155-48b9-87b2-2eac508bf83&title=&width=1067)
# **DisposableBean接口**
> 容器销毁后执行

```java
@Component
public class DisposableBeanDemo implements DisposableBean {
    /**
     * Invoked by the containing {@code BeanFactory} on destruction of a bean.
     *
     * @throws Exception in case of shutdown errors. Exceptions will get logged
     *                   but not rethrown to allow other beans to release their resources as well.
     */
    @Override
    public void destroy() throws Exception {
        System.out.println("DisposableBeanDemo destroy");
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680359030610-8928f682-b833-45f0-beae-d9b0dc078fe0.png#averageHue=%23232528&clientId=u6c146043-6ae0-4&from=paste&height=277&id=u936c8b41&originHeight=554&originWidth=2312&originalType=binary&ratio=2&rotation=0&showTitle=false&size=89164&status=done&style=none&taskId=u9dae5cbd-d889-49f6-a228-167ec67f711&title=&width=1156)
# CommandLineRunner 接口
> 在Spring容器初始化完成之后调用回调方法。

```java
@Component
public class CommandLineRunnerDemo implements CommandLineRunner {
    /**
     * 回调函数用于运行bean
     *
     * @param args incoming main method arguments 传入的主方法参数
     * @throws Exception on error
     */
    @Override
    public void run(String... args) throws Exception {
        System.out.println("CommandLineRunnerDemo CommandLineRunner run.");
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680355488755-9a12cb52-c45d-427c-809c-c283fe07c1a2.png#averageHue=%23212835&clientId=u6c146043-6ae0-4&from=paste&height=159&id=u1dbe14ca&originHeight=318&originWidth=2186&originalType=binary&ratio=2&rotation=0&showTitle=false&size=74453&status=done&style=none&taskId=ue5d2e2d5-1281-4893-a475-d5ca8f1e26a&title=&width=1093)
# **ApplicationContextInitializer接口**
```java
@FunctionalInterface
public interface ApplicationContextInitializer<C extends ConfigurableApplicationContext> {

	/**
	 * Initialize the given application context.
	 * @param applicationContext the application to configure
	 */
	void initialize(C applicationContext);

}
```
> 在Spring容器**ApplicationContext**创建之前执行。进行一些初始化操作。
> 利用这时候class还没被类加载器加载的时机，进行动态字节码注入等操作。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680356356906-621faaec-1b34-4ba4-8217-400cc970f87c.png#averageHue=%23222328&clientId=u6c146043-6ae0-4&from=paste&height=355&id=uf11cdce8&originHeight=710&originWidth=1976&originalType=binary&ratio=2&rotation=0&showTitle=false&size=123126&status=done&style=none&taskId=u25410430-0aa5-4945-b8a3-9aa0e1a94e9&title=&width=988)
## 使用
> 在src/main/resources/META-INF/spring.factories配置

```java
org.springframework.context.ApplicationContextInitializer= \
  com.kk0.extension.bean.ApplicationContextInitializerDemo
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680356384811-bee9c930-a5de-4c46-9f1e-01abdb9bcdc0.png#averageHue=%23202226&clientId=u6c146043-6ae0-4&from=paste&height=366&id=u74f2b178&originHeight=732&originWidth=2228&originalType=binary&ratio=2&rotation=0&showTitle=false&size=103327&status=done&style=none&taskId=ub4df1959-625b-417e-860e-0a6020ff362&title=&width=1114)
# **BeanDefinitionRegistryPostProcessor接口**
```java
public interface BeanDefinitionRegistryPostProcessor extends BeanFactoryPostProcessor {

	/**
	 * Modify the application context's internal bean definition registry after its
	 * standard initialization. All regular bean definitions will have been loaded,
	 * but no beans will have been instantiated yet. This allows for adding further
	 * bean definitions before the next post-processing phase kicks in.
	 * @param registry the bean definition registry used by the application context
	 * @throws org.springframework.beans.BeansException in case of errors
	 */
	void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException;

}
```
> 在BeanFactory实例化任何bean定义之前对这些定义进行修改或添加新的bean定义。
> 该接口实现类在第一个实现，其新增的Bean定义也提前创建。
> 这可以定义一个Bean。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680357504466-a8e4ff71-4de0-4d49-be12-05a8ad50224c.png#averageHue=%23212225&clientId=u6c146043-6ae0-4&from=paste&height=697&id=u15c97a6a&originHeight=1394&originWidth=1876&originalType=binary&ratio=2&rotation=0&showTitle=false&size=231168&status=done&style=none&taskId=ua671b9c9-5e94-4c9b-9420-0978daeda3b&title=&width=938)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680357526051-de55bfb3-42d7-488c-a2a6-f5c6b3bca89c.png#averageHue=%23202226&clientId=u6c146043-6ae0-4&from=paste&height=224&id=uc53861b8&originHeight=448&originWidth=2226&originalType=binary&ratio=2&rotation=0&showTitle=false&size=104367&status=done&style=none&taskId=u18b53f03-58ed-4dfb-a23c-6e5da61d38a&title=&width=1113)
# **BeanFactoryPostProcessor接口**
```java
@FunctionalInterface
public interface BeanFactoryPostProcessor {

	/**
	 * Modify the application context's internal bean factory after its standard
	 * initialization. All bean definitions will have been loaded, but no beans
	 * will have been instantiated yet. This allows for overriding or adding
	 * properties even to eager-initializing beans.
	 * @param beanFactory the bean factory used by the application context
	 * @throws org.springframework.beans.BeansException in case of errors
     * 在标准初始化后修改应用程序上下文的内部bean工厂。
     * 所有的bean定义都将被加载，但是还没有bean被实例化。这允许重写或添加属性，甚至可以直接初始化bean。
     *
	 */
	void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException;

}
```
> 这个接口是beanFactory的扩展接口，调用时机在spring在读取beanDefinition信息之后，实例化bean之前。
> 可以修改已经注册的beanDefinition的元信息。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680358718618-24f474e6-4187-47c3-9f3b-d0452dded02b.png#averageHue=%23202124&clientId=u6c146043-6ae0-4&from=paste&height=501&id=ub31129bb&originHeight=1002&originWidth=1890&originalType=binary&ratio=2&rotation=0&showTitle=false&size=184029&status=done&style=none&taskId=ubc8042ed-14d3-474b-b9df-e59cf76bfef&title=&width=945)
# **BeanFactoryAware接口**
```java
public interface BeanFactoryAware extends Aware {

	/**
	 * Callback that supplies the owning factory to a bean instance.
	 * <p>Invoked after the population of normal bean properties
	 * but before an initialization callback such as
	 * {@link InitializingBean#afterPropertiesSet()} or a custom init-method.
	 * @param beanFactory owning BeanFactory (never {@code null}).
	 * The bean can immediately call methods on the factory.
	 * @throws BeansException in case of initialization errors
	 * @see BeanInitializationException
	 */
	void setBeanFactory(BeanFactory beanFactory) throws BeansException;

}
```
> 在普通bean属性填充之后，但在初始化回调(如InitializingBean.afterPropertiesSet()或自定义初始化方法之前调用。
> BeanFactory实例传递给该类，并调用它的setBeanFactory()方法，从而使该类可以与Spring容器进行交互。

```java
public class BeanFactoryAwareDemo implements BeanFactoryAware {

    private BeanFactory beanFactory;

    /**
     * 在普通bean属性填充之后，但在初始化回调(如InitializingBean.afterPropertiesSet()或自定义初始化方法之前调用
     * @param beanFactory owning BeanFactory (never {@code null}).
     *                    The bean can immediately call methods on the factory.
     * @throws BeansException in case of initialization errors
     * @see BeanInitializationException
     */
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
```
# **BeanNameAware接口**
```java
public interface BeanNameAware extends Aware {

	/**
	 * Set the name of the bean in the bean factory that created this bean.
	 * <p>Invoked after population of normal bean properties but before an
	 * init callback such as {@link InitializingBean#afterPropertiesSet()}
	 * or a custom init-method.
	 * @param name the name of the bean in the factory.
	 * Note that this name is the actual bean name used in the factory, which may
	 * differ from the originally specified name: in particular for inner bean
	 * names, the actual bean name might have been made unique through appending
	 * "#..." suffixes. Use the {@link BeanFactoryUtils#originalBeanName(String)}
	 * method to extract the original bean name (without suffix), if desired.
	 */
	void setBeanName(String name);

}
```
> Spring容器会将该类对应的Bean的名称传递给该类。
> 可以自行修改这个beanName的值。

```java
public class BeanNameAwareDemo implements BeanNameAware {

    private String beanName;

    /**
     * 在创建此bean的bean工厂中设置bean的名称。
     * 在填充普通bean属性之后，但在初始化回调(如InitializingBean.afterPropertiesSet()或自定义初始化方法之前调用
     *
     * @param name the name of the bean in the factory.
     *             Note that this name is the actual bean name used in the factory, which may
     *             differ from the originally specified name: in particular for inner bean
     *             names, the actual bean name might have been made unique through appending
     *             "#..." suffixes. Use the {@link BeanFactoryUtils#originalBeanName(String)}
     *             method to extract the original bean name (without suffix), if desired.
     */
    @Override
    public void setBeanName(String name) {
        System.out.println("BeanNameAwareDemo beanName: " + name);
        this.beanName = name;
    }
}
```
# **FactoryBean接口**
```java
public interface FactoryBean<T> {

	/**
	 * The name of an attribute that can be
	 * {@link org.springframework.core.AttributeAccessor#setAttribute set} on a
	 * {@link org.springframework.beans.factory.config.BeanDefinition} so that
	 * factory beans can signal their object type when it can't be deduced from
	 * the factory bean class.
	 * @since 5.2
	 */
	String OBJECT_TYPE_ATTRIBUTE = "factoryBeanObjectType";


	/**
	 * Return an instance (possibly shared or independent) of the object
	 * managed by this factory.
	 * <p>As with a {@link BeanFactory}, this allows support for both the
	 * Singleton and Prototype design pattern.
	 * <p>If this FactoryBean is not fully initialized yet at the time of
	 * the call (for example because it is involved in a circular reference),
	 * throw a corresponding {@link FactoryBeanNotInitializedException}.
	 * <p>As of Spring 2.0, FactoryBeans are allowed to return {@code null}
	 * objects. The factory will consider this as normal value to be used; it
	 * will not throw a FactoryBeanNotInitializedException in this case anymore.
	 * FactoryBean implementations are encouraged to throw
	 * FactoryBeanNotInitializedException themselves now, as appropriate.
	 * @return an instance of the bean (can be {@code null})
	 * @throws Exception in case of creation errors
	 * @see FactoryBeanNotInitializedException
	 */
	@Nullable
	T getObject() throws Exception;

	/**
	 * Return the type of object that this FactoryBean creates,
	 * or {@code null} if not known in advance.
	 * <p>This allows one to check for specific types of beans without
	 * instantiating objects, for example on autowiring.
	 * <p>In the case of implementations that are creating a singleton object,
	 * this method should try to avoid singleton creation as far as possible;
	 * it should rather estimate the type in advance.
	 * For prototypes, returning a meaningful type here is advisable too.
	 * <p>This method can be called <i>before</i> this FactoryBean has
	 * been fully initialized. It must not rely on state created during
	 * initialization; of course, it can still use such state if available.
	 * <p><b>NOTE:</b> Autowiring will simply ignore FactoryBeans that return
	 * {@code null} here. Therefore, it is highly recommended to implement
	 * this method properly, using the current state of the FactoryBean.
	 * @return the type of object that this FactoryBean creates,
	 * or {@code null} if not known at the time of the call
	 * @see ListableBeanFactory#getBeansOfType
	 */
	@Nullable
	Class<?> getObjectType();

	/**
	 * Is the object managed by this factory a singleton? That is,
	 * will {@link #getObject()} always return the same object
	 * (a reference that can be cached)?
	 * <p><b>NOTE:</b> If a FactoryBean indicates to hold a singleton object,
	 * the object returned from {@code getObject()} might get cached
	 * by the owning BeanFactory. Hence, do not return {@code true}
	 * unless the FactoryBean always exposes the same reference.
	 * <p>The singleton status of the FactoryBean itself will generally
	 * be provided by the owning BeanFactory; usually, it has to be
	 * defined as singleton there.
	 * <p><b>NOTE:</b> This method returning {@code false} does not
	 * necessarily indicate that returned objects are independent instances.
	 * An implementation of the extended {@link SmartFactoryBean} interface
	 * may explicitly indicate independent instances through its
	 * {@link SmartFactoryBean#isPrototype()} method. Plain {@link FactoryBean}
	 * implementations which do not implement this extended interface are
	 * simply assumed to always return independent instances if the
	 * {@code isSingleton()} implementation returns {@code false}.
	 * <p>The default implementation returns {@code true}, since a
	 * {@code FactoryBean} typically manages a singleton instance.
	 * @return whether the exposed object is a singleton
	 * @see #getObject()
	 * @see SmartFactoryBean#isPrototype()
	 */
	default boolean isSingleton() {
		return true;
	}

}
```
> FactoryBean 是一个工厂bean，可以自定义bean创建过程。
> 可以代理对象创建。

```java
@Component
public class FactoryBeanDemo implements FactoryBean<FactoryBeanDemo.NewFactoryBean> {

    /**
     * 返回这个工厂管理的对象的实例(可能是共享的，也可能是独立的)。
     * @return
     * @throws Exception
     */
    @Override
    public NewFactoryBean getObject() throws Exception {
        System.out.println("FactoryBeanDemo getObject");
        return new NewFactoryBean();
    }

    /**
     * 返回这个FactoryBean创建的对象类型
     * @return
     */
    @Override
    public Class<?> getObjectType() {
        return NewFactoryBean.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

    public static class NewFactoryBean {

        public String print() {
            return "FactoryBeanDemo NewFactoryBean print: " + IdUtil.fastSimpleUUID();
        }

    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680442998026-8630313d-4df4-4b5b-895f-96a1dd94e0cc.png#averageHue=%2327282b&clientId=ub06bd0ef-e555-4&from=paste&height=163&id=u8f62d925&originHeight=326&originWidth=1632&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59126&status=done&style=none&taskId=u63a99c1a-20bd-4d60-964d-8e59363a0ed&title=&width=816)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680443019376-1ee43704-1dfa-4ea1-9c57-0a7368b6bae2.png#averageHue=%23232629&clientId=ub06bd0ef-e555-4&from=paste&height=93&id=u19c66942&originHeight=186&originWidth=2250&originalType=binary&ratio=2&rotation=0&showTitle=false&size=46491&status=done&style=none&taskId=u41aa0552-f67b-4da7-a00b-7dd2ffd0527&title=&width=1125)
# **SmartInitializingSingleton接口**
```java
public interface SmartInitializingSingleton {

	/**
	 * Invoked right at the end of the singleton pre-instantiation phase,
	 * with a guarantee that all regular singleton beans have been created
	 * already. {@link ListableBeanFactory#getBeansOfType} calls within
	 * this method won't trigger accidental side effects during bootstrap.
	 * <p><b>NOTE:</b> This callback won't be triggered for singleton beans
	 * lazily initialized on demand after {@link BeanFactory} bootstrap,
	 * and not for any other bean scope either. Carefully use it for beans
	 * with the intended bootstrap semantics only.
	 */
	void afterSingletonsInstantiated();

}

```
> 在spring容器管理的所有单例对象（非懒加载对象）初始化完成之后调用的回调接口。
> 

```java
@Component
public class SmartInitializingSingletonDemo implements SmartInitializingSingleton {

    /**
     * 在单例预实例化阶段结束时调用，并保证已经创建了所有常规单例bean
     */
    @Override
    public void afterSingletonsInstantiated() {

        System.out.println("SmartInitializingSingletonDemo");
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680443698457-324b51df-e4ec-4622-9867-1f5dfe951115.png#averageHue=%23212326&clientId=ub06bd0ef-e555-4&from=paste&height=215&id=u1edec1dd&originHeight=430&originWidth=2288&originalType=binary&ratio=2&rotation=0&showTitle=false&size=103080&status=done&style=none&taskId=u0488d859-90a8-4bde-b5fa-63a93509592&title=&width=1144)
# **ApplicationListener接口**
> 事件监听器接口，可以用于监听Spring应用上下文中的事件并处理这些事件。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680444366779-7b930eea-7a7b-4782-8d09-ef5dcab63ff7.png#averageHue=%233f3425&clientId=ub06bd0ef-e555-4&from=paste&height=119&id=uffa99ef2&originHeight=238&originWidth=1088&originalType=binary&ratio=2&rotation=0&showTitle=false&size=68842&status=done&style=none&taskId=uc528d7f3-7085-4635-89c3-a1b49d57de5&title=&width=544)

- **ContextRefreshedEvent**
   - ApplicationContext 被初始化或刷新时，该事件被发布。
- **ContextStartedEvent**
- **ContextStoppedEvent**
- **ContextClosedEvent**
- **RequestHandledEvent**
   - DispatcherServlet被使用了。每次http请求后发布一个事件。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680444771853-b1e11106-9428-4795-985f-e24ac2915db3.png#averageHue=%23222327&clientId=ub06bd0ef-e555-4&from=paste&height=270&id=ubd9168e5&originHeight=540&originWidth=2290&originalType=binary&ratio=2&rotation=0&showTitle=false&size=166707&status=done&style=none&taskId=u7138d702-23ce-43c1-8a3e-0959d3cb1a6&title=&width=1145)

## context事件分析
```java
public abstract class AbstractApplicationContext extends DefaultResourceLoader
		implements ConfigurableApplicationContext {

	@Override
	public void publishEvent(ApplicationEvent event) {
		publishEvent(event, null);
	}

	@Override
	public void publishEvent(Object event) {
		publishEvent(event, null);
	}    

	protected void publishEvent(Object event, @Nullable ResolvableType eventType) {
		Assert.notNull(event, "Event must not be null");

		// Decorate event as an ApplicationEvent if necessary
		ApplicationEvent applicationEvent;
		if (event instanceof ApplicationEvent) {
			applicationEvent = (ApplicationEvent) event;
		}
		else {
			applicationEvent = new PayloadApplicationEvent<>(this, event);
			if (eventType == null) {
				eventType = ((PayloadApplicationEvent<?>) applicationEvent).getResolvableType();
			}
		}

		// Multicast right now if possible - or lazily once the multicaster is initialized
		if (this.earlyApplicationEvents != null) {
			this.earlyApplicationEvents.add(applicationEvent);
		}
		else {
			getApplicationEventMulticaster().multicastEvent(applicationEvent, eventType);
		}

		// Publish event via parent context as well...
		if (this.parent != null) {
			if (this.parent instanceof AbstractApplicationContext) {
				((AbstractApplicationContext) this.parent).publishEvent(event, eventType);
			}
			else {
				this.parent.publishEvent(event);
			}
		}
	}

	protected void finishRefresh() {
		// Clear context-level resource caches (such as ASM metadata from scanning).
		clearResourceCaches();

		// Initialize lifecycle processor for this context.
		initLifecycleProcessor();

		// Propagate refresh to lifecycle processor first.
		getLifecycleProcessor().onRefresh();

		// Publish the final event.
		publishEvent(new ContextRefreshedEvent(this));

		// Participate in LiveBeansView MBean, if active.
		if (!NativeDetector.inNativeImage()) {
			LiveBeansView.registerApplicationContext(this);
		}
	}

	protected void doClose() {
		// Check whether an actual close attempt is necessary...
		if (this.active.get() && this.closed.compareAndSet(false, true)) {
			if (logger.isDebugEnabled()) {
				logger.debug("Closing " + this);
			}

			if (!NativeDetector.inNativeImage()) {
				LiveBeansView.unregisterApplicationContext(this);
			}

			try {
				// Publish shutdown event.
				publishEvent(new ContextClosedEvent(this));
			}
			catch (Throwable ex) {
				logger.warn("Exception thrown from ApplicationListener handling ContextClosedEvent", ex);
			}

			// Stop all Lifecycle beans, to avoid delays during individual destruction.
			if (this.lifecycleProcessor != null) {
				try {
					this.lifecycleProcessor.onClose();
				}
				catch (Throwable ex) {
					logger.warn("Exception thrown from LifecycleProcessor on context close", ex);
				}
			}

			// Destroy all cached singletons in the context's BeanFactory.
			destroyBeans();

			// Close the state of this context itself.
			closeBeanFactory();

			// Let subclasses do some final clean-up if they wish...
			onClose();

			// Reset local application listeners to pre-refresh state.
			if (this.earlyApplicationListeners != null) {
				this.applicationListeners.clear();
				this.applicationListeners.addAll(this.earlyApplicationListeners);
			}

			// Switch to inactive.
			this.active.set(false);
		}
	}

	@Override
	public void start() {
		getLifecycleProcessor().start();
		publishEvent(new ContextStartedEvent(this));
	}

	@Override
	public void stop() {
		getLifecycleProcessor().stop();
		publishEvent(new ContextStoppedEvent(this));
	}
            
        
}
```
# **BeanPostProcessor 接口**
> 需要对Bean对象进行一些额外的定制或处理。例如，某些Bean的初始化需要特殊的处理逻辑，或者Bean在实例化后需要进行一些其他操作。
> BeanPostProcessor接口就提供了一种实现这些需求的方式。其本身并不直接对Bean对象进行修改，而是通过在Bean对象生命周期的前后插入处理代码，达到对Bean对象的增强目的。

```java
public interface BeanPostProcessor {

	/**
	 * Apply this {@code BeanPostProcessor} to the given new bean instance <i>before</i> any bean
	 * initialization callbacks (like InitializingBean's {@code afterPropertiesSet}
	 * or a custom init-method). The bean will already be populated with property values.
	 * The returned bean instance may be a wrapper around the original.
	 * <p>The default implementation returns the given {@code bean} as-is.
	 * @param bean the new bean instance
	 * @param beanName the name of the bean
	 * @return the bean instance to use, either the original or a wrapped one;
	 * if {@code null}, no subsequent BeanPostProcessors will be invoked
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet
	 */
	@Nullable
	default Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
		return bean;
	}

	/**
	 * Apply this {@code BeanPostProcessor} to the given new bean instance <i>after</i> any bean
	 * initialization callbacks (like InitializingBean's {@code afterPropertiesSet}
	 * or a custom init-method). The bean will already be populated with property values.
	 * The returned bean instance may be a wrapper around the original.
	 * <p>In case of a FactoryBean, this callback will be invoked for both the FactoryBean
	 * instance and the objects created by the FactoryBean (as of Spring 2.0). The
	 * post-processor can decide whether to apply to either the FactoryBean or created
	 * objects or both through corresponding {@code bean instanceof FactoryBean} checks.
	 * <p>This callback will also be invoked after a short-circuiting triggered by a
	 * {@link InstantiationAwareBeanPostProcessor#postProcessBeforeInstantiation} method,
	 * in contrast to all other {@code BeanPostProcessor} callbacks.
	 * <p>The default implementation returns the given {@code bean} as-is.
	 * @param bean the new bean instance
	 * @param beanName the name of the bean
	 * @return the bean instance to use, either the original or a wrapped one;
	 * if {@code null}, no subsequent BeanPostProcessors will be invoked
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet
	 * @see org.springframework.beans.factory.FactoryBean
	 */
	@Nullable
	default Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		return bean;
	}

}

```
```java
public class BeanPostProcessorDemo implements BeanPostProcessor {

    // bean初始化之前
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof BeanPostProcessorBean) {
            BeanPostProcessorBean beanPostProcessorBean = (BeanPostProcessorBean) bean;
            beanPostProcessorBean.setBeanName("BeanPostProcessorDemo postProcessBeforeInitialization Bean Name");
            System.out.println("BeanPostProcessorDemo postProcessBeforeInitialization");
            return beanPostProcessorBean;
        }
        return bean;
    }

    // bean初始化之后
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof BeanPostProcessorBean) {
            BeanPostProcessorBean beanPostProcessorBean = (BeanPostProcessorBean) bean;
            System.out.println(beanPostProcessorBean.getBeanName());
            System.out.println("BeanPostProcessorDemo postProcessAfterInitialization");
        }
        return bean;
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680446766196-f3b750b6-3500-4b1d-8719-ec18eb41ad6a.png#averageHue=%2324292f&clientId=ub06bd0ef-e555-4&from=paste&height=115&id=uc3191cac&originHeight=230&originWidth=2202&originalType=binary&ratio=2&rotation=0&showTitle=false&size=55790&status=done&style=none&taskId=u0722d7e5-e6ce-41da-b9f7-0e33eef39be&title=&width=1101)
# **ApplicationContextAwareProcessor**

- EnvironmentAware
   - 用于获取EnviromentAware的一个扩展类
- EmbeddedValueResolverAware
   - 用于获取StringValueResolver的一个扩展类
- ResourceLoaderAware
   - 用于获取ResourceLoader的一个扩展类，ResourceLoader可以用于获取classpath内所有的资源对象。
- ApplicationEventPublisherAware
   - 用于获取ApplicationEventPublisher的一个扩展类
- MessageSourceAware
   - 用于获取MessageSource的一个扩展类，国际化
- ApplicationStartupAware
   - 

- ApplicationContextAware
   - 用来获取ApplicationContext的一个扩展类
```java
class ApplicationContextAwareProcessor implements BeanPostProcessor {


	private final ConfigurableApplicationContext applicationContext;

	private final StringValueResolver embeddedValueResolver;


	/**
	 * Create a new ApplicationContextAwareProcessor for the given context.
	 */
	public ApplicationContextAwareProcessor(ConfigurableApplicationContext applicationContext) {
		this.applicationContext = applicationContext;
		this.embeddedValueResolver = new EmbeddedValueResolver(applicationContext.getBeanFactory());
	}


	@Override
	@Nullable
	public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
		if (!(bean instanceof EnvironmentAware || bean instanceof EmbeddedValueResolverAware ||
				bean instanceof ResourceLoaderAware || bean instanceof ApplicationEventPublisherAware ||
				bean instanceof MessageSourceAware || bean instanceof ApplicationContextAware ||
				bean instanceof ApplicationStartupAware)) {
			return bean;
		}

		AccessControlContext acc = null;

		if (System.getSecurityManager() != null) {
			acc = this.applicationContext.getBeanFactory().getAccessControlContext();
		}

		if (acc != null) {
			AccessController.doPrivileged((PrivilegedAction<Object>) () -> {
				invokeAwareInterfaces(bean);
				return null;
			}, acc);
		}
		else {
			invokeAwareInterfaces(bean);
		}

		return bean;
	}

    // Aware扩展点
    private void invokeAwareInterfaces(Object bean) {
		if (bean instanceof EnvironmentAware) {
			((EnvironmentAware) bean).setEnvironment(this.applicationContext.getEnvironment());
		}
		if (bean instanceof EmbeddedValueResolverAware) {
			((EmbeddedValueResolverAware) bean).setEmbeddedValueResolver(this.embeddedValueResolver);
		}
		if (bean instanceof ResourceLoaderAware) {
			((ResourceLoaderAware) bean).setResourceLoader(this.applicationContext);
		}
		if (bean instanceof ApplicationEventPublisherAware) {
			((ApplicationEventPublisherAware) bean).setApplicationEventPublisher(this.applicationContext);
		}
		if (bean instanceof MessageSourceAware) {
			((MessageSourceAware) bean).setMessageSource(this.applicationContext);
		}
		if (bean instanceof ApplicationStartupAware) {
			((ApplicationStartupAware) bean).setApplicationStartup(this.applicationContext.getApplicationStartup());
		}
		if (bean instanceof ApplicationContextAware) {
			((ApplicationContextAware) bean).setApplicationContext(this.applicationContext);
		}
	}


}
```
# **InstantiationAwareBeanPostProcessor**
> InstantiationAwareBeanPostProcessor是Spring框架中的一个接口，
> 它可以在bean实例化之前或之后，或者在属性注入之前或之后进行处理。
> 它提供了一种扩展机制，可以在Spring容器实例化和配置bean时对其进行修改或定制化。
> 在实际使用中，通过实现该接口，可以对bean对象进行自定义的初始化、AOP代理处理、属性填充等操作。

```java
public interface InstantiationAwareBeanPostProcessor extends BeanPostProcessor {

	/**
	 * Apply this BeanPostProcessor <i>before the target bean gets instantiated</i>.
	 * The returned bean object may be a proxy to use instead of the target bean,
	 * effectively suppressing default instantiation of the target bean.
	 * <p>If a non-null object is returned by this method, the bean creation process
	 * will be short-circuited. The only further processing applied is the
	 * {@link #postProcessAfterInitialization} callback from the configured
	 * {@link BeanPostProcessor BeanPostProcessors}.
	 * <p>This callback will be applied to bean definitions with their bean class,
	 * as well as to factory-method definitions in which case the returned bean type
	 * will be passed in here.
	 * <p>Post-processors may implement the extended
	 * {@link SmartInstantiationAwareBeanPostProcessor} interface in order
	 * to predict the type of the bean object that they are going to return here.
	 * <p>The default implementation returns {@code null}.
	 * @param beanClass the class of the bean to be instantiated
	 * @param beanName the name of the bean
	 * @return the bean object to expose instead of a default instance of the target bean,
	 * or {@code null} to proceed with default instantiation
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @see #postProcessAfterInstantiation
	 * @see org.springframework.beans.factory.support.AbstractBeanDefinition#getBeanClass()
	 * @see org.springframework.beans.factory.support.AbstractBeanDefinition#getFactoryMethodName()
	 */
	@Nullable
	default Object postProcessBeforeInstantiation(Class<?> beanClass, String beanName) throws BeansException {
		return null;
	}

	/**
	 * Perform operations after the bean has been instantiated, via a constructor or factory method,
	 * but before Spring property population (from explicit properties or autowiring) occurs.
	 * <p>This is the ideal callback for performing custom field injection on the given bean
	 * instance, right before Spring's autowiring kicks in.
	 * <p>The default implementation returns {@code true}.
	 * @param bean the bean instance created, with properties not having been set yet
	 * @param beanName the name of the bean
	 * @return {@code true} if properties should be set on the bean; {@code false}
	 * if property population should be skipped. Normal implementations should return {@code true}.
	 * Returning {@code false} will also prevent any subsequent InstantiationAwareBeanPostProcessor
	 * instances being invoked on this bean instance.
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @see #postProcessBeforeInstantiation
	 */
	default boolean postProcessAfterInstantiation(Object bean, String beanName) throws BeansException {
		return true;
	}

	/**
	 * Post-process the given property values before the factory applies them
	 * to the given bean, without any need for property descriptors.
	 * <p>Implementations should return {@code null} (the default) if they provide a custom
	 * {@link #postProcessPropertyValues} implementation, and {@code pvs} otherwise.
	 * In a future version of this interface (with {@link #postProcessPropertyValues} removed),
	 * the default implementation will return the given {@code pvs} as-is directly.
	 * @param pvs the property values that the factory is about to apply (never {@code null})
	 * @param bean the bean instance created, but whose properties have not yet been set
	 * @param beanName the name of the bean
	 * @return the actual property values to apply to the given bean (can be the passed-in
	 * PropertyValues instance), or {@code null} which proceeds with the existing properties
	 * but specifically continues with a call to {@link #postProcessPropertyValues}
	 * (requiring initialized {@code PropertyDescriptor}s for the current bean class)
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @since 5.1
	 * @see #postProcessPropertyValues
	 */
	@Nullable
	default PropertyValues postProcessProperties(PropertyValues pvs, Object bean, String beanName)
			throws BeansException {

		return null;
	}

	/**
	 * Post-process the given property values before the factory applies them
	 * to the given bean. Allows for checking whether all dependencies have been
	 * satisfied, for example based on a "Required" annotation on bean property setters.
	 * <p>Also allows for replacing the property values to apply, typically through
	 * creating a new MutablePropertyValues instance based on the original PropertyValues,
	 * adding or removing specific values.
	 * <p>The default implementation returns the given {@code pvs} as-is.
	 * @param pvs the property values that the factory is about to apply (never {@code null})
	 * @param pds the relevant property descriptors for the target bean (with ignored
	 * dependency types - which the factory handles specifically - already filtered out)
	 * @param bean the bean instance created, but whose properties have not yet been set
	 * @param beanName the name of the bean
	 * @return the actual property values to apply to the given bean (can be the passed-in
	 * PropertyValues instance), or {@code null} to skip property population
	 * @throws org.springframework.beans.BeansException in case of errors
	 * @see #postProcessProperties
	 * @see org.springframework.beans.MutablePropertyValues
	 * @deprecated as of 5.1, in favor of {@link #postProcessProperties(PropertyValues, Object, String)}
	 */
	@Deprecated
	@Nullable
	default PropertyValues postProcessPropertyValues(
			PropertyValues pvs, PropertyDescriptor[] pds, Object bean, String beanName) throws BeansException {

		return pvs;
	}

}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680447771950-e7a8c9f9-4cb4-4d25-ab2a-8fed305dd388.png#averageHue=%23f3f3f3&clientId=ub06bd0ef-e555-4&from=paste&height=474&id=uc18cccbc&originHeight=632&originWidth=1356&originalType=binary&ratio=2&rotation=0&showTitle=false&size=187484&status=done&style=none&taskId=u747e850e-abb9-4d84-8b47-5be4afa28d4&title=&width=1017)
# **SmartInstantiationAwareBeanPostProcessor**
> SmartInstantiationAwareBeanPostProcessor是Spring框架中的一个接口，它继承自InstantiationAwareBeanPostProcessor接口。
> 在Spring容器实例化Bean时，SmartInstantiationAwareBeanPostProcessor可以对Bean实例化过程进行操作，例如：替换Bean的类、替代Bean实例、设置Bean属性等。
> 与InstantiationAwareBeanPostProcessor不同的是，SmartInstantiationAwareBeanPostProcessor增加了两个方法（predictBeanType和getEarlyBeanReference），可以更精细地控制Bean实例化过程。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680447833710-4e8b1cbf-45cd-42c9-87f1-7b5cea1db42b.png#averageHue=%23a3a4a8&clientId=ub06bd0ef-e555-4&from=paste&height=302&id=u8d0f2d13&originHeight=604&originWidth=1194&originalType=binary&ratio=2&rotation=0&showTitle=false&size=44243&status=done&style=none&taskId=uce803abf-044c-4bbb-b86d-ed8a95c6104&title=&width=597)
```java
public interface SmartInstantiationAwareBeanPostProcessor extends InstantiationAwareBeanPostProcessor {

	/**
	 * Predict the type of the bean to be eventually returned from this
	 * processor's {@link #postProcessBeforeInstantiation} callback.
	 * <p>The default implementation returns {@code null}.
	 * @param beanClass the raw class of the bean
	 * @param beanName the name of the bean
	 * @return the type of the bean, or {@code null} if not predictable
	 * @throws org.springframework.beans.BeansException in case of errors
	 */
	@Nullable
	default Class<?> predictBeanType(Class<?> beanClass, String beanName) throws BeansException {
		return null;
	}

	/**
	 * Determine the candidate constructors to use for the given bean.
	 * <p>The default implementation returns {@code null}.
	 * @param beanClass the raw class of the bean (never {@code null})
	 * @param beanName the name of the bean
	 * @return the candidate constructors, or {@code null} if none specified
	 * @throws org.springframework.beans.BeansException in case of errors
	 */
	@Nullable
	default Constructor<?>[] determineCandidateConstructors(Class<?> beanClass, String beanName)
			throws BeansException {

		return null;
	}

	/**
	 * Obtain a reference for early access to the specified bean,
	 * typically for the purpose of resolving a circular reference.
	 * <p>This callback gives post-processors a chance to expose a wrapper
	 * early - that is, before the target bean instance is fully initialized.
	 * The exposed object should be equivalent to the what
	 * {@link #postProcessBeforeInitialization} / {@link #postProcessAfterInitialization}
	 * would expose otherwise. Note that the object returned by this method will
	 * be used as bean reference unless the post-processor returns a different
	 * wrapper from said post-process callbacks. In other words: Those post-process
	 * callbacks may either eventually expose the same reference or alternatively
	 * return the raw bean instance from those subsequent callbacks (if the wrapper
	 * for the affected bean has been built for a call to this method already,
	 * it will be exposes as final bean reference by default).
	 * <p>The default implementation returns the given {@code bean} as-is.
	 * @param bean the raw bean instance
	 * @param beanName the name of the bean
	 * @return the object to expose as bean reference
	 * (typically with the passed-in bean instance as default)
	 * @throws org.springframework.beans.BeansException in case of errors
	 */
	default Object getEarlyBeanReference(Object bean, String beanName) throws BeansException {
		return bean;
	}

}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680447863232-383c4af1-78c2-49b2-b6bc-9b0bb6ced14b.png#averageHue=%23e8e8e8&clientId=ub06bd0ef-e555-4&from=paste&height=444&id=u8df8b434&originHeight=592&originWidth=1334&originalType=binary&ratio=2&rotation=0&showTitle=false&size=229991&status=done&style=none&taskId=ue2fdcde9-0442-4413-983f-a33afdc0b05&title=&width=1001)
