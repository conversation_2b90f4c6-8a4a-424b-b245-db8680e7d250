# 事务传播行为
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681635500288-10e7c655-1f33-43e6-8ffd-83ac71787efa.png#averageHue=%2340464f&clientId=u565424be-c41f-4&from=paste&height=266&id=u91396a0e&originHeight=532&originWidth=1082&originalType=binary&ratio=2&rotation=0&showTitle=false&size=102170&status=done&style=none&taskId=ue94fe1f8-1ed4-4cd6-9a2f-00a0793e925&title=&width=541)
## 0 PROPAGATION_REQUIRED
> 默认行为。
当前存在事务，就用当前事务，否则新起一个事务。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681652854268-fe970a01-489c-459c-8436-f2329ecff91b.png#averageHue=%23f7f7f7&clientId=u467203bd-98f6-4&from=paste&id=ub3d0c753&originHeight=341&originWidth=800&originalType=binary&ratio=2&rotation=0&showTitle=false&size=50617&status=done&style=none&taskId=u82d587c4-a031-487e-bd49-2b9737674ec&title=)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681731998814-8ebe82b1-a509-417f-a868-88574c55e559.png#averageHue=%23232428&clientId=ud6d5250a-6ef4-4&from=paste&height=116&id=BWf5k&originHeight=232&originWidth=1496&originalType=binary&ratio=2&rotation=0&showTitle=false&size=44216&status=done&style=none&taskId=u7da3f3c9-c201-4542-9d07-25a6013912e&title=&width=748)
## 1 PROPAGATION_SUPPORTS
> 当前存在事务（required），则支持该事物。
> 若当前不存在事务，则不使用事务运行。空事务。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681731998814-8ebe82b1-a509-417f-a868-88574c55e559.png#averageHue=%23232428&clientId=ud6d5250a-6ef4-4&from=paste&height=116&id=ubed4222c&originHeight=232&originWidth=1496&originalType=binary&ratio=2&rotation=0&showTitle=false&size=44216&status=done&style=none&taskId=u7da3f3c9-c201-4542-9d07-25a6013912e&title=&width=748)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681731973223-37202be2-3692-4894-a218-6eec43b1c3b2.png#averageHue=%23353a43&clientId=ud6d5250a-6ef4-4&from=paste&height=144&id=u593c54a1&originHeight=288&originWidth=1342&originalType=binary&ratio=2&rotation=0&showTitle=false&size=82537&status=done&style=none&taskId=uf8e62344-76bd-41d3-bb37-6c982bbc29e&title=&width=671)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681730675684-88d87875-4413-4e3d-afe7-7aaa91a7cc54.png#averageHue=%23222427&clientId=uff7026f9-b349-4&from=paste&height=252&id=u90d69111&originHeight=504&originWidth=1896&originalType=binary&ratio=2&rotation=0&showTitle=false&size=110628&status=done&style=none&taskId=ucbb10fd3-5a1f-4deb-9355-84ebdf61df2&title=&width=948)
## 2 PROPAGATION_MANDATORY
> 支持当前事务，如果不存在，就抛出异常。
> 当前存在事务就使用当前的事务，不存在事务就抛出异常: No existing transaction found for transaction marked with propagation 'mandatory'

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681732202668-a4946556-a7f1-4e5e-922a-dcd01c748f24.png#averageHue=%23242528&clientId=ud6d5250a-6ef4-4&from=paste&height=133&id=u88a04a0f&originHeight=266&originWidth=1460&originalType=binary&ratio=2&rotation=0&showTitle=false&size=53529&status=done&style=none&taskId=ubd1c9f2e-b9b0-4d41-979f-57c62915513&title=&width=730)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681730246448-558a5291-5664-430f-bbf7-ba15f1844a5b.png#averageHue=%23242528&clientId=uff7026f9-b349-4&from=paste&height=480&id=uc560194e&originHeight=960&originWidth=1910&originalType=binary&ratio=2&rotation=0&showTitle=false&size=221231&status=done&style=none&taskId=ua01f1e43-d8a7-45d5-88ab-45f04f516fa&title=&width=955)
当外层方法存在事务时，使用外层方法的事务，此时事务传播行为是REQUIRED	。
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681730388202-9dcbdbae-d783-4ee5-be9e-49e30bb13098.png#averageHue=%23212327&clientId=uff7026f9-b349-4&from=paste&height=462&id=ue458c3b1&originHeight=924&originWidth=1666&originalType=binary&ratio=2&rotation=0&showTitle=false&size=190555&status=done&style=none&taskId=uc079a78d-3aa1-4089-8b99-16bc36ba740&title=&width=833)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681730477118-530d73aa-f698-4f2c-9756-472e253ec50a.png#averageHue=%232c2f35&clientId=uff7026f9-b349-4&from=paste&height=222&id=u3a1f0b1e&originHeight=444&originWidth=1974&originalType=binary&ratio=2&rotation=0&showTitle=false&size=163450&status=done&style=none&taskId=u68efefa4-e410-4b6e-b034-42cfc59a0d3&title=&width=987)
当外层方法不存在事务，如外层是supports，此时外层进行一次getTransaction以无事务执行；内存mandatory也getTransaction，但是会抛出异常。
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681732438220-b8e0ece4-8e72-47a1-b394-c758f2d30781.png#averageHue=%23242528&clientId=ud6d5250a-6ef4-4&from=paste&height=111&id=u912e43a5&originHeight=222&originWidth=1432&originalType=binary&ratio=2&rotation=0&showTitle=false&size=45049&status=done&style=none&taskId=uee84d084-84e8-4291-b826-27d67540c82&title=&width=716)
## 3 PROPAGATION_REQUIRES_NEW
> 创建一个新事务，如果存在当前事务，就挂起当前事务

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681653130332-275fa841-009a-46d2-93e8-d304f35be720.png#averageHue=%23f3f3f3&clientId=u467203bd-98f6-4&from=paste&id=u3285d6f6&originHeight=276&originWidth=800&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57974&status=done&style=none&taskId=ude8be35e-70c1-4da7-b2f6-6bb9268685c&title=)
外层方法存在事务。先挂起外部事务，内部再新开一个事务执行，执行完再恢复外部事务。
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681732777453-890a4af6-98ff-41ee-8423-e4d2b684c1d1.png#averageHue=%23242528&clientId=ud6d5250a-6ef4-4&from=paste&height=160&id=u939b8eb0&originHeight=320&originWidth=1464&originalType=binary&ratio=2&rotation=0&showTitle=false&size=63449&status=done&style=none&taskId=ud09689cf-3db7-4025-a594-7558c8eb5c0&title=&width=732)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681732939917-f70d2210-9062-45d7-920b-27eb1e3655d2.png#averageHue=%23222326&clientId=ud6d5250a-6ef4-4&from=paste&height=444&id=u51ab49ed&originHeight=888&originWidth=2008&originalType=binary&ratio=2&rotation=0&showTitle=false&size=150818&status=done&style=none&taskId=u13b4728d-6002-4dfa-8e52-fd6a4cdce3e&title=&width=1004)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681732972315-8c98fb1c-08b8-445f-962a-61edff545d3e.png#averageHue=%23222326&clientId=ud6d5250a-6ef4-4&from=paste&height=333&id=uf5a36708&originHeight=666&originWidth=1910&originalType=binary&ratio=2&rotation=0&showTitle=false&size=92740&status=done&style=none&taskId=u9760204a-09fd-4363-8bac-46d85a04948&title=&width=955)
过去suspend，把事务状态清空？
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681733092248-bfd7aee4-257f-44e6-8f54-84561805b644.png#averageHue=%2326272a&clientId=ud6d5250a-6ef4-4&from=paste&height=475&id=ubecf9ae8&originHeight=950&originWidth=2060&originalType=binary&ratio=2&rotation=0&showTitle=false&size=233368&status=done&style=none&taskId=ua8cc6d38-2bfb-4e32-998a-ec0163a5087&title=&width=1030)
### 真相
> id=49的先提交事务，插入数据库，只有一行。
> id=48的后提交事务。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681733827533-bda9fd2a-7fff-474d-aeab-216b440a9bf6.png#averageHue=%23242529&clientId=ud6d5250a-6ef4-4&from=paste&height=231&id=uce6afb46&originHeight=462&originWidth=1462&originalType=binary&ratio=2&rotation=0&showTitle=false&size=89485&status=done&style=none&taskId=u7fcde322-9138-4a50-acd1-3595011bc90&title=&width=731)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681733814338-13b8460d-5ec1-4a78-8792-3a60b88b8c97.png#averageHue=%23f9f9f8&clientId=ud6d5250a-6ef4-4&from=paste&height=136&id=ue7641901&originHeight=272&originWidth=1352&originalType=binary&ratio=2&rotation=0&showTitle=false&size=66545&status=done&style=none&taskId=u74b6a86b-ae34-4a5e-a668-424a0ccff7e&title=&width=676)
## 4. PROPAGATION_NOT_SUPPORTED
> 不支持当前事务，始终以非事务方式执行，若外部存在事务，把事务暂停。

## 5. PROPAGATION_NEVER
> 不支持当前事务，如果当前存在事务，就抛出异常

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681734510065-3aa2416e-3419-4ff9-9d84-cdfd21384066.png#averageHue=%23242529&clientId=ud6d5250a-6ef4-4&from=paste&height=352&id=uac57153f&originHeight=704&originWidth=1404&originalType=binary&ratio=2&rotation=0&showTitle=false&size=126655&status=done&style=none&taskId=u72009acf-610c-4814-81db-0fe94becf5c&title=&width=702)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681734488616-ba0a36df-0738-4c45-bbb7-5d55c4b436be.png#averageHue=%23232428&clientId=ud6d5250a-6ef4-4&from=paste&height=196&id=u297a97d0&originHeight=392&originWidth=1788&originalType=binary&ratio=2&rotation=0&showTitle=false&size=66874&status=done&style=none&taskId=u09f53a61-ae7b-4fc9-8b56-5e2a5d49bf7&title=&width=894)
## 6 PROPAGATION_NESTED
> 如果当前事务存在，则在嵌套事务中执行，内层事务依赖外层事务，如果外层失败，则会回滚内层，内层失败不影响外层


1. 如果外部事务不存在，则该标记方法会以类似REQUIRED的方式开启新的事务。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681734915733-36fcd09c-c7df-4228-be33-8ce94607f887.png#averageHue=%23222326&clientId=ud6d5250a-6ef4-4&from=paste&height=441&id=ude9e4933&originHeight=882&originWidth=1532&originalType=binary&ratio=2&rotation=0&showTitle=false&size=139728&status=done&style=none&taskId=u57b969a7-5d8c-4d88-b97c-9511202e68f&title=&width=766)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681734948699-05bb77ad-79b2-4bf9-b765-701f59fdac5e.png#averageHue=%23232428&clientId=ud6d5250a-6ef4-4&from=paste&height=535&id=u8fa66cfe&originHeight=1070&originWidth=2160&originalType=binary&ratio=2&rotation=0&showTitle=false&size=237912&status=done&style=none&taskId=ua8d13b46-7d50-42d6-a260-a195108cea7&title=&width=1080)

2. 内层nested异常，外层required正常; 外层required异常，内层nested正常。

都回滚了..
# 隔离级别
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681635488671-cc09a8c9-8af2-41bb-9a22-5a0f82c26241.png#averageHue=%23404752&clientId=u565424be-c41f-4&from=paste&height=229&id=YxitZ&originHeight=458&originWidth=1020&originalType=binary&ratio=2&rotation=0&showTitle=false&size=83556&status=done&style=none&taskId=u2ed678fc-3d79-4352-a1b7-e0c09f3157e&title=&width=510)
```java
public enum Isolation {

	/**
	 * 使用底层数据存储的默认隔离级别。所有其他级别都对应于JDBC隔离级别	
     * @see java.sql.Connection
	 */
	DEFAULT(TransactionDefinition.ISOLATION_DEFAULT),

	/**
	 * A constant indicating that dirty reads, non-repeatable reads and phantom reads
	 * can occur. This level allows a row changed by one transaction to be read by
	 * another transaction before any changes in that row have been committed
	 * (a "dirty read"). If any of the changes are rolled back, the second
	 * transaction will have retrieved an invalid row.
	 * @see java.sql.Connection#TRANSACTION_READ_UNCOMMITTED
	 */
	READ_UNCOMMITTED(TransactionDefinition.ISOLATION_READ_UNCOMMITTED),

	/**
	 * A constant indicating that dirty reads are prevented; non-repeatable reads
	 * and phantom reads can occur. This level only prohibits a transaction
	 * from reading a row with uncommitted changes in it.
	 * @see java.sql.Connection#TRANSACTION_READ_COMMITTED
	 */
	READ_COMMITTED(TransactionDefinition.ISOLATION_READ_COMMITTED),

	/**
	 * A constant indicating that dirty reads and non-repeatable reads are
	 * prevented; phantom reads can occur. This level prohibits a transaction
	 * from reading a row with uncommitted changes in it, and it also prohibits
	 * the situation where one transaction reads a row, a second transaction
	 * alters the row, and the first transaction rereads the row, getting
	 * different values the second time (a "non-repeatable read").
	 * @see java.sql.Connection#TRANSACTION_REPEATABLE_READ
	 */
	REPEATABLE_READ(TransactionDefinition.ISOLATION_REPEATABLE_READ),

	/**
	 * A constant indicating that dirty reads, non-repeatable reads and phantom
	 * reads are prevented. This level includes the prohibitions in
	 * {@code ISOLATION_REPEATABLE_READ} and further prohibits the situation
	 * where one transaction reads all rows that satisfy a {@code WHERE}
	 * condition, a second transaction inserts a row that satisfies that
	 * {@code WHERE} condition, and the first transaction rereads for the
	 * same condition, retrieving the additional "phantom" row in the second read.
	 * @see java.sql.Connection#TRANSACTION_SERIALIZABLE
	 */
	SERIALIZABLE(TransactionDefinition.ISOLATION_SERIALIZABLE);


	private final int value;


	Isolation(int value) {
		this.value = value;
	}

	public int value() {
		return this.value;
	}

}
```
## DEFAULT
> 使用底层数据存储的默认隔离级别。所有其他级别都对应于JDBC隔离级别。

## READ_UNCOMMITTED
> 指示可能发生脏读、不可重复读和幻像读的常量。
> 这个级别允许**由一个事务更改的行在提交该行中的任何更改之前被另一个事务读取**(“脏读”)。
> 如果任何更改被回滚，第二个事务将检索到无效的行。

## READ_COMMITTED
> 表示防止脏读的常量;
> 不可重复读取和幻影读取可能发生。
> 该级别仅禁止事务读取包含未提交更改的行。

## REPEATABLE_READ
> 一个常量，表示脏读和不可重复读被阻止; 
> 幻读可能发生。
> 该级别禁止事务读取包含未提交更改的行，还禁止一个事务读取一行，第二个事务修改该行，第一个事务重新读取该行，第二次获得不同的值(“不可重复读取”)。

## SERIALIZABLE
> 一个常量，表示防止脏读、不可重复读和幻影读。
> 该级别包括ISOLATION_REPEATABLE_READ中的禁止，并进一步禁止这样的情况:一个事务读取满足where条件的所有行，第二个事务插入满足该where条件的行，第一个事务重新读取相同的条件，在第二次读取中获取额外的“幻影”行。

