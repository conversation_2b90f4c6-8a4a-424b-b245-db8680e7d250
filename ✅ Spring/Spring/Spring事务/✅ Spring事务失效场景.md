```sql
CREATE TABLE `product_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `price` decimal(10,4) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
```
# @Transactional修饰的方法不可重写
> 修饰方法必须是可重写的，通过AOP实现。 

- private不可重写
- static不可重写
- final不可重写

![img.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681729718677-7973a967-6fdd-4142-bd2b-113abe75f447.png#averageHue=%2324262b&clientId=u94830458-aa35-4&from=paste&id=uda52913d&originHeight=146&originWidth=775&originalType=binary&ratio=2&rotation=0&showTitle=false&size=22411&status=done&style=none&taskId=u80f4380d-92a6-4496-8097-06187ef5fce&title=)
# 同一个类，互相调用
![img.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681729733718-8216d741-8a78-4f64-bae3-31dd718faccf.png#averageHue=%23202226&clientId=u94830458-aa35-4&from=paste&id=u3b64b15c&originHeight=289&originWidth=1119&originalType=binary&ratio=2&rotation=0&showTitle=false&size=40609&status=done&style=none&taskId=u371549e0-6afe-48c1-83dc-a664162c4fa&title=)
# 数据库引擎不支持事务
> MyIsam

# 事务方法异常被吃了
> 异常需要抛出。

# 使用错误的传播行为
[✅ Spring事务传播行为和隔离级别](https://www.yuque.com/newq/java-study/qhkixuqe7677hfq2?view=doc_embed)
# 多线程事务方法调用
