# 声明式事务
## @Transactional
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681651145519-d29ea8cc-244b-451c-947c-c59e9d8ed48d.png#averageHue=%2332353b&clientId=u95616f46-8deb-4&from=paste&height=397&id=kFsmS&originHeight=794&originWidth=1034&originalType=binary&ratio=2&rotation=0&showTitle=false&size=111348&status=done&style=none&taskId=u2fdff7f0-1c73-4930-a704-208584a840b&title=&width=517)
```java
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface Transactional {

	/**
	 * Alias for {@link #transactionManager}.
	 * @see #transactionManager
	 */
	@AliasFor("transactionManager")
	String value() default "";

	/**
	 * A <em>qualifier</em> value for the specified transaction.
	 * <p>May be used to determine the target transaction manager, matching the
	 * qualifier value (or the bean name) of a specific
	 * {@link org.springframework.transaction.TransactionManager TransactionManager}
	 * bean definition.
	 * @since 4.2
	 * @see #value
	 * @see org.springframework.transaction.PlatformTransactionManager
	 * @see org.springframework.transaction.ReactiveTransactionManager
	 */
	@AliasFor("value")
	String transactionManager() default "";

	/**
	 * Defines zero (0) or more transaction labels.
	 * <p>Labels may be used to describe a transaction, and they can be evaluated
	 * by individual transaction managers. Labels may serve a solely descriptive
	 * purpose or map to pre-defined transaction manager-specific options.
	 * <p>See the documentation of the actual transaction manager implementation
	 * for details on how it evaluates transaction labels.
	 * @since 5.3
	 * @see org.springframework.transaction.interceptor.DefaultTransactionAttribute#getLabels()
	 */
	String[] label() default {};

	/**
	 * The transaction propagation type.
	 * <p>Defaults to {@link Propagation#REQUIRED}.
	 * @see org.springframework.transaction.interceptor.TransactionAttribute#getPropagationBehavior()
	 */
	Propagation propagation() default Propagation.REQUIRED;

	/**
	 * The transaction isolation level.
	 * <p>Defaults to {@link Isolation#DEFAULT}.
	 * <p>Exclusively designed for use with {@link Propagation#REQUIRED} or
	 * {@link Propagation#REQUIRES_NEW} since it only applies to newly started
	 * transactions. Consider switching the "validateExistingTransactions" flag to
	 * "true" on your transaction manager if you'd like isolation level declarations
	 * to get rejected when participating in an existing transaction with a different
	 * isolation level.
	 * @see org.springframework.transaction.interceptor.TransactionAttribute#getIsolationLevel()
	 * @see org.springframework.transaction.support.AbstractPlatformTransactionManager#setValidateExistingTransaction
	 */
	Isolation isolation() default Isolation.DEFAULT;

	/**
	 * The timeout for this transaction (in seconds).
	 * <p>Defaults to the default timeout of the underlying transaction system.
	 * <p>Exclusively designed for use with {@link Propagation#REQUIRED} or
	 * {@link Propagation#REQUIRES_NEW} since it only applies to newly started
	 * transactions.
	 * @return the timeout in seconds
	 * @see org.springframework.transaction.interceptor.TransactionAttribute#getTimeout()
	 */
	int timeout() default TransactionDefinition.TIMEOUT_DEFAULT;

	/**
	 * The timeout for this transaction (in seconds).
	 * <p>Defaults to the default timeout of the underlying transaction system.
	 * <p>Exclusively designed for use with {@link Propagation#REQUIRED} or
	 * {@link Propagation#REQUIRES_NEW} since it only applies to newly started
	 * transactions.
	 * @return the timeout in seconds as a String value, e.g. a placeholder
	 * @since 5.3
	 * @see org.springframework.transaction.interceptor.TransactionAttribute#getTimeout()
	 */
	String timeoutString() default "";

	/**
	 * A boolean flag that can be set to {@code true} if the transaction is
	 * effectively read-only, allowing for corresponding optimizations at runtime.
	 * <p>Defaults to {@code false}.
	 * <p>This just serves as a hint for the actual transaction subsystem;
	 * it will <i>not necessarily</i> cause failure of write access attempts.
	 * A transaction manager which cannot interpret the read-only hint will
	 * <i>not</i> throw an exception when asked for a read-only transaction
	 * but rather silently ignore the hint.
	 * @see org.springframework.transaction.interceptor.TransactionAttribute#isReadOnly()
	 * @see org.springframework.transaction.support.TransactionSynchronizationManager#isCurrentTransactionReadOnly()
	 */
	boolean readOnly() default false;

	/**
	 * Defines zero (0) or more exception {@linkplain Class classes}, which must be
	 * subclasses of {@link Throwable}, indicating which exception types must cause
	 * a transaction rollback.
	 * <p>By default, a transaction will be rolled back on {@link RuntimeException}
	 * and {@link Error} but not on checked exceptions (business exceptions). See
	 * {@link org.springframework.transaction.interceptor.DefaultTransactionAttribute#rollbackOn(Throwable)}
	 * for a detailed explanation.
	 * <p>This is the preferred way to construct a rollback rule (in contrast to
	 * {@link #rollbackForClassName}), matching the exception type, its subclasses,
	 * and its nested classes. See the {@linkplain Transactional class-level javadocs}
	 * for further details on rollback rule semantics and warnings regarding possible
	 * unintentional matches.
	 * @see #rollbackForClassName
	 * @see org.springframework.transaction.interceptor.RollbackRuleAttribute#RollbackRuleAttribute(Class)
	 * @see org.springframework.transaction.interceptor.DefaultTransactionAttribute#rollbackOn(Throwable)
	 */
	Class<? extends Throwable>[] rollbackFor() default {};

	/**
	 * Defines zero (0) or more exception name patterns (for exceptions which must be a
	 * subclass of {@link Throwable}), indicating which exception types must cause
	 * a transaction rollback.
	 * <p>See the {@linkplain Transactional class-level javadocs} for further details
	 * on rollback rule semantics, patterns, and warnings regarding possible
	 * unintentional matches.
	 * @see #rollbackFor
	 * @see org.springframework.transaction.interceptor.RollbackRuleAttribute#RollbackRuleAttribute(String)
	 * @see org.springframework.transaction.interceptor.DefaultTransactionAttribute#rollbackOn(Throwable)
	 */
	String[] rollbackForClassName() default {};

	/**
	 * Defines zero (0) or more exception {@link Class Classes}, which must be
	 * subclasses of {@link Throwable}, indicating which exception types must
	 * <b>not</b> cause a transaction rollback.
	 * <p>This is the preferred way to construct a rollback rule (in contrast to
	 * {@link #noRollbackForClassName}), matching the exception type, its subclasses,
	 * and its nested classes. See the {@linkplain Transactional class-level javadocs}
	 * for further details on rollback rule semantics and warnings regarding possible
	 * unintentional matches.
	 * @see #noRollbackForClassName
	 * @see org.springframework.transaction.interceptor.NoRollbackRuleAttribute#NoRollbackRuleAttribute(Class)
	 * @see org.springframework.transaction.interceptor.DefaultTransactionAttribute#rollbackOn(Throwable)
	 */
	Class<? extends Throwable>[] noRollbackFor() default {};

	/**
	 * Defines zero (0) or more exception name patterns (for exceptions which must be a
	 * subclass of {@link Throwable}) indicating which exception types must <b>not</b>
	 * cause a transaction rollback.
	 * <p>See the {@linkplain Transactional class-level javadocs} for further details
	 * on rollback rule semantics, patterns, and warnings regarding possible
	 * unintentional matches.
	 * @see #noRollbackFor
	 * @see org.springframework.transaction.interceptor.NoRollbackRuleAttribute#NoRollbackRuleAttribute(String)
	 * @see org.springframework.transaction.interceptor.DefaultTransactionAttribute#rollbackOn(Throwable)
	 */
	String[] noRollbackForClassName() default {};

}
```
## demo
```java
@Transactional(rollbackFor = Exception.class)
public void insert(ProductInfo productInfo) {
    productInfo.setCreateDate(new Date());
    productInfo.setUpdateDate(new Date());
    String name = productInfo.getName();
    int insert = productInfoMapper.insert(productInfo);
    System.out.println("insert: " + insert);
    if (name.startsWith("e")) {
        throw new RuntimeException("异常了，应该回滚了。。。");
    }
}
```
# 编程式事务
> [https://docs.spring.io/spring-framework/docs/current/reference/html/data-access.html#tx-prog-template](https://docs.spring.io/spring-framework/docs/current/reference/html/data-access.html#tx-prog-template)

## 实现方式

- The **TransactionTemplate** or **TransactionalOperator**.
- A **TransactionManager** implementation directly.
## TransactionTemplate 事务模板
### demo
```java
    @Autowired
    private TransactionTemplate transactionTemplate;

    public void insert(ProductInfo productInfo) {
        transactionTemplate.execute(new TransactionCallback<Object>() {
            @Override
            public Object doInTransaction(TransactionStatus status) {
                // 执行db操作
                return dbOperate(productInfo);
            }
        });

        // 无返回值
        transactionTemplate.executeWithoutResult(new Consumer<TransactionStatus>() {
            @Override
            public void accept(TransactionStatus transactionStatus) {
                dbOperate(productInfo);
            }
        });
    }

    private Integer dbOperate(ProductInfo productInfo) {
        productInfo.setCreateDate(new Date());
        productInfo.setUpdateDate(new Date());
        String name = productInfo.getName();
        int insert = productInfoMapper.insert(productInfo);
        System.out.println("insert: " + insert);
        if (name.startsWith("e")) {
            throw new RuntimeException("异常了，应该回滚了。。。");
        }
        return insert;
    }
```
### TransactionTemplate源码
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681654664744-a82b014d-6931-4192-a277-8e3fb4ea1939.png#averageHue=%234e5a6b&clientId=ud302bc73-a5ff-4&from=paste&height=256&id=u85fd50f8&originHeight=512&originWidth=1696&originalType=binary&ratio=2&rotation=0&showTitle=false&size=55820&status=done&style=none&taskId=u9942db4f-c984-46fa-9a84-d8f8deec9f5&title=&width=848)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681654640048-542eaa2f-172f-4329-ad74-33fdd8711f05.png#averageHue=%2334373d&clientId=ud302bc73-a5ff-4&from=paste&height=364&id=u78cf85fd&originHeight=728&originWidth=1420&originalType=binary&ratio=2&rotation=0&showTitle=false&size=126591&status=done&style=none&taskId=u3e11326a-f809-41b4-8483-8fc2c80e0ea&title=&width=710)
```java
public class TransactionTemplate extends DefaultTransactionDefinition
		implements TransactionOperations, InitializingBean {

	/** Logger available to subclasses. */
	protected final Log logger = LogFactory.getLog(getClass());

	@Nullable
	private PlatformTransactionManager transactionManager;


	/**
	 * Construct a new TransactionTemplate for bean usage.
	 * <p>Note: The PlatformTransactionManager needs to be set before
	 * any {@code execute} calls.
	 * @see #setTransactionManager
	 */
	public TransactionTemplate() {
	}

	/**
	 * Construct a new TransactionTemplate using the given transaction manager.
	 * @param transactionManager the transaction management strategy to be used
	 */
	public TransactionTemplate(PlatformTransactionManager transactionManager) {
		this.transactionManager = transactionManager;
	}

	/**
	 * Construct a new TransactionTemplate using the given transaction manager,
	 * taking its default settings from the given transaction definition.
	 * @param transactionManager the transaction management strategy to be used
	 * @param transactionDefinition the transaction definition to copy the
	 * default settings from. Local properties can still be set to change values.
	 */
	public TransactionTemplate(PlatformTransactionManager transactionManager, TransactionDefinition transactionDefinition) {
		super(transactionDefinition);
		this.transactionManager = transactionManager;
	}


	/**
	 * Set the transaction management strategy to be used.
	 */
	public void setTransactionManager(@Nullable PlatformTransactionManager transactionManager) {
		this.transactionManager = transactionManager;
	}

	/**
	 * Return the transaction management strategy to be used.
     * 事务管理器
	 */
	@Nullable
	public PlatformTransactionManager getTransactionManager() {
		return this.transactionManager;
	}

	@Override
	public void afterPropertiesSet() {
		if (this.transactionManager == null) {
			throw new IllegalArgumentException("Property 'transactionManager' is required");
		}
	}


	@Override
	@Nullable
	public <T> T execute(TransactionCallback<T> action) throws TransactionException {
		Assert.state(this.transactionManager != null, "No PlatformTransactionManager set");

		if (this.transactionManager instanceof CallbackPreferringPlatformTransactionManager) {
			return ((CallbackPreferringPlatformTransactionManager) this.transactionManager).execute(this, action);
		}
		else {
            // 开启一个事务
			TransactionStatus status = this.transactionManager.getTransaction(this);
			T result;
			try {
                // 执行数据库操作代码，实现类
                // org.springframework.transaction.support.TransactionCallback
				result = action.doInTransaction(status);
			}
			catch (RuntimeException | Error ex) {
				// Transactional code threw application exception -> rollback
                // 异常回滚事务，并抛出异常
				rollbackOnException(status, ex);
				throw ex;
			}
			catch (Throwable ex) {
				// Transactional code threw unexpected exception -> rollback
                // 异常回滚事务，并抛出异常
				rollbackOnException(status, ex);
				throw new UndeclaredThrowableException(ex, "TransactionCallback threw undeclared checked exception");
			}
            // 提交事务
			this.transactionManager.commit(status);
			return result;
		}
	}

	/**
	 * Perform a rollback, handling rollback exceptions properly.
	 * @param status object representing the transaction
	 * @param ex the thrown application exception or error
	 * @throws TransactionException in case of a rollback error
	 */
	private void rollbackOnException(TransactionStatus status, Throwable ex) throws TransactionException {
		Assert.state(this.transactionManager != null, "No PlatformTransactionManager set");

		logger.debug("Initiating transaction rollback on application exception", ex);
		try {
			this.transactionManager.rollback(status);
		}
		catch (TransactionSystemException ex2) {
			logger.error("Application exception overridden by rollback exception", ex);
			ex2.initApplicationException(ex);
			throw ex2;
		}
		catch (RuntimeException | Error ex2) {
			logger.error("Application exception overridden by rollback exception", ex);
			throw ex2;
		}
	}


	@Override
	public boolean equals(@Nullable Object other) {
		return (this == other || (super.equals(other) && (!(other instanceof TransactionTemplate) ||
				getTransactionManager() == ((TransactionTemplate) other).getTransactionManager())));
	}

}

```
#### TransactionStatus  事务状态
> org.springframework.transaction.TransactionStatus

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681654874158-f62a8c76-ef9f-4695-8961-876567710564.png#averageHue=%23494f4b&clientId=ud302bc73-a5ff-4&from=paste&height=170&id=u28859b4b&originHeight=340&originWidth=1310&originalType=binary&ratio=2&rotation=0&showTitle=false&size=30760&status=done&style=none&taskId=ub3bd7ec4-a832-4abf-baab-f86b8c9d9d8&title=&width=655)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681654929214-3b697e1c-ca99-4d56-b58c-73923f655923.png#averageHue=%2330333a&clientId=ud302bc73-a5ff-4&from=paste&height=310&id=ud3b5fb42&originHeight=620&originWidth=1190&originalType=binary&ratio=2&rotation=0&showTitle=false&size=100734&status=done&style=none&taskId=ud212f6bd-f2ef-4b12-94d3-3da527c5c6f&title=&width=595)
#### 获取一个事务getTransaction
```java
public abstract class AbstractPlatformTransactionManager implements PlatformTransactionManager, Serializable {

	@Override
	public final TransactionStatus getTransaction(@Nullable TransactionDefinition definition)
			throws TransactionException {

		// Use defaults if no transaction definition given.
        // 事务定义，为空设置一个默认的
		TransactionDefinition def = (definition != null ? definition : TransactionDefinition.withDefaults());

        // 获取事务，抽象方法，子类实现
		Object transaction = doGetTransaction();
		boolean debugEnabled = logger.isDebugEnabled();

        // 存在事务。
		if (isExistingTransaction(transaction)) {
			// Existing transaction found -> check propagation behavior to find out how to behave.
			return handleExistingTransaction(def, transaction, debugEnabled);
		}

		// Check definition settings for new transaction.
		if (def.getTimeout() < TransactionDefinition.TIMEOUT_DEFAULT) {
			throw new InvalidTimeoutException("Invalid transaction timeout", def.getTimeout());
		}

        // 不存在事务，检查传播行为
		// No existing transaction found -> check propagation behavior to find out how to proceed.
		if (def.getPropagationBehavior() == TransactionDefinition.PROPAGATION_MANDATORY) {
            // MANDATORY 当前不存在事务，抛出异常。
			throw new IllegalTransactionStateException(
					"No existing transaction found for transaction marked with propagation 'mandatory'");
		}
        
		else if (def.getPropagationBehavior() == TransactionDefinition.PROPAGATION_REQUIRED ||
				def.getPropagationBehavior() == TransactionDefinition.PROPAGATION_REQUIRES_NEW ||
				def.getPropagationBehavior() == TransactionDefinition.PROPAGATION_NESTED) {
            // 挂起当前事务，再开启一个新事务
			SuspendedResourcesHolder suspendedResources = suspend(null);
			if (debugEnabled) {
				logger.debug("Creating new transaction with name [" + def.getName() + "]: " + def);
			}
			try {
				return startTransaction(def, transaction, debugEnabled, suspendedResources);
			}
			catch (RuntimeException | Error ex) {
				resume(null, suspendedResources);
				throw ex;
			}
		}
		else {
            // 不需要事务 SUPPORTS
			// Create "empty" transaction: no actual transaction, but potentially synchronization.
			if (def.getIsolationLevel() != TransactionDefinition.ISOLATION_DEFAULT && logger.isWarnEnabled()) {
				logger.warn("Custom isolation level specified but no actual transaction initiated; " +
						"isolation level will effectively be ignored: " + def);
			}
			boolean newSynchronization = (getTransactionSynchronization() == SYNCHRONIZATION_ALWAYS);
			return prepareTransactionStatus(def, null, true, newSynchronization, debugEnabled, null);
		}
	}


}
```
## TransactionalOperator
> 响应式

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681655609056-4e8cd41d-00bd-48cc-97b3-4af55d7ef518.png#averageHue=%23edf0f2&clientId=ud302bc73-a5ff-4&from=paste&height=384&id=uf3887052&originHeight=768&originWidth=1728&originalType=binary&ratio=2&rotation=0&showTitle=false&size=163575&status=done&style=none&taskId=uf854cd43-d0ba-4874-a3a6-ed08a750a85&title=&width=864)

## TransactionManager 事务管理器
### PlatformTransactionManager
> 这是Spring命令式事务基础设施中的中心接口。应用程序可以直接使用它，但它主要不是作为API:通常，应用程序将通过AOP使用TransactionTemplate或声明性事务界定。
> 对于实现者，建议从提供的org.springframework. transactionmanager类派生，该类预先实现了定义的传播行为并负责事务同步处理。子类必须为底层事务的特定状态实现模板方法，例如:开始、挂起、恢复、提交

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681656053463-2aa58ae3-a202-41fa-8bc2-2ef6c379bde4.png#averageHue=%2337553a&clientId=ud302bc73-a5ff-4&from=paste&height=152&id=u137f13d9&originHeight=304&originWidth=788&originalType=binary&ratio=2&rotation=0&showTitle=false&size=22073&status=done&style=none&taskId=ua2b39af2-266d-488a-a7ec-1b6be59b8bc&title=&width=394)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681656036085-aef00200-e533-4ff2-aabb-5155f84d121c.png#averageHue=%23333840&clientId=ud302bc73-a5ff-4&from=paste&height=174&id=ud969cf20&originHeight=348&originWidth=1262&originalType=binary&ratio=2&rotation=0&showTitle=false&size=50068&status=done&style=none&taskId=u944806ac-1112-40a8-af01-eca413181c5&title=&width=631)
#### demo
```java
    @Autowired
    private PlatformTransactionManager transactionManager;

    public void insert(ProductInfo productInfo) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus transaction = transactionManager.getTransaction(definition);
        try {
            dbOperate(productInfo);
        } catch (Exception e) {
            transactionManager.rollback(transaction);
            throw e;
        }
        transactionManager.commit(transaction);
    }

    private Integer dbOperate(ProductInfo productInfo) {
        productInfo.setCreateDate(new Date());
        productInfo.setUpdateDate(new Date());
        String name = productInfo.getName();
        int insert = productInfoMapper.insert(productInfo);
        System.out.println("insert: " + insert);
        if (name.startsWith("e")) {
            throw new RuntimeException("异常了，应该回滚了。。。");
        }
        return insert;
    }
```
# 事务阶段监听器
## 使用场景
> 在事务提交后，做些处理，如rpc，发送mq操作。

## @TransactionalEventListener
> @TransactionalEventListener注释公开一个阶段属性，该属性允许您自定义侦听器应绑定到的事务阶段。
> 有效阶段包括BEFORE_COMMIT、AFTER_COMMIT（默认）、AFTER_ROLLBACK，以及聚合事务完成（提交或回滚）的AFTER_COMPLETION。

> **@TransactionalEventListener仅适用于由 PlatformTransactionManager 管理的线程绑定事务。**

```java
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@EventListener
public @interface TransactionalEventListener {

	/**
	 * Phase to bind the handling of an event to.
	 * <p>The default phase is {@link TransactionPhase#AFTER_COMMIT}.
	 * <p>If no transaction is in progress, the event is not processed at
	 * all unless {@link #fallbackExecution} has been enabled explicitly.
	 */
	TransactionPhase phase() default TransactionPhase.AFTER_COMMIT;

	/**
	 * Whether the event should be handled if no transaction is running.
	 */
	boolean fallbackExecution() default false;

	/**
	 * Alias for {@link #classes}.
	 */
	@AliasFor(annotation = EventListener.class, attribute = "classes")
	Class<?>[] value() default {};

	/**
	 * The event classes that this listener handles.
	 * <p>If this attribute is specified with a single value, the annotated
	 * method may optionally accept a single parameter. However, if this
	 * attribute is specified with multiple values, the annotated method
	 * must <em>not</em> declare any parameters.
	 */
	@AliasFor(annotation = EventListener.class, attribute = "classes")
	Class<?>[] classes() default {};

	/**
	 * Spring Expression Language (SpEL) attribute used for making the event
	 * handling conditional.
	 * <p>The default is {@code ""}, meaning the event is always handled.
	 * @see EventListener#condition
	 */
	@AliasFor(annotation = EventListener.class, attribute = "condition")
	String condition() default "";

	/**
	 * An optional identifier for the listener, defaulting to the fully-qualified
	 * signature of the declaring method (e.g. "mypackage.MyClass.myMethod()").
	 * @since 5.3
	 * @see EventListener#id
	 * @see TransactionalApplicationListener#getListenerId()
	 */
	@AliasFor(annotation = EventListener.class, attribute = "id")
	String id() default "";

}
```
### 事务阶段

- BEFORE_COMMIT
> 在事务提交之前处理事件。

- AFTER_COMMIT
> 在提交成功完成后处理事件。

- AFTER_ROLLBACK
> 如果事务已回滚，则处理该事件。

- AFTER_COMPLETION
> 在事务完成后处理事件。无论提交还是回滚。

```java
public enum TransactionPhase {

	/**
	 * Handle the event before transaction commit.
	 * @see TransactionSynchronization#beforeCommit(boolean)
	 */
	BEFORE_COMMIT,

	/**
	 * Handle the event after the commit has completed successfully.
	 * <p>Note: This is a specialization of {@link #AFTER_COMPLETION} and therefore
	 * executes in the same sequence of events as {@code AFTER_COMPLETION}
	 * (and not in {@link TransactionSynchronization#afterCommit()}).
	 * <p>Interactions with the underlying transactional resource will not be
	 * committed in this phase. See
	 * {@link TransactionSynchronization#afterCompletion(int)} for details.
	 * @see TransactionSynchronization#afterCompletion(int)
	 * @see TransactionSynchronization#STATUS_COMMITTED
	 */
	AFTER_COMMIT,

	/**
	 * Handle the event if the transaction has rolled back.
	 * <p>Note: This is a specialization of {@link #AFTER_COMPLETION} and therefore
	 * executes in the same sequence of events as {@code AFTER_COMPLETION}.
	 * <p>Interactions with the underlying transactional resource will not be
	 * committed in this phase. See
	 * {@link TransactionSynchronization#afterCompletion(int)} for details.
	 * @see TransactionSynchronization#afterCompletion(int)
	 * @see TransactionSynchronization#STATUS_ROLLED_BACK
	 */
	AFTER_ROLLBACK,

	/**
	 * Handle the event after the transaction has completed.
	 * <p>For more fine-grained events, use {@link #AFTER_COMMIT} or
	 * {@link #AFTER_ROLLBACK} to intercept transaction commit
	 * or rollback, respectively.
	 * <p>Interactions with the underlying transactional resource will not be
	 * committed in this phase. See
	 * {@link TransactionSynchronization#afterCompletion(int)} for details.
	 * @see TransactionSynchronization#afterCompletion(int)
	 */
	AFTER_COMPLETION

}
```
### demo
#### 定义事件
```java
public class TransactionalEvent extends ApplicationEvent {

    @Getter
    private ProductInfo productInfo;

    public TransactionalEvent(Object source, ProductInfo productInfo) {
        super(source);
        this.productInfo = productInfo;
    }
}
```
#### 发布事件
```java
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Transactional(rollbackFor = Exception.class)
    public void insert(ProductInfo productInfo) {
        eventPublisher.publishEvent(new TransactionalEvent(this, productInfo));
        dbOperate(productInfo);
    }
```
#### 监听事件
```java
@Component
public class TransactionalEventListenerDemo {

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT,
            classes = TransactionalEvent.class)
    public void beforeCommit(TransactionalEvent event) {
        System.out.println("监听到事务要开始提交了");
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,
            classes = TransactionalEvent.class)
    public void afterCommit(TransactionalEvent event) {
        System.out.println("监听到事务提交了");
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_ROLLBACK,
            classes = TransactionalEvent.class)
    public void afterRollback(TransactionalEvent event) {
        System.out.println("监听到事务回滚了");
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMPLETION,
            classes = TransactionalEvent.class)
    public void afterCompletion(TransactionalEvent event) {
        System.out.println("监听到事务操作完成了");
    }
}
```
## TransactionSynchronizationManager
### demo
```java
    @Transactional(rollbackFor = Exception.class)
    public void insert(ProductInfo productInfo) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void beforeCommit(boolean readOnly) {
                    System.out.println("事务要开始提交了");
                }

                @Override
                public void suspend() {
                    System.out.println("Suspend this synchronization.");
                }

                @Override
                public void resume() {
                    System.out.println("Resume this synchronization.");
                }

                @Override
                public void beforeCompletion() {
                    System.out.println("事务完成之前了");
                }

                @Override
                public void afterCommit() {
                    System.out.println("事务提交了");
                }

                @Override
                public void afterCompletion(int status) {
                    System.out.println("事务完成之后了");
                }

                @Override
                public void flush() {
                    System.out.println("将底层会话刷新到数据存储中");
                }
            });
        }
        // 发布事件
//        eventPublisher.publishEvent(new TransactionalEvent(this, productInfo));
        dbOperate(productInfo);
    }
```
### 整合util
```java
public class DoTransactionCompletion implements TransactionSynchronization {

    private final Runnable runnable;

    public DoTransactionCompletion(Runnable runnable) {
        this.runnable = runnable;
    }

    @Override
    public void afterCompletion(int status) {
        // 再事务提交之后, 异步做一些事情
        if (status == TransactionSynchronization.STATUS_COMMITTED) {
            this.runnable.run();
        }
    }
}
```
```java
@Slf4j
public class TransactionUtils {

    public static void doAfterTransaction(DoTransactionCompletion doTransactionCompletion) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(doTransactionCompletion);
        }
    }

    public static void doAfterTransaction(Runnable runnable) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new DoTransactionCompletion(runnable));
        }
    }

    @Transactional
    public void doTx() {

        // start tx
//        TransactionUtils.doAfterTransaction(new DoTransactionCompletion(() -> {
//            // send Mq... RPC
//        }));
        TransactionUtils.doAfterTransaction(() -> {
            // send Mq... RPC
            log.info("test2");
        });

        // end tx
    }
}
```
### 源码
> org.springframework.transaction.support.TransactionSynchronizationManager
> 使用ThreadLocal保存事务的信息。不同线程的事务互相隔离。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681736682163-95cef30c-25ad-45a7-9a3d-df0a7e40cfc2.png#averageHue=%23232427&clientId=u0ba64b3e-bf5c-4&from=paste&height=635&id=u7e627878&originHeight=1270&originWidth=1652&originalType=binary&ratio=2&rotation=0&showTitle=false&size=284241&status=done&style=none&taskId=uc7e670eb-bf52-43a6-b41a-59500a2d5ff&title=&width=826)
