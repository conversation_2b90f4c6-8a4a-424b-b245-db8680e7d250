# Spring 创建Bean的方式
## 1. @Component + @ComponentScan
### @Component
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
public @interface Component {

	/**
	 * The value may indicate a suggestion for a logical component name,
	 * to be turned into a Spring bean in case of an autodetected component.
	 * @return the suggested component name, if any (or empty String otherwise)
	 */
	String value() default "";

}
```
### @Controller
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Controller {

	/**
	 * The value may indicate a suggestion for a logical component name,
	 * to be turned into a Spring bean in case of an autodetected component.
	 * @return the suggested component name, if any (or empty String otherwise)
	 */
	@AliasFor(annotation = Component.class)
	String value() default "";

}
```
### @Service
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Service {

	/**
	 * The value may indicate a suggestion for a logical component name,
	 * to be turned into a Spring bean in case of an autodetected component.
	 * @return the suggested component name, if any (or empty String otherwise)
	 */
	@AliasFor(annotation = Component.class)
	String value() default "";

}
```
### @Repository
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Repository {

	/**
	 * The value may indicate a suggestion for a logical component name,
	 * to be turned into a Spring bean in case of an autodetected component.
	 * @return the suggested component name, if any (or empty String otherwise)
	 */
	@AliasFor(annotation = Component.class)
	String value() default "";

}
```
### @ComponentScan
```groovy
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Repeatable(ComponentScans.class)
public @interface ComponentScan {
}
```
### @SpringBootApplication
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(excludeFilters = { @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
		@Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class) })
public @interface SpringBootApplication {
}
```
## 2. @Bean + @Configuration
### @Bean
```groovy
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Bean {

	@AliasFor("name")
	String[] value() default {};

	@AliasFor("value")
	String[] name() default {};

	@Deprecated
	Autowire autowire() default Autowire.NO;

	boolean autowireCandidate() default true;

	String initMethod() default "";

	String destroyMethod() default AbstractBeanDefinition.INFER_METHOD;

}

```
### @Configuration
```groovy
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Configuration {

    // 等价用了Component注解，也是个Spring组件
	@AliasFor(annotation = Component.class)
	String value() default "";

    // 用于指示是否要对@Bean注解的方法进行代理，以实现方法内部调用的拦截和其他功能，默认值为true。
	boolean proxyBeanMethods() default true;

}
```
### @AliasFor
```groovy
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface AliasFor {

	/**
	 * Alias for {@link #attribute}.
	 * <p>Intended to be used instead of {@link #attribute} when {@link #annotation}
	 * is not declared &mdash; for example: {@code @AliasFor("value")} instead of
	 * {@code @AliasFor(attribute = "value")}.
	 */
	@AliasFor("attribute")
	String value() default "";

	/**
	 * The name of the attribute that <em>this</em> attribute is an alias for.
	 * @see #value
	 */
	@AliasFor("value")
	String attribute() default "";

	/**
	 * The type of annotation in which the aliased {@link #attribute} is declared.
	 * <p>Defaults to {@link Annotation}, implying that the aliased attribute is
	 * declared in the same annotation as <em>this</em> attribute.
	 */
	Class<? extends Annotation> annotation() default Annotation.class;

}
```
## 3. @Import注解
```groovy
@Import(Animal.class)
```
```groovy
/**
 * Indicates one or more <em>component classes</em> to import &mdash; typically
 * {@link Configuration @Configuration} classes.
 *
 * <p>Provides functionality equivalent to the {@code <import/>} element in Spring XML.
 * Allows for importing {@code @Configuration} classes, {@link ImportSelector} and
 * {@link ImportBeanDefinitionRegistrar} implementations, as well as regular component
 * classes (as of 4.2; analogous to {@link AnnotationConfigApplicationContext#register}).
 *
 * <p>{@code @Bean} definitions declared in imported {@code @Configuration} classes should be
 * accessed by using {@link org.springframework.beans.factory.annotation.Autowired @Autowired}
 * injection. Either the bean itself can be autowired, or the configuration class instance
 * declaring the bean can be autowired. The latter approach allows for explicit, IDE-friendly
 * navigation between {@code @Configuration} class methods.
 *
 * <p>May be declared at the class level or as a meta-annotation.
 *
 * <p>If XML or other non-{@code @Configuration} bean definition resources need to be
 * imported, use the {@link ImportResource @ImportResource} annotation instead.
 *
 * <AUTHOR> Beams
 * <AUTHOR> Hoeller
 * @since 3.0
 * @see Configuration
 * @see ImportSelector
 * @see ImportBeanDefinitionRegistrar
 * @see ImportResource
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Import {

	/**
	 * {@link Configuration @Configuration}, {@link ImportSelector},
	 * {@link ImportBeanDefinitionRegistrar}, or regular component classes to import.
	 */
	Class<?>[] value();

}
```
## 4. BeanDefinitionRegistryPostProcessor 接口
> 手动设置Bean定义。RootBeanDefinition。
> **需要配合@Component或者@Import注解。把实现类先创建为Bean。**

```groovy
@Component
public class BeanDefinitionRegistryPostProcessorDemo implements BeanDefinitionRegistryPostProcessor {
    /**
     * BeanFactory实例化所有bean之前， 新增新的bean定义
     *
     * @param registry the bean definition registry used by the application context
     * @throws BeansException in case of errors
     */
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {

        // 新增新的bean定义
        RootBeanDefinition beanDefinition = new RootBeanDefinition(NewBean.class);
        registry.registerBeanDefinition("newBean", beanDefinition);
    }

    /**
     * 所有的bean定义都将被加载，但是还没有bean被实例化。这允许重写或添加属性，甚至可以直接初始化bean。
     *
     * @param beanFactory the bean factory used by the application context
     * @throws BeansException in case of errors
     */
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        NewBean newBean = beanFactory.getBean(NewBean.class);
        newBean.setNewName("newName");
        newBean.say();
    }
}
```
## 5. ImportSelector 接口+@Import注解
```groovy
public class Cat {

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

```
```groovy
public class ImportSelectorDemo implements ImportSelector {
    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        return new String[]{
                Cat.class.getName()
        };
    }
}
```
```groovy
@SpringBootApplication
@Import({ImportSelectorDemo.class})
public class SpringExtensionApp {
}
```
## 6. xml方式
```groovy
<bean id="xxxx"  class="xxxx.xxxx"/>
```
## 7. 通过BeanFactory注册Bean
> org.springframework.beans.factory.support.DefaultListableBeanFactory
> 注册单例bean

```groovy
public class Animal {

    private Dog dog;

    public void eat() {
        System.out.println(dog.getName());
    }

}
```
```groovy
@Component
public class BeanFactoryAwareDemo implements BeanFactoryAware {

    private BeanFactory beanFactory;

    /**
     * 在普通bean属性填充之后，但在初始化回调(如InitializingBean.afterPropertiesSet()或自定义初始化方法之前调用
     * @param beanFactory owning BeanFactory (never {@code null}).
     *                    The bean can immediately call methods on the factory.
     * @throws BeansException in case of initialization errors
     * @see BeanInitializationException
     */
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        DefaultListableBeanFactory listableBeanFactory = (DefaultListableBeanFactory) beanFactory;
        listableBeanFactory.registerSingleton("animalDemo", new Animal());
        this.beanFactory = beanFactory;
    }
}
```
### DefaultListableBeanFactory
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682001641946-b784ae02-862f-40f2-9d69-5dfd295cfbdc.png#averageHue=%23202125&clientId=uddbea2b2-d792-4&from=paste&height=685&id=u26e97168&originHeight=1370&originWidth=2972&originalType=binary&ratio=2&rotation=0&showTitle=false&size=217129&status=done&style=none&taskId=u145e6aa8-5cff-4318-84e0-8d0a1ea4c16&title=&width=1486)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682001789317-5e31448d-c6b2-45b9-83a7-5cf7e6f79d19.png#averageHue=%23313439&clientId=uddbea2b2-d792-4&from=paste&height=493&id=u880e9238&originHeight=986&originWidth=1442&originalType=binary&ratio=2&rotation=0&showTitle=false&size=243936&status=done&style=none&taskId=u24f54ae0-18d3-4549-ab33-66bdc62cdb8&title=&width=721)
