```java
@Slf4j
@Component
public class MyRefreshEventListener implements ApplicationContextAware {

    private ConfigurableApplicationContext context;

    private final ContextRefresher refresh;

    private final ForwardProperties forwardProperties;

    private final RestConfig restConfig;

    public MyRefreshEventListener(ContextRefresher refresh, ForwardProperties forwardProperties, RestConfig restConfig) {
        this.refresh = refresh;
        this.forwardProperties = forwardProperties;
        this.restConfig = restConfig;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ConfigurableApplicationContext context = (ConfigurableApplicationContext) applicationContext;
        this.context = context;
    }

    @EventListener
    public void handleRefreshEvent(RefreshEvent event) {
        // 在此处处理刷新事件
        log.info("Event received " + event.getEventDesc());
        Set<String> keys = this.refresh.refresh();
        log.info("refresh keys: {}", keys);
        if (containsPollConfig(keys)) {
            // 刷新 RestTemplate
            log.info("forwardProperties： {}", forwardProperties.getConnectionPoolConfig());
            restConfig.refresh(forwardProperties, this.context.getBeanFactory());
        }
    }

    private boolean containsPollConfig(Set<String> keys) {
        return keys.stream().anyMatch(key -> key.contains("connectionPoolConfig"));
    }

}
```
