# Spring Bean的作用域
> Bean 的作用域可以决定 Bean 实例的生命周期和可见范围。

## 作用域

1. singleton（单例）：每个容器中只会存在一个 Bean 实例，默认作用域。
2. prototype（原型）：每次获取时都会创建一个新的 Bean 实例。
3. request（请求）：在同一个 HTTP 请求中，每次获取时都会返回相同的 Bean 实例。
4. session（会话）：在同一个 HTTP 会话中，每次获取时都会返回相同的 Bean 实例。
5. global-session（全局会话）：在基于 Portlet 的 web 应用中，表示全局会话。如果部署环境不支持全局会话，则该作用域等价于 session。
## @Scope注解
```groovy
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Scope {

	/**
	 * Alias for {@link #scopeName}.
	 * @see #scopeName
	 */
	@AliasFor("scopeName")
	String value() default "";

	/**
	 * Specifies the name of the scope to use for the annotated component/bean.
	 * <p>Defaults to an empty string ({@code ""}) which implies
	 * {@link ConfigurableBeanFactory#SCOPE_SINGLETON SCOPE_SINGLETON}.
	 * @since 4.2
	 * @see ConfigurableBeanFactory#SCOPE_PROTOTYPE
	 * @see ConfigurableBeanFactory#SCOPE_SINGLETON
	 * @see org.springframework.web.context.WebApplicationContext#SCOPE_REQUEST
	 * @see org.springframework.web.context.WebApplicationContext#SCOPE_SESSION
	 * @see #value
	 */
	@AliasFor("value")
	String scopeName() default "";

	/**
	 * Specifies whether a component should be configured as a scoped proxy
	 * and if so, whether the proxy should be interface-based or subclass-based.
	 * <p>Defaults to {@link ScopedProxyMode#DEFAULT}, which typically indicates
	 * that no scoped proxy should be created unless a different default
	 * has been configured at the component-scan instruction level.
	 * <p>Analogous to {@code <aop:scoped-proxy/>} support in Spring XML.
	 * @see ScopedProxyMode
	 */
	ScopedProxyMode proxyMode() default ScopedProxyMode.DEFAULT;

}
```
```groovy
public interface ConfigurableBeanFactory extends HierarchicalBeanFactory, SingletonBeanRegistry {

	/**
	 * Scope identifier for the standard singleton scope: {@value}.
	 * <p>Custom scopes can be added via {@code registerScope}.
	 * @see #registerScope
	 */
	String SCOPE_SINGLETON = "singleton";

	/**
	 * Scope identifier for the standard prototype scope: {@value}.
	 * <p>Custom scopes can be added via {@code registerScope}.
	 * @see #registerScope
	 */
	String SCOPE_PROTOTYPE = "prototype";
}
```

## 设置一个Bean的作用域
```groovy
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class Animal {

    private Dog dog;

    public void eat() {
        System.out.println(dog.getName());
    }

}
```
```groovy
@Bean(name="userService", scope=BeanDefinition.SCOPE_PROTOTYPE)
public UserService userService(UserRepository userRepository) {
    UserServiceImpl userService = new UserServiceImpl();
    userService.setUserRepository(userRepository);
    return userService;
}
```
