# Spring循环依赖分析
## 循环依赖如何发生

- 两个Bean 互相引用
## 解决循环依赖的前提

- 必须都是**单例**
- **不全是构造器注入**，且beanName字母顺序在前的不能是构造器注入
## 三级缓存
### 一级缓存：singletonObjects

- 存储已经创建完成的单例Bean（完整的）

`private final Map<String, Object> singletonObjects = new ConcurrentHashMap<String, Object>(256);`
### 二级缓存：earlySingletonObjects

- 存储已经完成实例化，未进行属性注入和初始化的Bean

`private final Map<String, Object> earlySingletonObjects = new HashMap<String, Object>(16);`
### 三级缓存：singletonFactories

- 存储建立这个Bean的一个工厂，通过工厂能获取这个Bean，延迟Bean的生成，工厂生成的Bean会塞入二级缓存。

`private final Map<String, ObjectFactory<?>> singletonFactories = new HashMap<String, ObjectFactory<?>>(16);`

## 如何解决循环依赖
### 过程

1. 通过BeanName到以及**一级缓存**查找，有则返回完整Bean， 没有进行2
2. 查看对应的Bean是否在创建中，不在直接返回null，如果是，就到**二级缓存**查找Bean，找到就返回，否则进行3
   - 这步返回null说明Bean还未创建，标记Bean正则创建中，再调用createBean来创建Bean，也就是doCreateBean方法
   - 在实例化Bean之后，就会往三级缓存塞入一个工厂，调用这个工厂的geObject方法就可以得到这个Bean
   - 提前暴露还未完整的bean，对方可以利用这个工厂得到一个不完整的Bean，破坏了循环的条件
3. 到**三级缓存**通过BeanName查找对应的工厂，存在工厂就通过工厂创建Bean，并放到二级缓存中
4. 三个缓存都没有找到，就返回null
### 源码
```java
protected Object getSingleton(String beanName, boolean allowEarlyReference) {
   // Spring首先从singletonObjects（一级缓存）中尝试获取
   Object singletonObject = this.singletonObjects.get(beanName);
   // 若是获取不到而且对象在建立中，则尝试从earlySingletonObjects(二级缓存)中获取
   if (singletonObject == null && isSingletonCurrentlyInCreation(beanName)) {
     synchronized (this.singletonObjects) {
         // 尝试从二级缓存中获取
         singletonObject = this.earlySingletonObjects.get(beanName);
         if (singletonObject == null && allowEarlyReference) {
           // 获取三级缓存
           ObjectFactory<?> singletonFactory = this.singletonFactories.get(beanName);
           if (singletonFactory != null) {
               // 调用三级缓存，
               // 若是仍是获取不到而且容许从singletonFactories经过getObject获取，则经过singletonFactory.getObject()(三级缓存)获取
               singletonObject = singletonFactory.getObject();
               // 若是获取到了则将singletonObject放入到earlySingletonObjects,也就是将三级缓存提高到二级缓存中
               // 放入到二级缓存中
               this.earlySingletonObjects.put(beanName, singletonObject);
               // 三级缓存中移除beanName
               this.singletonFactories.remove(beanName);
           }
         }
     }
   }
   // 完整对象或者还未初始化的对象
   return (singletonObject != NULL_OBJECT ? singletonObject : null);
 }
```

## 只用二级缓存行不行，为什么要三级缓存？

- 只有二级缓存逻辑上可行
- 三级缓存 getObject
```java
protected Object getEarlyBeanReference(String beanName, RootBeanDefinition mbd, Object bean) {
    Object exposedObject = bean;
    if (!mbd.isSynthetic() && hasInstantiationAwareBeanPostProcessors()) {
        for (SmartInstantiationAwareBeanPostProcessor bp : getBeanPostProcessorCache().smartInstantiationAware) {
            exposedObject = bp.getEarlyBeanReference(exposedObject, beanName);
        }
    }
    return exposedObject;
}
```

   - 中间判断如果是 true 说明有 InstantiationAwareBeanPostProcessors ，
且循环的 **smartInstantiationAware** 类型，如有这个 BeanPostProcessor 说明 Bean 需要**被 aop 代理**。
   - 这个工厂的作用就是**判断这个对象是否需要代理**，如果否则直接返回，如果是则返回代理对象。
   - 代理对象的生成是基于**后置处理器**，是在**被代理的对象初始化后**，再调用生成的，所以如果你提早代理了其实是违背了 Bean 定义的生命周期。
- 单例Bean创建完整后会清除二三级缓存

## 开启循环依赖
> springboot 2.6开始 默认关闭循环依赖。
> 不建议开启，存在循环依赖，应优化代码。

```yaml
spring:
  main:
    allow-circular-references: true
```
