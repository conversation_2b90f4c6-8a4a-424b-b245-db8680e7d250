# RocketMQ问题

## [REJECTREQUEST]system busy, start flow control for a while BROKER: 172.16.250.140:30911



## CODE: 2  DESC: [TIMEOUT_CLEAN_QUEUE]broker busy

> 压测发送消息
>
> ![image-20230708164139892](images/image-20230708164139892.png)

> For more information, please visit the url, http://rocketmq.apache.org/docs/faq/; nested exception is org.apache.rocketmq.client.exception.MQBrokerException: CODE: 2  DESC: [TIMEOUT_CLEAN_QUEUE]broker busy, start flow control for a while, period in queue: 203ms, size of queue: 6 BROKER: 172.16.251.224:10911
> For more information, please visit the url, http://rocketmq.apache.org/docs/faq/] with root cause
>
> org.apache.rocketmq.client.exception.MQBrokerException: CODE: 2  DESC: [TIMEOUT_CLEAN_QUEUE]broker busy, start flow control for a while, period in queue: 203ms, size of queue: 6 BROKER: 172.16.251.224:10911

触发broker流控

![image-20230708164200162](images/image-20230708164200162.png)



解决

> 由于如果出现TIMEOUT_CLEAN_QUEUE的错误，客户端暂时不会对其进行重试，故现阶段的建议是适当增加快速失败的判断标准

```
# 发送消息任务队列等待时长ms
waitTimeMillsInSendQueue=500
```

https://zhuanlan.zhihu.com/p/494444805

![image-20230708165139539](images/image-20230708165139539.png)

## CODE: 2  DESC: [PCBUSY_CLEAN_QUEUE]broker busy

> org.apache.rocketmq.client.exception.MQBrokerException: CODE: 2  DESC: [PCBUSY_CLEAN_QUEUE]broker busy, start flow control for a while, period in queue: 1011ms, size of queue: 37 BROKER: 172.16.251.224:12911
> For more information, please visit the url, http://rocketmq.apache.org/docs/faq/



> 百度:
>
> 在 store.log 中找到刷盘的日志，刷盘时间超过 500 ms 就会打印该日志。 可以看到这台机器上刷盘耗时很长，这个时间超过 1000 ms 就可能引发 [PCBUSY_CLEAN_QUEUE]broker busy。

- [PCBUSY_CLEAN_QUEUE]broker busy 
  - 判断pagecache是否忙的依据就是在写入消息时，在向内存追加消息时加锁的时间，默认的判断标准是加锁时间超过1s，就认为是pagecache压力大，向客户端抛出相关的错误日志。

查看日志

> cat /home/<USER>/logs/broker-b/rocketmqlogs/store.log  | grep FlushRealTimeService
>
> cat /home/<USER>/logs/broker-a/rocketmqlogs/store.log  | grep FlushRealTimeService

![image-20230708221434786](images/image-20230708221434786.png)

> 测试机器磁盘刷盘速度慢
>
> 生产需要IO写入性能高的机器!

### 解决方案

> `transientStorePoolEnable=true`是RocketMQ要配置项，用于启用Broker的瞬态存储池（Transient Store Pool）功能。
>
> 消息写入对外内存, 但可能会消息丢失, 

![image-20230708223232799](images/image-20230708223232799.png)

## 有可能遇到的错误

> - [REJECTREQUEST]system busy
> - too many requests and system thread pool busy
> - [PC_SYNCHRONIZED]broker busy
> - [PCBUSY_CLEAN_QUEUE]broker busy    遇到
> - [TIMEOUT_CLEAN_QUEUE]broker busy   遇到

## 查看RocketMQ 消息写入的耗时分布情况

> cat /home/<USER>/logs/broker-a/rocketmqlogs/store.log  | grep PAGECACHERT
>
> tail -100f /home/<USER>/logs/broker-a/rocketmqlogs/store.log  | grep PAGECACHERT

> 2023-07-08 22:20:29 INFO StoreStatsService - [PAGECACHERT] TotalPut 64540, PutMessageDistributeTime 
>
> [<=0ms]:61092
> [0~10ms]:3311
> [10~50ms]:109
> [50~100ms]:20
> [100~200ms]:0
> [200~500ms]:8
> [500ms~1s]:0
> [1~2s]:0
> [2~3s]:0
> [3~4s]:0
> [4~5s]:0
> [5~10s]:0
> [10s~]:0
>
> 

![image-20230708222242505](images/image-20230708222242505.png)



# 百度参考

> **https://cloud.tencent.com/developer/article/1708865**  RocketMQ消息发送常见错误与解决方案

# 压测情况

> 同步发送消息
>
> rocketMQTemplate.syncSend(topic, DateUtil.*now*());

![image-20230708222359644](images/image-20230708222359644.png)