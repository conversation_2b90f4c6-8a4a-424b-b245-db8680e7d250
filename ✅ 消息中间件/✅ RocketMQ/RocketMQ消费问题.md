# RocketMQ消费问题

> 4.9.x
>
> http://172.16.251.53:18080/#/topic
>
> 一个topic4个队列
>
> 本地6个消费者, 总是有队列没有分配消费者
>
> 是顺序消费
>
> rocketmq 5.9.3
>
> 使用starter 2.2.2
>
> 还是手动new消费者类DefaultMQPushConsumer 都不行
>
> base是发送顺序消息



怎么解决???



![image-20230707223218401](images/image-20230707223218401.png)



> 并发消费没问题 每个队列都有消费者

![image-20230707223339545](images/image-20230707223339545.png)



> 一个topic, 4个队列 tag两个
>
> 有个tag队列没有消费者

![image-20230707223857001](images/image-20230707223857001.png)





> 分多个topic 可以!!
>
> 





![image-20230707225058049](images/image-20230707225058049.png)

![image-20230707225111905](images/image-20230707225111905.png)