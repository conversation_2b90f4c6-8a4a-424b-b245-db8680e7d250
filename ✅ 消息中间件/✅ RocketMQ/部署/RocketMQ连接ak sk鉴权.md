# RocketMQ连接ak sk鉴权

## broker配置

```properties
# broker.conf 新增开启acl配置
aclEnable=true
```

![image-*****************](images/image-*****************.png)

## acl配置 plain_acl.yml

```yml
globalWhiteRemoteAddresses:
- 172.16.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
```

### 配置说明

> https://rocketmq.apache.org/zh/docs/4.x/bestPractice/04access

#### 权限定义

对RocketMQ的Topic资源访问权限控制定义主要如下表所示，分为以下四种

| 权限 | 含义              |
| ---- | ----------------- |
| DENY | 拒绝              |
| ANY  | PUB 或者 SUB 权限 |
| PUB  | 发送权限          |
| SUB  | 订阅权限          |

####  权限定义的关键属性

| 字段                       | 取值                      | 含义                    |
| -------------------------- | ------------------------- | ----------------------- |
| globalWhiteRemoteAddresses | *;192.168.*.*;*********** | 全局IP白名单            |
| accessKey                  | 字符串                    | Access Key              |
| secretKey                  | 字符串                    | Secret Key              |
| whiteRemoteAddress         | *;192.168.*.*;*********** | 用户IP白名单            |
| admin                      | true;false                | 是否管理员账户          |
| defaultTopicPerm           | DENY;PUB;SUB;PUB\|SUB     | 默认的Topic权限         |
| defaultGroupPerm           | DENY;PUB;SUB;PUB\|SUB     | 默认的ConsumerGroup权限 |
| topicPerms                 | topic=权限                | 各个Topic的权限         |
| groupPerms                 | group=权限                | 各个ConsumerGroup的权限 |