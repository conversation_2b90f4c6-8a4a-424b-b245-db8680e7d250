# RocketMQ5.x-多 Master 多 Salve-异步复制-集群搭建



# 准备

```bash
# 下载安装包
mkdir -p /home/<USER>
wget https://dist.apache.org/repos/dist/release/rocketmq/5.1.4/rocketmq-all-5.1.4-bin-release.zip
unzip rocketmq-all-5.1.4-bin-release
cd rocketmq-all-5.1.4-bin-release
mv * /home/<USER>/
cd ..
rm -rf rocketmq-all-5.1.4-bin-release
mkdir -p /home/<USER>/logs
sed -i 's#${user.home}#/home/<USER>' /home/<USER>/conf/*.xml

```

## host配置

> ip以真实设备为准
>
> 两台机器 部署broker-a和broker-a-s 形成一主一从
>
> 三台机器 部署broker-a, broker-a-s和broker-b 形成二主一从
>
> 四台机器 部署broker-a, broker-a-s, broker-b和broker-b-s 形成二主二从

| 节点名称       | IP             | 说明           |
|------------|----------------|--------------|
| broker-a   | ************** | 集群a的master节点 |
| broker-a-s | ************** | 集群a的slave节点  |
| broker-b   | ************** | 集群b的master节点 |
| broker-b-s |                | 集群b的slave节点  |

```text
echo "************** rocketmq-n0" | sudo tee -a /etc/hosts
echo "************** rocketmq-n1" | sudo tee -a /etc/hosts
echo "************** rocketmq-n2" | sudo tee -a /etc/hosts

echo "************** rocketmq-n0" | sudo tee -a /etc/hosts
echo "************** rocketmq-n1" | sudo tee -a /etc/hosts
echo "************** rocketmq-n2" | sudo tee -a /etc/hosts
```

# 配置

## broker-a.conf

> /home/<USER>/conf

```text
sudo tee /home/<USER>/conf/broker-a.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-a
#0 表示 Master，>0 表示 Slave
brokerId=0
#nameServer地址，分号分割
namesrvAddr=rocketmq-n0:9876;rocketmq-n1:9876;rocketmq-n2:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=2
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=true
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=48
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=65536
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
brokerRole=ASYNC_MASTER
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=true
EOF
```

## broker-a-s.conf

> /home/<USER>/conf

```text
sudo tee /home/<USER>/conf/broker-a-s.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-a
#0 表示 Master，>0 表示 Slave
brokerId=1
#nameServer地址，分号分割
namesrvAddr=rocketmq-n0:9876;rocketmq-n1:9876;rocketmq-n2:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=2
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=true
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=120
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=65536
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
brokerRole=SLAVE
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=true
EOF
```

## broker-b.conf

> /home/<USER>/conf

```text
sudo tee /home/<USER>/conf/broker-b.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-b
#0 表示 Master，>0 表示 Slave
brokerId=0
#nameServer地址，分号分割
namesrvAddr=rocketmq-n0:9876;rocketmq-n1:9876;rocketmq-n2:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=2
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=true
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=48
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=65536
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
brokerRole=ASYNC_MASTER
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=true
EOF
```

## broker-b-s.conf

> /home/<USER>/conf

```text
sudo tee /home/<USER>/conf/broker-b-s.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-b
#0 表示 Master，>0 表示 Slave
brokerId=1
#nameServer地址，分号分割
namesrvAddr=rocketmq-n0:9876;rocketmq-n1:9876;rocketmq-n2:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=2
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=true
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=48
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=65536
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
brokerRole=SLAVE
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=true
EOF
```

## plain_acl.yml

> /home/<USER>/conf/plain_acl.yml

```text
sudo tee /home/<USER>/conf/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 172.16.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
```

# 启动namesrv

```bash

sh /home/<USER>/bin/mqshutdown namesrv

nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &

```

# 启动broker

```bash

sh /home/<USER>/bin/mqshutdown broker
sh /home/<USER>/bin/mqshutdown namesrv

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-a.conf  --enable-proxy > /home/<USER>/logs/broker.log 2>&1 &

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-a-s.conf  --enable-proxy > /home/<USER>/logs/broker.log 2>&1 &

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-b.conf  --enable-proxy > /home/<USER>/logs/broker.log 2>&1 &

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-b-s.conf  --enable-proxy > /home/<USER>/logs/broker.log 2>&1 &
```

# 运维

```
# 查询集群

  sh /home/<USER>/bin/mqadmin clusterlist -n  rocketmq-n0:9876 rocketmq-n1:9876 rocketmq-n2:9876

```


