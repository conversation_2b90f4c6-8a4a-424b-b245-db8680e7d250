![image-20231121152857245](images/image-20231121152857245.png)

# 0. 安装jdk

```shell
mkdir /home/<USER>
cd /home/<USER>
wget https://builds.openlogic.com/downloadJDK/openlogic-openjdk/8u412-b08/openlogic-openjdk-8u412-b08-linux-x64.tar.gz
tar -zxvf openlogic-openjdk-8u412-b08-linux-x64.tar.gz

```

## 配置环境变量

```shell

vim /etc/profile
export JAVA_HOME=/home/<USER>/openlogic-openjdk-8u412-b08-linux-x64
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH

source /etc/profile


rm -f /usr/bin/java

ln -s /home/<USER>/openlogic-openjdk-8u412-b08-linux-x64/bin/java /usr/bin/java

java -version
```

# 1. 准备

```bash
# 下载安装包

mkdir /home/<USER>
mkdir -p /home/<USER>/logs
cd /home/<USER>
wget https://dist.apache.org/repos/dist/release/rocketmq/4.9.7/rocketmq-all-4.9.7-bin-release.zip
unzip rocketmq-all-4.9.7-bin-release.zip
mv /home/<USER>/rocketmq-all-4.9.7-bin-release/* /home/<USER>/
rm -rf rocketmq-all-4.9.7-bin-release.zip
rm -rf rocketmq-all-4.9.7-bin-release/
sed -i 's#${user.home}#/home/<USER>' /home/<USER>/conf/*.xml
 mkdir -p /home/<USER>/conf/acl
 
 mkdir /home/<USER>/home/<USER>/logs
 
 
 sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/*.xml
```

# 2. name-server部署

```shell
sh /home/<USER>/bin/mqshutdown namesrv

nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &
 tail -100f /home/<USER>/logs/rocketmqlogs/namesrv.log
 
 ************:9876;************:9876;************:9876
```

# 3. broker集群部署

```shell
sh /home/<USER>/bin/mqshutdown broker

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &

tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
 
```

## broker-a0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr= ************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=24
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

## broker-a1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=24
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

## broker-a2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=24
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

## broker-b0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=24
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

## broker-b1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

## broker-b2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=24
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
EOF
```

# 开启ACL

```shell
sed -i 's#aclEnable=false#aclEnable=true#g' /home/<USER>/conf/broker.conf
```

## plain_acl.yml

```shell
sudo tee /home/<USER>/conf/acl/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 10.107.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
```

> /home/<USER>/conf/plain_acl.yml

```shell
sudo tee /home/<USER>/conf/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 10.107.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
 
```

# 启动namesrv

```bash

sh /home/<USER>/bin/mqshutdown namesrv

nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &

```

# 启动broker

```bash
sh /home/<USER>/bin/mqshutdown broker

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &
```

# 运维

## 磁盘使用

```shell
sh /home/<USER>/bin/mqshutdown broker

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf    > /home/<USER>/logs/broker.log 2>&1 &
 
 tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
```



```properties
# 控制 CommitLog 刷盘的时间间隔，单位为毫秒。
flushIntervalCommitLog=2000

# 控制 ConsumeQueue 刷盘的时间间隔，单位为毫秒。
flushIntervalConsumeQueue=10000

# 每次刷盘时最少刷新的页数，默认值为 4（16 KB 数据）。
flushCommitLogLeastPages=16

# consumeQueue 文件每次最少刷2页
flushConsumeQueueLeastPages=2

# 延迟消息进度刷盘的间隔，默认值为 10000 毫秒。
flushDelayOffsetInterval=30000

# 内存中消息的最大传输字节数，默认是 256K。 单位字节
maxTransferBytesOnMessageInMemory=1048576

# 此参数控制 consumeQueue 文件的大小，默认是 600000 个条目（每个条目 20 字节，即 12 MB） 单位字节
mappedFileSizeConsumeQueue=12000000
```

```shell
 # 查询哪个线程占用高 10条
 pidstat -dt -p  {pid} | sort -k 8 -r | head -n 10
 
 
```





```shell
# 查询集群

  sh /home/<USER>/bin/mqadmin clusterlist -n  ************:9876 ************:9876 ************:9876
  
  jdk11   https://github.com/apache/rocketmq/issues/3321
  runbroker.sh
  JAVA_OPT="${JAVA_OPT} --add-exports java.base/jdk.internal.ref=ALL-UNNAMED"
  
  # 删除topic
    sh /home/<USER>/bin/mqadmin deleteTopic -n  ************:9876 -t BENCHMARK_TOPIC -c RaftCluster 
   # 创建topic
   sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t TestTopic  -r 8  -w 8 
   
   
   sh /home/<USER>/bin/mqadmin deleteTopic -n  ************:9876 -t BENCHMARK_TOPIC -c RaftCluster
    sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t BENCHMARK_TOPIC  -r 8  -w 8 

-x producer -n ************:9876 -t BENCHMARK_TOPIC -s 1024 -c 16 -a true -ak cqtcloudcc -sk cqt@2020

sh producer.sh -t cluster-perf-tst8 -w 60 -s 1024 -n x.x.x.x:9876

sh /home/<USER>/bin/mqadmin getNamesrvConfig -n ************:9876 -c RaftCluster

sh /home/<USER>/bin/mqadmin getAccessConfigSubCommand -n ************:9876 -c RaftCluster

# 清理Broker上过期的CommitLog文件，Broker最多会执行20次删除操作，每次最多删除10个文件
sh /home/<USER>/bin/mqadmin deleteExpiredCommitLog -n ************:9876 -c RaftCluster

# 清理Broker上不使用的Topic，从内存中释放Topic的Consume Queue，如果手动删除Topic会产生不使用的Topic	
sh /home/<USER>/bin/mqadmin cleanUnusedTopic -n ************:9876 -c RaftCluster
```

## 重置集群

```shell
sh /home/<USER>/bin/mqshutdown broker
rm -rf /home/<USER>/logs
rm -rf /home/<USER>/store
mkdir /home/<USER>/logs
mkdir /home/<USER>/store
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &
 
```

## 修改对外内存

```
sed -i 's#-XX:MaxDirectMemorySize=15g#-XX:MaxDirectMemorySize=10g#g' /home/<USER>/bin/runbroker.sh
```

## 验证集群

```shell
  sh /home/<USER>/bin/mqadmin clusterlist -n  ************:9876 10.107.27.52:9876 10.107.27.53:9876
  
  sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t BENCHMARK_TOPIC  -r 8  -w 8 
```

## 清理缓存

```shell
sh /home/<USER>/bin/cleancache.sh
```

## 查询堆内存

```shell
jmap -heap 10645
```

## 查询堆外内存

```
jcmd <PID> VM.native_memory summary

jcmd 10645 VM.native_memory summary
```

## 创建/修改主题

```shell
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_status  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t base_ext_exchange  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_agent_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_broadcast_topic  -r 3  -w 3 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_outside_topic  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_store_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outbound_call_task  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outsidecdr_quality_topic  -r 4  -w 4 

```



## /home

```
sh /home/<USER>/bin/mqadmin updateTopic -n 10.107.27.53:9876 -c RaftCluster -t BENCHMARK_TOPIC  -r 8  -w 8 


sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/*.xml
sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/broker.conf
sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/plain_acl.yml


sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/*.xml

sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/broker.conf



sh /home/<USER>/bin/mqshutdown namesrv
 sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &
 tail -100f /home/<USER>/logs/rocketmqlogs/namesrv.log
 
 sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &
tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
 

```

