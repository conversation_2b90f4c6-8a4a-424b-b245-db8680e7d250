# 重置原集群

```
sh /home/<USER>/bin/mqshutdown broker
sh /home/<USER>/bin/mqshutdown namesrv

mv /home/<USER>/home/<USER>

```

# 安装jdk

```shell
mkdir /home/<USER>
cd /home/<USER>
wget http://58.220.49.186:9999/common/openlogic-openjdk-8u412-b08-linux-x64.tar.gz
tar -zxvf openlogic-openjdk-8u412-b08-linux-x64.tar.gz

```

## 配置环境变量

```shell

vim /etc/profile
export JAVA_HOME=/home/<USER>/openlogic-openjdk-8u412-b08-linux-x64
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH

source /etc/profile


rm -f /usr/bin/java

ln -s /home/<USER>/openlogic-openjdk-8u412-b08-linux-x64/bin/java /usr/bin/java

java -version
```

# 安装

```bash

mkdir /home/<USER>
cd /home/<USER>
wget https://dist.apache.org/repos/dist/release/rocketmq/4.9.7/rocketmq-all-4.9.7-bin-release.zip
unzip rocketmq-all-4.9.7-bin-release.zip
mv /home/<USER>/rocketmq-all-4.9.7-bin-release/* /home/<USER>/

sed -i 's#${user.home}#/home/<USER>' /home/<USER>/conf/*.xml
mkdir -p /home/<USER>/conf/acl
mkdir -p /home/<USER>/logs

sh /home/<USER>/bin/cleancache.sh
sh /home/<USER>/bin/os.sh

```

# 服务器配置

| ip           | host                    | 配置      | dLegerSelfId | 角色               |
| ------------ | ----------------------- | --------- | ------------ | ------------------ |
| ************ | brokerA0、name-server01 | broker-a0 | a0           | Broker、NameServer |
| ************ | brokerA1、name-server02 | broker-a1 | a1           | Broker、NameServer |
| ************ | brokerA2、name-server03 | broker-a2 | a2           | Broker、NameServer |
| ************ | brokerB0                | broker-b0 | b0           | Broker             |
| ************ | brokerB1                | broker-b1 | b1           | Broker             |
| ************ | brokerB2                | broker-b2 | b2           | Broker             |

## host配置

```shell
echo "************ brokerA0" | sudo tee -a /etc/hosts
echo "************ brokerA1" | sudo tee -a /etc/hosts
echo "************ brokerA2" | sudo tee -a /etc/hosts
echo "************ brokerB0" | sudo tee -a /etc/hosts
echo "************ brokerB1" | sudo tee -a /etc/hosts
echo "************ brokerB2" | sudo tee -a /etc/hosts

echo "************ name-server01" | sudo tee -a /etc/hosts
echo "************ name-server02" | sudo tee -a /etc/hosts
echo "************ name-server03" | sudo tee -a /etc/hosts
```



# ACL配置

## plain_acl.yml

> 每台都执行

```shell
sudo tee /home/<USER>/conf/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 10.107.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
 
```



# name-server启动

> 选取3台执行, 和原来在哪个服务器部署的一致

```shell
sh /home/<USER>/bin/mqshutdown namesrv
nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &
tail -100f /home/<USER>/logs/rocketmqlogs/namesrv.log
 
```

# broker启动

> 下面配置保存后, 每台执行

```shell
sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &
tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
 
```

## broker-a.conf

> 修改dLegerSelfId

```shell
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip 保证单网卡可不填
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr= name-server01:9876;name-server02:9876;name-server03:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-brokerA0:20911;a1-brokerA1:20911;a2-brokerA2:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```



## broker-a0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
#brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr= ************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```

## broker-a1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```

## broker-a2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```

## broker-b0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```

## broker-b1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```

## broker-b2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
maxMessageSize=104857600
EOF
```





# 运维



## 创建/修改主题

```shell
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t BENCHMARK_TOPIC  -r 16  -w 16
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc  -r 16  -w 16 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_status  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t base_ext_exchange  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_agent_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_broadcast_topic  -r 3  -w 3 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_outside_topic  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_store_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outbound_call_task  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outsidecdr_quality_topic  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n 172.16.246.183:9876 -c RocketMQ-Cluster -t cloudcc_cdr_delay_check_topic  -r 4  -w 4 

```

## 修改对外内存

```
sed -i 's#-XX:MaxDirectMemorySize=15g#-XX:MaxDirectMemorySize=10g#g' /home/<USER>/bin/runbroker.sh
```

## 验证集群

```shell
sh /home/<USER>/bin/mqadmin clusterlist -n  ************:9876 ************:9876 ************:9876
```

## 清理缓存

```shell
sh /home/<USER>/bin/cleancache.sh
```

## 查询堆内存

```shell
jmap -heap 10645
```



