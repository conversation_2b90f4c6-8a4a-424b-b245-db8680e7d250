# RocketMQ5.2.0-control-多 Master 多 Salve-异步复制-集群搭建

# 准备

```bash
# 下载安装包

# wget 
cd /home
wget http://*************:9999/common/rocketmq-all-5.2.0-bin-release.zip
unzip rocketmq-all-5.2.0-bin-release.zip  
mv /home/<USER>/home/<USER>

mkdir -p /home/<USER>/logs
mkdir -p /home/<USER>/conf/acl
sed -i 's#${user.home}#/home/<USER>' /home/<USER>/conf/*.xml


sed -i 's#/home/<USER>/home/<USER>' /home/<USER>/conf/*.xml
 
sed -i 's#-Xms8g -Xmx8g#-Xms2g -Xmx2g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms4g -Xmx4g -Xmn2g#-Xms1g -Xmx1g -Xmn512m#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms4g -Xmx4g#-Xms1g -Xmx1g#g' /home/<USER>/bin/runserver.sh


```

## host配置

> ip以真实设备为准
>
> 两台机器 部署broker-a和broker-a-s 形成一主一从
>
> 三台机器 部署broker-a, broker-a-s和broker-b 形成二主一从
>
> 四台机器 部署broker-a, broker-a-s, broker-b和broker-b-s 形成二主二从

```shell
echo "*********** rocketmq-n0" | tee -a /etc/hosts
echo "*********** rocketmq-n1" | tee -a /etc/hosts
echo "*********** rocketmq-n2" | tee -a /etc/hosts
```

# 配置

## namesrv-n0.conf

```shell
nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv-n0.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &
```

```shell

sh /home/<USER>/bin/mqshutdown namesrv

tee /home/<USER>/conf/namesrv.conf <<-'EOF'
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n0
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true
EOF

nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &
```

## namesrv-n1.conf

```shell
nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv-n1.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &
```

```shell

sh /home/<USER>/bin/mqshutdown namesrv

tee /home/<USER>/conf/namesrv.conf <<-'EOF'
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n1
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true
EOF
nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &

```

## namesrv-n2.conf

```shell
nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv-n2.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &
```

```shell

sh /home/<USER>/bin/mqshutdown namesrv

tee /home/<USER>/conf/namesrv.conf <<-'EOF'
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n2
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true
EOF

nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &
```

## broker-a.conf

> /home/<USER>/conf
>
> 自动主备切换模式下Broker无需指定brokerId和brokerRole，其由Controller组件进行分配

```shell
tee /home/<USER>/conf/broker-a.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-control-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-a
#0 表示 Master，>0 表示 Slave
#brokerId=0
brokerId=-1
brokerRole=SLAVE
#brokerIP1=**************
#nameServer地址，分号分割
fetchNameSrvAddrByDnsLookup=false
namesrvAddr=***********:9876;***********:9876;***********:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=4
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=false
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=48
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=104857600
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
#brokerRole=ASYNC_MASTER
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=false

# https://rocketmq.apache.org/zh/docs/deploymentOperations/03autofailover#broker-部署
# Broker controller 模式的总开关，只有该值为 true，自动主从切换模式才会打开
enableControllerMode=true
fetchControllerAddrByDnsLookup=false
controllerAddr=***********:9878;***********:9878;***********:9878
# 向 controller 同步 Broker 副本信息的时间间隔
syncBrokerMetadataPeriod=5000
# 检查 SyncStateSet 的时间间隔，检查 SyncStateSet 可能会 shrink SyncState
checkSyncStateSetPeriod=5000
# 同步 controller 元数据的时间间隔，主要是获取 active controller 的地址
syncControllerMetadataPeriod=10000
EOF

```



## rmq-proxy.json

```bash
tee /home/<USER>/conf/rmq-proxy.json <<-'EOF'
{
  "namesrvAddr": "***********:9876;***********:9876;***********:9876",
  "proxyMode": "cluster",
  "rocketMQClusterName": "rocketmq-control-cluster",
  "remotingListenPort": 8380,
  "grpcServerPort": 8081,
  "useEndpointPortFromRequest": true,
  "enableACL": false
}
EOF
```

## plain_acl.yml

> /home/<USER>/conf/plain_acl.yml

```shell
sudo tee /home/<USER>/conf/acl/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 172.16.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
```

# 启动namesrv

```bash

sh /home/<USER>/bin/mqshutdown namesrv
nohup sh /home/<USER>/bin/mqnamesrv  -c /home/<USER>/conf/namesrv.conf > /home/<USER>/logs/mqnamesrv.log 2>&1 &


tail -100f /home/<USER>/logs/rocketmqlogs/namesrv.log
```

# 启动broker

```shell

sh /home/<USER>/bin/mqshutdown broker
sh /home/<USER>/bin/mqshutdown namesrv
 
 # broke-a集群
sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-a.conf --enable-proxy  > /home/<USER>/logs/broker.log 2>&1 &
 tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
 
# broker-b集群
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-b.conf -pc  /home/<USER>/conf/rmq-proxy-b.json --enable-proxy  > /home/<USER>/logs/broker.log 2>&1 &
sh /home/<USER>/bin/mqshutdown broker

 tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
```

# 运维

## cmd

```bash
# 查询集群
sh /home/<USER>/bin/mqadmin clusterlist -n ***********:9876 ***********:9876 ***********:9876

sh /home/<USER>/bin/mqadmin clusterlist -n   ***********:9876 

sh /home/<USER>/bin/mqadmin clusterlist -n 172.16.250.214:9876 172.16.250.215:9876 172.16.250.217:9876

# 创建主题
sh /home/<USER>/bin/mqadmin updateTopic -n ***********:9876 ***********:9876 ***********:9876 -c rocketmq-control-cluster -t ai-inspection-topic  -r 8  -w 8 

sh /home/<USER>/bin/mqadmin updateTopic -n ***********:9876 ***********:9876 ***********:9876 -c rocketmq-control-cluster -t ai-inspection-broadc-topic  -r 8  -w 8 

sh /home/<USER>/bin/mqshutdown broker
sh /home/<USER>/bin/mqshutdown namesrv
rm -rf store

sh bin/mqadmin getControllerMetaData -a rocketmq-n0:9878
sh bin/mqadmin getControllerMetaData -a rocketmq-n1:9878
sh bin/mqadmin getControllerMetaData -a rocketmq-n2:9878


sh bin/mqadmin getControllerMetaData -a 172.16.250.214:9878

sh bin/mqadmin getSyncStateSet -a rocketmq-n2:9878 -b broker-a

#查看 BrokerEpochEntry
sh /home/<USER>/bin/mqadmin getBrokerEpoch -n rocketmq-n2:9876 -b broker-a
 

sh bin/mqadmin cleanBrokerMetadata  -a rocketmq-n2:9878 -bn broker-a -c rocketmq-control-cluster

tail -100f logs/rocketmqlogs/namesrv.log
tail -f logs/rocketmqlogs/broker.log 
```

## sh bin/mqadmin getSyncStateSet -a rocketmq-n2:9878 -b broker-a

![image-20231228101448769](images/image-20231228101448769.png)

## sh /home/<USER>/bin/mqadmin getBrokerEpoch -n rocketmq-n2:9876 -b broker-a

![image-20231228101513845](images/image-20231228101513845.png)

## sh bin/mqadmin getControllerMetaData -a rocketmq-n0:9878

![image-20231228101555154](images/image-20231228101555154.png)

## 主备切换失败

```
（1）下线主备Broker
（2）利用cleanBrokerData命令清除Controller中该组Broker元数据
	 sh bin/mqadmin cleanBrokerMetadata  -a rocketmq-n2:9878 -bn broker-a -c rocketmq-control-cluster
（3）删除主备Broker下的文件～/store/epochFileCheckpoint和epochFileCheckpoint.bak
	rm -rf store/epochFileCheckpoint
（4）Broker重新上线（尽量保证旧的主备关系，先主后备上线）
```

# 问题

## Broker-set: broker-a hasn't been registered in controller

![image-20231228102201012](images/image-20231228102201012.png)
