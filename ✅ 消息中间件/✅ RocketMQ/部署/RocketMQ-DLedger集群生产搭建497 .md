# 重置原集群

```
sh /home/<USER>/bin/mqshutdown broker
sh /home/<USER>/bin/mqshutdown namesrv

mv /home/<USER>/home/<USER>

```

# 安装jdk

> 确认jps可用 已安装jdk忽略
>
> 原来使用yum安装的,需重新装下此jdk

```shell
mkdir /home/<USER>
cd /home/<USER>
wget https://builds.openlogic.com/downloadJDK/openlogic-openjdk/8u412-b08/openlogic-openjdk-8u412-b08-linux-x64.tar.gz
tar -zxvf openlogic-openjdk-8u412-b08-linux-x64.tar.gz

```

### 配置环境变量

```shell

vim /etc/profile
export JAVA_HOME=/home/<USER>/openlogic-openjdk-8u412-b08-linux-x64
export JRE_HOME=${JAVA_HOME}/jre
export CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
export PATH=${JAVA_HOME}/bin:$PATH

source /etc/profile


rm -f /usr/bin/java

ln -s /home/<USER>/openlogic-openjdk-8u412-b08-linux-x64/bin/java /usr/bin/java

java -version
```

# 安装

```bash

mkdir /home/<USER>
cd /home/<USER>
wget https://dist.apache.org/repos/dist/release/rocketmq/4.9.7/rocketmq-all-4.9.7-bin-release.zip
unzip rocketmq-all-4.9.7-bin-release.zip
mv /home/<USER>/rocketmq-all-4.9.7-bin-release/* /home/<USER>/

sed -i 's#${user.home}#/home/<USER>' /home/<USER>/conf/*.xml
mkdir -p /home/<USER>/conf/acl
mkdir -p /home/<USER>/logs

sh /home/<USER>/bin/cleancache.sh
sh /home/<USER>/bin/os.sh

```

# 服务器配置

> 服务器ip根据实际情况修改, 全局修改对应的ip

| ip           | 配置      | 角色               |
| ------------ | --------- | ------------------ |
| ************ | broker-a0 | Broker, NameServer |
| ************ | broker-a1 | Broker, NameServer |
| ************ | broker-a2 | Broker, NameServer |
| ************ | broker-b0 | Broker             |
| ************ | broker-b1 | Broker             |
| ************ | broker-b2 | Broker             |

# ACL配置

## plain_acl.yml

> 每台都执行

```shell
sudo tee /home/<USER>/conf/plain_acl.yml <<-'EOF'
globalWhiteRemoteAddresses:
- 10.107.*.*
accounts:
- accessKey: cqtcqtcqt
  secretKey: cqt!010@Rocketmq
  admin: true
  defaultTopicPerm: DENY
  defaultGroupPerm: SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
- accessKey: cqtcloudcc
  secretKey: cqt@2020
  admin: true
  defaultTopicPerm: PUB|SUB
  defaultGroupPerm: PUB|SUB
  topicPerms:
  - cloudcc=PUB|SUB
  groupPerms:
  - cloudcc_business=PUB|SUB
dataVersion:
- counter: 28
  timestamp: *************
EOF
 
```



# name-server启动

> 选取3台执行, 和原来在哪个服务器部署的一致

```shell
sh /home/<USER>/bin/mqshutdown namesrv
nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &
tail -100f /home/<USER>/logs/rocketmqlogs/namesrv.log
 
```

# broker启动

> 下面配置保存后, 每台执行

```shell
sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &
tail -100f /home/<USER>/logs/rocketmqlogs/broker.log
 
```

## broker-a0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr= ************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```

## broker-a1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```

## broker-a2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeA
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeA
#dLeger集群下的节点配置
dLegerPeers=a0-************:20911;a1-************:20911;a2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=a2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```

## broker-b0.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b0
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```

## broker-b1.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b1
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```

## broker-b2.conf

> ************

```properties
sudo tee /home/<USER>/conf/broker.conf <<-'EOF'
#集群名称
brokerClusterName=RaftCluster
#broker集群名称
brokerName=RaftNodeB
#brokerip
brokerIP1=************
#监听端口
listenPort=10911
#namesrv地址列表
namesrvAddr=************:9876;************:9876;************:9876
#是否启用DLeger集群模式
enableDLegerCommitLog=true
#与brokerName保持一致就好
dLegerGroup=RaftNodeB
#dLeger集群下的节点配置
dLegerPeers=b0-************:20911;b1-************:20911;b2-************:20911
## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=b2
#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=32
#发送消息线程等待时间，默认200ms
waitTimeMillsInSendQueue=1000
useReentrantLockWhenPutMessage=true
aclEnable=true
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=88
# 拉取消息线程池大小
pullMessageThreadPoolNums=128
flushDiskType=ASYNC_FLUSH
maxTransferCountOnMessageInMemory=1000
#限制的消息大小
maxMessageSize=104857600
EOF
```





# 运维



## 创建/修改主题

```shell
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t BENCHMARK_TOPIC  -r 16  -w 16
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc  -r 16  -w 16 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_status  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t base_ext_exchange  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_agent_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_broadcast_topic  -r 3  -w 3 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_outside_topic  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_cdr_store_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_ext_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outbound_call_task  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n ************:9876 -c RaftCluster -t cloudcc_outsidecdr_quality_topic  -r 4  -w 4 

```

## 修改对外内存

```
sed -i 's#-XX:MaxDirectMemorySize=15g#-XX:MaxDirectMemorySize=10g#g' /home/<USER>/bin/runbroker.sh
```

## 验证集群

```shell
sh /home/<USER>/bin/mqadmin clusterlist -n  ************:9876 ************:9876 ************:9876
```

## 清理缓存

```shell
sh /home/<USER>/bin/cleancache.sh
```

## 查询堆内存

```shell
jmap -heap 10645
```

## 监控启动状态

### monitor-rocketmq-broker.sh

> broker进程监控

```shell
sudo tee /home/<USER>/bin/monitor-rocketmq-broker.sh <<-'EOF'
#!/bin/bash

# RocketMQ 进程名称
PROCESS_NAME="BrokerStartup"

# RocketMQ 启动命令
START_CMD="nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker.conf > /home/<USER>/logs/broker.log 2>&1 &"

# 检查 RocketMQ 进程是否运行
if ps aux | grep -v grep | grep "$PROCESS_NAME" > /dev/null; then
    echo "$(date): RocketMQ $PROCESS_NAME is running." >> /home/<USER>/logs/check.log
else
    echo "$(date): RocketMQ $PROCESS_NAME is not running. Starting RocketMQ $PROCESS_NAME..." >> /home/<USER>/logs/check.log
    # 执行启动命令
    eval $START_CMD
    if [ $? -eq 0 ]; then
        echo "$(date): RocketMQ $PROCESS_NAME started successfully." >> /home/<USER>/logs/check.log
    else
        echo "$(date): Failed to start RocketMQ $PROCESS_NAME." >> /home/<USER>/logs/check.log
    fi
fi
EOF
chmod 777 /home/<USER>/bin/monitor-rocketmq-broker.sh
```

### monitor-rocketmq-nameserver.sh

```shell
sudo tee /home/<USER>/bin/monitor-rocketmq-nameserver.sh <<-'EOF'
#!/bin/bash

# RocketMQ 进程名称
PROCESS_NAME="NamesrvStartup"

# RocketMQ 启动命令
START_CMD="nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &"

# 检查 RocketMQ 进程是否运行
if ps aux | grep -v grep | grep "$PROCESS_NAME" > /dev/null; then
    echo "$(date): RocketMQ $PROCESS_NAME is running." >> /home/<USER>/logs/check.log
else
    echo "$(date): RocketMQ $PROCESS_NAME is not running. Starting RocketMQ $PROCESS_NAME..." >> /home/<USER>/logs/check.log
    # 执行启动命令
    eval $START_CMD
    if [ $? -eq 0 ]; then
        echo "$(date): RocketMQ $PROCESS_NAME started successfully." >> /home/<USER>/logs/check.log
    else
        echo "$(date): Failed to start RocketMQ $PROCESS_NAME." >> /home/<USER>/logs/check.log
    fi
fi
EOF
chmod 777 /home/<USER>/bin/monitor-rocketmq-nameserver.sh
```

### 添加cron定时任务

> crontab -e

```shell
* * * * * /home/<USER>/bin/monitor-rocketmq-broker.sh
* * * * * /home/<USER>/bin/monitor-rocketmq-nameserver.sh
```

