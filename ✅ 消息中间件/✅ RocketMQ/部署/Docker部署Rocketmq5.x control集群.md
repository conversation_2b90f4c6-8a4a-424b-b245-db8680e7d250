

# 准备

```shell
mkdir /home/<USER>
mkdir /home/<USER>/rocketmqlogs
mkdir /home/<USER>/controller

chmod 777 /home/<USER>/rocketmqlogs
chmod 777 /home/<USER>/controller
```



# nameserver部署

## namesrv-n0.conf

```properties
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n0
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true

```



## docker-compose-name-server-n0.yaml

```yaml
services:
  rocketmq-name-server-n0:
    image: harbor.cqt.com:11000/docker/rocketmq:5.2.0
    container_name: rocketmq-name-server-n0
    restart: always
    privileged: true
    volumes:
      - ./rocketmqlogs:/home/<USER>/logs/rocketmqlogs
      - ./controller:/home/<USER>/controller
      - ./namesrv-n0.conf:/home/<USER>/namesrv.conf
    environment:
      - JAVA_OPT=-Xms1g -Xmx1g -Xmn512m
    command: sh mqnamesrv -c /home/<USER>/namesrv.conf
    network_mode: host
```

## namesrv-n1.conf

```properties
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n1
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true

```



## docker-compose-name-server-n1.yaml

```yaml
services:
  rocketmq-name-server-n1:
    image: harbor.cqt.com:11000/docker/rocketmq:5.2.0
    container_name: rocketmq-name-server-n1
    restart: always
    privileged: true
    volumes:
      - ./rocketmqlogs:/home/<USER>/logs/rocketmqlogs
      - ./controller:/home/<USER>/controller
      - ./namesrv-n1.conf:/home/<USER>/namesrv.conf
    environment:
      - JAVA_OPT=-Xms1g -Xmx1g -Xmn512m
    command: sh mqnamesrv -c /home/<USER>/namesrv.conf
    network_mode: host
```

## namesrv-n2.conf

```properties
listenPort=9876
enableControllerInNamesrv=true
controllerDLegerGroup=group1
controllerDLegerPeers=n0-***********:9878;n1-***********:9878;n2-***********:9878
controllerDLegerSelfId=n2
controllerStorePath=/home/<USER>/controller/DledgerController
enableElectUncleanMaster=true
notifyBrokerRoleChanged=true

```



## docker-compose-name-server-n2.yaml

```yaml
services:
  rocketmq-name-server-n2:
    image: harbor.cqt.com:11000/docker/rocketmq:5.2.0
    container_name: rocketmq-name-server-n2
    restart: always
    privileged: true
    volumes:
      - ./rocketmqlogs:/home/<USER>/logs/rocketmqlogs
      - ./controller:/home/<USER>/controller
      - ./namesrv-n2.conf:/home/<USER>/namesrv.conf
    environment:
      - JAVA_OPT=-Xms1g -Xmx1g -Xmn512m
    command: sh mqnamesrv -c /home/<USER>/namesrv.conf
    network_mode: host
```

# Broker部署

## rmq-proxy.json

```shell
tee /home/<USER>/rmq-proxy.json <<-'EOF'
{
  "namesrvAddr": "***********:9876;***********:9876;***********:9876",
  "proxyMode": "cluster",
  "rocketMQClusterName": "rocketmq-control-cluster",
  "remotingListenPort": 8380,
  "grpcServerPort": 8081,
  "useEndpointPortFromRequest": true,
  "enableACL": false
}
EOF
```

## broker-a.conf

```shell
tee /home/<USER>/broker-a.conf <<-'EOF'
#所属集群名字
brokerClusterName=rocketmq-control-cluster
#broker名字，注意此处不同的配置文件填写的不一样
brokerName=broker-a
#0 表示 Master，>0 表示 Slave
#brokerId=0
brokerId=-1
brokerRole=SLAVE
#brokerIP1=**************
#nameServer地址，分号分割
fetchNameSrvAddrByDnsLookup=false
namesrvAddr=***********:9876;***********:9876;***********:9876
#在发送消息时，自动创建服务器不存在的topic，默认创建的队列数
defaultTopicQueueNums=4
#是否允许 Broker 自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=false
#是否允许 Broker 自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true
#Broker 对外服务的监听端口，10911为默认值
listenPort=10911
#表示Master监听Slave请求的端口,默认为服务端口+1
haListenPort=10912
#删除文件时间点，默认凌晨 4点
deleteWhen=04
#文件保留时间，默认 48 小时
fileReservedTime=48
#commitLog每个文件的大小默认1G
mapedFileSizeCommitLog=1073741824
#ConsumeQueue每个文件默认存30W条，根据业务情况调整
mapedFileSizeConsumeQueue=300000
#destroyMapedFileIntervalForcibly=120000
#redeleteHangedFileInterval=120000
#检测物理文件磁盘空间
diskMaxUsedSpaceRatio=88
#存储路径
storePathRootDir=/home/<USER>/store
#commitLog 存储路径
storePathCommitLog=/home/<USER>/store/commitlog
#消费队列存储路径存储路径
storePathConsumeQueue=/home/<USER>/store/consumequeue
#消息索引存储路径
storePathIndex=/home/<USER>/store/index
#checkpoint 文件存储路径
storeCheckpoint=/home/<USER>/store/checkpoint
#abort 文件存储路径
abortFile=/home/<USER>/store/abort
#限制的消息大小
maxMessageSize=104857600
#flushCommitLogLeastPages=4
#flushConsumeQueueLeastPages=2
#flushCommitLogThoroughInterval=10000
#flushConsumeQueueThoroughInterval=60000
#Broker 的角色
#- ASYNC_MASTER  异步复制Master
#- SYNC_MASTER  同步双写Master
#- SLAVE
#brokerRole=ASYNC_MASTER
#刷盘方式
#- ASYNC_FLUSH  异步刷盘
#- SYNC_FLUSH  同步刷盘
flushDiskType=ASYNC_FLUSH
#checkTransactionMessageEnable=false
#发消息线程池数量
sendMessageThreadPoolNums=64
#拉消息线程池数量
pullMessageThreadPoolNums=128
#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=10000
#发送队列线程容量
sendThreadPoolQueueCapacity=100000
#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
aclEnable=false

# https://rocketmq.apache.org/zh/docs/deploymentOperations/03autofailover#broker-部署
# Broker controller 模式的总开关，只有该值为 true，自动主从切换模式才会打开
enableControllerMode=true
fetchControllerAddrByDnsLookup=false
controllerAddr=***********:9878;***********:9878;***********:9878
# 向 controller 同步 Broker 副本信息的时间间隔
syncBrokerMetadataPeriod=5000
# 检查 SyncStateSet 的时间间隔，检查 SyncStateSet 可能会 shrink SyncState
checkSyncStateSetPeriod=5000
# 同步 controller 元数据的时间间隔，主要是获取 active controller 的地址
syncControllerMetadataPeriod=10000
EOF

```





> 
>
> nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-a.conf --enable-proxy  > /home/<USER>/logs/broker.log 2>&1 &

## docker-compose-rocketmq-broker-a.yaml

```yaml
tee /home/<USER>/docker-compose-rocketmq-broker-a.yaml <<-'EOF'
services:
  rocketmq-broker-a:
    image: harbor.cqt.com:11000/docker/rocketmq:5.2.0
    container_name: rocketmq-broker-a
    restart: always
    privileged: true
    volumes:
      - ./rocketmqlogs:/home/<USER>/logs/rocketmqlogs
      - ./broker-a.conf:/home/<USER>/broker.conf
      - ./rmq-proxy.json:/home/<USER>/rmq-proxy.json
    environment:
      - JAVA_OPT=-Xms1g -Xmx1g -Xmn512m
    command: sh mqbroker  -c /home/<USER>/broker.conf -pc /home/<USER>/rmq-proxy.json --enable-proxy 
    network_mode: host
EOF
```

