![image-20231121152857245](images/image-20231121152857245.png)

# 准备

```bash
# 下载安装包

mkdir /home/<USER>
mkdir -p /home/<USER>/logs
cd /home/<USER>
wget https://dist.apache.org/repos/dist/release/rocketmq/4.9.8/rocketmq-all-4.9.8-bin-release.zip
unzip rocketmq-all-4.9.8-bin-release.zip
mv /home/<USER>/rocketmq-all-4.9.8-bin-release/* /home/<USER>/
rm -rf rocketmq-all-4.9.8-bin-release.zip
rm -rf rocketmq-all-4.9.8-bin-release/
 

```

# 配置

## broker-n0.conf

```properties

#集群名称
brokerClusterName = RaftCluster

#broker集群名称-同一组的broker要保持一致
brokerName=RaftNode00

#brokerip
brokerIP1=**************

#监听端口
listenPort=30911

#namesrv地址列表
namesrvAddr=**************:9876;**************:9876;**************:9876

#数据存储根路径
storePathRootDir=/home/<USER>/rmqstore/node00

#commitlog数据存储根路径
storePathCommitLog=/home/<USER>/rmqstore/node00/commitlog

#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=1000

#消费时一次拉取的数量由 broker 和 consumer 客户端共同决定，默认为 32 条. consumer 端由 pullBatchSize 设置
maxTransferCountOnMessageInMemory=1000

#是否启用DLeger集群模式
enableDLegerCommitLog=true

#与brokerName保持一致就好
dLegerGroup=RaftNode00

#dLeger集群下的节点配置
dLegerPeers=n0-**************:20911;n1-**************:20911;n2-**************:20911

## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=n0

#服务端处理消息发送线程池数量, 默认为1
sendMessageThreadPoolNums=8

# ak sk
aclEnable=true

defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80

#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
```

## broker-n1.conf

```properties

#集群名称
brokerClusterName = RaftCluster

#broker集群名称-同一组的broker要保持一致
brokerName=RaftNode00

#brokerip
brokerIP1=**************

#监听端口
listenPort=30911

#namesrv地址列表
namesrvAddr=**************:9876;**************:9876;**************:9876

#数据存储根路径
storePathRootDir=/home/<USER>/rmqstore/node00

#commitlog数据存储根路径
storePathCommitLog=/home/<USER>/rmqstore/node00/commitlog

#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=1000

#消费时一次拉取的数量由 broker 和 consumer 客户端共同决定，默认为 32 条. consumer 端由 pullBatchSize 设置
maxTransferCountOnMessageInMemory=1000

#是否启用DLeger集群模式
enableDLegerCommitLog=true

#与brokerName保持一致就好
dLegerGroup=RaftNode00

#dLeger集群下的节点配置
dLegerPeers=n0-**************:20911;n1-**************:20911;n2-**************:20911

## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=n1

#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=8

# ak sk
aclEnable=true

defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80

#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
```

## broker-n2.conf

```properties

#集群名称
brokerClusterName = RaftCluster

#broker集群名称-同一组的broker要保持一致
brokerName=RaftNode00

#brokerip
brokerIP1=**************

#监听端口
listenPort=30911

#namesrv地址列表
namesrvAddr=**************:9876;**************:9876;**************:9876

#数据存储根路径
storePathRootDir=/home/<USER>/rmqstore/node00

#commitlog数据存储根路径
storePathCommitLog=/home/<USER>/rmqstore/node00/commitlog

#broker检测队列中的消息等待时间（默认是200毫秒，自行配置）
waitTimeMillsInSendQueue=1000

#消费时一次拉取的数量由 broker 和 consumer 客户端共同决定，默认为 32 条. consumer 端由 pullBatchSize 设置
maxTransferCountOnMessageInMemory=1000

#是否启用DLeger集群模式
enableDLegerCommitLog=true

#与brokerName保持一致就好
dLegerGroup=RaftNode00

#dLeger集群下的节点配置
dLegerPeers=n0-**************:20911;n1-**************:20911;n2-**************:20911

## must be unique
#当前节点在dLeger集群下的标识
dLegerSelfId=n2

#服务端处理消息发送线程池数量
sendMessageThreadPoolNums=8

# ak sk
aclEnable=true

defaultTopicQueueNums=16
autoCreateTopicEnable=false
deleteWhen=04
fileReservedTime=48
diskMaxUsedSpaceRatio=80

#关闭堆外内存
transientStorePoolEnable=false
#关闭文件预热
warmMapedFileEnable=false
#开启堆内传
transferMsgByHeap=true
```

# 启动namesrv

```bash
nohup sh /home/<USER>/bin/mqnamesrv > /home/<USER>/logs/mqnamesrv.log 2>&1 &
 sh /home/<USER>/bin/mqshutdown namesrv
```

# 启动broker

```bash
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-n0.conf > /home/<USER>/logs/broker-n0.log 2>&1 &

 sh /home/<USER>/bin/mqshutdown namesrv
sh /home/<USER>/bin/mqshutdown broker
nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-n1.conf > /home/<USER>/logs/broker-n1.log 2>&1 &

nohup sh /home/<USER>/bin/mqbroker -c /home/<USER>/conf/broker-n2.conf > /home/<USER>/logs/broker-n2.log 2>&1 &
```



# 运维

```
# 查询集群
 sh /home/<USER>/bin/mqadmin clusterlist -n  **************:9876 **************:9876 **************:9876  
 
 sh /home/<USER>/bin/mqadmin clusterlist -n  172.16.250.213:9876 172.16.250.215:9876 172.16.250.216:9876
 
  sh /home/<USER>/bin/mqadmin clusterlist -n  172.16.250.140:9876 172.16.250.141:9876 172.16.250.142:9876
```



# 使用

```
**************:9876;**************:9876,**************:9876

172.16.250.213:9876;172.16.250.215:9876;172.16.250.216:9876

sh consumer.sh -t BenchmarkTest -n 172.16.250.213:9876 172.16.250.215:9876 172.16.250.216:9876 -g test2


```



```
docker tag rocketmq-dashboard:latest 58.220.49.186:11000/cloudcc/rocketmq-dashboard:latest
```



```
 rockermq
    name-server: 172.16.250.140:9876;172.16.250.141:9876;172.16.250.142:9876
    access-key: cqtcloudcc
    secret-key: cqt@2020
    
rocketmq-dashboard
    http://172.16.250.140:18080/#/cluster admin/cqt@2020
  
 xxl-job-admin
 http://172.16.250.141:8800/xxl-job-admin/toLogin    admin/cqt!010@Xxljob
```

