# RocketMQ生产环境实践

## RocketMQ 服务端版本

> 4.9.7
>
> https://rocketmq.apache.org/zh/download
>
> https://dist.apache.org/repos/dist/release/rocketmq/4.9.7/rocketmq-all-4.9.7-bin-release.zip

![image-20230708144356004](images/image-20230708144356004.png)

![image-20230708144547262](images/image-20230708144547262.png)

## SpringBoot客户端

> rocketmq-spring-boot-starter 2.2.2版本的rocketmq-client为4.9.3
>
> 所以要替换成4.9.7

```xml
		<rocketmq.version>4.9.7</rocketmq.version>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>rocketmq-client</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>${rocketmq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
```

