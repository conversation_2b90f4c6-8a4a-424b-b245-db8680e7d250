# Linux查询io命令

##                     

```shell
# 查看当前各个磁盘设备的io情况
iostat -m -x 1

# 检查sda磁盘中哪个应用程序占用的io比较高
pidstat -d  1

# 分析应用程序中哪一个线程占用的io比较高
pidstat -dt -p {pid} 1 

# 分析这个线程在干什么？
perf trace -t {pid} -o /tmp/tmp_aa.pstrace

# 查看这个文件句柄是什么
lsof -p {pid}|grep 159u
```

```shell
flushCommitLogTimed 	表示await方法等待FlushIntervalCommitlog,如果为true表示使用Thread.sleep方法等待 false

commitIntervalCommitLog commitlog提交频率 200ms
flushIntervalCommitLog  commitlog刷盘频率 500ms	

```

```yaml
# false 使用 AQS 等待; true 使用 sleep 等待, 默认false
flushCommitLogTimed=false
# 每隔 500ms 刷盘, 默认500ms
flushIntervalCommitLog=2000
# 每次刷盘时，至少4页, 默认4页
flushCommitLogLeastPages=8
# 间隔多久未刷盘，会强制刷盘, 默认1000*10ms
flushCommitLogThoroughInterval=20000

```

```shell
# 动态修改
sh mqadmin updateBrokerConfig -c 集群名称 -n namesrv -k maxTransferCountOnMessageInMemory -v 400

sh /home/<USER>/bin/mqadmin updateBrokerConfig -n 10.106.27.51:9876 -c RaftCluster -k flushIntervalCommitLog -v 2000

```
