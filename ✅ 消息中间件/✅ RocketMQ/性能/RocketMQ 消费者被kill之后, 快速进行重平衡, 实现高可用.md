# 背景

>  RocketMQ集群, topic有两个消费者, 当一个消费者被kill, 或者topic增加队列之后,  或有一段时间没有消费者, 会导致消息积压, 当业务需要消息被立即消费, 就有可能导致故障.

>  目前需要新增队列, 队列空闲时 有broker挂掉, 加快队列重平衡的时间. 实现队列快速被分配给可用消费者, 实现高可用

# 解决方案

> 修改重平衡的时间配置
>
> 修改消费者向borker发送心跳的时间
>
> 修改消费者向NameServer拉取路由配置的时间

## jvm配置

```shell
# 设置客户端定期从 NameServer 拉取最新路由信息的间隔时间, 默认是30秒
-Drocketmq.client.pollNameServerInterval=5000
# 设置客户端（生产者/消费者）向 Broker 发送 心跳 的时间间隔（毫秒） 默认是30秒
-Drocketmq.client.heartbeatBrokerInterval=3000
# 设置 重平衡 任务的检查间隔时间 默认是20秒
-Drocketmq.client.rebalance.waitInterval=5000
```

## ClientConfigBeanPostProcessor

> `BeanPostProcessor` 是 **Spring** 框架中的一个接口，用于在 **Spring 容器**初始化 Bean 之前或之后进行自定义操作。它提供了一种机制，可以在 **Bean** 的生命周期中插入逻辑，比如修改 Bean 的属性、实现代理等。

> `BeanPostProcessor` 可以在 Bean **初始化之前**修改ClientConfig的配置

```java
@Slf4j
@Component
public class ClientConfigBeanPostProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof ClientConfig) {
            ClientConfig clientConfig = (ClientConfig) bean;
            String pollNameServerInterval = SpringUtil.getProperty("rocketmq.client.pollNameServerInterval");
            log.info("rocketmq pollNameServerInterval: {}", pollNameServerInterval);
            if (StrUtil.isNotEmpty(pollNameServerInterval)) {
                clientConfig.setPollNameServerInterval(Integer.parseInt(pollNameServerInterval));
            }

            String heartbeatBrokerInterval = SpringUtil.getProperty("rocketmq.client.heartbeatBrokerInterval");
            log.info("rocketmq heartbeatBrokerInterval: {}", heartbeatBrokerInterval);
            if (StrUtil.isNotEmpty(heartbeatBrokerInterval)) {
                clientConfig.setHeartbeatBrokerInterval(Integer.parseInt(heartbeatBrokerInterval));
            }
        }
        return bean;
    }
}

```

## RocketMqConfigSmartInitializingSingleton

> `SmartInitializingSingleton` 是 **Spring Framework** 中的一个接口，用于在 **Spring 容器初始化所有单例 Bean** 之后执行一些自定义逻辑。它的主要目的是在所有单例 Bean 都已初始化完成后，执行一次性的初始化操作。

> 在全部bean初始化完成之后, 修改每个DefaultMQPushConsumer的配置
> pollNameServerInterval
> heartbeatBrokerInterval

```java
@Slf4j
@Component
@RequiredArgsConstructor
public class RocketMqConfigSmartInitializingSingleton implements SmartInitializingSingleton {

    private final Environment environment;

    @Override
    public void afterSingletonsInstantiated() {
        Map<String, DefaultRocketMQListenerContainer> listenerContainers = SpringUtil.getApplicationContext().getBeansOfType(DefaultRocketMQListenerContainer.class);
        listenerContainers.values().forEach(container -> {
            DefaultMQPushConsumer consumer = container.getConsumer();
            String consumerGroup = consumer.getConsumerGroup();
            String pollNameServerInterval = environment.getProperty("rocketmq.client.pollNameServerInterval");
            log.info("rocketmq consumerGroup: {}, pollNameServerInterval: {}", consumerGroup, pollNameServerInterval);
            if (StrUtil.isNotEmpty(pollNameServerInterval)) {
                consumer.setPollNameServerInterval(Integer.parseInt(pollNameServerInterval));
            }

            String heartbeatBrokerInterval = environment.getProperty("rocketmq.client.heartbeatBrokerInterval");
            log.info("rocketmq consumerGroup: {}, heartbeatBrokerInterval: {}", consumerGroup, heartbeatBrokerInterval);
            if (StrUtil.isNotEmpty(heartbeatBrokerInterval)) {
                consumer.setHeartbeatBrokerInterval(Integer.parseInt(heartbeatBrokerInterval));
            }
        });
    }
}

```



# 源码解释

## 一些客户端的定义任务

> 客户端创建MQClientInstance之后, 会启一些定时任务
>
> 主要是要修改这两个值. 默认是30秒
> this.clientConfig.getHeartbeatBrokerInterval()
>
> this.clientConfig.getPollNameServerInterval()

```java
	private void startScheduledTask() {
        // 每2分钟 拉取最新的 NameServer 地址
        if (null == this.clientConfig.getNamesrvAddr()) {
            this.scheduledExecutorService.scheduleAtFixedRate(() -> {
                try {
                    MQClientInstance.this.mQClientAPIImpl.fetchNameServerAddr();
                } catch (Throwable t) {
                    log.error("ScheduledTask fetchNameServerAddr exception", t);
                }
            }, 1000 * 10, 1000 * 60 * 2, TimeUnit.MILLISECONDS);
        }

        // 定期从 NameServer 更新 Topic 路由信息
        this.scheduledExecutorService.scheduleAtFixedRate(() -> {
            try {
                MQClientInstance.this.updateTopicRouteInfoFromNameServer();
            } catch (Throwable t) {
                log.error("ScheduledTask updateTopicRouteInfoFromNameServer exception", t);
            }
        }, 10, this.clientConfig.getPollNameServerInterval(), TimeUnit.MILLISECONDS);

        // 定期清理已下线的 Broker; 发送 心跳 到所有在线 Broker
        this.scheduledExecutorService.scheduleAtFixedRate(() -> {
            try {
                // 清理已下线的 Broker，避免客户端持有无效的 Broker 信息。
                MQClientInstance.this.cleanOfflineBroker();
                // 发送 心跳 到所有在线 Broker
                MQClientInstance.this.sendHeartbeatToAllBrokerWithLock();
            } catch (Throwable t) {
                log.error("ScheduledTask sendHeartbeatToAllBroker exception", t);
            }
        }, 1000, this.clientConfig.getHeartbeatBrokerInterval(), TimeUnit.MILLISECONDS);

        // 定期 持久化消费者的消费进度
        this.scheduledExecutorService.scheduleAtFixedRate(() -> {
            try {
                MQClientInstance.this.persistAllConsumerOffset();
            } catch (Throwable t) {
                log.error("ScheduledTask persistAllConsumerOffset exception", t);
            }
        }, 1000 * 10, this.clientConfig.getPersistConsumerOffsetInterval(), TimeUnit.MILLISECONDS);

        // 定期调整 线程池的大小 或其他参数
        this.scheduledExecutorService.scheduleAtFixedRate(() -> {
            try {
                MQClientInstance.this.adjustThreadPool();
            } catch (Throwable t) {
                log.error("ScheduledTask adjustThreadPool exception", t);
            }
        }, 1, 1, TimeUnit.MINUTES);
    }
```

## 重平衡的线程

> 在MQClientInstance启动之后  也会启动RebalanceService这个线程
> 从环境变量中读取rocketmq.client.rebalance.waitInterval

```java
public class RebalanceService extends ServiceThread {
    private static long waitInterval =
        Long.parseLong(System.getProperty(
            "rocketmq.client.rebalance.waitInterval", "20000"));
    private static long minInterval =
        Long.parseLong(System.getProperty(
            "rocketmq.client.rebalance.minInterval", "1000"));
    private final Logger log = LoggerFactory.getLogger(RebalanceService.class);
    private final MQClientInstance mqClientFactory;
    private long lastRebalanceTimestamp = System.currentTimeMillis();

    public RebalanceService(MQClientInstance mqClientFactory) {
        this.mqClientFactory = mqClientFactory;
    }

    @Override
    public void run() {
        log.info(this.getServiceName() + " service started");

        long realWaitInterval = waitInterval;
        while (!this.isStopped()) {
            this.waitForRunning(realWaitInterval);

            long interval = System.currentTimeMillis() - lastRebalanceTimestamp;
            if (interval < minInterval) {
                realWaitInterval = minInterval - interval;
            } else {
                // 进行重平衡操作
                boolean balanced = this.mqClientFactory.doRebalance();
                realWaitInterval = balanced ? waitInterval : minInterval;
                lastRebalanceTimestamp = System.currentTimeMillis();
            }
        }

        log.info(this.getServiceName() + " service end");
    }

    @Override
    public String getServiceName() {
        return RebalanceService.class.getSimpleName();
    }
}

```

