# RocketMQ内存设置多少合适

### 修改broker.conf

```shell
sed -i 's#transientStorePoolEnable=false#transientStorePoolEnable=true#g' /home/<USER>/conf/broker.conf

sed -i 's#transientStorePoolEnable=true#transientStorePoolEnable=false#g' /home/<USER>/conf/broker.conf
sed -i 's#transferMsgByHeap=true#transferMsgByHeap=false#g' /home/<USER>/conf/broker.conf

sed -i 's#sendMessageThreadPoolNums=32#sendMessageThreadPoolNums=16#g' /home/<USER>/conf/broker.conf
sed -i 's#sendMessageThreadPoolNums=16#sendMessageThreadPoolNums=32#g' /home/<USER>/conf/broker.conf


pullMessageThreadPoolNums=128
sed -i 's#pullMessageThreadPoolNums=128#pullMessageThreadPoolNums=16#g' /home/<USER>/conf/broker.conf
sed -i 's#pullMessageThreadPoolNums=16#pullMessageThreadPoolNums=8#g' /home/<USER>/conf/broker.conf
sed -i 's#pullMessageThreadPoolNums=8#pullMessageThreadPoolNums=16#g' /home/<USER>/conf/broker.conf
```

### 修改NameServer内存

```shell
sed -i 's#-Xms4g -Xmx4g -Xmn2g#-Xms1g -Xmx1g -Xmn512M#g' /home/<USER>/bin/runserver.sh
sed -i 's#-Xms4g -Xmx4g#-Xms1g -Xmx1g#g' /home/<USER>/bin/runserver.sh

sed -i 's#-Xms1g -Xmx1g -Xmn512M#-Xms2g -Xmx2g -Xmn1g#g' /home/<USER>/bin/runserver.sh
sed -i 's#-Xms1g -Xmx1g#-Xms3g -Xmx3g#g' /home/<USER>/bin/runserver.sh
```

### 修改Broker内存

```shell
sed -i 's#-Xms8g -Xmx8g#-Xms1g -Xmx1g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-XX:MaxDirectMemorySize=10g#-XX:MaxDirectMemorySize=1g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms1g -Xmx1g#-Xms2g -Xmx2g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms2g -Xmx2g#-Xms3g -Xmx3g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms3g -Xmx3g#-Xms4g -Xmx4g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms4g -Xmx4g#-Xms5g -Xmx5g#g' /home/<USER>/bin/runbroker.sh
sed -i 's#-Xms5g -Xmx5g#-Xms3g -Xmx3g#g' /home/<USER>/bin/runbroker.sh


sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc  -r 8  -w 8
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc  -r 10  -w 10
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc  -r 16  -w 16
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc  -r 20  -w 20
```



| JDK  | NameServer内存         | Broker内存    | tps  |
| ---- | ---------------------- | ------------- | ---- |
| 8    | -Xms1g -Xmx1g -Xmn512M | -Xms1g -Xmx1g |      |
| 8    | -Xms2g -Xmx2g -Xmn1g   | -Xms3g -Xmx3g | 9583 |
|      |                        |               |      |

```shell
sh /home/<USER>/bin/mqshutdown broker && sh /home/<USER>/bin/mqshutdown namesrv

sh /home/<USER>/bin/monitor-rocketmq-broker.sh && sh /home/<USER>/bin/monitor-rocketmq-nameserver.sh


sh /home/<USER>/bin/mqshutdown namesrv sh /home/<USER>/bin/monitor-rocketmq-nameserver.sh

sh /home/<USER>/bin/mqshutdown broker && sh /home/<USER>/bin/monitor-rocketmq-broker.sh


```



```
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc  -r 16  -w 16 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_ext_status  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t base_ext_exchange  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_agent_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_broadcast_topic  -r 3  -w 3 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_cdr_outside_topic  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_cdr_store_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_ext_log_topic  -r 8  -w 8 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_outbound_call_task  -r 4  -w 4 
sh /home/<USER>/bin/mqadmin updateTopic -n 10.255.53.23:9876 -c RaftCluster -t cloudcc_outsidecdr_quality_topic  -r 4  -w 4 
```



