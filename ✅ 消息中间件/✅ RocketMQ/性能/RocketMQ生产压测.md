> 磁盘优化前 tps 7000 io使用率 56%
>
> 磁盘优化后 tps 6000 io使用率 47.81%
>
> 内存 tps 9400 io使用率 0.15%

# 磁盘优化前

> 16个生产者发送消息并消费

![image-20240523171837658](images/image-20240523171837658.png)

![image-20240523172744550](images/image-20240523172744550.png)

![image-20240523172600961](images/image-20240523172600961.png)



![image-20240523172613273](images/image-20240523172613273.png)

![image-20240523172645219](images/image-20240523172645219.png)

![image-20240523172715284](images/image-20240523172715284.png)

# 磁盘优化后

> 16个生产者发送消息并消费

![image-20240523194638157](images/image-20240523194638157.png)

![image-20240523194702175](images/image-20240523194702175.png)

![image-20240523194614083](images/image-20240523194614083.png)

![image-20240523194724873](images/image-20240523194724873.png)

![image-20240523194746513](images/image-20240523194746513.png)

![image-20240523194814208](images/image-20240523194814208.png)

![image-20240523194828743](images/image-20240523194828743.png)

# 内存

> mq进程在/home目录, 数据在/data目录
>
> 16个生产者发送消息并消费

![image-20240523185441241](images/image-20240523185441241.png)

![image-20240523190353193](images/image-20240523190353193.png)

![image-20240523190334250](images/image-20240523190334250.png)

![image-20240523190408008](images/image-20240523190408008.png)

![image-20240523190435435](images/image-20240523190435435.png)

![image-20240523190501087](images/image-20240523190501087.png)

![image-20240523190512426](images/image-20240523190512426.png)
