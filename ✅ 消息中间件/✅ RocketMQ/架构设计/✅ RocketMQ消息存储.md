# RocketMQ消息存储
# ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487232897-5a6f0cd7-b959-4133-a96c-750b9a82b13e.png#averageHue=%23f7eeee&clientId=uc96e6597-9974-4&from=paste&height=896&id=uafdc434c&originHeight=896&originWidth=1142&originalType=binary&ratio=1&rotation=0&showTitle=false&size=91937&status=done&style=none&taskId=u073b3811-5746-4810-98d2-dd49d7b908f&title=&width=1142)
## 消息存储整体架构
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487508413-4c10ecd7-1204-4d78-a988-1a5a3ea7ba95.png#averageHue=%230c0907&clientId=uc96e6597-9974-4&from=paste&height=570&id=u477fa8e4&originHeight=570&originWidth=300&originalType=binary&ratio=1&rotation=0&showTitle=false&size=20809&status=done&style=none&taskId=udd7b3a9a-9425-41a2-8518-7a08e3faf0e&title=&width=300)![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487523889-c699fe9e-8fe3-46eb-88f7-80bddb38929e.png#averageHue=%23050404&clientId=uc96e6597-9974-4&from=paste&height=582&id=uccd9543e&originHeight=582&originWidth=299&originalType=binary&ratio=1&rotation=0&showTitle=false&size=10705&status=done&style=none&taskId=u6d93e837-e26e-4d35-a9ab-31bad7426ae&title=&width=299)![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487541467-5c536fa2-b6f4-45cc-8a7e-426dd3988807.png#averageHue=%23060504&clientId=uc96e6597-9974-4&from=paste&height=533&id=ue689de29&originHeight=533&originWidth=301&originalType=binary&ratio=1&rotation=0&showTitle=false&size=11803&status=done&style=none&taskId=u62302baa-3697-4f85-a652-909597377cb&title=&width=301)
### CommitLog 存储主体

- 消息主体以及元数据的存储主体，存储Producer端写入的消息主体内容,消息内容不是定长的。
- **单个文件大小默认1G, 文件名长度为20位，左边补零**，剩余为起始偏移量，
   - 00000000000000000000代表了第一个文件，起始偏移量为0，文件大小为1G=1073741824；
   - 当第一个文件写满了，第二个文件为00000000001073741824，起始偏移量为1073741824，
- 消息主要是**顺序写入日志文件**，当文件满了，写入下一个文件；

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487879663-36a4a522-a3d1-4170-b507-dcb2ee3846d7.png#averageHue=%230f0b07&clientId=uc96e6597-9974-4&from=paste&height=78&id=u5ee08fcd&originHeight=78&originWidth=488&originalType=binary&ratio=1&rotation=0&showTitle=false&size=5220&status=done&style=none&taskId=u5198511f-6454-4c22-b97d-539b1a100d7&title=&width=488)
### ConsumeQueue 逻辑消费队列
> 消息消费队列，引入的目的主要是提高消息消费的性能，由于RocketMQ是基于主题topic的订阅模式，消息消费是针对主题进行的，如果要遍历commitlog文件中根据topic检索消息是非常低效的。
> - Consumer即可根据ConsumeQueue来查找待消费的消息。
> - ConsumeQueue（逻辑消费队列）作为**消费消息的索引**，保存了指定Topic下的**队列消息在CommitLog中的起始物理偏移量offset**，**消息大小size**和**消息Tag的HashCode值**。
> - consumequeue文件可以看成是基于topic的commitlog索引文件
> 
ConsumeQueue具体存储路径为：**$HOME/store/consumequeue/{topic}/{queueId}/{fileName}**。
> 同样consumequeue文件采取定长设计，
> **每一个条目共20个字节，分别为8字节的commitlog物理偏移量、4字节的消息长度、8字节tag hashcode；**
> **单个文件由30W个条目组成**，可以像数组一样随机访问每一个条目，每个ConsumeQueue文件大小约**5.72M**；

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682487959402-d294469f-a3c2-4ff0-8520-b3c0406885c3.png#averageHue=%230b0806&clientId=uc96e6597-9974-4&from=paste&height=151&id=u72bf2468&originHeight=151&originWidth=508&originalType=binary&ratio=1&rotation=0&showTitle=false&size=9035&status=done&style=none&taskId=u309021fc-72af-40df-a7a4-ddeb68c2413&title=&width=508)
### IndexFile 索引文件
> IndexFile（索引文件）提供了**一种可以通过key或时间区间来查询消息的方法**。
> Index文件的存储位置是：**$HOME/store/index/{fileName}**，文件名fileName是以**创建时的时间戳**命名的。
> 固定的单个IndexFile文件大小约为**400M**，一个IndexFile可以保存 **2000W**个索引。
> IndexFile的底层存储设计为在文件系统中实现**HashMap**结构，故RocketMQ的索引文件其底层实现为**hash索引**。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682488547526-e458f1af-ce32-42a3-943d-f5bbee4d7dec.png#averageHue=%230a0705&clientId=uc96e6597-9974-4&from=paste&height=102&id=u586f84d3&originHeight=102&originWidth=475&originalType=binary&ratio=1&rotation=0&showTitle=false&size=6296&status=done&style=none&taskId=u14d30bb5-c699-4491-9a83-e7564cdcccf&title=&width=475)
## 页缓存与内存映射
### PageCache
> 对于数据的写入，OS会先写入至Cache内，随后通过异步的方式由pdflush内核线程将Cache内的数据刷盘至物理磁盘上。
> 对于数据的读取，如果一次读取文件时出现未命中PageCache的情况，OS从物理磁盘上访问读取文件的同时，会顺序对其他相邻块的数据文件进行预读取。

当生产者发布消息时，消息首先被追加到内存中的MappedByteBuffer（内存映射文件）中，然后异步地由FlushRealTimeService线程将数据持久化到磁盘上。
在消费者消费消息时，消息会被缓存在PageCache中以提高访问速度。
### Mmap
RocketMQ主要通过**MappedByteBuffer**对文件进行读写操作。
其中，利用了NIO中的**FileChannel**模型**将磁盘上的物理文件直接映射到用户态的内存地址中**（这种Mmap的方式减少了传统IO将磁盘文件数据在操作系统内核地址空间的缓冲区和用户应用程序地址空间的缓冲区之间来回进行拷贝的性能开销），
将对文件的操作转化为直接对内存地址进行操作，从而极大地提高了文件的读写效率（正因为需要使用内存映射机制，故RocketMQ的文件存储都使用定长结构来存储，方便一次将整个文件映射至内存）。
```java
private void init(final String fileName, final int fileSize) throws IOException {
    this.fileName = fileName;
    this.fileSize = fileSize;
    this.file = new File(fileName);
    this.fileFromOffset = Long.parseLong(this.file.getName());
    boolean ok = false;

    ensureDirOK(this.file.getParent());

    try {
        this.fileChannel = new RandomAccessFile(this.file, "rw").getChannel();
        this.mappedByteBuffer = this.fileChannel.map(MapMode.READ_WRITE, 0, fileSize);
        TOTAL_MAPPED_VIRTUAL_MEMORY.addAndGet(fileSize);
        TOTAL_MAPPED_FILES.incrementAndGet();
        ok = true;
    } catch (FileNotFoundException e) {
        log.error("Failed to create file " + this.fileName, e);
        throw e;
    } catch (IOException e) {
        log.error("Failed to map file " + this.fileName, e);
        throw e;
    } finally {
        if (!ok && this.fileChannel != null) {
            this.fileChannel.close();
        }
    }
}
```
## 消息刷盘
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682490843499-5b8420cf-3def-4d63-9e9d-3d0f59e9674b.png#averageHue=%23f2ecec&clientId=uc96e6597-9974-4&from=paste&height=612&id=ud7e35f86&originHeight=612&originWidth=664&originalType=binary&ratio=1&rotation=0&showTitle=false&size=27782&status=done&style=none&taskId=u6a721b9d-edf8-4085-8887-cda4f364c7b&title=&width=664)
### 同步刷盘
只有在消息**真正持久化至磁盘**后RocketMQ的Broker端才会真正返回给Producer端一个成功的ACK响应。
同步刷盘对MQ消息可靠性来说是一种不错的保障，但是性能上会有较大影响，一般适用于金融业务应用该模式较多。
### 异步刷盘
能够充分利用OS的**PageCache**的优势，**只要消息写入PageCache即可将成功的ACK返回给Producer端**。
消息刷盘采用**后台异步线程提交**的方式进行，降低了读写延迟，提高了MQ的性能和吞吐量。
