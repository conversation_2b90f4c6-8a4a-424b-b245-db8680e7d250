# RocketMQ通讯机制
## 通信基本流程
![](https://cdn.nlark.com/yuque/0/2023/jpeg/684952/1682575143670-840d0196-67d2-4572-a7d6-43c2f2390706.jpeg)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682575213049-6daad1d1-5f3a-4d35-b229-b570df1d131d.png#averageHue=%2325272c&clientId=uc11ca4d7-9ba8-4&from=paste&height=178&id=u3aa6d93d&originHeight=178&originWidth=1320&originalType=binary&ratio=1&rotation=0&showTitle=false&size=35923&status=done&style=none&taskId=u2a451329-a88d-4781-85f3-17e38becefb&title=&width=1320)
## 定时拉取任务配置

## 生产者如何选择队列发送？
### 
## 消费者负载均衡怎么做？

## Remoting通信类结构
> ## rocketmq-remoting 模块是 RocketMQ消息队列中负责网络通信的模块。为了实现客户端与服务器之间高效的数据请求与接收，RocketMQ消息队列自定义了通信协议并在Netty的基础之上扩展了通信模块。

### ![rocketmq_design_3.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682575432785-38d35cf3-9d08-4b62-90ad-ea77c978c404.png#averageHue=%23424242&clientId=uc11ca4d7-9ba8-4&from=paste&height=689&id=ud68f375d&originHeight=689&originWidth=1030&originalType=binary&ratio=1&rotation=0&showTitle=false&size=66199&status=done&style=none&taskId=uc6cf53aa-b76d-4998-8e5c-977a2f21f1e&title=&width=1030)
## 自定义通信协议和编解码
`org.apache.rocketmq.remoting.protocol.RemotingCommand`
### 消息协议
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682575802073-be2731aa-9763-433a-a855-8c85f00b7b53.png#averageHue=%231f2125&clientId=uc11ca4d7-9ba8-4&from=paste&height=616&id=ud4facd44&originHeight=616&originWidth=778&originalType=binary&ratio=1&rotation=0&showTitle=false&size=71088&status=done&style=none&taskId=u3328f160-0d88-43d3-8985-472d07e49a8&title=&width=778)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682575732903-f31202fb-c712-4d1e-90a2-1aafedc98cdf.png#averageHue=%2323252a&clientId=uc11ca4d7-9ba8-4&from=paste&height=289&id=ueb8a8bb8&originHeight=289&originWidth=1104&originalType=binary&ratio=1&rotation=0&showTitle=false&size=38168&status=done&style=none&taskId=u6e3f008a-f58c-4771-8dae-f49771f2aed&title=&width=1104)
### 传输数据内容
![rocketmq_design_4.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682575988063-c8d9ac96-39c7-45f3-aa04-e7b98a103c04.png#averageHue=%23f0f0ef&clientId=uc11ca4d7-9ba8-4&from=paste&height=103&id=ua8b7e842&originHeight=75&originWidth=620&originalType=binary&ratio=1&rotation=0&showTitle=false&size=20733&status=done&style=none&taskId=u4cb37911-02c0-4afc-bac6-85d07197903&title=&width=851)
(1) 消息长度：总长度，四个字节存储，占用一个int类型；
(2) 序列化类型&消息头长度：同样占用一个int类型，第一个字节表示序列化类型，后面三个字节表示消息头长度；
(3) 消息头数据：经过序列化后的消息头数据；
(4) 消息主体数据：消息主体的二进制字节数据内容；
#### 编码
```java
    public ByteBuffer encode() {
        // 1> header length size
        int length = 4;

        // 2> header data length
        byte[] headerData = this.headerEncode();
        length += headerData.length;

        // 3> body data length
        if (this.body != null) {
            length += body.length;
        }

        ByteBuffer result = ByteBuffer.allocate(4 + length);

        // length 消息长度
        result.putInt(length);

        // header length 序列化类型&消息头长度
        result.putInt(markProtocolType(headerData.length, serializeTypeCurrentRPC));

        // header data 消息头数据
        result.put(headerData);

        // body data; 消息主体数据
        if (this.body != null) {
            result.put(this.body);
        }

        result.flip();

        return result;
    }
```
#### 解码
```java
    public static RemotingCommand decode(final ByteBuf byteBuffer) throws RemotingCommandException {
        int length = byteBuffer.readableBytes();
        int oriHeaderLen = byteBuffer.readInt();
        int headerLength = getHeaderLength(oriHeaderLen);
        if (headerLength > length - 4) {
            throw new RemotingCommandException("decode error, bad header length: " + headerLength);
        }

        RemotingCommand cmd = headerDecode(byteBuffer, headerLength, getProtocolType(oriHeaderLen));

        int bodyLength = length - 4 - headerLength;
        byte[] bodyData = null;
        if (bodyLength > 0) {
            bodyData = new byte[bodyLength];
            byteBuffer.readBytes(bodyData);
        }
        cmd.body = bodyData;

        return cmd;
    }
```
## 多线程机制

 ![rocketmq_design_6.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682576941268-c482a72c-84f9-477c-83fc-65bc029e97b0.png#averageHue=%237f7f7f&clientId=u1b1c3fe7-f584-4&from=paste&height=497&id=uafbae09e&originHeight=497&originWidth=1008&originalType=binary&ratio=1&rotation=0&showTitle=false&size=64670&status=done&style=none&taskId=u524c7218-6a23-41d6-a989-fe793941e81&title=&width=1008)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682577046413-a25e59c8-0208-4493-b7f9-a18fbc6bc738.png#averageHue=%2324272c&clientId=u1b1c3fe7-f584-4&from=paste&height=351&id=ud97e01b2&originHeight=351&originWidth=1313&originalType=binary&ratio=1&rotation=0&showTitle=false&size=66338&status=done&style=none&taskId=u932ccc69-d9d0-4aef-b37e-7631a2598b8&title=&width=1313)
