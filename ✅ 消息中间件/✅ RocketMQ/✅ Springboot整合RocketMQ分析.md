#  消息发送和消费
## yaml配置
```yaml
rocketmq:
  name-server: rocketmq_name_server_494:9876
  consumer:
    group: springboot_consumer_group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: springboot_producer_group
    # 发送消息超时时间，默认3000
    send-message-timeout: 3000
    # 发送消息失败重试次数，默认2
    retry-times-when-send-failed: 2
    # 异步消息重试此处，默认2
    retry-times-when-send-async-failed: 2
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    max-message-size: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compress-message-body-threshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retry-next-server: true
```
## 消费
```java
@Slf4j
@Component
@RocketMQMessageListener(topic = "sync-message-topic", consumerGroup = "springboot_producer_group")
public class SyncMessageConsumer implements RocketMQListener<NormalMessageDTO> {

    @Override
    public void onMessage(NormalMessageDTO message) {
        log.info("receive sync message: {}", message);
    }
}
```
## 发送消息
### 同步消息
> curl -X POST [http://localhost:5001/sendSyncMessage/sync-message-topic](http://localhost:5001/sendSyncMessage/sync-message-topic)

```java
@Autowired
private RocketMQTemplate rocketMQTemplate;

@PostMapping("sendSyncMessage/{topic}")
public void sendSyncMessage(@PathVariable("topic") String topic) {
    NormalMessageDTO normalMessageDTO = new NormalMessageDTO();
    Message<NormalMessageDTO> message = MessageBuilder.withPayload(normalMessageDTO)
            .build();
    rocketMQTemplate.send(topic, message);
    rocketMQTemplate.syncSend(topic, message);
}
```
### 异步消息
```java
@PostMapping("sendAsyncMessage/{topic}")
public void sendAsyncMessage(@PathVariable("topic") String topic) {
    NormalMessageDTO normalMessageDTO = new NormalMessageDTO();
    Message<NormalMessageDTO> message = MessageBuilder.withPayload(normalMessageDTO)
            .build();
    rocketMQTemplate.asyncSend(topic, message, new SendCallback() {
        @Override
        public void onSuccess(SendResult sendResult) {
            log.info("send success: {}", sendResult);
        }

        @Override
        public void onException(Throwable throwable) {
            log.info("send exception: ", throwable);
        }
    });
}
```
### 单向消息
> curl -X POST [http://localhost:5001/sendOneWayMessage/oneway-message-topic](http://localhost:5001/sendOneWayMessage/oneway-message-topic)

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681475714053-77082256-e167-439f-aeca-8d92fe292310.png#averageHue=%23373c46&clientId=u49167f60-86ba-4&from=paste&height=132&id=u95b161cb&originHeight=264&originWidth=866&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59427&status=done&style=none&taskId=u54249db8-6546-4cd9-a2c6-e89451ee090&title=&width=433)
### 延时消息
> curl -X POST [http://localhost:5001/sendDelayMessage/delay-message-topic/2](http://localhost:5001/sendDelayMessage/delay-message-topic/2)

```java
    @PostMapping("sendDelayMessage/{topic}/{delayLevel}")
    public void sendDelayMessage(@PathVariable("topic") String topic,
                                 @PathVariable("delayLevel") Integer delayLevel) {
        NormalMessageDTO normalMessageDTO = new NormalMessageDTO();
        Message<NormalMessageDTO> message = MessageBuilder.withPayload(normalMessageDTO)
                .build();
        SendResult sendResult = rocketMQTemplate.syncSend(topic, message, 3000, delayLevel);
        System.out.println(sendResult);
    }
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681482581497-c806fa91-051d-4f99-b1ea-e1bc98fbfc36.png#averageHue=%23222327&clientId=u49167f60-86ba-4&from=paste&height=652&id=u989a031d&originHeight=1304&originWidth=1966&originalType=binary&ratio=2&rotation=0&showTitle=false&size=274047&status=done&style=none&taskId=u53aedb16-4a6d-48e4-b1f9-5ff740974d3&title=&width=983)
### 顺序消息
>  curl -X POST [http://localhost:5001/sendOrderlyMessage/orderly-message-topic](http://localhost:5001/sendOrderlyMessage/orderly-message-topic)

```java
    private static final AtomicInteger index = new AtomicInteger();

    @PostMapping("sendOrderlyMessage/{topic}")
    public void sendOrderlyMessage(@PathVariable("topic") String topic) {
        NormalMessageDTO normalMessageDTO = new NormalMessageDTO();
        normalMessageDTO.setIndex(index.getAndIncrement());
        Message<NormalMessageDTO> message = MessageBuilder
                .withPayload(normalMessageDTO)
                .build();
        SendResult sendResult = rocketMQTemplate.syncSendOrderly(topic, message,
                "hashKey");
    }
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681482772028-b958b0ca-2426-41a9-ae06-2b43e1698050.png#averageHue=%23373a40&clientId=u49167f60-86ba-4&from=paste&height=325&id=u13866840&originHeight=650&originWidth=1312&originalType=binary&ratio=2&rotation=0&showTitle=false&size=173832&status=done&style=none&taskId=u9254532c-d098-4f88-8ce5-fb8751aaaf8&title=&width=656)
### 事务消息
# 问题

