> [https://rocketmq.apache.org/zh/docs/4.x/](https://rocketmq.apache.org/zh/docs/4.x/)

# RocketMQ部署架构
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680704626016-a7cb4a82-c209-4386-b5ec-56ffb814e7ce.png#averageHue=%23666666&clientId=ud82bce47-6540-4&from=paste&id=u894349c8&originHeight=541&originWidth=1342&originalType=binary&ratio=2&rotation=0&showTitle=false&size=60504&status=done&style=none&taskId=u77bf4279-6da9-4071-ae6a-c09a8f3af19&title=)
RocketMQ架构上主要分为四部分，如上图所示:

-  Producer：消息发布的角色，支持分布式集群方式部署。Producer通过MQ的负载均衡模块选择相应的Broker集群队列进行消息投递，投递的过程支持快速失败并且低延迟。 
-  Consumer：消息消费的角色，支持分布式集群方式部署。支持以push推，pull拉两种模式对消息进行消费。同时也支持集群方式和广播方式的消费，它提供实时消息订阅机制，可以满足大多数用户的需求。 
-  NameServer：NameServer是一个非常简单的Topic路由注册中心，其角色类似Dubbo中的zookeeper，支持Broker的动态注册与发现。主要包括两个功能：Broker管理，NameServer接受Broker集群的注册信息并且保存下来作为路由信息的基本数据。然后提供心跳检测机制，检查Broker是否还存活；路由信息管理，每个NameServer将保存关于Broker集群的整个路由信息和用于客户端查询的队列信息。然后Producer和Consumer通过NameServer就可以知道整个Broker集群的路由信息，从而进行消息的投递和消费。NameServer通常也是集群的方式部署，各实例间相互不进行信息通讯。Broker是向每一台NameServer注册自己的路由信息，所以每一个NameServer实例上面都保存一份完整的路由信息。当某个NameServer因某种原因下线了，Broker仍然可以向其它NameServer同步其路由信息，Producer和Consumer仍然可以动态感知Broker的路由的信息。 
-  BrokerServer：Broker主要负责消息的存储、投递和查询以及服务高可用保证，为了实现这些功能，Broker包含了以下几个重要子模块。 
   1. Remoting Module：整个Broker的实体，负责处理来自Client端的请求。
   2. Client Manager：负责管理客户端(Producer/Consumer)和维护Consumer的Topic订阅信息。
   3. Store Service：提供方便简单的API接口处理消息存储到物理硬盘和查询功能。
   4. HA Service：高可用服务，提供Master Broker 和 Slave Broker之间的数据同步功能。
   5. Index Service：根据特定的Message key对投递到Broker的消息进行索引服务，以提供消息的快速查询。
# 组件
## NameServer 
> NameServer是一个简单的 Topic 路由注册中心，支持 Topic、Broker 的动态注册与发现。

- Broker管理：管理Broker的注册信息，心跳检测Broker是否存活。
- 路由信息管理：每个NameServer将保存关于 Broker 集群的整个路由信息和用于客户端查询的队列信息。Producer和Consumer通过NameServer就可以知道整个Broker集群的路由信息，从而进行消息的投递和消费。

## Broker
> Broker主要负责消息的存储、投递和查询以及服务高可用保证。

- Master 与 Slave 的对应关系通过指定相同的BrokerName。
- 不同的BrokerId 来定义，BrokerId为0表示Master，非0表示Slave。
## Topic 主题
> 消息存放的地方

### Tag
> 使用 Tag 可以实现对 Topic 中的消息进行过滤

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680705744030-7bf9cc1c-b384-4bb6-802b-7362bc104f95.png#averageHue=%232b2b2b&clientId=ud82bce47-6540-4&from=paste&height=236&id=u350e14d7&originHeight=472&originWidth=1574&originalType=binary&ratio=2&rotation=0&showTitle=false&size=29812&status=done&style=none&taskId=udf67b584-3553-4c30-8f8b-eb89e8ce774&title=&width=787)
### 队列
> 一个topic分了很多队列，并且可能分布在不同的 Broker 上。 16个

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680705993190-d4c4aab4-7148-4551-a561-62c75dbf7172.png#averageHue=%236f6f6f&clientId=ud82bce47-6540-4&from=paste&height=576&id=udc1311af&originHeight=1152&originWidth=1390&originalType=binary&ratio=2&rotation=0&showTitle=false&size=90872&status=done&style=none&taskId=u5cf8eb26-6b92-4028-8cd9-b669c9d6370&title=&width=695)
## 消息

- **topic**，表示要发送的消息的主题。
- **body** 表示消息的存储内容
- **properties** 表示消息属性
- **transactionId** 会在事务消息中使用。
### 消息属性
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680705590856-a2f871f8-5bf7-4948-b586-8a89d14755d8.png#averageHue=%23f5f5f5&clientId=ud82bce47-6540-4&from=paste&height=647&id=u2aba3047&originHeight=1294&originWidth=1018&originalType=binary&ratio=2&rotation=0&showTitle=false&size=66000&status=done&style=none&taskId=u86b9b370-c3b8-4866-8589-9717e892dbf&title=&width=509)![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680705602626-a7327eec-08c1-4435-8fcc-c10f540f639c.png#averageHue=%23f5f5f5&clientId=ud82bce47-6540-4&from=paste&height=638&id=u7c647947&originHeight=1276&originWidth=388&originalType=binary&ratio=2&rotation=0&showTitle=false&size=112803&status=done&style=none&taskId=u31467cec-ee23-4264-9bd8-65a1a333642&title=&width=194)
#### Keys
> 消息的表示Id，一般的唯一的

## Producer 生产者

## Consumer 消费者
### 消费者Consumer和消费者组Consumer Group

- 集群消费模式：当使用集群消费模式时，RocketMQ 认为任意一条消息只需要被消费组内的任意一个消费者处理即可。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680706272071-3a13cdf5-66a1-423f-beed-6119432f8270.png#averageHue=%233a3a3a&clientId=ud82bce47-6540-4&from=paste&height=377&id=u75a1cb00&originHeight=753&originWidth=1336&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57513&status=done&style=none&taskId=u606c79d8-dfd1-4704-a13f-4e3960c8334&title=&width=668)

- 广播消费模式：当使用广播消费模式时，RocketMQ 会将每条消息推送给消费组所有的消费者，保证消息至少被每个消费者消费一次。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680706282488-5481ec59-8c84-4d72-b9f2-90b96fe0a14f.png#averageHue=%233f3f3f&clientId=ud82bce47-6540-4&from=paste&height=413&id=ud04f2d43&originHeight=826&originWidth=1398&originalType=binary&ratio=2&rotation=0&showTitle=false&size=66040&status=done&style=none&taskId=u6fdc6c13-9161-46fa-8f88-c88a5119a55&title=&width=699)
### 负载均衡

#### 消息分配策略
> - 平均分配策略（默认）
>    - 一个Topic的队列数量小于消费者数量，有些消费者会得不到消息，提高不了消费效率。
>    - ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680706505959-b54d7486-6011-4b8d-bce0-048768a026aa.png#averageHue=%23f6f6f6&clientId=ud82bce47-6540-4&from=paste&height=376&id=u2f2a75fc&originHeight=752&originWidth=1674&originalType=binary&ratio=2&rotation=0&showTitle=false&size=287929&status=done&style=none&taskId=u81a86324-ee6e-4602-b6db-7bc22ff35b3&title=&width=837)
> - 机房优先分配策略
> - 一致性hash分配策略

```java
 consumer.setAllocateMessageQueueStrategy(new AllocateMessageQueueAveragely());
```
### 消费位点
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680706550766-999f04be-3932-485b-8bca-462ce73699b2.png#averageHue=%23bbbbbb&clientId=ud82bce47-6540-4&from=paste&height=471&id=ue3ce9f8f&originHeight=942&originWidth=1271&originalType=binary&ratio=2&rotation=0&showTitle=false&size=67584&status=done&style=none&taskId=u7be2823a-6800-4224-8cbc-3d370d9b21d&title=&width=635.5)

### 推、拉和长轮询
#### 推Push
> - Push是**服务端主动推送**消息给客户端，优点是及时性较好，但如果客户端没有做好流控，一旦服务端推送大量消息到客户端时，就会导致客户端消息堆积甚至崩溃。

#### 拉Pull
> - Pull是**客户端需要主动到服务端取数据**，优点是客户端可以依据自己的消费能力进行消费，但拉取的频率也需要用户自己控制，拉取频繁容易造成服务端和客户端的压力，拉取间隔长又容易造成消费不及时。





