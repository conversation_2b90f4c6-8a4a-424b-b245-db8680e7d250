# 消息发送
> [https://rocketmq.apache.org/zh/docs/4.x/producer/02message1](https://rocketmq.apache.org/zh/docs/4.x/producer/02message1)
> [https://rocketmq.apache.org/zh/docs/4.x/bestPractice/01bestpractice](https://rocketmq.apache.org/zh/docs/4.x/bestPractice/01bestpractice)

## 普通消息
```java
public enum CommunicationMode {
    SYNC,
    ASYNC,
    ONEWAY,
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680751174450-03880cfc-e20f-4bcf-ab2d-3f5f3960e21d.png#averageHue=%232c323c&clientId=u343a09ff-ce6e-4&from=paste&height=171&id=u9684e741&originHeight=171&originWidth=446&originalType=binary&ratio=1&rotation=0&showTitle=false&size=21123&status=done&style=none&taskId=u0134b090-2409-41c3-a93d-8a9f0ee9319&title=&width=446)

### 同步发送
> 消息发送方发出一条消息后，会在收到服务端同步响应之后才发下一条消息的通讯方式, 是最可靠的. 实时返回SendResult

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680760821582-bf88b50d-2ecd-40bd-8248-e5ed2c83a19c.png#averageHue=%232e3135&clientId=u343a09ff-ce6e-4&from=paste&height=487&id=xk8KV&originHeight=487&originWidth=928&originalType=binary&ratio=1&rotation=0&showTitle=false&size=102192&status=done&style=none&taskId=u06b5501e-37be-418f-936d-9da0e15caa2&title=&width=928)
```java
private static void syncMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {

    DefaultMQProducer producer = new DefaultMQProducer("coding-group");
    producer.setNamesrvAddr("172.16.251.53:9876");
    producer.start();
    Message message = new Message();
    message.setTopic("sync-message-topic");
    message.setBody(IdUtil.fastSimpleUUID().getBytes(Charset.defaultCharset()));

    // 同步返回SendResult
    SendResult sendResult = producer.send(message);
    log.info("sendResult:{}", sendResult);

    // 发送批量消息
    ArrayList<Message> list = Lists.newArrayList(message);
    SendResult sendResult1 = producer.send(list);

    // 发送指定超时时间ms CommunicationMode.SYNC
    SendResult sendResult2 = producer.send(message, 1000);
}
```
### 异步发送
> 异步发送需要实现异步发送回调接口（SendCallback）,返回Void

```java
    private static void asyncMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {

        /*
         * 默认线程池 @see org.apache.rocketmq.client.impl.producer.DefaultMQProducerImpl#DefaultMQProducerImpl(org.apache.rocketmq.client.producer.DefaultMQProducer, org.apache.rocketmq.remoting.RPCHook)
         */

        DefaultMQProducer producer = new DefaultMQProducer("coding-group");
        producer.setNamesrvAddr("172.16.251.53:9876");
        producer.setAsyncSenderExecutor(null);
        producer.start();
        Message message = new Message();
        message.setTopic("async-message-topic");
        message.setBody(IdUtil.fastSimpleUUID().getBytes(Charset.defaultCharset()));
        // 异步返回Void
        producer.send(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("send message success");
            }

            @Override
            public void onException(Throwable e) {
                log.error("send message exception: ", e);
            }
        });

    }
```
### 单向发送
> 发送方只负责发送消息，不等待服务端返回响应且没有回调函数触发，即只发送请求不等待应答。 适合对可靠性要求并不高的场景.

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680760782586-4e236ef9-1d30-4148-be36-8cef311cd7ed.png#averageHue=%2334373c&clientId=u343a09ff-ce6e-4&from=paste&height=79&id=ZuOFQ&originHeight=79&originWidth=959&originalType=binary&ratio=1&rotation=0&showTitle=false&size=16057&status=done&style=none&taskId=u101515dd-d84f-482b-bf0d-64f20a032a6&title=&width=959)
```java
private static void onewayMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {

        DefaultMQProducer producer = new DefaultMQProducer("coding-group");
        producer.setNamesrvAddr("172.16.251.53:9876");
        producer.start();
        Message message = new Message();
        message.setTopic("oneway-message-topic");
        message.setBody(IdUtil.fastSimpleUUID().getBytes(Charset.defaultCharset()));

        // 返回Void
        producer.sendOneway(message);
    }
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680760014168-3d66d553-fc5c-4bec-b759-4172ba5422c5.png#averageHue=%23010101&clientId=u343a09ff-ce6e-4&from=paste&id=ubb725edb&originHeight=641&originWidth=900&originalType=url&ratio=1&rotation=0&showTitle=false&size=46454&status=done&style=none&taskId=u8c957b29-8375-47e4-aa81-fde0c5624fc&title=)
### 源码分析
> 重试次数

```java
int timesTotal = communicationMode == CommunicationMode.SYNC ? 1 + this.defaultMQProducer.getRetryTimesWhenSendFailed() : 1;
```
> 异步线程池

```java
    public DefaultMQProducerImpl(final DefaultMQProducer defaultMQProducer, RPCHook rpcHook) {
        this.defaultMQProducer = defaultMQProducer;
        this.rpcHook = rpcHook;

        this.asyncSenderThreadPoolQueue = new LinkedBlockingQueue<Runnable>(50000);
        // 默认线程池
        this.defaultAsyncSenderExecutor = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors(),
            1000 * 60,
            TimeUnit.MILLISECONDS,
            this.asyncSenderThreadPoolQueue,
            new ThreadFactory() {
                private AtomicInteger threadIndex = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "AsyncSenderExecutor_" + this.threadIndex.incrementAndGet());
                }
            });
        if (defaultMQProducer.getBackPressureForAsyncSendNum() > 10) {
            semaphoreAsyncSendNum = new Semaphore(Math.max(defaultMQProducer.getBackPressureForAsyncSendNum(),10), true);
        } else {
            semaphoreAsyncSendNum = new Semaphore(10, true);
            log.info("semaphoreAsyncSendNum can not be smaller than 10.");
        }

        if (defaultMQProducer.getBackPressureForAsyncSendNum() > 1024 * 1024) {
            semaphoreAsyncSendSize = new Semaphore(Math.max(defaultMQProducer.getBackPressureForAsyncSendNum(),1024 * 1024), true);
        } else {
            semaphoreAsyncSendSize = new Semaphore(1024 * 1024, true);
            log.info("semaphoreAsyncSendSize can not be smaller than 1M.");
        }
    }
```
## 顺序消息
> FIFO 支持分区顺序消息, 同一个ShardingKey的消息会被分配到同一个队列中，并按照顺序被消费。
> - 生产顺序性: 单一生产者 + 串行发送
> - 消费顺序性

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680759981166-32046112-0588-4a4c-b479-dcc02995ddcf.png#averageHue=%23818181&clientId=u343a09ff-ce6e-4&from=paste&id=uf2c9cace&originHeight=610&originWidth=2546&originalType=url&ratio=1&rotation=0&showTitle=false&size=121795&status=done&style=none&taskId=u7ea3a078-6f13-4019-bff5-3310038fdcd&title=)

```java
public interface MessageQueueSelector {
    MessageQueue select(final List<MessageQueue> mqs, final Message msg, final Object arg);
}
```
```java
private static void orderMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException, UnsupportedEncodingException {

        DefaultMQProducer producer = new DefaultMQProducer("coding-group");
        producer.setNamesrvAddr("172.16.251.53:9876");
        producer.start();
        Message message = new Message();
        message.setTopic("order-message-topic");
        message.setKeys(IdUtil.fastSimpleUUID());
        message.setBody(IdUtil.fastSimpleUUID().getBytes(Charset.defaultCharset()));

        String[] tags = new String[]{"TagA", "TagB", "TagC", "TagD", "TagE"};
        for (int i = 0; i < 100; i++) {
            int orderId = i % 10;
            Message msg =
                    new Message("order-message-topic",
                            tags[i % tags.length],
                            "KEY" + i,
                            ("Hello RocketMQ " + i).getBytes(RemotingHelper.DEFAULT_CHARSET));
            SendResult sendResult = producer.send(msg, new MessageQueueSelector() {
                @Override
                public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
                    // mqs 队列列表
                    // msg 消息
                    // arg send方法传入的参数 orderId
                    Integer id = (Integer) arg;
                    int index = id % mqs.size();
                    return mqs.get(index);
                }
            }, orderId);
            log.info("sendResult: {}", sendResult);
        }
    }
```
### 源码sendOneway sendSelectImpl
```java
private SendResult sendSelectImpl(
        Message msg,
        MessageQueueSelector selector,
        Object arg,
        final CommunicationMode communicationMode,
        final SendCallback sendCallback, final long timeout
    ) throws MQClientException, RemotingException, MQBrokerException, InterruptedException {
        long beginStartTime = System.currentTimeMillis();
        this.makeSureStateOK();
        Validators.checkMessage(msg, this.defaultMQProducer);

        TopicPublishInfo topicPublishInfo = this.tryToFindTopicPublishInfo(msg.getTopic());
        if (topicPublishInfo != null && topicPublishInfo.ok()) {
            MessageQueue mq = null;
            try {
                List<MessageQueue> messageQueueList =
                    mQClientFactory.getMQAdminImpl().parsePublishMessageQueues(topicPublishInfo.getMessageQueueList());
                Message userMessage = MessageAccessor.cloneMessage(msg);
                String userTopic = NamespaceUtil.withoutNamespace(userMessage.getTopic(), mQClientFactory.getClientConfig().getNamespace());
                userMessage.setTopic(userTopic);
            	// 执行选择队列逻辑, 自定义
                mq = mQClientFactory.getClientConfig().queueWithNamespace(selector.select(messageQueueList, userMessage, arg));
            } catch (Throwable e) {
                throw new MQClientException("select message queue threw exception.", e);
            }

            long costTime = System.currentTimeMillis() - beginStartTime;
            if (timeout < costTime) {
                throw new RemotingTooMuchRequestException("sendSelectImpl call timeout");
            }
            if (mq != null) {
                return this.sendKernelImpl(msg, mq, communicationMode, sendCallback, null, timeout - costTime);
            } else {
                throw new MQClientException("select message queue return null.", null);
            }
        }

        validateNameServerSetting();
        throw new MQClientException("No route info for this topic, " + msg.getTopic(), null);
    }
```
## 延迟消息
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680760931844-d9367f93-edd2-431f-8194-85d83e0482c5.png#averageHue=%23f5f5f4&clientId=u343a09ff-ce6e-4&from=paste&height=551&id=uc2ace5a0&originHeight=551&originWidth=823&originalType=binary&ratio=1&rotation=0&showTitle=false&size=16284&status=done&style=none&taskId=u107005c9-268b-46ea-a621-95f09dbc98d&title=&width=823)
```java
    private static void delayMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {

        DefaultMQProducer producer = new DefaultMQProducer("coding-group");
        producer.setNamesrvAddr("172.16.251.53:9876");
        producer.start();

        int totalMessagesToSend = 100;
        for (int i = 0; i < totalMessagesToSend; i++) {
            Message message = new Message();
            message.setTopic("delay-message-topic");
            message.setKeys(IdUtil.fastSimpleUUID());
            message.setBody(("delay message : " + i).getBytes(Charset.defaultCharset()));
            // 消息延时等级
            message.setDelayTimeLevel(3);
            SendResult sendResult = producer.send(message);
            log.info("sendResult: {}", sendResult);
        }
    }
```

## 事务消息
> 基于 RocketMQ 的分布式事务消息功能，在普通消息基础上，支持二阶段的提交能力。将二阶段提交和本地事务绑定，实现全局提交结果的一致性。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680761650689-705659e7-8e0f-46ea-9336-e924f520c50a.png#averageHue=%23343434&clientId=u343a09ff-ce6e-4&from=paste&id=u1e468499&originHeight=544&originWidth=1996&originalType=url&ratio=1&rotation=0&showTitle=false&size=126382&status=done&style=none&taskId=u3281dbf9-e23e-4659-8701-3a0df498744&title=)

- executeLocalTransaction 半事务消息发送成功后，执行本地事务的方法
```java
public enum LocalTransactionState {
    // 提交事务，允许消费者消费该消息
    COMMIT_MESSAGE,
    // 回滚事务，消息将被丢弃不允许消费。
    ROLLBACK_MESSAGE,
    // 暂时无法判断状态，等待固定时间以后Broker端根据回查规则向生产者进行消息回查。
    UNKNOW,
}
```

- checkLocalTransaction是由于二次确认消息没有收到，Broker端回查事务状态的方法。
   - 回查规则：本地事务执行完成后，若Broker端收到的本地事务返回状态为LocalTransactionState.UNKNOW，或生产者应用退出导致本地事务未提交任何状态。则Broker端会向消息生产者发起事务回查，第一次回查后仍未获取到事务状态，则之后每隔一段时间会再次回查。
```java
public interface TransactionListener {
    /**
     * When send transactional prepare(half) message succeed, this method will be invoked to execute local transaction.
     *
     * @param msg Half(prepare) message
     * @param arg Custom business parameter
     * @return Transaction state
     */
    LocalTransactionState executeLocalTransaction(final Message msg, final Object arg);

    /**
     * When no response to prepare(half) message. broker will send check message to check the transaction status, and this
     * method will be invoked to get local transaction status.
     *
     * @param msg Check message
     * @return Transaction state
     */
    LocalTransactionState checkLocalTransaction(final MessageExt msg);
}
```
# 消息消费
## Push消费
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680768985924-598b79c1-0311-4fc3-86a3-530f1d08d905.png#averageHue=%2369876b&clientId=u343a09ff-ce6e-4&from=paste&height=84&id=u4478ebfa&originHeight=84&originWidth=624&originalType=binary&ratio=1&rotation=0&showTitle=false&size=17416&status=done&style=none&taskId=u3475b598-b403-4819-91b1-a8a69e43920&title=&width=624)
### 并发消费
```java
    public static void pushOneway() throws MQClientException {

        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer("coding-group");
        consumer.setNamesrvAddr("172.16.251.53:9876");
        consumer.subscribe("oneway-message-topic", "*");
        consumer.setMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    log.info("oneway msg: {}", msg);
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        consumer.start();
    }
```
```java
/**
 * A MessageListenerConcurrently object is used to receive asynchronously delivered messages concurrently
 */
public interface MessageListenerConcurrently extends MessageListener {
    /**
     * It is not recommend to throw exception,rather than returning ConsumeConcurrentlyStatus.RECONSUME_LATER if
     * consumption failure
     *
     * @param msgs msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
     * @return The consume status
     */
    ConsumeConcurrentlyStatus consumeMessage(final List<MessageExt> msgs,
        final ConsumeConcurrentlyContext context);
}
```
```java
public enum ConsumeConcurrentlyStatus {
    /**
     * Success consumption
     */
    CONSUME_SUCCESS,
    /**
     * Failure consumption,later try to consume
     */
    RECONSUME_LATER;
}
```
### 顺序消费
```java
    public static void pushOrder() throws MQClientException {

        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer("coding-group");
        consumer.setNamesrvAddr("172.16.251.53:9876");
        // 订阅主题topic, 消息过滤的条件subExpression. *代表所有tag , TagA|TagB
        consumer.subscribe("order-message-topic", "*");
        consumer.setMessageListener(new MessageListenerOrderly() {
            @Override
            public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
                for (MessageExt msg : msgs) {
                    log.info("order msg: {}", msg);
                }
                return ConsumeOrderlyStatus.SUCCESS;
            }
        });
        consumer.start();
    }
```
```java
/**
 * A MessageListenerOrderly object is used to receive messages orderly. One queue by one thread
 */
public interface MessageListenerOrderly extends MessageListener {
    /**
     * It is not recommend to throw exception,rather than returning ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT
     * if consumption failure
     *
     * @param msgs msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
     * @return The consume status
     */
    ConsumeOrderlyStatus consumeMessage(final List<MessageExt> msgs,
        final ConsumeOrderlyContext context);
}
```
```java
public enum ConsumeOrderlyStatus {
    /**
     * Success consumption
     */
    SUCCESS,
    /**
     * Rollback consumption(only for binlog consumption)
     */
    @Deprecated
    ROLLBACK,
    /**
     * Commit offset(only for binlog consumption)
     */
    @Deprecated
    COMMIT,
    /**
     * Suspend current queue a moment
     */
    SUSPEND_CURRENT_QUEUE_A_MOMENT;
}
```
### 消息过滤
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680769817377-ef6a5c7b-a666-49fd-a83a-a5c6d4bc85ad.png#averageHue=%23f3f2f1&clientId=u343a09ff-ce6e-4&from=paste&height=279&id=u5bddd333&originHeight=279&originWidth=934&originalType=binary&ratio=1&rotation=0&showTitle=false&size=36621&status=done&style=none&taskId=u1affb27c-4af1-4400-bfbf-5b6140c476b&title=&width=934)
```java
public class ExpressionType {

    /**
     * <ul>
     * Keywords:
     * <li>{@code AND, OR, NOT, BETWEEN, IN, TRUE, FALSE, IS, NULL}</li>
     * </ul>
     * <p/>
     * <ul>
     * Data type:
     * <li>Boolean, like: TRUE, FALSE</li>
     * <li>String, like: 'abc'</li>
     * <li>Decimal, like: 123</li>
     * <li>Float number, like: 3.1415</li>
     * </ul>
     * <p/>
     * <ul>
     * Grammar:
     * <li>{@code AND, OR}</li>
     * <li>{@code >, >=, <, <=, =}</li>
     * <li>{@code BETWEEN A AND B}, equals to {@code >=A AND <=B}</li>
     * <li>{@code NOT BETWEEN A AND B}, equals to {@code >B OR <A}</li>
     * <li>{@code IN ('a', 'b')}, equals to {@code ='a' OR ='b'}, this operation only support String type.</li>
     * <li>{@code IS NULL}, {@code IS NOT NULL}, check parameter whether is null, or not.</li>
     * <li>{@code =TRUE}, {@code =FALSE}, check parameter whether is true, or false.</li>
     * </ul>
     * <p/>
     * <p>
     * Example:
     * (a > 10 AND a < 100) OR (b IS NOT NULL AND b=TRUE)
     * </p>
     */
    public static final String SQL92 = "SQL92";

    /**
     * Only support or operation such as
     * "tag1 || tag2 || tag3", <br>
     * If null or * expression,meaning subscribe all.
     */
    public static final String TAG = "TAG";

    public static boolean isTagType(String type) {
        if (type == null || "".equals(type) || TAG.equals(type)) {
            return true;
        }
        return false;
    }
}

```
```java
/**
 * Message selector: select message at server.
 * <p>
 * Now, support:
 * <li>Tag: {@link org.apache.rocketmq.common.filter.ExpressionType#TAG}
 * </li>
 * <li>SQL92: {@link org.apache.rocketmq.common.filter.ExpressionType#SQL92}
 * </li>
 * </p>
 */
public class MessageSelector {

    /**
     * @see org.apache.rocketmq.common.filter.ExpressionType
     */
    private String type;

    /**
     * expression content.
     */
    private String expression;

    private MessageSelector(String type, String expression) {
        this.type = type;
        this.expression = expression;
    }

    /**
     * Use SQL92 to select message.
     *
     * @param sql if null or empty, will be treated as select all message.
     */
    public static MessageSelector bySql(String sql) {
        return new MessageSelector(ExpressionType.SQL92, sql);
    }

    /**
     * Use tag to select message.
     *
     * @param tag if null or empty or "*", will be treated as select all message.
     */
    public static MessageSelector byTag(String tag) {
        return new MessageSelector(ExpressionType.TAG, tag);
    }

    public String getExpressionType() {
        return type;
    }

    public String getExpression() {
        return expression;
    }
}
```
#### tag过滤
```java
consumer.subscribe("TagFilterTest", "TagA||TagB");
consumer.subscribe("TagFilterTest", "TagA");
consumer.subscribe("TagFilterTest", "*");
```
#### SQL92过滤
> broker服务端开启 enablePropertyFilter=true

```java
Message msg = new Message("topic", "tagA", "Hello MQ".getBytes());
// 设置自定义属性A，属性值为1。
msg.putUserProperties("a", "1");

consumer.subscribe("SqlFilterTest",
    MessageSelector.bySql("(TAGS is not null and TAGS in ('TagA', 'TagB'))" +
        "and (a is not null and a between 0 and 3)"));
```

## 消息重试和死信队列
> 

### 消息重试
```java
        // 最大重试次数
        consumer.setMaxReconsumeTimes(10);
        // 重试间隔: 消费失败后重新投递给Consumer消费的间隔时间ms, 仅顺序消费有效.
        consumer.setSuspendCurrentQueueTimeMillis(5000);
```
| 消费类型 | 重试间隔 | 最大重试次数 | 重试机制 |
| --- | --- | --- | --- |
| 顺序消费 | 间隔时间可通过自定义设置，SuspendCurrentQueueTimeMillis | 最大重试次数可通过自定义参数MaxReconsumeTimes取值进行配置。该参数取值无最大限制。若未设置参数值，默认最大重试次数为Integer.MAX | 顺序消费消费失败后会先在**客户端本地重试直到最大重试次数**，这样可以避免消费失败的消息被跳过，消费下一条消息而打乱顺序消费的顺序 |
| 并发消费 | 间隔时间根据重试次数阶梯变化，取值范围：1秒～2小时。不支持自定义配置 | 最大重试次数可通过自定义参数MaxReconsumeTimes取值进行配置。默认值为16次，该参数取值无最大限制，建议使用默认值 | 并发消费消费失败后会将**消费失败的消息重新投递回服务端**，再等待服务端重新投递回来，在这期间会正常消费队列后面的消息。 |

> 并发消费失败后并不是投递回原Topic，而是投递到一个特殊Topic，其命名为%RETRY%ConsumerGroupName，集群模式下并发消费每一个ConsumerGroup会对应一个特殊Topic，并会订阅该Topic。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680770712291-40fa3c31-013e-4b53-9deb-bcfdd0012514.png#averageHue=%23fdfcfc&clientId=u343a09ff-ce6e-4&from=paste&height=324&id=uf727b535&originHeight=324&originWidth=546&originalType=binary&ratio=1&rotation=0&showTitle=false&size=12911&status=done&style=none&taskId=u63196495-94e7-4470-9111-70498504e27&title=&width=546)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680770669223-6ba40707-e863-4ae4-a49b-fdc12de5f5d1.png#averageHue=%23f5f4f4&clientId=u343a09ff-ce6e-4&from=paste&height=498&id=u0adf8c70&originHeight=498&originWidth=793&originalType=binary&ratio=1&rotation=0&showTitle=false&size=14089&status=done&style=none&taskId=ud9051338-f957-431f-ae24-fabbce133a1&title=&width=793)
### 死信队列
> 消息重试，达到最大重试次数后，若消费依然失败，则表明消费者在正常情况下无法正确地消费该消息。
> 这类消息称为死信消息（Dead-Letter Message），存储死信消息的特殊队列称为死信队列（Dead-Letter Queue）
> 死信Topic名称为%DLQ%ConsumerGroupName

## Pull消费
> 客户端主动向broker拉去消息

### DefaultMQPullConsumer
> 建议废除了.

```java
/**
 * @deprecated Default pulling consumer. This class will be removed in 2022, and a better implementation {@link
 * DefaultLitePullConsumer} is recommend to use in the scenario of actively pulling messages.
 */
@Deprecated
public class DefaultMQPullConsumer extends ClientConfig implements MQPullConsumer {

}
```
### DefaultLitePullConsumer
```java
@Slf4j
public class LitePullConsumer {

    public static volatile boolean running = true;

    public static void main(String[] args) throws Exception {
        DefaultLitePullConsumer litePullConsumer = new DefaultLitePullConsumer("coding-group");
        litePullConsumer.setNamesrvAddr("172.16.251.53:9876");
        litePullConsumer.subscribe("oneway-message-topic", "*");
        litePullConsumer.setPullBatchSize(100);
        litePullConsumer.start();
        try {
            while (running) {
                List<MessageExt> messageExts = litePullConsumer.poll(5000);
                log.info("messageExts: {}", messageExts);
            }
        } finally {
            litePullConsumer.shutdown();
        }
    }
}
```

