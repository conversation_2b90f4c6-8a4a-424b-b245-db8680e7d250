## 1. 消费到数据, mq管理平台上确实unacked
> 消费了, 出现unacked, 程序又,没报错, 大概率是没有确认消息, 这样, 下次重启还会再次消费, 
> channel.basicAck(deliveryTag, true);   
> 这个的位置要注意, 建议在finally里

## 2. 消费手动ack
### 开启手动ack配置
```yaml
 spring
   rabbitmq:
      publisher-returns: false
      listener:
        direct:
          acknowledge-mode: manual
          prefetch: 10
          consumers-per-queue: 5
          retry:
            enabled: true
            max-attempts: 1
        simple:
          acknowledge-mode: manual
          prefetch: 5
          concurrency: 5
          retry:
            enabled: true
            max-attempts: 1
        type: direct
      username: admin
      password: cqt@1234
      addresses: ***********:5672,***********:5672
      template:
        #是否接收return消息的配置，true为接受confirm消息，默认为false
        mandatory: true
      virtual-host: meituan
      publisher-confirm-type: simple
```
### 消费端手动ack模板
```java
@Component
@Slf4j
public class CustomerRecycleAxybNumConsumer {

    @RabbitListener(queues = CustomerRabbitMqConfig.BIND_RECYCLE_AXYB_DELAY_QUEUE)
    @RabbitHandler
    public void onLazyMessage(Message msg, Channel channel) {
        long deliveryTag = msg.getMessageProperties().getDeliveryTag();
        String data = new String(msg.getBody());
        AckActionEnum action = AckActionEnum.ACCEPT;
        try {
            // 业务员逻辑处理
            ...
        } catch (Exception e) {
           
            action = AckActionEnum.RETRY;
            // 根据异常决定是否重试还是拒绝

        } finally {
            // ack处理
            RabbitmqAck.ackDeal(channel, deliveryTag, action);
        }
    }
}
```
```java
@Slf4j
public class RabbitmqAck {

    public static void ackDeal(Channel channel, long deliveryTag, AckActionEnum action) {
        try {
            switch (action) {
                case ACCEPT:
                    channel.basicAck(deliveryTag, true);
                    break;
                case RETRY:
                    channel.basicNack(deliveryTag, false, true);
                    break;
                case REJECT:
                    channel.basicNack(deliveryTag, false, false);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("ack error: {}", e.getMessage());
        }
    }
}
```
## 3. 生产端, 发送消息确认

