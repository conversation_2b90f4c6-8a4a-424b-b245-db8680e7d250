# Kafka如何保证消息可靠性
## 生产者ack机制 At Least Once
```yaml
spring:
  kafka:
    producer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: all
```
## 消费者手动提交offset At Least Once
```yaml
spring:
  consumer:
  bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
  enable-auto-commit: false
```
### 代码实现手动提交

## 生产者发送事务消息+幂等 Exactly-once
[Kafka消息类型](https://www.yuque.com/newq/java-study/fdtyml6vo9249c3i?view=doc_embed)
## 消费者 Exactly-once
> 隔离级别READ_COMMITTED， 手动提交offset。
> 确保消费者只消费生产者已经提交的事务消息。

```yaml
spring:
  consumer:
    # 读取事务性写入的消息的隔离级别。
    isolation-level: read_committed
```
### IsolationLevel
```java
public enum IsolationLevel {

    /**
    * Read everything including aborted transactions.
    * 读取所有内容，包括已终止的事务。
    */
    READ_UNCOMMITTED((byte) 0),

    /**
    * Read records from committed transactions, in addition to records not part of
    * transactions.
    * 从已提交的事务中读取记录，以及不属于事务的记录。
    */
    READ_COMMITTED((byte) 1);

    private final byte id;

    IsolationLevel(byte id) {
        this.id = id;
    }

    public byte id() {
        return this.id;
    }

}
```
