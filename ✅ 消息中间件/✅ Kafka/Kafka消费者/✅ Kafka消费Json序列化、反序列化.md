# Kafka消费Json序列化、反序列化
## 生产者
> ObjectMapper 

### yml配置
```yaml
    producer:
      bootstrap-servers: 172.16.251.53:9192,172.16.251.53:9292,172.16.251.53:9392
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: all
      batch-size: 16384
      retries: 3
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
```
### demo
```java
    @PostMapping("testJsonMessage")
    public void testMessageStore() {
        // 主题，key，消息体
        for (int i = 0; i < 1; i++) {
            // JSON序列化
            kafkaTemplate.send("testJsonMessage",
                    IdUtil.fastSimpleUUID(),
                    new Order());
        }
    }
```
## 消费者
### yml配置
```yaml
    consumer:
      bootstrap-servers: 172.16.251.53:9192,172.16.251.53:9292,172.16.251.53:9392
      enable-auto-commit: false
      group-id: consumer-test
      auto-commit-interval: 1S
      # 一次最多从kafka拉取数据条数
      max-poll-records: 100
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        partition.assignment.strategy: org.apache.kafka.clients.consumer.RoundRobinAssignor
        spring.json.trusted.packages: "*"
```
没加这个配置报错：
The class 'com.kk0.kafka.normal.model.Order' is not in the trusted packages: [java.util, java.lang]. If you believe this class is safe to deserialize, please provide its name. If the serialization is only done by a trusted source, you can also enable trust all (*). 
这个错误信息说明 JVM 无法反序列化 'com.kk0.kafka.normal.model.Order' 类的对象，因为该类未包含在受信任包列表中。要解决这个问题，可以将 'com.kk0.kafka.normal.model.Order' 添加到受信任包列表中，或者如果您确信序列化数据来源是可信的，则可以启用 trust all (*) 选项。具体操作取决于您使用的反序列化方式和应用程序的配置方式。
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682690397058-23ab2fa7-960a-4124-957b-bb60c92528d8.png#averageHue=%23fefdfb&clientId=ubce58bfe-5c97-4&from=paste&height=94&id=u38c5f3da&originHeight=188&originWidth=1486&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57201&status=done&style=none&taskId=u24a30f9c-66f3-45db-b60a-2d6545b5217&title=&width=743)
### demo
```java
    @KafkaListener(topics = "testJsonMessage",
            groupId = "testJsonMessage",
            concurrency = "5")
    public void consumer(Order order, Acknowledgment ack) {
        try {
            System.out.println(order);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            ack.acknowledge();
        }
    }
```
