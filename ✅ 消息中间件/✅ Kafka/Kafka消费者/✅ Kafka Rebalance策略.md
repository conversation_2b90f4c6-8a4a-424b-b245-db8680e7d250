# Kafka Rebalance策略
## Kafka消费者组概述
> Kafka集群部署，有多个节点。一个Topic可以有多个Partition。
> 消费者可以集群部署，消费组id groupId一样就是一个组的。
> 每个Partition只能分配给一个消费者。
> 若有3个节点，Topic有3个Partition；则有共有9个Partition。消费者组有3个消费者，应该每个消费者分配3个Partition。
> 当消费者新增或减少时，Partition需要重新分配给消费者。

## Kafka Rebalance策略定义
Kafka Rebalance是一个自适应的过程，它根据消费者加入、退出、挂起或失败等事件动态调整消费者组内每个消费者所分配到的Partition。
Rebalance目的在于实现以下两个重要目标：**负载平衡和最小化消费停顿。**

## Rebalance过程
> 由消费者组leader分配Partition
> org.apache.kafka.clients.consumer.internals.AbstractCoordinator.JoinGroupResponseHandler

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682434979654-aa83e6ed-fc6c-47c0-887e-46fa912a4ee0.png#averageHue=%23fefbfa&clientId=ucc286926-6b92-4&from=paste&height=690&id=u985525c7&originHeight=1380&originWidth=1572&originalType=binary&ratio=2&rotation=0&showTitle=false&size=441786&status=done&style=none&taskId=u50bec8f3-a2ae-461a-b942-d667c350152&title=&width=786)
## Kafka Rebalance策略算法
> 默认策略 Range
> org.apache.kafka.clients.consumer.internals.AbstractPartitionAssignor 

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682433392865-15727533-4dca-4bca-ab27-7bbf004dea18.png#averageHue=%233f4e45&clientId=ucc286926-6b92-4&from=paste&height=204&id=u146801b6&originHeight=408&originWidth=2552&originalType=binary&ratio=2&rotation=0&showTitle=false&size=208447&status=done&style=none&taskId=uc0cfd32e-2212-425a-87ac-d0de06718ca&title=&width=1276)
### RangeAssignor 
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682433621439-1158f8b4-b4ea-496f-b862-806e05132122.png#averageHue=%2338533a&clientId=ucc286926-6b92-4&from=paste&height=263&id=u5c217efd&originHeight=526&originWidth=664&originalType=binary&ratio=2&rotation=0&showTitle=false&size=34206&status=done&style=none&taskId=u8fc76378-57ac-49e4-b611-3ae0e10b60c&title=&width=332)
> 将所有Partition按照Hash值进行排序，然后将不同Partition范围均匀地分配给各个Consumer。

```java
public class RangeAssignor extends AbstractPartitionAssignor {
    public static final String RANGE_ASSIGNOR_NAME = "range";

    @Override
    public String name() {
        return RANGE_ASSIGNOR_NAME;
    }

    private Map<String, List<MemberInfo>> consumersPerTopic(Map<String, Subscription> consumerMetadata) {
        Map<String, List<MemberInfo>> topicToConsumers = new HashMap<>();
        for (Map.Entry<String, Subscription> subscriptionEntry : consumerMetadata.entrySet()) {
            String consumerId = subscriptionEntry.getKey();
            MemberInfo memberInfo = new MemberInfo(consumerId, subscriptionEntry.getValue().groupInstanceId());
            for (String topic : subscriptionEntry.getValue().topics()) {
                put(topicToConsumers, topic, memberInfo);
            }
        }
        return topicToConsumers;
    }

    @Override
    public Map<String, List<TopicPartition>> assign(Map<String, Integer> partitionsPerTopic,
                                                    Map<String, Subscription> subscriptions) {
        Map<String, List<MemberInfo>> consumersPerTopic = consumersPerTopic(subscriptions);

        Map<String, List<TopicPartition>> assignment = new HashMap<>();
        for (String memberId : subscriptions.keySet())
            assignment.put(memberId, new ArrayList<>());

        for (Map.Entry<String, List<MemberInfo>> topicEntry : consumersPerTopic.entrySet()) {
            String topic = topicEntry.getKey();
            List<MemberInfo> consumersForTopic = topicEntry.getValue();

            Integer numPartitionsForTopic = partitionsPerTopic.get(topic);
            if (numPartitionsForTopic == null)
                continue;

            Collections.sort(consumersForTopic);

            int numPartitionsPerConsumer = numPartitionsForTopic / consumersForTopic.size();
            int consumersWithExtraPartition = numPartitionsForTopic % consumersForTopic.size();

            List<TopicPartition> partitions = AbstractPartitionAssignor.partitions(topic, numPartitionsForTopic);
            for (int i = 0, n = consumersForTopic.size(); i < n; i++) {
                int start = numPartitionsPerConsumer * i + Math.min(i, consumersWithExtraPartition);
                int length = numPartitionsPerConsumer + (i + 1 > consumersWithExtraPartition ? 0 : 1);
                assignment.get(consumersForTopic.get(i).memberId).addAll(partitions.subList(start, start + length));
            }
        }
        return assignment;
    }
}
```
### RoundRobinAssignor
> 将所有Partition轮流分配给各个Consumer，确保尽可能平均地向每一个Consumer进行分配。

```java
public class RoundRobinAssignor extends AbstractPartitionAssignor {
    public static final String ROUNDROBIN_ASSIGNOR_NAME = "roundrobin";

    @Override
    public Map<String, List<TopicPartition>> assign(Map<String, Integer> partitionsPerTopic,
                                                    Map<String, Subscription> subscriptions) {
        Map<String, List<TopicPartition>> assignment = new HashMap<>();
        List<MemberInfo> memberInfoList = new ArrayList<>();
        for (Map.Entry<String, Subscription> memberSubscription : subscriptions.entrySet()) {
            assignment.put(memberSubscription.getKey(), new ArrayList<>());
            memberInfoList.add(new MemberInfo(memberSubscription.getKey(),
                                              memberSubscription.getValue().groupInstanceId()));
        }

        CircularIterator<MemberInfo> assigner = new CircularIterator<>(Utils.sorted(memberInfoList));

        for (TopicPartition partition : allPartitionsSorted(partitionsPerTopic, subscriptions)) {
            final String topic = partition.topic();
            while (!subscriptions.get(assigner.peek().memberId).topics().contains(topic))
                assigner.next();
            assignment.get(assigner.next().memberId).add(partition);
        }
        return assignment;
    }

    private List<TopicPartition> allPartitionsSorted(Map<String, Integer> partitionsPerTopic,
                                                     Map<String, Subscription> subscriptions) {
        SortedSet<String> topics = new TreeSet<>();
        for (Subscription subscription : subscriptions.values())
            topics.addAll(subscription.topics());

        List<TopicPartition> allPartitions = new ArrayList<>();
        for (String topic : topics) {
            Integer numPartitionsForTopic = partitionsPerTopic.get(topic);
            if (numPartitionsForTopic != null)
                allPartitions.addAll(AbstractPartitionAssignor.partitions(topic, numPartitionsForTopic));
        }
        return allPartitions;
    }

    @Override
    public String name() {
        return ROUNDROBIN_ASSIGNOR_NAME;
    }

}
```
### StickyAssignor
> 在分配Partition时，考虑每个Consumer消费速率的差异，较慢的Consumer会获取更少的Partition，但能保持更稳定的负载。该算法需要考虑许多因素，例如Consumer间的Session ID、Client ID、Heartbeat等信息。

1. 分区的分配要尽可能均匀。
2. 分区的分配尽可能与上次分配的保持相同。
### CooperativeStickyAssignor 
 
 
## Rebalance触发机制

- ✅ 一个新的消费者加入了消费者组
- ❌ 一个Consumer重新加入却期望得到更多的Partitions
- ❌ 某个Consumer因某种原因退出了消费者组
- ❌ 某个Partition因故障失效了
- ✅ 动态添加分区Partition了
- ❌ 在设置“[max.poll.interval.ms](http://max.poll.interval.ms/)”属性时，该时间的到来可能会导致Rebalance的发生
## 修改rebalance策略
```yaml
      properties:
        partition.assignment.strategy: org.apache.kafka.clients.consumer.RoundRobinAssignor
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682434458292-3108e87e-c6e2-4940-bd96-35bd5a48e9e2.png#averageHue=%23242629&clientId=ucc286926-6b92-4&from=paste&height=138&id=uc5ae5a31&originHeight=276&originWidth=1676&originalType=binary&ratio=2&rotation=0&showTitle=false&size=43719&status=done&style=none&taskId=u1d72b3cd-0256-4817-8c1f-566c6d3f84d&title=&width=838)

