# Kafka消费消息过滤器
> 在消费端，在消息抵达监听容器KafkaListener之前，判断消息是否要进行消费。
> 为true消息被丢弃，为false消息传递给监听容器。

## RecordFilterStrategy 接口
```java
public interface RecordFilterStrategy<K, V> {

    /**
    * Return true if the record should be discarded.
    * @param consumerRecord the record.
    * @return true to discard.
    */
    boolean filter(ConsumerRecord<K, V> consumerRecord);

    /**
    * Filter an entire batch of records; to filter all records, return an empty list, not
    * null.
    * @param records the records.
    * @return the filtered records.
    * @since 2.8
    */
    default List<ConsumerRecord<K, V>> filterBatch(List<ConsumerRecord<K, V>> records) {
        Iterator<ConsumerRecord<K, V>> iterator = records.iterator();
        while (iterator.hasNext()) {
            if (filter(iterator.next())) {
                iterator.remove();
            }
        }
        return records;
    }

}
```
## 代码实现
### 监听容器工厂ConcurrentKafkaListenerContainerFactory 
```java
    @Bean("kafkaListenerContainerFactory2")
    public ConcurrentKafkaListenerContainerFactory<?, ?> kafkaListenerContainerFactory2(
            ConcurrentKafkaListenerContainerFactoryConfigurer configurer,
            ObjectProvider<ConsumerFactory<Object, Object>> kafkaConsumerFactory,
            KafkaProperties properties) {
        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        configurer.configure(factory, kafkaConsumerFactory
                .getIfAvailable(() -> new DefaultKafkaConsumerFactory<>(properties.buildConsumerProperties())));
        factory.setRecordFilterStrategy(new RecordFilterStrategy<Object, Object>() {

            @Override
            public boolean filter(ConsumerRecord consumerRecord) {
                Object value = consumerRecord.value();
                log.info("value filter: {}", value);
                // true被丢弃，false进行消费
                return false;
            }
        });
        return factory;
    }
```
### 监听器配置containerFactory 
```java
    @KafkaListener(topics = "sendToDestTopic",
            groupId = "sendTo",
            concurrency = "3",
            containerFactory = "kafkaListenerContainerFactory2")
    public void sendToDestTopic(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            log.error("sendToDestTopic receive: {}", record.value());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            ack.acknowledge();
        }
    }
```
