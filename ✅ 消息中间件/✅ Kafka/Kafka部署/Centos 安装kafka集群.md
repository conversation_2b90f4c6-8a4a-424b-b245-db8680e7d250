# 设备
```
************
************
************
```
# 准备
```
 mkdir -p /home/<USER>
 mkdir -p /home/<USER>

mkdir -p /home/<USER>/kafka_2.13-2.8.2/zookeeper/data
mkdir -p /home/<USER>/kafka_2.13-2.8.2/zookeeper/logs
mkdir -p /home/<USER>/kafka_2.13-2.8.2/kafka-logs

下载kafka
cd /home/<USER>
wget https://archive.apache.org/dist/kafka/2.8.2/kafka_2.13-2.8.2.tgz
tar  -zxvf kafka_2.13-2.8.2.tgz
```
### host
```properties
************ zk01
************ zk02
************ zk03
```
# 安装zk集群
```
vim /home/<USER>/kafka_2.13-2.8.2/config/zookeeper.properties
```
## 251.1
```properties
# the directory where the snapshot is stored.
dataDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/data
dataLogDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/logs
# the port at which the clients will connect
clientPort=2181
# disable the per-ip limit on the number of connections since this is a non-production config
maxClientCnxns=0
tickTime=2000
initLimit=10
syncLimit=5
# Disable the adminserver by default to avoid port conflicts.
# Set the port to something non-conflicting if choosing to enable this
admin.enableServer=false
# admin.serverPort=8080
server.1=zk01:2888:3888 
server.2=zk02:2888:3888 
server.3=zk03:2888:3888

```
```
cd /home/<USER>/kafka_2.13-2.8.2/zookeeper/data
echo 1 > myid
```
## 251.2
```properties
# the directory where the snapshot is stored.
dataDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/data
dataLogDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/logs
# the port at which the clients will connect
clientPort=2181
# disable the per-ip limit on the number of connections since this is a non-production config
maxClientCnxns=0
tickTime=2000
initLimit=10
syncLimit=5
# Disable the adminserver by default to avoid port conflicts.
# Set the port to something non-conflicting if choosing to enable this
admin.enableServer=false
# admin.serverPort=8080
server.1=zk01:2888:3888 
server.2=zk02:2888:3888 
server.3=zk03:2888:3888
```
```properties
cd /home/<USER>/kafka_2.13-2.8.2/zookeeper/data
echo 2 > myid
```
## 251.5
```properties
# the directory where the snapshot is stored.
dataDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/data
dataLogDir=/home/<USER>/kafka_2.13-2.8.2/zookeeper/logs
# the port at which the clients will connect
clientPort=2181
# disable the per-ip limit on the number of connections since this is a non-production config
maxClientCnxns=0
tickTime=2000
initLimit=10
syncLimit=5
# Disable the adminserver by default to avoid port conflicts.
# Set the port to something non-conflicting if choosing to enable this
admin.enableServer=false
# admin.serverPort=8080
server.1=zk01:2888:3888
server.2=zk02:2888:3888
server.3=zk03:2888:3888
```
```properties
cd /home/<USER>/kafka_2.13-2.8.2/zookeeper/data
echo 3 > myid
```
## 启动zk
```properties
nohup /home/<USER>/kafka_2.13-2.8.2/bin/zookeeper-server-start.sh /home/<USER>/kafka_2.13-2.8.2/config/zookeeper.properties >/dev/null 2>/dev/null &
```

# 安装kafka集群
## 251.1
```properties
# 每台服务器的broker.id都不能相同
broker.id=0
# 是否可以删除topic
delete.topic.enable=true
# topic 在当前broker上的分片个数，与broker保持一致
num.partitions=3
# 每个主机地址不一样：
listeners=PLAINTEXT://************:9092
advertised.listeners=PLAINTEXT://************:9092
# 具体一些参数
log.dirs=/home/<USER>/kafka_2.13-2.8.2/kafka-logs
# 设置zookeeper集群地址与端口如下：
zookeeper.connect=zk01:2181,zk02:2181,zk03:2181
```
## 251.2
```properties
# 每台服务器的broker.id都不能相同
broker.id=1
# 是否可以删除topic
delete.topic.enable=true
# topic 在当前broker上的分片个数，与broker保持一致
num.partitions=3
# 每个主机地址不一样：
listeners=PLAINTEXT://************:9092
advertised.listeners=PLAINTEXT://************:9092
# 具体一些参数
log.dirs=/home/<USER>/kafka_2.13-2.8.2/kafka-logs
# 设置zookeeper集群地址与端口如下：
zookeeper.connect=zk01:2181,zk02:2181,zk03:2181
```
## 251.5
```properties
# 每台服务器的broker.id都不能相同
broker.id=2
# 是否可以删除topic
delete.topic.enable=true
# topic 在当前broker上的分片个数，与broker保持一致
num.partitions=3
# 每个主机地址不一样：
listeners=PLAINTEXT://************:9092
advertised.listeners=PLAINTEXT://************:9092
# 具体一些参数
log.dirs=/home/<USER>/kafka_2.13-2.8.2/kafka-logs
# 设置zookeeper集群地址与端口如下：
zookeeper.connect=zk01:2181,zk02:2181,zk03:2181
```
## 启动kafka
```properties
nohup /home/<USER>/kafka_2.13-2.8.2/bin/kafka-server-start.sh /home/<USER>/kafka_2.13-2.8.2/config/server.properties  >/dev/null 2>/dev/null &
```
