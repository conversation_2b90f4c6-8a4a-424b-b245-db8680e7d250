# Kafka Docker-compose部署-单机
## Kafka版本号
kafka:2.13-2.8.1 是 Kafka 的一个版本号，由以下两个部分组成：

- 2.13：表示 Kafka 使用的 Scala 版本，其中 2.13 是指 Scala 2.13.x。Scala 是一门运行在 Java 虚拟机上的编程语言，Kafka 在很长一段时间内都是使用 Scala 来实现的。因此，对应用于 Kafka 中的 Scala 版本号需要注意。
- 2.8.1：表示 Kafka 主版本号和次版本号，其中 2 是主版本号，8 是次版本号，1 是修正版本号。每个 Kafka 版本的版本号通常包含一个主要版本号、一个次要版本号和一个修订号，用于标识版本的新特性、错误修复和性能优化等功能。

总之，kafka:2.13-2.8.1 表示 Kafka 使用了 Scala 2.13.x 版本，并且这个版本的主版本号为 2，次版本号为 8，修订版本号为 1。这个版本号可以帮助用户区分不同的 Kafka 版本，选择适合的版本并根据需要进行升级和维护。
## 创建目录
```groovy
mkdir -p /home/<USER>/kafka/kafka01/data /home/<USER>/kafka/kafka01/config /home/<USER>/kafka/kafka01/logs /home/<USER>/kafka/kafka01/kafka-logs
mkdir -p /home/<USER>/kafka/kafka02/data /home/<USER>/kafka/kafka02/config /home/<USER>/kafka/kafka02/logs /home/<USER>/kafka/kafka02/kafka-logs
mkdir -p /home/<USER>/kafka/kafka03/data /home/<USER>/kafka/kafka03/config /home/<USER>/kafka/kafka03/logs /home/<USER>/kafka/kafka03/kafka-logs

mkdir -p /home/<USER>/kafka/zk01/conf /home/<USER>/kafka/zk02/conf /home/<USER>/kafka/zk03/conf
mkdir -p /home/<USER>/kafka/zk01/data /home/<USER>/kafka/zk02/data /home/<USER>/kafka/zk03/data
mkdir -p /home/<USER>/kafka/zk01/datalog /home/<USER>/kafka/zk02/datalog /home/<USER>/kafka/zk03/datalog
```
## mac-m1-zk-kafka-docker-compose.yml
> [https://hub.docker.com/r/bitnami/kafka](https://hub.docker.com/r/bitnami/kafka)

```yaml
version: '2'
services:
  zk01:
    image: zookeeper:3.8.0-temurin
    hostname: zk01
    privileged: true
    container_name: zk01
    networks:
      kafka:
        aliases:
          - zk01
    ports:
      - 2181:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk01/data:/data
      - ./zk01/datalog:/datalog
      - ./zk01/conf:/conf

  zk02:
    image: zookeeper:3.8.0-temurin
    hostname: zk02
    privileged: true
    container_name: zk02
    networks:
      kafka:
        aliases:
          - zk02
    ports:
      - 2182:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk02/data:/data
      - ./zk02/datalog:/datalog
      - ./zk02/conf:/conf

  zk03:
    image: zookeeper:3.8.0-temurin
    hostname: zk03
    privileged: true
    container_name: zk03
    networks:
      kafka:
        aliases:
          - zk03
    ports:
      - 2183:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk03/data:/data
      - ./zk03/datalog:/datalog
      - ./zk03/conf:/conf

  kafka01:
    image: wurstmeister/kafka:2.13-2.8.1
    container_name: kafka01
    hostname: kafka01
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka01
    ports:
      - 9192:9192
    environment:
      KAFKA_BROKER_ID: 51
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://:9192
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9192
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka01/kafka-logs:/kafka
      - ./kafka01/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka02:
    image: wurstmeister/kafka:2.13-2.8.1
    container_name: kafka02
    hostname: kafka02
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka02
    ports:
      - 9292:9292
    environment:
      KAFKA_BROKER_ID: 52
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://:9292
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9292
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka02/kafka-logs:/kafka
      - ./kafka02/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka03:
    image: wurstmeister/kafka:2.13-2.8.1
    container_name: kafka03
    hostname: kafka03
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka03
    ports:
      - 9392:9392
    environment:
      KAFKA_BROKER_ID: 53
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://:9392
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9392
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka03/kafka-logs:/kafka
      - ./kafka03/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka-manager:
    image: scjtqs/kafka-manager:latest
    container_name: kafka-manager
    hostname: kafka-manager
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka-manager
    ports:
      - 29000:9000
    environment:
      ZK_HOSTS: zk01:2181,zk02:2181,zk03:2181
    depends_on:
      - zk01
      - zk02
      - zk03
      - kafka01
      - kafka02
      - kafka03

networks:
  kafka:
    name: kafka
    driver: bridge
```
## centos
```yaml
version: '2'
services:
  zk01:
    image: zookeeper:3.8.0-temurin
    restart: always
    hostname: zk01
    privileged: true
    container_name: zk01
    networks:
      kafka:
        aliases:
          - zk01
    ports:
      - 2181:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk01/data:/data
      - ./zk01/datalog:/datalog
      - ./zk01/conf:/conf

  zk02:
    image: zookeeper:3.8.0-temurin
    restart: always
    hostname: zk02
    privileged: true
    container_name: zk02
    networks:
      kafka:
        aliases:
          - zk02
    ports:
      - 2182:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk02/data:/data
      - ./zk02/datalog:/datalog
      - ./zk02/conf:/conf

  zk03:
    image: zookeeper:3.8.0-temurin
    restart: always
    hostname: zk03
    privileged: true
    container_name: zk03
    networks:
      kafka:
        aliases:
          - zk03
    ports:
      - 2183:2181
    environment:
      TZ: Asia/Shanghai
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zk01:2888:3888;2181 server.2=zk02:2888:3888;2181 server.3=zk03:2888:3888;2181
    volumes:
      - ./zk03/data:/data
      - ./zk03/datalog:/datalog
      - ./zk03/conf:/conf

  kafka01:
    image: wurstmeister/kafka:2.13-2.8.1
    restart: always
    container_name: kafka01
    hostname: kafka01
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka01
    ports:
      - 9192:9092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka01:9092
      KAFKA_LISTENERS: PLAINTEXT://kafka01:9092
      KAFKA_ADVERTISED_HOST_NAME: kafka01
      KAFKA_ADVERTISED_PORT: 9092
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka01/kafka-logs:/kafka
      - ./kafka01/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka02:
    image: wurstmeister/kafka:2.13-2.8.1
    restart: always
    container_name: kafka02
    hostname: kafka02
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka02
    ports:
      - 9292:9092
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka02:9092
      KAFKA_LISTENERS: PLAINTEXT://kafka02:9092
      KAFKA_ADVERTISED_HOST_NAME: kafka02
      KAFKA_ADVERTISED_PORT: 9092
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka02/kafka-logs:/kafka
      - ./kafka02/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka03:
    image: wurstmeister/kafka:2.13-2.8.1
    container_name: kafka03
    hostname: kafka03
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka03
    ports:
      - 9392:9092
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_ZOOKEEPER_CONNECT: zk01:2181/kafka,zk02:2181/kafka,zk03:2181/kafka
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka03:9092
      KAFKA_LISTENERS: PLAINTEXT://kafka03:9092
      KAFKA_ADVERTISED_HOST_NAME: kafka03
      KAFKA_ADVERTISED_PORT: 9092
      KAFKA_HEAP_OPTS: -Xmx512m -Xms512m
    volumes:
      - ./kafka03/kafka-logs:/kafka
      - ./kafka03/logs:/opt/kafka/logs
    depends_on:
      - zk01
      - zk02
      - zk03

  kafka-manager:
    image: scjtqs/kafka-manager:latest
    restart: always
    depends_on:
      - zk01
      - zk02
      - zk03
      - kafka01
      - kafka02
      - kafka03
    container_name: kafka-manager
    hostname: kafka-manager
    privileged: true
    networks:
      kafka:
        aliases:
          - kafka-manager
    ports:
      - 29000:9000
    environment:
      ZK_HOSTS: zk01:2181,zk02:2181,zk03:2181

networks:
  kafka:
    name: kafka
    driver: bridge
```
## 启动
```
docker-compose -f zk-kafka-docker-compose.yml up -d
```
## CMAKE
> [http://localhost:29000/clusters/kafka/brokers](http://localhost:29000/clusters/kafka/brokers)

## 问题
> kafka挂载配置文件server.properties 不行。

