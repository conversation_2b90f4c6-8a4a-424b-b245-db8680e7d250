# Kafka基本概念
## broker
### 主题 Topic
> 很多个分区

### 分区 Partition
> 一个分区只属于一个主题。
> 不同分区消息不同。
> 在硬盘上，是个日志文件。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682688244130-6fe68406-c8de-43e3-ba7e-a0bb9e52c238.png#averageHue=%23093240&clientId=u17e11e5f-f4aa-4&from=paste&height=113&id=u788ab509&originHeight=226&originWidth=1012&originalType=binary&ratio=2&rotation=0&showTitle=false&size=46441&status=done&style=none&taskId=u516b85a6-9529-48ab-a57e-1265ef31b58&title=&width=506)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682688272993-65e9d254-0bd3-4939-ad63-172382aac2f9.png#averageHue=%230c333e&clientId=u17e11e5f-f4aa-4&from=paste&height=240&id=ucbde6ba2&originHeight=480&originWidth=1294&originalType=binary&ratio=2&rotation=0&showTitle=false&size=92820&status=done&style=none&taskId=u825f2a62-f761-4107-8b49-a30f294c7ac&title=&width=647)
#### 偏移量 Offset
> offset是分区中的唯一标识。不能跨分区。
> kafka通过它保证消息在分区内的顺序性。
> 分区有序而不是主题有序。

#### 副本 Replica
> 副本为了容灾。同一分区的不同副本中保存的消息时一样的。
> 副本之间是一主多从的关系，主副本负责读写，从副本负责消息同步，副本位于不同的broker中，当主副本异常时，在从副本中提升一个为主副本。

### 分区原则

- 指定Partition。
- 未指定Partition但有key，将Key的Hash值与Topic的分区数进行取余得到Partition。
- 未指定Partition也没有指定Key，第一次调用时随机生成一个整数（后面每次调用自增），将这个值与Topic的分区数进行取余得到Partition。轮训算法。
## 生产者
> 消息生产者。

### 生产者运行流程

1. 一条消息发过来首先会被封装成一个 ProducerRecord 对象
2. 对该对象进行序列化处理（可以使用默认，也可以自定义序列化）
3. 对消息进行分区处理，分区的时候需要获取集群的元数据，决定这个消息会被发送到哪个主题的哪个分区
4. 分好区的消息不会直接发送到服务端，而是放入**生产者的缓存区**，多条消息会被封装成一个批次（Batch），默认一个批次的大小是 batch.size=16KB
5. Sender 线程启动以后会从缓存里面去获取可以发送的批次
6. Sender 线程把一个一个批次发送到服务端

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682694073074-c71c0d8d-33e5-4b4a-ab5e-493612701ff2.png#averageHue=%23e6e5e4&clientId=ud8e7b086-7c08-4&from=paste&height=392&id=ubf9c7e2c&originHeight=784&originWidth=850&originalType=binary&ratio=2&rotation=0&showTitle=false&size=107975&status=done&style=none&taskId=uc12ab7a2-a080-4f58-8a31-968eceab48d&title=&width=425)
### 配置项
#### buffer.memory 生产者能用到的内存总大小
> 用于控制Producer所能使用的总内存大小。
> buffer.memory 参数的值会直接影响到 Producer 总体上可以支撑多少个并发请求。

#### max.request.size 消息最大大小
> 单位 字节。发送消息小于这个值会报错

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682692156932-8a95bf97-d239-446c-8805-c7a4e1d72343.png#averageHue=%23222326&clientId=ud8e7b086-7c08-4&from=paste&height=399&id=u1b979737&originHeight=798&originWidth=1774&originalType=binary&ratio=2&rotation=0&showTitle=false&size=152832&status=done&style=none&taskId=u26d8be4e-92fb-48b1-8969-6450bf5da0c&title=&width=887)
#### batch.size 生产者缓存区大小
> 生产者缓存区大小，超过这个值就会批量发送给broker。

#### linger.ms 消息在缓冲区中的等待时间
> 消息在缓冲区中的等待时间（ms），500ms内消息未达到batch.size值，会进行一次batch发送。

配置了10000ms，发送到消费间隔了10秒。
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682693078145-0695d5a1-3fb6-4052-94d2-24f4c0a0b0ea.png#averageHue=%231f2023&clientId=ud8e7b086-7c08-4&from=paste&height=178&id=u2f07ac7b&originHeight=356&originWidth=1790&originalType=binary&ratio=2&rotation=0&showTitle=false&size=86429&status=done&style=none&taskId=uae5a3b6c-2953-4fc9-b582-98fa58b2c96&title=&width=895)
#### enable.idempotence 开启幂等消息
当 enable.idempotence 被设置为 true 时，Producer 会向 Kafka Broker 发送一个额外的请求来获取当前的 epoch 和 sequence 信息，并将这些信息添加到每个消息的 RecordHeader 中。一旦 Kafka Broker 收到该消息，它就会在内部维护一个幂等性状态，并根据 epoch 和 sequence 的值判断是否是重复消息。
需要注意的是，启用幂等性发送功能会增加系统的开销并降低性能，因此应该谨慎选择是否要开启该功能。
#### transaction.timeout.ms 事务超时时间
用于控制事务的超时时间。在使用 Kafka 事务功能时，Producer 会将多条消息发送到 Kafka Broker 上的同一个分区中，Msgs同属于一个事务；该参数指定生产者必须等待的时间，在此时间内必须尝试提交或回滚此事务。如果在超时时间内该事务还未完成，Producer 将取消该事务并抛出异常。
可以通过调整 transaction.timeout.ms 的值来适应不同大小、性能和负载情况下的不同事务处理需求。需要根据实际情景和需求进行考虑和设置。默认情况下，transaction.timeout.ms 的值为 60000 豪秒（即60秒）。
#### max.in.flight.requests.per.connection 每个连接上正在进行的未确认请求的最大数量
用于控制每个连接上正在进行的未确认请求的最大数量。在 Kafka 0.9.0 及以后的版本中，默认的 max.in.flight.requests.per.connection 值为 5。
一般情况下，通过增加可传输的未经确认处理请求的最大数量，可以提高吞吐量。但也要在一定范围，否则会适得其反。
#### retries 生产者重试次数
 
## 消费者
> 消息消费者，同一groupId，不同消费者消费不重复。

### 消费模式
#### push模式
> Consumer端无法控制消费速率

#### pull模式
> Consumer端控制消费速率。

### 配置项
#### spring.json.trusted.packages json序列化信任的包
> value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

#### max.poll.interval.ms 在轮询新消息时阻塞等待时间
> Kafka使用轮询方式（polling）获取消息。Pull模式，Consumer端控制消费速率。
> 这个配置表示轮询时尝试获取新消息的最长阻塞等待时间。
> 有新消息理解返回消息，并继续下一次轮询，没消息则返回一个空集合。

#### max.poll.records 每次轮询最多获取的消息数
> 值越大可以增加Consumer处理速度。

 
