# ReplyingKafkaTemplate 请求等待回复
## 说明
> 一个实现请求/回复语义的KafkaTemplate。
> 生产者发送消息给broker，等待消费者消费完成，消费者返回一个消息给生产者，同步等待。可设置等待超时时间。

## demo
### yml配置
```yaml
spring:
  kafka:
    producer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: all
      batch-size: 16384
      retries: 3
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      enable-auto-commit: true
      group-id: consumer-test
      auto-commit-interval: 1S
      # 一次最多从kafka拉取数据条数
      max-poll-records: 100
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      # 在侦听器容器中运行的线程数。
      concurrency: 5
      #listner负责ack，每调用一次，就立即commit
      # 手动提交, 代码控制监听, 自动监听注释掉就好
      #ack-mode: manual_immediate
```
### 配置类
```java
@Configuration
public class KafkaReplyingConfig {
    
    @Bean
    public ConcurrentMessageListenerContainer<?, ?> repliesContainer(ConcurrentKafkaListenerContainerFactory<?, ?> containerFactory) {
        ConcurrentMessageListenerContainer<?, ?> repliesContainer = containerFactory.createContainer("replies");
        repliesContainer.getContainerProperties().setGroupId("repliesGroup");
        repliesContainer.setAutoStartup(false);
        return repliesContainer;
    }

    @Bean
    public ReplyingKafkaTemplate<String, String, String> replyingTemplate(ProducerFactory<String, String> pf, ConcurrentMessageListenerContainer<String, String> repliesContainer) {
        return new ReplyingKafkaTemplate(pf, repliesContainer);
    }

    @Bean
    public KafkaTemplate<?, ?> kafkaTemplate(ProducerFactory<Object, Object> pf) {
        return new KafkaTemplate(pf);
    }
}
```
### 消息
```java
@RestController
@Slf4j
public class ReplyingKafkaTemplateDemo {

    @Autowired
    private ReplyingKafkaTemplate replyingKafkaTemplate;

    /**
     * curl -X POST 127.0.0.1:6012/replyingMessage
     */
    @PostMapping("replyingMessage")
    public String replyingMessage() throws ExecutionException, InterruptedException, TimeoutException {
        ProducerRecord<String, String> record = new ProducerRecord<>("replyingMessage",
                "foo");

        RequestReplyFuture<String, String, String> receive = replyingKafkaTemplate.sendAndReceive(record);
        ConsumerRecord<String, String> consumerRecord = receive.get(10, TimeUnit.SECONDS);

        return consumerRecord.value();

    }

    @KafkaListener(topics = "replyingMessage", id = "repliesGroup")
    @SendTo
    public String consumer(ConsumerRecord<String, String> record) {
        System.out.println(record);
        return "xxx";
    }
}

```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682154087689-857e1048-35da-400a-8fc2-5c5d45111b87.png#averageHue=%230c3848&clientId=ud64a70dc-10f3-4&from=paste&height=81&id=u9ae93951&originHeight=162&originWidth=1362&originalType=binary&ratio=2&rotation=0&showTitle=false&size=31803&status=done&style=none&taskId=u13dfbd6e-9341-495e-af97-e6b84e2e609&title=&width=681)
## 注意点

- 不能在事务环境下。
- 消费时@SendTo必须要。
## 方法
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682154193022-bf82603c-db0e-4975-badb-ac2b80c59ad3.png#averageHue=%23373b41&clientId=ud64a70dc-10f3-4&from=paste&height=254&id=u142962f2&originHeight=508&originWidth=1684&originalType=binary&ratio=2&rotation=0&showTitle=false&size=149935&status=done&style=none&taskId=ucc019da5-816a-41a5-9679-71bd59fb04e&title=&width=842)
## 使用场景

- 类似RPC
- 消息的发送者需要知道消息消费者的具体的消费情况。
- 一条消息中发送一批数据，需要知道消费者成功处理了哪些数据。
## 实现原理
