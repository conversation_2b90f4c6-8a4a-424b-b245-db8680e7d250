# Kafka消息类型
## 普通消息
```java
	@RestController
    public class NormalMessageProducer {

        @Autowired
        private KafkaTemplate kafkaTemplate;

        @PostMapping("sendNormalMessage")
        public void sendNormalMessage() {
            kafkaTemplate.send("NormalMessageProducer", IdUtil.fastSimpleUUID());
        }

        @KafkaListener(topics = "NormalMessageProducer", groupId = "NormalMessageConsumer")
        public void consumer(String message) {
            System.out.println(message);
        }
    }
```
## 事务消息
> 允许生产者将一组消息作为事务原子性地写入 Kafka 集群。**使用事务机制可以确保一组消息都成功或都失败**，**从而避免了在发送期间产生的故障或错误导致消息丢失或重复处理的问题**。事务机制需要配合正确的配置和 API 使用，明确标记组中的每一条消息，选择合适的回滚策略等等。

### yml配置
```yaml
spring:
  kafka:
    producer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: all
      batch-size: 16384
      retries: 3
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 启用对producer的事务消息支持
      transaction-id-prefix: transaction_kafka
      properties:
        enable.idempotence: true
        transaction.timeout.ms: 60000
    consumer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      enable-auto-commit: true
      group-id: consumer-test
      auto-commit-interval: 1S
      # 一次最多从kafka拉取数据条数
      max-poll-records: 100
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 读取事务性写入的消息的隔离级别。
      isolation-level: read_committed
    listener:
      # 在侦听器容器中运行的线程数。
      concurrency: 5
      #listner负责ack，每调用一次，就立即commit
      # 手动提交, 代码控制监听, 自动监听注释掉就好
      #ack-mode: manual_immediate
```
### demo
```java
@RestController
public class TransactionProducerDemo {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @PostMapping("sendMessage/{index}")
    @Transactional
    public void sendMessage(@PathVariable Integer index) {
        kafkaTemplate.send("TransactionMessageDemo", IdUtil.fastSimpleUUID());
        if (index == 1) {
            throw new RuntimeException();
        }
    }

    @PostMapping("sendTxMessage/{index}")
    public void sendTxMessage(@PathVariable Integer index) {
        kafkaTemplate.executeInTransaction(new KafkaOperations.OperationsCallback<String, String, Object>() {
            @Override
            public Object doInOperations(KafkaOperations<String, String> operations) {

                for (int i = 0; i < 10; i++) {
                    operations.send("TransactionMessageDemo", IdUtil.fastSimpleUUID());
                }
                if (index == 1) {
                    throw new RuntimeException();
                }
                return true;
            }
        });
    }
}
```
### 发送事务消息方式
> 抛出异常，就不会发送到kafka

- @Transactional
- kafkaTemplate.executeInTransaction()
### 注意点

- 发送事务消息，retries配置必须大于0

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682091781808-9731a39f-864a-4331-af24-52d9c1743bc7.png#averageHue=%232a2b2f&clientId=ua5525b91-cc52-4&from=paste&height=219&id=u4ba1e587&originHeight=438&originWidth=2278&originalType=binary&ratio=2&rotation=0&showTitle=false&size=151470&status=done&style=none&taskId=u3264f912-789a-4983-8ef8-3825534b22b&title=&width=1139)
### 使用场景

## 幂等性消息
> Kafka 生产者支持生产幂等消息，即具有相同 key 和 value 的消息只会在服务端被保存一次。这意味着，即使生产者在发送消息时发生了网络中断、服务器返回错误或其他故障，它仍然可以安全地重试发送相同的消息，而不会产生重复的消息。启用幂等性机制后，Kafka 生产者会追踪上一次通过生产者发送的每个消息记录，如果遇到相同的记录，则不会重复发送并忽略此请求。


> 幂等消息就是生产者保证最多成功发送一条消息到broker。
> 将Producer的enable.idempotence配置项设为true。
> Kafka的幂等性实现了**对于单个Producer会话、单个TopicPartition级别的不重不漏**，也就是最细粒度的保证。
> Producer的max.in.flight.requests.per.connection参数不能设为大于5的值。

## 顺序性消息
> Kafka 可以**保障一个 partition 内的消息顺序性**，partition 之间的消息顺序无法保证，增加 partition 的时候需要考虑消息顺序对业务的影响。

- 全局有序
   - 一个topic只能有一个partition。
- 局部有序
   - 发送消息时指定Partition Key，根据key hash，发送到对应的partition
   - 在不增加partition数量的情况下想提高消费速度，可以考虑再次hash唯一标识（例如订单orderId）到不同的线程上，多个消费者线程并发处理消息（依旧可以保证局部有序）。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682155432656-8d3134b4-dcf9-497e-850b-15dec6955da3.png#averageHue=%23373737&clientId=u0fd0a74e-8f34-4&from=paste&height=715&id=u42602c93&originHeight=1430&originWidth=1148&originalType=binary&ratio=2&rotation=0&showTitle=false&size=207158&status=done&style=none&taskId=u975f9733-a1ef-4658-b7b2-90caa0bd078&title=&width=574)
### 消息重试可能导致乱序。

- max.in.flight.requests.per.connection参数
   - 指定了生产者在收到[服务器](https://cloud.tencent.com/product/cvm?from=20065&from_column=20065)响应之前可以发送多少个消息。
   - 把它设为1就可以保证消息是按照发送的顺序写入服务器的。但会降低吞吐量。
## ❌ 批量消息
## ❌ 延时消息
## ❌ 死信消息
