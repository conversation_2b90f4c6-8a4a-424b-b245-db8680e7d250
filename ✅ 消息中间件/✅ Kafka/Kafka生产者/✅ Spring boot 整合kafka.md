# Spring boot 整合kafka
## 0. maven
```xml
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
```
## 1. 配置文件yml

```yaml
server:
  port: 6011

spring:
  kafka:
    producer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: all
      batch-size: 16384
      retries: 3
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 启用对producer的事务消息支持
    #      transaction-id-prefix: transaction_kafka
    #      properties:
    #        enable.idempotence: true
    #        transaction.timeout.ms: 60000
    consumer:
      bootstrap-servers: kafka01:9192,kafka02:9292,kafka03:9392
      enable-auto-commit: true
      group-id: consumer-test
      auto-commit-interval: 1S
      # 一次最多从kafka拉取数据条数
      max-poll-records: 100
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 读取事务性写入的消息的隔离级别。
    #      isolation-level: read_committed
    listener:
      # 在侦听器容器中运行的线程数。
      concurrency: 5
      #listner负责ack，每调用一次，就立即commit
      # 手动提交, 代码控制监听, 自动监听注释掉就好
      #ack-mode: manual_immediate
```
## 2. 生产者

```java
@RestController
public class ProducerDemo {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @PostMapping("sendMessage")
    public void sendMessage() {
        kafkaTemplate.send("TransactionMessageDemo", IdUtil.fastSimpleUUID());
    }
}
```
### KafkaTemplate
### ReplyingKafkaTemplate 
[✅ Springboot ReplyingKafkaTemplate 请求等待回复](https://www.yuque.com/newq/java-study/trcug6khkhq9512e?view=doc_embed)
## 3. 消费者

```java
@Slf4j
@Component
public class ConsumerDemo {

    @KafkaListener(topics = "TransactionMessageDemo", groupId = "ConsumerDemo")
    public void consumer(String message) {
        System.out.println(message);
    }

}
```
## 4. 一些自动配置

```java
@Configuration
public class KafkaConfig {

    /**
     * kafka监听工厂
     *
     * @param configurer
     * @return
     */
    @Bean("kafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> kafkaListenerContainerFactory(
            ConcurrentKafkaListenerContainerFactoryConfigurer configurer,
            ConsumerFactory consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        //开启批量消费功能
        factory.setBatchListener(true);
        //自动启动
        factory.setAutoStartup(true);
        configurer.configure(factory, consumerFactory);
        return factory;
    }


}

```

## 5. ack提交
### 自动提交
> enable-auto-commit: true
> 在消费完一批消息后自动将偏移量提交到 Kafka 服务端。

### 手动提交
> 代码提交ack。
> org.springframework.kafka.support.Acknowledgment 接口

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682156157809-c2900a19-8683-40c4-958c-4e0ffc16cb46.png#averageHue=%232f333b&clientId=ua10a6e5d-81e5-4&from=paste&height=221&id=u525cc0a4&originHeight=442&originWidth=1494&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59858&status=done&style=none&taskId=u96e477f9-580a-4dde-9fa8-30923324a1b&title=&width=747)
```java
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.listener.ack-mode=manual
```
```java
@KafkaListener(topics = "NormalMessageProducer", groupId = "NormalMessageConsumer")
public void consumer(String message, Acknowledgment ack) {
    try {
        System.out.println(message);
    } catch (Exception e) {
        throw new RuntimeException(e);
    } finally {
        ack.acknowledge();
    }
}
```
#### 
