# Kafka @SendTo注解使用
## @SendTo
```java
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SendTo {

    /**
    * The destination for a message created from the return value of a method.
    */
    String[] value() default {};

}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682523124587-11f46527-530e-45a4-8c3e-ff16841b7a84.png#averageHue=%233e3324&clientId=u704991cc-02e7-4&from=paste&height=477&id=u02875882&originHeight=954&originWidth=838&originalType=binary&ratio=2&rotation=0&showTitle=false&size=95882&status=done&style=none&taskId=u9ee212c3-5c4b-48c1-8db5-775bfe6e9c0&title=&width=419)
## demo
> 消息消费完成，将方法返回值转发到另一个Tpoic。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682523223714-7ebdf4af-41ac-4f4f-9c87-bed6eb09d890.png#averageHue=%23212225&clientId=u704991cc-02e7-4&from=paste&height=646&id=u159de9bc&originHeight=1292&originWidth=1602&originalType=binary&ratio=2&rotation=0&showTitle=false&size=186095&status=done&style=none&taskId=u720dfea0-e8aa-430d-a506-6abaf4cb64d&title=&width=801)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682523293713-d7b0c255-cb4b-4d0e-abab-73204d79e7ce.png#averageHue=%231e1f22&clientId=u704991cc-02e7-4&from=paste&height=337&id=ucd8f2161&originHeight=674&originWidth=2292&originalType=binary&ratio=2&rotation=0&showTitle=false&size=200310&status=done&style=none&taskId=u579321dc-870c-40ac-bc62-922e2c9fd6c&title=&width=1146)
