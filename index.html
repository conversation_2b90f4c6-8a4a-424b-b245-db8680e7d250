<!DOCTYPE html>
<html lang="">
<head>
  <meta charset="UTF-8">
  <title>Document</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="Description">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
  <link 
    rel="stylesheet"
    href="//cdn.jsdelivr.net/npm/docsify-darklight-theme@latest/dist/style.min.css"
    title="docsify-darklight-theme"
    type="text/css"
  />
</head>
<body>
  <div id="app">加载中...</div>
  <script>
    window.$docsify = {
      name: '我的文档',
      repo: '',
      nativeEmoji: true,
      loadSidebar: true,  // 侧边栏
      loadNavbar: true,   // 顶部导航栏
      // loadNavbar: '/_navbar.md'
      subMaxLevel: 3,      // 目录层级
      search: 'auto',     // 开启搜索
      auto2top: true,
      autoHeader: true,
      homepage: 'README.md',
      // logo: '/_media/icon.svg',
      maxLevel: 4,
      mergeNavbar: true,
      routerMode: 'history',
      busuanzi: true,
      search: {
        depth: 6,
      },
      // disqus: 'shortname',
      sidebarDisplayLevel: 2,
      count:{
        countable: true,
        position: 'top',
        margin: '10px',
        float: 'right',
        fontsize:'0.9em',
        color:'rgb(90,90,90)',
        language:'chinese',
        localization: {
          words: "",
          minute: ""
        },
        isExpected: true
      },
      share: {
          reddit: true,
          linkedin: true,
          facebook: true,
          twitter: true,
          whatsapp: true,
          telegram: true,
      },
      // toc: {
      //   scope: '.markdown-section',
      //   headings: 'h1, h2, h3, h4, h5, h6',
      //   title: 'Table of Contents',
      // },
      timeUpdater: {
        text: ">last update time: {docsify-updated}",
        formatUpdated: "{YYYY}-{MM}-{DD} {HH}:{mm}:{ss}",
        whereToPlace: "top",  // "top" or "bottom", default to "bottom"
      },
      hideCode: {
        scroll: false, // Enable scrolling
        height: 1200 // Max height
      },
      breadcrumb: {
        showHome: true,
        homeText: 'Home',
        separator: ' &rsaquo; ',
        casing: 'capitalize',
        linkColor: 'var(--theme-color, #42b983)',
        size: 'small'
      },
      darklightTheme: {
        siteFont : "PT Sans",
        defaultTheme : 'dark',
        codeFontFamily : 'Roboto Mono, Monaco, courier, monospace',
        bodyFontSize : '17px',
        dark: {
            accent: '#42b983',
            toogleBackground : '#ffffff',
            background: '#091a28',
            textColor: '#b4b4b4',
            codeTextColor : '#ffffff',
            codeBackgroundColor : '#0e2233',
            borderColor : '#0d2538',
            blockQuoteColor : '#858585',
            highlightColor : '#d22778',
            sidebarSublink : '#b4b4b4',
            codeTypeColor : '#ffffff',
            coverBackground : 'linear-gradient(to left bottom, hsl(118, 100%, 85%) 0%,hsl(181, 100%, 85%) 100%)',
            toogleImage : 'url(https://cdn.jsdelivr.net/npm/docsify-darklight-theme@latest/icons/sun.svg)'
        },
        light: {
            accent: '#42b983',
            toogleBackground : '#091a28',
            background: '#ffffff',
            textColor: '#34495e',
            codeTextColor : '#525252',
            codeBackgroundColor : '#f8f8f8',
            borderColor : 'rgba(0, 0, 0, 0.07)',
            blockQuoteColor : '#858585',
            highlightColor : '#d22778',
            sidebarSublink : '#b4b4b4',
            codeTypeColor : '#091a28',
            coverBackground : 'linear-gradient(to left bottom, hsl(118, 100%, 85%) 0%,hsl(181, 100%, 85%) 100%)',
            toogleImage : 'url(https://cdn.jsdelivr.net/npm/docsify-darklight-theme@latest/icons/moon.svg)'
        }
      },
      // 页面右侧toc
      toc: {
        tocMaxLevel: 2,
        target: "h2, h3, h4, h5, h6",
      },
      // 跳转后自动到顶部
      auto2top: true,
    };
  </script>
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  <!-- <script src="//cdn.jsdelivr.net/npm/docsify/lib/docsify.min.js"></script> -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/emoji.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code/dist/docsify-copy-code.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-bash.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-markdown.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-nginx.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-php.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-python.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-java.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-yml.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-yaml.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination/dist/docsify-pagination.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/external-script.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/zoom-image.min.js"></script>
  <script src="//unpkg.com/docsify-count/dist/countable.min.js"></script>
  <script src="//unpkg.com/docsify-share/build/index.min.js"></script>
  <script src="//unpkg.com/docsify-copy-code@2"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-sidebar-collapse/dist/docsify-sidebar-collapse.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/docsify-updated/src/time-updater.min.js"></script>
  <script src="https://unpkg.com/docsify-busuanzi@1.0.1/dist/docsify-busuanzi.min.js"></script>
  <!-- 一个可以隐藏代码的 docsify 插件。 -->
  <script src="https://cdn.jsdelivr.net/npm/docsify-hide-code/dist/docsify-hide-code.min.js"></script>
  <!-- 插入脚本标签，以便在加载 Docsify 脚本后加载插件脚本文件。 https://github.com/simochee/docsify-plugin-page-history-->
  <script src="//cdn.jsdelivr.net/npm/docsify@v4/lib/plugins/front-matter.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/docsify-plugin-page-history/lib/docsify-plugin-page-history.min.js"></script>
  <!-- Adds the breadcrumb JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/docsify-breadcrumb@latest/dist/index.min.js"></script>
  <!-- Adds the default breadcrumb styling -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsify-breadcrumb@latest/dist/breadcrumb.min.css">
  <!-- <script src="https://unpkg.com/docsify-toc@1.0.0/dist/toc.js"></script> -->
  <!-- 用于自动添加空格的 docsify 插件 -->
  <!-- <script src="//cdn.jsdelivr.net/npm/@sy-records/docsify-pangu/dist/pangu.min.js"></script> -->
  <script 
    src="//cdn.jsdelivr.net/npm/docsify-darklight-theme@latest/dist/index.min.js"
    type="text/javascript">
  </script>

  <!-- 页面右侧 TOC -->
  <script src="https://cdn.jsdelivr.net/npm/docsify-plugin-toc@1.1.0/dist/docsify-plugin-toc.min.js"></script>
</body>
</html>
