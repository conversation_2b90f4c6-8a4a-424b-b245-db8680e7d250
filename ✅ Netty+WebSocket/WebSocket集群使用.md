# Gateway转发ws

```yml
spring:
  cloud:
    gateway:
      default-filters:
        - PreservesHostHeader
      routes:
        - id: websocket_route
          uri: ws://localhost:8080/ws
          predicates:
            - Path=/ws/**
          filters:
            # 将升级请求头从 http 转换为 websocket
            - name: WebSocketUpgrade
            # 必须包含 RouteToRequestUrl 过滤器，否则会报错
            - name: RouteToRequestUrl****
```

# Nginx转发ws

```
#第一步：
upstream websocket-router {
    server 127.0.0.1:7000 max_fails=10 weight=1 fail_timeout=5s;
    keepalive 1000;
}
#第二步：
server {
    listen      80; 

    ssl_session_cache    shared:SSL:1m;
    ssl_session_timeout  5m;

    ssl_ciphers  HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers  on;

    location / {
        client_max_body_size 100M;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade; #支持wss
        proxy_set_header Connection "upgrade"; #支持wssi
        proxy_pass http://websocket-router; #代理路由
        root   html;
        index  index.html index.htm;
    }
}

```
