# Netty实现连接鉴权和获取客户端IP.md

## Netty实现Websocket的原理

> 在Http的基础上, 升级协议为Websocket  
>
> 连接鉴权和获取客户端ip, 需要在升级协议之前进行

## netty通道初始化

```java
@Component
public class WebsocketChannelInitializer extends ChannelInitializer<SocketChannel> {

    @Resource
    private WebSocketHandler webSocketHandler;

    @Value("${websocket.url}")
    private String websocketUrl;

    @Override
    protected void initChannel(SocketChannel socketChannel) throws Exception {

        // 获取pipeline通道
        ChannelPipeline pipeline = socketChannel.pipeline();
        // 因为基于http协议，使用http的编码和解码器
        pipeline.addLast(new HttpServerCodec());
        // 是以块方式写，添加ChunkedWriteHandler处理器
        pipeline.addLast(new ChunkedWriteHandler());
        /*
          说明
          1. http数据在传输过程中是分段, HttpObjectAggregator ，就是可以将多个段聚合
          2. 这就就是为什么，当浏览器发送大量数据时，就会发出多次http请求
        */
        pipeline.addLast(new HttpObjectAggregator(8192));
        // 连接鉴权
        pipeline.addLast(new ChannelAuthHandler());
        // http请求头参数获取, 客户端ip
        pipeline.addLast(new HttpHeadersHandler());
        /* 说明
          1. 对应websocket ，它的数据是以 帧(frame) 形式传递
          2. 可以看到WebSocketFrame 下面有六个子类
          3. 浏览器请求时 ws://localhost:7000/msg 表示请求的uri
          4. WebSocketServerProtocolHandler 核心功能是将 http协议升级为 ws协议 , 保持长连接
          5. 是通过一个 状态码 101
        */
        pipeline.addLast(new WebSocketServerProtocolHandler("/msg"));
        // 自定义的handler ，处理业务逻辑
//        pipeline.addLast(new MyServerHandler());

        pipeline.addLast(webSocketHandler);
    }
}
```



## 连接鉴权Handler

```java
public class ChannelAuthHandler extends SimpleChannelInboundHandler<FullHttpRequest> {

    @Override
    public void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) throws Exception {
        String token = request.headers().get("token");
        if ("admin".equals(token)) {
            System.out.println("token校验通过");
            // 传递到下一个handler：升级握手
            ctx.fireChannelRead(request.retain());
            // 在本channel上移除这个handler消息处理，即只处理一次，鉴权通过与否
            ctx.pipeline().remove(ChannelAuthHandler.class);
        } else {
            System.out.println("token校验失败");
            ctx.close();
        }

    }
}
```



## 客户端ip获取

> FullHttpRequest

```java
@Slf4j
public class HttpHeadersHandler extends ChannelInboundHandlerAdapter {

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof FullHttpRequest) {
            HttpHeaders headers = ((FullHttpRequest) msg).headers();
            String ip = headers.get("X-Real-IP");
            // 如果没经过nginx，就直接获取远端地址
            if (!StringUtils.hasLength(ip)) {
                InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
                ip = address.getAddress().getHostAddress();
            }
            log.info("客户端IP: {}", ip);
            NettyUtil.setAttr(ctx.channel(), NettyUtil.IP, ip);
        }
        ctx.fireChannelRead(msg);
    }
}
```

## Netty工具类

```java
public class NettyUtil {

    public final static AttributeKey<String> IP = AttributeKey.valueOf("IP");

    public static void setAttr(Channel channel, AttributeKey<String> attributeKey, String value) {
        Attribute<String> attr = channel.attr(attributeKey);
        attr.set(value);
    }
}
```

