# Netty 服务 ws 注册到 Nacos，动态上下线结合 Gateway 转发 ws

## Websocket实现

```java
public class WebSocketServerInitializer extends ChannelInitializer<SocketChannel> {

    @Override
    public void initChannel(SocketChannel ch) throws Exception {
        // 获取 Channel Pipeline
        ChannelPipeline pipeline = ch.pipeline();

        // 添加 HTTP Request/Response 编解码器
        pipeline.addLast(new HttpServerCodec());
        // 添加 HTTP 聚合器，将多个请求或响应合并为一个 FullHttpRequest/FullHttpResponse
        pipeline.addLast(new HttpObjectAggregator(64 * 1024));

        // 添加 WebSocket 编解码器
        pipeline.addLast(new WebSocketFrameCodec());
        // 添加 WebSocket 请求处理器
        pipeline.addLast(new WebSocketServerHandler());
    }

}

public class WebSocketServerHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

    private String webSocketUrl;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        webSocketUrl = "ws://" + getHost(ctx) + "/websocket";
        System.out.println("WebSocket Client connected, url: " + webSocketUrl);
    }
    
    // 在这里处理所有类型的 WebSocket 数据帧
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) {
        if (frame instanceof TextWebSocketFrame) {
            // 处理文本消息
            String message = ((TextWebSocketFrame) frame).text();
            System.out.println("Received message: " + message);
        } else if (frame instanceof BinaryWebSocketFrame) {
            // 处理二进制消息
            ByteBuf payload = ((BinaryWebSocketFrame) frame).content();
            System.out.println("Received bytes: " + payload.toString(CharsetUtil.UTF_8));
        } else if (frame instanceof PingWebSocketFrame) {
            // 发送 Ping 消息
            ctx.write(new PongWebSocketFrame(frame.content().retain()));
        } else if (frame instanceof CloseWebSocketFrame) {
            // 处理关闭连接消息
            ctx.close();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }

    // 获取客户端 IP 和端口号
    private String getHost(ChannelHandlerContext ctx) {
        InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        return socketAddress.getHostString() + ":" + socketAddress.getPort();
    }
}

```

## Netty启动

```java
public class WebSocketServer {
    
    public static void main(String[] args) throws Exception {
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
             .channel(NioServerSocketChannel.class)
             .childHandler(new WebSocketServerInitializer());

            ChannelFuture f = b.bind(8080).sync();
            f.channel().closeFuture().sync();
        } finally {
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }
}
```



## 注册 Nacos

## 设置 ShutdownHook

```java
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    if (namingService != null) {
        namingService.deregisterInstance("websocket-server", "127.0.0.1", 8080);
    }
}));
```

## Gateway
