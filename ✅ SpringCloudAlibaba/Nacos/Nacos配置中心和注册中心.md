# 1. Nacos配置中心
### 1.1 依赖

```java
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
```
### 1.2 yml配置

```yaml
bootstrap.yml

spring:
  application:
    name: springcloud-consumer1
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        group: DEV_GROUP
        namespace: 32a1091e-efe5-4381-8d3d-4d191e7bb080
```
### 1.3 启动类

```java
@SpringBootApplication
@EnableFeignClients
@EnableDiscoveryClient
public class Consumer1App {

    public static void main(String[] args) {
        SpringApplication.run(Consumer1App.class, args);
    }
}
```

# 2. Nacos服务注册中心
