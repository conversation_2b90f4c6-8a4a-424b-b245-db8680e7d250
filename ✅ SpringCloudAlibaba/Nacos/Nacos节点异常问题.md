# Nacos节点异常问题

## 问题描述

> Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]

```json
{
  "lastRefreshTime": 1706684515598,
  "raftMetaData": {
    "metaDataMap": {
      "naming_instance_metadata": {
        "leader": "172.16.251.226:7846",
        "raftGroupMember": [
          "172.16.251.225:7846",
          "172.16.251.223:7846",
          "172.16.251.226:7846"
        ],
        "term": 12
      },
      "naming_persistent_service": {
        "leader": "172.16.251.226:7846",
        "raftGroupMember": [
          "172.16.251.225:7846",
          "172.16.251.223:7846",
          "172.16.251.226:7846"
        ],
        "term": 13
      },
      "naming_persistent_service_v2": {
        "errMsg": "Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]",
        "leader": "172.16.251.223:7846",
        "raftGroupMember": [
          "172.16.251.225:7846",
          "172.16.251.223:7846",
          "172.16.251.226:7846"
        ],
        "term": 14
      },
      "naming_service_metadata": {
        "errMsg": "Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]",
        "leader": "172.16.251.223:7846",
        "raftGroupMember": [
          "172.16.251.225:7846",
          "172.16.251.223:7846",
          "172.16.251.226:7846"
        ],
        "term": 15
      }
    }
  },
  "raftPort": "7846",
  "readyToUpgrade": true,
  "version": "2.2.3"
}
```

## 日志查询

```shell
tail -100f logs/protocol-raft.log
```

```
2024-01-28 04:00:51,728 ERROR Fail to refresh route configuration for group : naming_service_metadata, status is : Status[UNKNOWN<-1>: handleRequest internal error]

2024-01-28 04:00:51,728 ERROR Fail to refresh route configuration for group : naming_persistent_service_v2, status is : Status[UNKNOWN<-1>: handleRequest internal error]

2024-01-28 04:00:53,308 INFO This Raft event changes : RaftEvent{groupId='naming_persistent_service_v2', leader='172.16.251.223:7846', term=25, raftClusterInfo=[172.16.251.225:7846, 172.16.251.223:7846, 172.16.251.226:7846]}

2024-01-28 04:00:58,017 ERROR Fail to refresh leader for group : naming_service_metadata, status is : Status[UNKNOWN<-1>: Unknown leader, Unknown leader, Unknown leader]

2024-01-28 04:00:58,023 ERROR Fail to refresh route configuration for group : naming_service_metadata, status is : Status[UNKNOWN<-1>: handleRequest internal error]

2024-01-28 04:00:58,023 INFO This Raft event changes : RaftEvent{groupId='naming_service_metadata', leader='172.16.251.223:7846', term=27, raftClusterInfo=[172.16.251.225:7846, 172.16.251.223:7846, 172.16.251.226:7846]}

```

![](./images/1706685856155.png)

## github 同类问题

> https://github.com/alibaba/nacos/issues/6877
> https://github.com/alibaba/nacos/issues/7237
![](./images/1706686168740.png)

![](./images/1706686082071.png)

## Fail to save raft meta, path=/home/<USER>/data/protocol/raft/naming_instance_metadata/meta-data]

```json
{
  "lastRefreshTime": 1706756067784,
  "raftMetaData": {
    "metaDataMap": {
      "naming_instance_metadata": {
        "errMsg": "Error [type=ERROR_TYPE_META, status=Status[EIO<1014>: Fail to save raft meta, path=/home/<USER>/data/protocol/raft/naming_instance_metadata/meta-data]]",
        "leader": "172.16.250.140:7846",
        "raftGroupMember": [
          "172.16.250.140:7846",
          "172.16.250.142:7846",
          "172.16.250.141:7846"
        ],
        "term": 137
      },
      "naming_persistent_service": {
        "errMsg": "Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]",
        "leader": "172.16.250.142:7846",
        "raftGroupMember": [
          "172.16.250.140:7846",
          "172.16.250.142:7846",
          "172.16.250.141:7846"
        ],
        "term": 140
      },
      "naming_persistent_service_v2": {
        "errMsg": "Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]",
        "leader": "172.16.250.142:7846",
        "raftGroupMember": [
          "172.16.250.140:7846",
          "172.16.250.142:7846",
          "172.16.250.141:7846"
        ],
        "term": 84
      },
      "naming_service_metadata": {
        "errMsg": "Error [type=ERROR_TYPE_LOG, status=Status[EIO<1014>: Failed operation in LogStorage]]",
        "leader": "172.16.250.142:7846",
        "raftGroupMember": [
          "172.16.250.140:7846",
          "172.16.250.142:7846",
          "172.16.250.141:7846"
        ],
        "term": 129
      }
    }
  },
  "raftPort": "7846",
  "readyToUpgrade": true,
  "version": "2.2.3"
}
```
