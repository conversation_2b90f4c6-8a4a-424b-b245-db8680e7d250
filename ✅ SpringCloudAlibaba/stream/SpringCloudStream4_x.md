# SpringCloudStream4.x

## 多个消费function

> 2024-04-24T14:08:57.277+08:00 WARN 25520 --- [           main] c.f.c.c.BeanFactoryAwareFunctionRegistry : Multiple
> functional beans were found [process, sendTestData], thus can't determine default function definition. Please use '
> spring.cloud.function.definition' property to explicitly define it.

## 集成rabbitmq

### 延迟队列

### 死信队列

### 广播

## 集成kafka

## 集成rocketmq

### 定时消息

### 顺序消息

## rabbitmq和kafka切换

## 同时存在kafka和rabbitmq
