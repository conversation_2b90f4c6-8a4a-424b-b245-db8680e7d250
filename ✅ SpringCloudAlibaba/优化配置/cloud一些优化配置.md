# ribbon饥饿加载
```yaml
ribbon:
  eager-load:
    enabled: true
    # 服务名称, 多个逗号隔开?
    clients: meituan-hmyc

```
> 不知道是否有效, 待验证

> 刚启动, 调用远程服务feign, gateway转发, 第一次调用非常慢


> Java全局配置? 
> 

# gateway 转发负载均衡(ribbon)
```yaml
meituan-hmyc:
  ribbon:
    # 响应时间
    # NFLoadBalancerRuleClassName: com.netflix.loadbalancer.WeightedResponseTimeRule
    # 轮询
    NFLoadBalancerRuleClassName: com.netflix.loadbalancer.RoundRobinRule

```
# gateway服务调用超时时间修改
```yaml
spring:
  cloud:
    gateway:
      dynamicRoute:
        enabled: true
      discovery:
        locator:
          enabled: true
      httpclient:
        connect-timeout: 60000
        response-timeout: 60000
```
# open-feign超时时间配置
```yaml

feign:
  # 默认使用httpclient, 也可使用okhttp
  httpclient:
    connection-timer-repeat: 20000
    connection-timeout: 20000
    max-connections: 1000
    max-connections-per-route: 100
```
> okhttp相比httpclient 性能如何?
> 测试?

