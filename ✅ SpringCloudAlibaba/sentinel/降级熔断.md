## 配置类
```java
package com.alibaba.csp.sentinel.slots.block.degrade;

import com.alibaba.csp.sentinel.slots.block.AbstractRule;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;

import java.util.Objects;

/**
 * <p>
 * Degrade is used when the resources are in an unstable state, these resources
 * will be degraded within the next defined time window. There are two ways to
 * measure whether a resource is stable or not:
 * </p>
 * <ul>
 * <li>
 * Average response time ({@code DEGRADE_GRADE_RT}): When
 * the average RT exceeds the threshold ('count' in 'DegradeRule', in milliseconds), the
 * resource enters a quasi-degraded state. If the RT of next coming 5
 * requests still exceed this threshold, this resource will be downgraded, which
 * means that in the next time window (defined in 'timeWindow', in seconds) all the
 * access to this resource will be blocked.
 * </li>
 * <li>
 * Exception ratio: When the ratio of exception count per second and the
 * success qps exceeds the threshold, access to the resource will be blocked in
 * the coming window.
 * </li>
 * </ul>
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public class DegradeRule extends AbstractRule {

    public DegradeRule() {}

    public DegradeRule(String resourceName) {
        setResource(resourceName);
    }

    /**
     * Circuit breaking strategy (0: average RT, 1: exception ratio, 2: exception count).
     * 断路策略（0：平均RT，1：异常比率，2：异常计数）
     */
    private int grade = RuleConstant.DEGRADE_GRADE_RT;

    /**
     * Threshold count. The exact meaning depends on the field of grade.
     * <ul>
     *     <li>In average RT mode, it means the maximum response time(RT) in milliseconds.</li>
     *     <li>In exception ratio mode, it means exception ratio which between 0.0 and 1.0.</li>
     *     <li>In exception count mode, it means exception count</li>
     * <ul/>
      阈值计数。确切的含义取决于年级的领域。
            在平均 RT 模式下，它表示最大响应时间 (RT)，以毫秒为单位。
            在异常率模式下，表示异常率在 0.0 和 1.0 之间。
            在异常计数模式下，表示异常计数
     */
    private double count;

    /**
     * Recovery timeout (in seconds) when circuit breaker opens. After the timeout, the circuit breaker will
     * transform to half-open state for trying a few requests.
    断路器打开时的恢复超时（以秒为单位）。超时后，断路器将转换为半开状态以尝试一些请求。
     */
    private int timeWindow;

    /**
     * Minimum number of requests (in an active statistic time span) that can trigger circuit breaking.
    可以触发断路的最小请求数（在活动统计时间跨度内）。
     *
     * @since 1.7.0
     */
    private int minRequestAmount = RuleConstant.DEGRADE_DEFAULT_MIN_REQUEST_AMOUNT;

    /**
     * The threshold of slow request ratio in RT mode.
	RT模式下的慢请求率阈值。
     *
     * @since 1.8.0
     */
    private double slowRatioThreshold = 1.0d;

    /**
     * The interval statistics duration in millisecond.
	间隔统计持续时间（以毫秒为单位）。
     *
     * @since 1.8.0
     */
    private int statIntervalMs = 1000;

```

## nacos配置
```java
[
  {
    "resource": "/order/getEx",
    "limitApp": "default",
    "grade": 1,
    "count": 0.1,
    "timeWindow": 5,
    "minRequestAmount": 3,
    "slowRatioThreshold": 1,
    "statIntervalMs": 1000
  }
]
```
## 熔断策略 grade
> [https://sentinelguard.io/zh-cn/docs/circuit-breaking.html](https://sentinelguard.io/zh-cn/docs/circuit-breaking.html)

### 0. 慢调用比例 (SLOW_REQUEST_RATIO)
```java
	选择以慢调用比例作为阈值，需要设置允许的慢调用 RT（即最大的响应时间）（count)，请求的响应时间大于该值则统计为慢调用。

	当单位统计时长（statIntervalMs）内请求数目大于设置的最小请求数目（minRequestAmount），并且慢调用的比例大于阈值（slowRatioThreshold），则接下来的熔断时长（timeWindow 秒）内请求会自动被熔断。
    经过熔断时长后熔断器会进入探测恢复状态（HALF-OPEN 状态），若接下来的一个请求响应时间小于设置的慢调用 RT （count)则结束熔断，若大于设置的慢调用 RT 则会再次被熔断。
```
### 1. 异常比例 (ERROR_RATIO)
```java
当单位统计时长（statIntervalMs）内请求数目大于设置的最小请求数目（minRequestAmount），
    并且异常的比例大于阈值（count)，
    则接下来的熔断时长内请求会自动被熔断。
    经过熔断时长（timeWindow 秒）后熔断器会进入探测恢复状态（HALF-OPEN 状态），
    若接下来的一个请求成功完成（没有错误）则结束熔断，否则会再次被熔断。
    异常比率（count）的阈值范围是 [0.0, 1.0]，代表 0% - 100%。
```

### 2. 异常数 (ERROR_COUNT)
```java
当单位统计时长（statIntervalMs）内的异常数目（count)超过阈值（minRequestAmount）之后会自动进行熔断。
经过熔断时长（timeWindow 秒）后熔断器会进入探测恢复状态（HALF-OPEN 状态），
若接下来的一个请求成功完成（没有错误）则结束熔断，否则会再次被熔断。
```

## 
## 说明
![image.png](https://cdn.nlark.com/yuque/0/2022/png/684952/1664866000339-e307c38b-7c5c-4aae-9c33-b2c1ec844d1e.png#clientId=uda0377dd-d186-4&from=paste&height=376&id=u62cef5e7&originHeight=856&originWidth=1604&originalType=binary&ratio=1&rotation=0&showTitle=false&size=215512&status=done&style=none&taskId=ud6a90f7b-b61a-4f1f-b1af-0fc5747583e&title=&width=704)

## 配置持久化到nacos
```java
ReadableDataSource<String, List<DegradeRule>> degradeRuleDataSource =
    new NacosDataSource<>(properties,
            nacosConfigProperties.getGroup(),
            "degrade-config.json",
            source -> JSON.parseObject(source, new TypeReference<List<DegradeRule>>() {
            }));
DegradeRuleManager.register2Property(degradeRuleDataSource.getProperty());
```
## spring cloud gateway集成sentinel

## feign集成sentinel

## @SentinelResource 使用
> [https://sentinelguard.io/zh-cn/docs/annotation-support.html](https://sentinelguard.io/zh-cn/docs/annotation-support.html)

```java
@SentinelResource(value = "sayHello", fallback = "helloFallback", blockHandler = "exceptionHandler")
public String sayHello(String name) {
    throw new RuntimeException("hello");
}

/**
 * Fallback 函数，函数签名与原函数一致或加一个 Throwable 类型的参数.
 */
public String helloFallback(String name, Throwable t) {
    return String.format("Halooooo %s", name);
}

/**
 * Block 异常处理函数，参数最后多一个 BlockException，其余与原函数一致.
 */
public String exceptionHandler(String name, BlockException ex) {
    // Do some log here.
    log.error("", ex);
    return "Oops, error occurred at " + name;
}
```
### 说明
```java
value： 资源名
fallback：异常时降级方法名称， 出入参一致+Throwable
fallbackClass：异常时降级时要执行的方法所在类，为static
    
blockHandler：处理BlockException的方法名称, public 出入参一致+BlockException
blockHandlerClass: public static  (好像private也可以？)

exceptionsToIgnore： 排除异常类
```
