# 1. 动态路由json配置

```json
[
  {
    "id": "meituan-hmyc",
    "order": 1,
    "predicates": [
      {
        "args": {
          "pattern": "/**"
        },
        "name": "Path"
      }
    ]
  }
]

```
# 2. 开启动态路由配置
```yaml
spring:
  cloud:
    gateway:
      dynamicRoute:
        enabled: true
```
# 3. 动态路由配置类
```java
@Slf4j
@Configuration
public class DynamicRouteConfig {

    private final ApplicationEventPublisher publisher;

    public DynamicRouteConfig(ApplicationEventPublisher publisher) {
        this.publisher = publisher;
    }

    @ConditionalOnProperty(prefix = "spring.cloud.gateway.dynamicRoute", name = "enabled", havingValue = "true")
    public class NaocsDynamicRoute {

        @Resource
        private NacosConfigProperties nacosConfigProperties;

        @Bean
        public NacosRouteDefinitionRepository nacosRouteDefinitionRepository() {

            return new NacosRouteDefinitionRepository(publisher, nacosConfigProperties);
        }
    }
}
```
# 4. nacos配置动态刷新
```java
@Slf4j
public class NacosRouteDefinitionRepository implements RouteDefinitionRepository {

    private static final String DATA_ID = "meituan-gateway-router.json";

    private final ApplicationEventPublisher publisher;

    private final NacosConfigProperties nacosConfigProperties;

    public NacosRouteDefinitionRepository(ApplicationEventPublisher publisher, NacosConfigProperties nacosConfigProperties) {
        this.publisher = publisher;
        this.nacosConfigProperties = nacosConfigProperties;
        addListener();
    }


    @Override
    public Flux<RouteDefinition> getRouteDefinitions() {
        try {
            Properties properties = nacosConfigProperties.assembleConfigServiceProperties();
            ConfigService configService = NacosFactory.createConfigService(properties);
            String content = configService.getConfig(DATA_ID, nacosConfigProperties.getGroup(), 5000);
            log.info(content);
            List<RouteDefinition> definitionList = getListByStr(content);
            return Flux.fromIterable(definitionList);
        } catch (NacosException e) {
            log.error("getRouteDefinitions: ", e);
        }
        return Flux.fromIterable(ListUtil.empty());
    }

    /**
     * 路由变化只需要往 ApplicationEventPublisher 推送一个 RefreshRoutesEvent 事件即刻，
     * gateway会自动监听该事件并调用 getRouteDefinitions 方法更新路由信息
     */
    private void addListener() {
        ConfigService configService = new NacosConfigManager(nacosConfigProperties).getConfigService();
        try {
            configService.addListener(DATA_ID, nacosConfigProperties.getGroup(), new Listener() {
                @Override
                public Executor getExecutor() {
                    return null;
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    publisher.publishEvent(new RefreshRoutesEvent(this));
                }
            });
        } catch (NacosException e) {
            log.error("addListener: ", e);
        }
    }

    private List<RouteDefinition> getListByStr(String content) {
        if (StrUtil.isNotEmpty(content)) {
            return JSONUtil.toList(content, RouteDefinition.class);
        }
        return ListUtil.empty();
    }

    @Override
    public Mono<Void> save(Mono<RouteDefinition> route) {
        return null;
    }

    @Override
    public Mono<Void> delete(Mono<String> routeId) {
        return null;
    }


}

```
# 5. 验证
> 添加路由
> 修改路由
> 删除路由

