# Gateway 内置全局过滤器 GlobalFilter

## GlobalFilter 接口

```java
/**
 * 对Web请求进行拦截式、链式处理的契约，可用于实现横切、与应用程序无关的需求，如安全性、超时等。
 *
 * <AUTHOR>
 * @since 5.0
 */
public interface GlobalFilter {

	/**
	 * 处理Web请求并(可选地)通过给定的GatewayFilterChain委托给下一个WebFilter。
	 * @param exchange the current server exchange
	 * @param chain provides a way to delegate to the next filter
	 * @return {@code Mono<Void>} to indicate when request processing is complete
	 */
	Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain);

}
```

## GlobalFilter 具体实现

![](images/Pasted%20image%****************.png)

## SpringBoot 自动配置

比如 NettyRoutingFilter，自动配置类 org.springframework.cloud.gateway.config.GatewayAutoConfiguration

### 源码流程

![](images/Pasted%20image%****************.png)

需要在配置开启 Filter 才生效

![](images/Pasted%20image%****************.png)

![](images/Pasted%20image%****************.png)

SpringBootCondition 是一个抽象类，定义了抽象方法由子类实现。

![](images/Pasted%20image%****************.png)

![](images/Pasted%20image%****************.png)

在 yml 配置，判断是否为 true，则可以创建对应 Bean

![](images/Pasted%20image%****************.png)

![](images/Pasted%20image%****************.png)

根据 Fileter 类获取过滤器名称，拼接字符串

![](images/Pasted%20image%****************.png)

### 配置开关

在 spring-configuration-metadata.json

![](images/Pasted%20image%****************.png)

```json
    {
      "name": "spring.cloud.gateway.global-filter.netty-routing.enabled",
      "type": "java.lang.Boolean",
      "description": "Enables the netty-routing global filter.",
      "defaultValue": "true"
    },
```

## 默认 GlobalFilter 顺序

![image-*****************](images/image-*****************.png)

```java
public interface Ordered {

	/**
	 * 优先级最高，最小Integer，负数 -**********
	 * @see java.lang.Integer#MIN_VALUE
	 */
	int HIGHEST_PRECEDENCE = Integer.MIN_VALUE;

	/**
	 * 优先级最低，最大Integer **********
	 * @see java.lang.Integer#MAX_VALUE
	 */
	int LOWEST_PRECEDENCE = Integer.MAX_VALUE;

	int getOrder();

}

```

| 过滤器名称                              | order                               | 作用                                            |
| --------------------------------------- | ----------------------------------- | ----------------------------------------------- |
| RemoveCachedBodyFilter                  | _HIGHEST_PRECEDENCE_                |                                                 |
| AdaptCachedBodyGlobalFilter             | Ordered._HIGHEST_PRECEDENCE_ + 1000 |                                                 |
| NettyWriteResponseFilter                | -1                                  |                                                 |
| ForwardPathFilter                       | 0                                   |                                                 |
| GatewayMetricsFilter                    | 0                                   |                                                 |
| RouteToRequestUrlFilter                 | 10000                               | 判断路由是否为 lb                               |
| ReactiveLoadBalancerClientFilter        | 10150                               | 路由配置 lb:// 负载均衡，从注册中心获取服务实例 |
| LoadBalancerServiceInstanceCookieFilter | 10151                               | 传递 cookie 给服务实例                          |
| WebsocketRoutingFilter                  | Ordered._LOWEST_PRECEDENCE_ - 1     | 转发 ws、wss                                    |
| NettyRoutingFilter                      | Ordered._LOWEST_PRECEDENCE_         | 转发 http、https                                |
| ForwardRoutingFilter                    | Ordered._LOWEST_PRECEDENCE_         | 转发 forward 自身接口                           |

## 自定义 GlobalFilter

```java
@Component
@RequiredArgsConstructor
public class GerRouteFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		// 自定义处理
         return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        // 决定在哪一步执行
        return 0;
    }
}
```

## 处理过程源码分析