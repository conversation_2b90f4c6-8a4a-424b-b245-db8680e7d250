# Gateway 内置路由断言 Predicate 使用

## Gateway 版本

![](images/Pasted%20image%2020230526204904.png)

## 内置路由断言工厂总览

![](images/Pasted%20image%2020230526201604.png)

## 1. AfterRoutePredicateFactory 指定时间之后

> 此谓词匹配在指定日期时间之后发生的请求。早于这个时间无法请求

### application.yml

```yaml
            # 指定时间之后
            - name: After
              args:
                datetime: 2017-01-20T17:42:47.789-07:00[America/Denver]
```

### 源码

```java
public class AfterRoutePredicateFactory extends AbstractRoutePredicateFactory<AfterRoutePredicateFactory.Config> {

	/**
	 * DateTime key.
	 */
	public static final String DATETIME_KEY = "datetime";

	public AfterRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Collections.singletonList(DATETIME_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange serverWebExchange) {
				// 当前时间
				final ZonedDateTime now = ZonedDateTime.now();
				// 当前时间是否是指定时间之后发生
				return now.isAfter(config.getDatetime());
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("After: %s", config.getDatetime());
			}
		};
	}

	public static class Config {

		@NotNull
		private ZonedDateTime datetime;

		public ZonedDateTime getDatetime() {
			return datetime;
		}

		public void setDatetime(ZonedDateTime datetime) {
			this.datetime = datetime;
		}

	}

}
```

## 2. BeforeRoutePredicateFactory 指定时间之前

> 匹配在指定日期时间之前发生的请求

### application.yml

```yaml
            # 指定时间之前
            - name: Before
              args:
                datetime: 2024-01-20T17:42:47.789-07:00[America/Denver]
```

### 源码

```java
public class BeforeRoutePredicateFactory extends AbstractRoutePredicateFactory<BeforeRoutePredicateFactory.Config> {

	/**
	 * DateTime key.
	 */
	public static final String DATETIME_KEY = "datetime";

	public BeforeRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Collections.singletonList(DATETIME_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange serverWebExchange) {
				final ZonedDateTime now = ZonedDateTime.now();
				// 当前时间是否为指定时间之前发生
				return now.isBefore(config.getDatetime());
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Before: %s", config.getDatetime());
			}
		};
	}

	public static class Config {

		private ZonedDateTime datetime;

		public ZonedDateTime getDatetime() {
			return datetime;
		}

		public void setDatetime(ZonedDateTime datetime) {
			this.datetime = datetime;
		}

	}

}
```

## 3. BetweenRoutePredicateFactory 在两个指定时间之间

> 匹配在 datetime1 之后和 datetime2 之前发生的请求

### application.yml

```yaml
            # 在datetime1和datetime2之间
            - name: Between
              args:
                datetime1: 2017-01-20T17:42:47.789-07:00[America/Denver]
                datetime2: 2024-01-20T17:42:47.789-07:00[America/Denver]
```

### 源码

```java
public class BetweenRoutePredicateFactory extends AbstractRoutePredicateFactory<BetweenRoutePredicateFactory.Config> {

	/**
	 * DateTime 1 key.
	 */
	public static final String DATETIME1_KEY = "datetime1";

	/**
	 * DateTime 2 key.
	 */
	public static final String DATETIME2_KEY = "datetime2";

	public BetweenRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList(DATETIME1_KEY, DATETIME2_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		Assert.isTrue(config.getDatetime1().isBefore(config.getDatetime2()),
				config.getDatetime1() + " must be before " + config.getDatetime2());

		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange serverWebExchange) {
				final ZonedDateTime now = ZonedDateTime.now();
				// 当前时间在datetime1之后，在datetime2之前
				// [datetime1, datetime2]
				return now.isAfter(config.getDatetime1()) && now.isBefore(config.getDatetime2());
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Between: %s and %s", config.getDatetime1(), config.getDatetime2());
			}
		};
	}

	@Validated
	public static class Config {

		@NotNull
		private ZonedDateTime datetime1;

		@NotNull
		private ZonedDateTime datetime2;

		public ZonedDateTime getDatetime1() {
			return datetime1;
		}

		public Config setDatetime1(ZonedDateTime datetime1) {
			this.datetime1 = datetime1;
			return this;
		}

		public ZonedDateTime getDatetime2() {
			return datetime2;
		}

		public Config setDatetime2(ZonedDateTime datetime2) {
			this.datetime2 = datetime2;
			return this;
		}

	}

}
```

## 4. CookieRoutePredicateFactory Cookie参数验证

> 接口传入的 Cookie，判断指定 Cookie 名称的所有 Cookie，是否存在与指定正则是否匹配。

### application.yml

```yaml
            # Cookie判断
            - name: Cookie
              args:
                name: chocolate
                regexp: ch.p
```

### 源码

```java
public class CookieRoutePredicateFactory extends AbstractRoutePredicateFactory<CookieRoutePredicateFactory.Config> {

	/**
	 * Name key.
	 */
	public static final String NAME_KEY = "name";

	/**
	 * Regexp key.
	 */
	public static final String REGEXP_KEY = "regexp";

	public CookieRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList(NAME_KEY, REGEXP_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
				// 从request中取出所有cookie
				List<HttpCookie> cookies = exchange.getRequest().getCookies().get(config.name);
				// cookie 匹配失败
				if (cookies == null) {
					return false;
				}
				// 遍历所有cookie
				for (HttpCookie cookie : cookies) {
					// 校验正则正确，匹配成功，结束
					if (cookie.getValue().matches(config.regexp)) {
						return true;
					}
				}
				return false;
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Cookie: name=%s regexp=%s", config.name, config.regexp);
			}
		};
	}

	@Validated
	public static class Config {

		@NotEmpty
		private String name;

		@NotEmpty
		private String regexp;

		public String getName() {
			return name;
		}

		public Config setName(String name) {
			this.name = name;
			return this;
		}

		public String getRegexp() {
			return regexp;
		}

		public Config setRegexp(String regexp) {
			this.regexp = regexp;
			return this;
		}

	}

}
```

## 5. HeaderRoutePredicateFactory Header参数验证

> 指定请求头参数是否与指定正则匹配

### application.yml

```yaml
            # 请求体参数正则判断
            - name: Header
              args:
                header: X-Request-Id
                regexp: \d+
```

### 源码

```java
public class HeaderRoutePredicateFactory extends AbstractRoutePredicateFactory<HeaderRoutePredicateFactory.Config> {

	/**
	 * Header key.
	 */
	public static final String HEADER_KEY = "header";

	/**
	 * Regexp key.
	 */
	public static final String REGEXP_KEY = "regexp";

	public HeaderRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList(HEADER_KEY, REGEXP_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		boolean hasRegex = !ObjectUtils.isEmpty(config.regexp);

		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
        // 获取指定请求头参数的值列表
				List<String> values = exchange.getRequest().getHeaders().getOrDefault(config.header,
						Collections.emptyList());
				if (values.isEmpty()) {
					return false;
				}
				// 只有配置了正则，这个断言才有效
				if (hasRegex) {
					// 遍历判断值和指定正则是否存在匹配
					for (int i = 0; i < values.size(); i++) {
						String value = values.get(i);
						if (value.matches(config.regexp)) {
							return true;
						}
					}
					return false;
				}

				// there is a value and since regexp is empty, we only check existence.
				return true;
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Header: %s regexp=%s", config.header, config.regexp);
			}
		};
	}

	@Validated
	public static class Config {

		@NotEmpty
		private String header;

		private String regexp;

		public String getHeader() {
			return header;
		}

		public Config setHeader(String header) {
			this.header = header;
			return this;
		}

		public String getRegexp() {
			return regexp;
		}

		public Config setRegexp(String regexp) {
			this.regexp = regexp;
			return this;
		}

	}

}
```

## 6. MethodRoutePredicateFactory 请求类型判断

> 接口请求类型判断，如GET、POST等，是否匹配

### application.yml

```yaml
            # 请求类型判断
            - name: Method
              args:
                # 多个逗号隔开
                methods: GET
```

### 源码

```java
public class MethodRoutePredicateFactory extends AbstractRoutePredicateFactory<MethodRoutePredicateFactory.Config> {

	/**
	 * Methods key.
	 */
	public static final String METHODS_KEY = "methods";

	public MethodRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList(METHODS_KEY);
	}

	@Override
	public ShortcutType shortcutType() {
		return ShortcutType.GATHER_LIST;
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
        // 当前请求类型
				HttpMethod requestMethod = exchange.getRequest().getMethod();
        // 遍历判断是否与指定类型匹配
				return stream(config.getMethods()).anyMatch(httpMethod -> httpMethod == requestMethod);
			}

			@Override
			public String toString() {
				return String.format("Methods: %s", Arrays.toString(config.getMethods()));
			}
		};
	}

	@Validated
	public static class Config {

		private HttpMethod[] methods;

		public HttpMethod[] getMethods() {
			return methods;
		}

		public void setMethods(HttpMethod... methods) {
			this.methods = methods;
		}

	}

}
```

## 7. HostRoutePredicateFactory 请求头Host

> 请求头Host参数与指定正则是否存在一个匹配，以下patterns任意一个匹配成功即可。

### application.yml

```yaml
            # 请求头Host判断
            - name: Host
              args:
                patterns:
                  - '**.kk0.org'
                  - '**.kk1.org'
```

### 源码

```java
public class HostRoutePredicateFactory extends AbstractRoutePredicateFactory<HostRoutePredicateFactory.Config> {

	private PathMatcher pathMatcher = new AntPathMatcher(".");

	public HostRoutePredicateFactory() {
		super(Config.class);
	}

	public void setPathMatcher(PathMatcher pathMatcher) {
		this.pathMatcher = pathMatcher;
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Collections.singletonList("patterns");
	}

	@Override
	public ShortcutType shortcutType() {
		return ShortcutType.GATHER_LIST;
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
        // 获取请求体参数Host
				String host = exchange.getRequest().getHeaders().getFirst("Host");
				String match = null;
        // 遍历，判断是否存在与指定正则列表存在一个匹配
				for (int i = 0; i < config.getPatterns().size(); i++) {
					String pattern = config.getPatterns().get(i);
					if (pathMatcher.match(pattern, host)) {
						match = pattern;
						break;
					}
				}

				if (match != null) {
          // 存在匹配，获取uri参数
					Map<String, String> variables = pathMatcher.extractUriTemplateVariables(match, host);
					ServerWebExchangeUtils.putUriTemplateVariables(exchange, variables);
					return true;
				}

				return false;
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Hosts: %s", config.getPatterns());
			}
		};
	}

	@Validated
	public static class Config {

		private List<String> patterns = new ArrayList<>();

		public List<String> getPatterns() {
			return patterns;
		}

		public Config setPatterns(List<String> patterns) {
			this.patterns = patterns;
			return this;
		}

		@Override
		public String toString() {
			return new ToStringCreator(this).append("patterns", patterns).toString();
		}

	}

}

```

## 8. PathRoutePredicateFactory 请求uri匹配

> 匹配uri，最常用。
>
> matchTrailingSlash 匹配尾随斜杠（好像配置没用？）
>
> matchTrailingSlash=true，/service-api/getTwo和/service-api/getTwo/   都可匹配成功。
>
> matchTrailingSlash=false，只有/service-api/getTwo可匹配成功。

### application.yml

```yaml
            # 请求Uri判断
            - name: Path
              args:
                # 匹配尾随斜杠
                matchTrailingSlash: true
                patterns:
                  - /service-api/**
```

### 源码

```java
public class PathRoutePredicateFactory extends AbstractRoutePredicateFactory<PathRoutePredicateFactory.Config> {

	private static final Log log = LogFactory.getLog(PathRoutePredicateFactory.class);

	private static final String MATCH_TRAILING_SLASH = "matchTrailingSlash";

	private PathPatternParser pathPatternParser = new PathPatternParser();

	public PathRoutePredicateFactory() {
		super(Config.class);
	}

	private static void traceMatch(String prefix, Object desired, Object actual, boolean match) {
		if (log.isTraceEnabled()) {
			String message = String.format("%s \"%s\" %s against value \"%s\"", prefix, desired,
					match ? "matches" : "does not match", actual);
			log.trace(message);
		}
	}

	public void setPathPatternParser(PathPatternParser pathPatternParser) {
		this.pathPatternParser = pathPatternParser;
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList("patterns", MATCH_TRAILING_SLASH);
	}

	@Override
	public ShortcutType shortcutType() {
		return ShortcutType.GATHER_LIST_TAIL_FLAG;
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		final ArrayList<PathPattern> pathPatterns = new ArrayList<>();
		synchronized (this.pathPatternParser) {
			pathPatternParser.setMatchOptionalTrailingSeparator(config.isMatchTrailingSlash());
			config.getPatterns().forEach(pattern -> {
				PathPattern pathPattern = this.pathPatternParser.parse(pattern);
				pathPatterns.add(pathPattern);
			});
		}
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
				PathContainer path = parsePath(exchange.getRequest().getURI().getRawPath());

				PathPattern match = null;
				for (int i = 0; i < pathPatterns.size(); i++) {
					PathPattern pathPattern = pathPatterns.get(i);
					if (pathPattern.matches(path)) {
						match = pathPattern;
						break;
					}
				}

				if (match != null) {
					traceMatch("Pattern", match.getPatternString(), path, true);
					PathMatchInfo pathMatchInfo = match.matchAndExtract(path);
					putUriTemplateVariables(exchange, pathMatchInfo.getUriVariables());
					exchange.getAttributes().put(GATEWAY_PREDICATE_MATCHED_PATH_ATTR, match.getPatternString());
					String routeId = (String) exchange.getAttributes().get(GATEWAY_PREDICATE_ROUTE_ATTR);
					if (routeId != null) {
						// populated in RoutePredicateHandlerMapping
						exchange.getAttributes().put(GATEWAY_PREDICATE_MATCHED_PATH_ROUTE_ID_ATTR, routeId);
					}
					return true;
				}
				else {
					traceMatch("Pattern", config.getPatterns(), path, false);
					return false;
				}
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Paths: %s, match trailing slash: %b", config.getPatterns(),
						config.isMatchTrailingSlash());
			}
		};
	}

	@Validated
	public static class Config {

		private List<String> patterns = new ArrayList<>();

		private boolean matchTrailingSlash = true;

		public List<String> getPatterns() {
			return patterns;
		}

		public Config setPatterns(List<String> patterns) {
			this.patterns = patterns;
			return this;
		}

		/**
		 * @deprecated use {@link #isMatchTrailingSlash()}
		 */
		@Deprecated
		public boolean isMatchOptionalTrailingSeparator() {
			return isMatchTrailingSlash();
		}

		/**
		 * @deprecated use {@link #setMatchTrailingSlash(boolean)}
		 */
		@Deprecated
		public Config setMatchOptionalTrailingSeparator(boolean matchOptionalTrailingSeparator) {
			setMatchTrailingSlash(matchOptionalTrailingSeparator);
			return this;
		}

		public boolean isMatchTrailingSlash() {
			return matchTrailingSlash;
		}

		public Config setMatchTrailingSlash(boolean matchTrailingSlash) {
			this.matchTrailingSlash = matchTrailingSlash;
			return this;
		}

		@Override
		public String toString() {
			return new ToStringCreator(this).append("patterns", patterns)
					.append(MATCH_TRAILING_SLASH, matchTrailingSlash).toString();
		}

	}

}
```

## 9. QueryRoutePredicateFactory 请求参数正则判断

> 判断表单参数，是否存在参数与正则匹配

### application.yml

```yaml
            # 请求表单参数判断
            - name: Query
              args:
                param: number
                regexp: \d+
```

### 源码

```java
public class QueryRoutePredicateFactory extends AbstractRoutePredicateFactory<QueryRoutePredicateFactory.Config> {

	/**
	 * Param key.
	 */
	public static final String PARAM_KEY = "param";

	/**
	 * Regexp key.
	 */
	public static final String REGEXP_KEY = "regexp";

	public QueryRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Arrays.asList(PARAM_KEY, REGEXP_KEY);
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
        // 只有配置了正则才判断
				if (!StringUtils.hasText(config.regexp)) {
					// check existence of header
					return exchange.getRequest().getQueryParams().containsKey(config.param);
				}
				
        // 根据配置参数名称获取值
				List<String> values = exchange.getRequest().getQueryParams().get(config.param);
				if (values == null) {
					return false;
				}
        // 遍历判断参数值是否与正则匹配
				for (String value : values) {
					if (value != null && value.matches(config.regexp)) {
						return true;
					}
				}
				return false;
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("Query: param=%s regexp=%s", config.getParam(), config.getRegexp());
			}
		};
	}

	@Validated
	public static class Config {

		@NotEmpty
		private String param;

		private String regexp;

		public String getParam() {
			return param;
		}

		public Config setParam(String param) {
			this.param = param;
			return this;
		}

		public String getRegexp() {
			return regexp;
		}

		public Config setRegexp(String regexp) {
			this.regexp = regexp;
			return this;
		}

	}

}
```

## 10. RemoteAddrRoutePredicateFactory 请求远程地址判断

### application.yml

```yaml
            # 请求远程地址判断
            - name: RemoteAddr
              args:
                sources: 127.0.0.1
```

### 源码

```java
public class RemoteAddrRoutePredicateFactory
		extends AbstractRoutePredicateFactory<RemoteAddrRoutePredicateFactory.Config> {

	private static final Log log = LogFactory.getLog(RemoteAddrRoutePredicateFactory.class);

	public RemoteAddrRoutePredicateFactory() {
		super(Config.class);
	}

	@Override
	public ShortcutType shortcutType() {
		return GATHER_LIST;
	}

	@Override
	public List<String> shortcutFieldOrder() {
		return Collections.singletonList("sources");
	}

	@NotNull
	private List<IpSubnetFilterRule> convert(List<String> values) {
		List<IpSubnetFilterRule> sources = new ArrayList<>();
		for (String arg : values) {
			addSource(sources, arg);
		}
		return sources;
	}

	@Override
	public Predicate<ServerWebExchange> apply(Config config) {
		List<IpSubnetFilterRule> sources = convert(config.sources);

		return new GatewayPredicate() {
			@Override
			public boolean test(ServerWebExchange exchange) {
				InetSocketAddress remoteAddress = config.remoteAddressResolver.resolve(exchange);
				if (remoteAddress != null && remoteAddress.getAddress() != null) {
					String hostAddress = remoteAddress.getAddress().getHostAddress();
					String host = exchange.getRequest().getURI().getHost();

					if (log.isDebugEnabled() && !hostAddress.equals(host)) {
						log.debug("Remote addresses didn't match " + hostAddress + " != " + host);
					}

					for (IpSubnetFilterRule source : sources) {
						if (source.matches(remoteAddress)) {
							return true;
						}
					}
				}

				return false;
			}

			@Override
			public Object getConfig() {
				return config;
			}

			@Override
			public String toString() {
				return String.format("RemoteAddrs: %s", config.getSources());
			}
		};
	}

	private void addSource(List<IpSubnetFilterRule> sources, String source) {
		if (!source.contains("/")) { // no netmask, add default
			source = source + "/32";
		}

		String[] ipAddressCidrPrefix = source.split("/", 2);
		String ipAddress = ipAddressCidrPrefix[0];
		int cidrPrefix = Integer.parseInt(ipAddressCidrPrefix[1]);

		sources.add(new IpSubnetFilterRule(ipAddress, cidrPrefix, IpFilterRuleType.ACCEPT));
	}

	@Validated
	public static class Config {

		@NotEmpty
		private List<String> sources = new ArrayList<>();

		@NotNull
		private RemoteAddressResolver remoteAddressResolver = new RemoteAddressResolver() {
		};

		public List<String> getSources() {
			return sources;
		}

		public Config setSources(List<String> sources) {
			this.sources = sources;
			return this;
		}

		public Config setSources(String... sources) {
			this.sources = Arrays.asList(sources);
			return this;
		}

		public Config setRemoteAddressResolver(RemoteAddressResolver remoteAddressResolver) {
			this.remoteAddressResolver = remoteAddressResolver;
			return this;
		}

	}

}
```

## 11. WeightRoutePredicateFactory

> 此路由会将 ~80% 的流量转发给 weighthigh.org，将 ~20% 的流量转发给 weighlow.org

### application.yml

```yaml
spring:
  cloud:
    gateway:
      routes:
      - id: weight_high
        uri: https://weighthigh.org
        predicates:
        - Weight=group1, 8
      - id: weight_low
        uri: https://weightlow.org
        predicates:
        - Weight=group1, 2
```



## 12. XForwardedRemoteAddrRoutePredicateFactory

## 路由配置json
```json
[{
		"id": "coding-cloud-service",
		"predicates": [{
			"name": "Path",
			"args": {
				"_genkey_0": "/getOne"
			}
		}],
		"filters": [

		],
		"uri": "lb://coding-cloud-service",
		"metadata": {

		},
		"order": 0
	},
	{
		"id": "coding-cloud-service-two",
		"predicates": [{
				"name": "After",
				"args": {
					"datetime": "2017-01-20T17:42:47.789-07:00[America/Denver]"
				}
			},
			{
				"name": "Before",
				"args": {
					"datetime": "2024-01-20T17:42:47.789-07:00[America/Denver]"
				}
			},
			{
				"name": "Between",
				"args": {
					"datetime1": "2017-01-20T17:42:47.789-07:00[America/Denver]",
					"datetime2": "2024-01-20T17:42:47.789-07:00[America/Denver]"
				}
			},
			{
				"name": "Header",
				"args": {
					"header": "X-Request-Id",
					"regexp": "\\d+"
				}
			},
			{
				"name": "Method",
				"args": {
					"methods": "GET"
				}
			},
			{
				"name": "Host",
				"args": {
					"patterns.0": "**.kk0.org",
					"patterns.1": "**.kk1.org"
				}
			},
			{
				"name": "Path",
				"args": {
					"matchTrailingSlash": "false",
					"patterns.0": "/service-api/**"
				}
			},
			{
				"name": "Query",
				"args": {
					"param": "number",
					"regexp": "\\d+"
				}
			},
			{
				"name": "RemoteAddr",
				"args": {
					"sources": "127.0.0.1"
				}
			}
		],
		"filters": [{
			"name": "RewritePath",
			"args": {
				"_genkey_0": "/service-api(?<segment>.*)",
				"_genkey_1": "/service-two$\\{segment}"
			}
		}],
		"uri": "lb://coding-cloud-service-two",
		"metadata": {

		},
		"order": 0
	}
]
```