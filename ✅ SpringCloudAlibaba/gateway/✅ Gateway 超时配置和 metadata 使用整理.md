# Gateway 超时配置和 metadata 使用整理

## 全局超时时间配置

### gateway.httpclient 配置

```java
package org.springframework.cloud.gateway.config;

@ConfigurationProperties("spring.cloud.gateway.httpclient")
@Validated
public class HttpClientProperties {

	/** The connect timeout in millis, the default is 45s. */
	private Integer connectTimeout;

	/** The response timeout. */
	private Duration responseTimeout;
	...
}
```

```yaml
spring:
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
      httpclient:
        pool:
          maxIdleTime: 2000
        # 连接超时时间ms
        connect-timeout: 3000
        # 响应超时时间ms
        response-timeout: 3000
```

## 局部超时时间配置

### 路由配置 metadata

> 作用与指定服务

#### json 配置

```json
[
  {
    "id": "coding-cloud-service",
    "predicates": [
      {
        "name": "Path",
        "args": {
          "_genkey_0": "/getOne"
        }
      }
    ],
    "filters": [
    ],
    "uri": "lb://coding-cloud-service",
    "metadata": {
      "response-timeout": 1000,
      "connect-timeout": 1000
    },
    "order": 0
  }
]
```

#### 路由定义 RouteDefinition

![](images/Pasted%20image%****************.png)

#### 使用位置 NettyRoutingFilter

> 这个 Filter 是真正执行路由服务接口的地方，这里已经获取具体的 ip 端口，要进行 http 请求，这里对 HttpClient 设置 metadata 配置的连接时间 connect-timeout 和获取响应超时时间 response-timeout，。

##### 获取 HttpClient 并设置超时时间

```java
	protected HttpClient getHttpClient(Route route, ServerWebExchange exchange) {
		// connect-timeout ms 不为空就设置，默认是45s
		// org.springframework.cloud.gateway.config.HttpClientProperties
		Object connectTimeoutAttr = route.getMetadata().get(CONNECT_TIMEOUT_ATTR);
		if (connectTimeoutAttr != null) {
			Integer connectTimeout = getInteger(connectTimeoutAttr);
			return this.httpClient.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeout);
		}
		return httpClient;
	}
```

##### 响应超时时间

```java
	private Duration getResponseTimeout(Route route) {
		// response-timeout
		Object responseTimeoutAttr = route.getMetadata().get(RESPONSE_TIMEOUT_ATTR);
		if (responseTimeoutAttr != null && responseTimeoutAttr instanceof Number) {
			Long routeResponseTimeout = ((Number) responseTimeoutAttr).longValue();
			if (routeResponseTimeout >= 0) {
				// 配置了是数字，大于0 返回毫秒
				return Duration.ofMillis(routeResponseTimeout);
			}
			else {
				return null;
			}
		}
		return properties.getResponseTimeout();
	}
```

## metadata 使用

-   设置两个超时时间
-   设置自定义属性，在后面再取出使用 route.getMetadata()
