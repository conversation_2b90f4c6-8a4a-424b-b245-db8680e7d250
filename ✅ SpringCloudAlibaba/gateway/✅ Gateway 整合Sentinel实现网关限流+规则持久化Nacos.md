# Gateway 整合Sentinel实现网关限流+规则持久化Nacos

## maven依赖

```xml
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
```

## yml配置sentinel

```yml
spring:
  cloud:
    sentinel:
      eager: true
      transport:
        dashboard: http://localhost:8099
      scg:
        fallback:
          mode: response
          response-body: '{"code": 500,"message": "请求失败，稍后重试！"}'
          response-status: 500
```

## sentinel网关限流规则

> https://kgithub.com/alibaba/Sentinel/wiki/网关限流

###  GatewayFlowRule 网关限流规则

#### 配置类

```java
public class GatewayFlowRule {

    private String resource;
    private int resourceMode = SentinelGatewayConstants.RESOURCE_MODE_ROUTE_ID;

    private int grade = RuleConstant.FLOW_GRADE_QPS;
    private double count;
    private long intervalSec = 1;

    private int controlBehavior = RuleConstant.CONTROL_BEHAVIOR_DEFAULT;
    private int burst;
    /**
     * For throttle (rate limiting with queueing).
     */
    private int maxQueueingTimeoutMs = 500;

    /**
     * For parameter flow control. If not set, the gateway rule will be
     * converted to normal flow rule.
     */
    private GatewayParamFlowItem paramItem;
}
```

```java
public class GatewayParamFlowItem {

    /**
     * Should be set when applying to parameter flow rules.
     */
    private Integer index;

    /**
     * Strategy for parsing item (e.g. client IP, arbitrary headers and URL parameters).
     */
    private int parseStrategy;
    /**
     * Field to get (only required for arbitrary headers or URL parameters mode).
     */
    private String fieldName;
    /**
     * Matching pattern. If not set, all values will be kept in LRU map.
     */
    private String pattern;
    /**
     * Matching strategy for item value.
     */
    private int matchStrategy = SentinelGatewayConstants.PARAM_MATCH_STRATEGY_EXACT;
}
```

#### GatewayFlowRule.json

```json
[
    {
        // 资源名称, routeId名称或API分组名称 (API 名称)
        "resource": "service-two",
        // 资源类型, 0 routeId, 1 API分组 (API 类型)
        "resourceMode": 1,
        // 突发请求时,额外允许的请求数目   (Burst size)
        "burst": 500,
        // 流控方式, 0 快速失败, 2 匀速排队  (阈值类型)
        "controlBehavior": 2,
        // 限流阈值,针对grade  (QPS 阈值 | 线程数)
        "count": 100.0,
        // 限流指标维度, 0 线程数, 1 qps  (阈值类型)
        "grade": 1,
        // 统计时间窗口,秒  (间隔)
        "intervalSec": 1,
        // 匀速排队下的最长排队时间,毫秒   (超时时间)
        "maxQueueingTimeoutMs": 500,
        // 参数限流配置(针对请求属性)
        "paramItem": {
            "index": 1,
            // 参数属性 0-Client IP    1-Remote Host    2-Header    3-URL 参数    4-Cookie
            "parseStrategy": 3,
            // 字段名称 ( Header-Header名称    URL参数-URL参数名称    Cookie-Cookie名称 )
            "fieldName": "number",
            // 参数值匹配模式, 0-精确, 3-子窜, 2-正则
            "matchStrategy": 0,
            // 匹配串
            "pattern": "1234"
        }

    }
]
```

#### 官方介绍

其中网关限流规则 `GatewayFlowRule` 的字段解释如下：

- `resource`：资源名称，可以是网关中的 route 名称或者用户自定义的 API 分组名称。

- `resourceMode`：

  - 规则是针对 API Gateway 的 route（`RESOURCE_MODE_ROUTE_ID -0`）默认是 route
  - 还是用户在 Sentinel 中定义的 API 分组（`RESOURCE_MODE_CUSTOM_API_NAME - 1`）。

- `grade`：限流指标维度，同限流规则的 `grade` 字段。

  - *QPS阈值维度	 FLOW_GRADE_THREAD* = 0 	
  - 线程数维度    *FLOW_GRADE_QPS* = 1

- `count`：限流阈值

- `intervalSec`：统计时间窗口，单位是秒，默认是 1 秒。

- `controlBehavior`：流量整形的控制效果，同限流规则的 `controlBehavior` 字段，目前支持快速失败和匀速排队两种模式，默认是快速失败。

  - 快速失败    *CONTROL_BEHAVIOR_DEFAULT* = 0
  - 匀速排队    *CONTROL_BEHAVIOR_RATE_LIMITER* = 2

- `burst`：应对突发请求时额外允许的请求数目。

- `maxQueueingTimeoutMs`：匀速排队模式下的最长排队时间，单位是毫秒，仅在匀速排队模式下生效。

- `paramItem`

  ：参数限流配置。若不提供，则代表不针对参数进行限流，该网关规则将会被转换成普通流控规则；否则会转换成热点规则。其中的字段：

  - `parseStrategy`：从请求中提取参数的策略，
    - 提取来源 IP（`PARAM_PARSE_STRATEGY_CLIENT_IP - 0`）、
    - Host（`PARAM_PARSE_STRATEGY_HOST - 1` ）、
    - 任意 Header（`PARAM_PARSE_STRATEGY_HEADER - 2`）
    - 任意 URL 参数（`PARAM_PARSE_STRATEGY_URL_PARAM - 3`）
    - 任意Cookie（`PARAM_PARSE_STRATEGY_COOKIE - 4`）
  - `fieldName`：若提取策略选择 Header 模式或 URL 参数模式，则需要指定对应的 header 名称或 URL 参数名称。
  - `pattern`：参数值的匹配模式，只有匹配该模式的请求属性值会纳入统计和流控；若为空则统计该请求属性的所有值。（1.6.2 版本开始支持）
  - `matchStrategy`：参数值的匹配策略，
    - 目前支持精确匹配（`PARAM_MATCH_STRATEGY_EXACT - 0`）、
    - 子串匹配（`PARAM_MATCH_STRATEGY_CONTAINS - 3`）
    - 和正则匹配（`PARAM_MATCH_STRATEGY_REGEX - 2`）。（1.6.2 版本开始支持）

### ApiDefinition API自定义分组

#### 配置类

```java
public class ApiDefinition {

    private String apiName;
    private Set<ApiPredicateItem> predicateItems;
}

public class ApiPathPredicateItem implements ApiPredicateItem {

    private String pattern;
    private int matchStrategy = SentinelGatewayConstants.URL_MATCH_STRATEGY_EXACT;
}
```

#### ApiDefinition.json

```json
[
  {
    // API 名称
    "apiName": "service-two",
    "predicateItems": [
      {
        // 匹配串
        "pattern": "/service-api/**",
        // 匹配模式, 0-精确, 1-前缀, 2-正则
        "matchStrategy": 2
      },
      {
        "pattern": "/service-api",
        "matchStrategy": 1
      },
      {
        "pattern": "/service-api/getTwo",
        "matchStrategy": 0
      }
    ]
  }
]

```

## sentine流控规则持久化nacos

### API分组类

```java
@Data
public class ApiPathDefinition {

    private String apiName;

    private Set<ApiPathPredicateItem> predicateItems;

    @Data
    public static class ApiPathPredicateItem {

        private String pattern;

        private int matchStrategy;

    }

}
```

### 监控nacos配置

```java
@Configuration
@RequiredArgsConstructor
public class GatewayFlowRuleConfig implements CommandLineRunner {

    private final NacosConfigProperties nacosConfigProperties;

    /**
     * 规则动态配置
     * <a href="https://github.com/alibaba/Sentinel/wiki/%E5%8A%A8%E6%80%81%E8%A7%84%E5%88%99%E6%89%A9%E5%B1%95">...</a>
     */
    @Override
    public void run(String... args) throws Exception {
        /**
         * 流控规则
         * @see com.alibaba.csp.sentinel.slots.block.RuleConstant
         */

        Properties properties = nacosConfigProperties.assembleConfigServiceProperties();
        // API分组
        ReadableDataSource<String, Set<ApiDefinition>> apiDefinition = new NacosDataSource<>(properties, nacosConfigProperties.getGroup(),
                "ApiDefinition.json",
                new Converter<String, Set<ApiDefinition>>() {
                    @Override
                    public Set<ApiDefinition> convert(String source) {
                        List<ApiPathDefinition> lists = JSONObject.parseArray(source, ApiPathDefinition.class);

                        Set<ApiDefinition> resultSet = new HashSet<>();
                        for (ApiPathDefinition apiPathDefinition : lists) {
                            ApiDefinition apiDefinition = new ApiDefinition();
                            apiDefinition.setApiName(apiPathDefinition.getApiName());
                            Set<ApiPathDefinition.ApiPathPredicateItem> itemSet = apiPathDefinition.getPredicateItems();
                            Set<ApiPredicateItem> predicateItems = new HashSet<>();
                            for (ApiPathDefinition.ApiPathPredicateItem apiPathPredicateItem : itemSet) {
                                ApiPathPredicateItem apiPredicateItem = new ApiPathPredicateItem();
                                apiPredicateItem.setPattern(apiPathPredicateItem.getPattern());
                                apiPredicateItem.setMatchStrategy(apiPathPredicateItem.getMatchStrategy());
                                predicateItems.add(apiPredicateItem);
                            }
                            apiDefinition.setPredicateItems(predicateItems);
                            resultSet.add(apiDefinition);
                        }
                        return resultSet;
                    }
                });

        GatewayApiDefinitionManager.register2Property(apiDefinition.getProperty());

        // 限流规则
        ReadableDataSource<String, Set<GatewayFlowRule>> gatewayFlowRule =
                new NacosDataSource<>(properties, nacosConfigProperties.getGroup(),
                        "GatewayFlowRule.json",
                        source -> JSON.parseObject(source, new TypeReference<Set<GatewayFlowRule>>() {
                        }));
        GatewayRuleManager.register2Property(gatewayFlowRule.getProperty());

    }

}
```

## 网关流控实现原理

当通过 `GatewayRuleManager` 加载网关流控规则（`GatewayFlowRule`）时，无论是否针对请求属性进行限流，Sentinel 底层都会将网关流控规则转化为热点参数规则（`ParamFlowRule`），存储在 `GatewayRuleManager` 中，与正常的热点参数规则相隔离。转换时 Sentinel 会根据请求属性配置，为网关流控规则设置参数索引（`idx`），并同步到生成的热点参数规则中。

外部请求进入 API Gateway 时会经过 Sentinel 实现的 filter，其中会依次进行 **路由/API 分组匹配**、**请求属性解析**和**参数组装**。Sentinel 会根据配置的网关流控规则来解析请求属性，并依照参数索引顺序组装参数数组，最终传入 `SphU.entry(res, args)` 中。Sentinel API Gateway Adapter Common 模块向 Slot Chain 中添加了一个 `GatewayFlowSlot`，专门用来做网关规则的检查。`GatewayFlowSlot` 会从 `GatewayRuleManager` 中提取生成的热点参数规则，根据传入的参数依次进行规则检查。若某条规则不针对请求属性，则会在参数最后一个位置置入预设的常量，达到普通流控的效果。

![image-20230602205420171](images/image-20230602205420171.png)

## sentinel dashboard

### 标记gateway

```shell
# 注：通过 Spring Cloud Alibaba Sentinel 自动接入的 API Gateway 整合则无需此参数
-Dcsp.sentinel.app.type=1
```

### 实时监控

![image-20230602203627540](images/image-20230602203627540.png)

### 请求链路

![image-20230602203417785](images/image-20230602203417785.png)

### API分组管理

![image-20230602203438092](images/image-20230602203438092.png)

#### 新增分组

![image-20230602203743914](images/image-20230602203743914.png)

### 网关流控管理

![image-20230602203553748](images/image-20230602203553748.png)

#### **新增网关流控规则**

![image-20230602203826749](images/image-20230602203826749.png)
