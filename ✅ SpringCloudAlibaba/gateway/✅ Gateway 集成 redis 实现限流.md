# Gateway 集成 Redis 实现限流

## 基于 RequestRateLimiterGatewayFilterFactory

### yml 配置

```yaml
          filters:
            - RewritePath=/service-api(?<segment>.*), /service-two$\{segment}
            - name: RequestRateLimiter
              args:
                # 允许用户每秒执行的请求数
                redis-rate-limiter.replenishRate: 1
                # 允许用户在一秒钟内执行的最大请求数
                redis-rate-limiter.burstCapacity: 1
                # 每个请求从存储桶中获取的令牌数
                redis-rate-limiter.requestedTokens: 1
```

### 核心源码

#### RequestRateLimiterGatewayFilterFactory@apply

```java
	@Override
	public GatewayFilter apply(Config config) {
        // 限流key定义
		KeyResolver resolver = getOrDefault(config.keyResolver, defaultKeyResolver);
		// 限流处理器
        RateLimiter<Object> limiter = getOrDefault(config.rateLimiter, defaultRateLimiter);
		boolean denyEmpty = getOrDefault(config.denyEmptyKey, this.denyEmptyKey);
		HttpStatusHolder emptyKeyStatus = HttpStatusHolder
				.parse(getOrDefault(config.emptyKeyStatus, this.emptyKeyStatusCode));

		return (exchange, chain) -> resolver.resolve(exchange).defaultIfEmpty(EMPTY_KEY).flatMap(key -> {
			if (EMPTY_KEY.equals(key)) {
				if (denyEmpty) {
					setResponseStatus(exchange, emptyKeyStatus);
					return exchange.getResponse().setComplete();
				}
				return chain.filter(exchange);
			}
			String routeId = config.getRouteId();
			if (routeId == null) {
				Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
				routeId = route.getId();
			}
            // 根据路由获取限流参数，执行相应的限流处理方法
			return limiter.isAllowed(routeId, key).flatMap(response -> {

				for (Map.Entry<String, String> header : response.getHeaders().entrySet()) {
					exchange.getResponse().getHeaders().add(header.getKey(), header.getValue());
				}

				if (response.isAllowed()) {
					return chain.filter(exchange);
				}

				setResponseStatus(exchange, config.getStatusCode());
				return exchange.getResponse().setComplete();
			});
		});
	}
```

![image-20230529221750269](images/image-20230529221750269.png)

#### RedisRateLimiter#isAllowed

> 使用令牌桶算法，每次请求消耗令牌桶中的令牌，如设置每次请求消耗一个令牌，桶令牌消耗完后，请求拒绝。

```java
	@Override
	@SuppressWarnings("unchecked")
	public Mono<Response> isAllowed(String routeId, String id) {
		if (!this.initialized.get()) {
			throw new IllegalStateException("RedisRateLimiter is not initialized");
		}

		Config routeConfig = loadConfiguration(routeId);

		// How many requests per second do you want a user to be allowed to do?
        // 每秒请求数
		int replenishRate = routeConfig.getReplenishRate();

		// How much bursting do you want to allow?
        // 突增 允许用户在一秒钟内执行的最大请求数
		int burstCapacity = routeConfig.getBurstCapacity();

		// How many tokens are requested per request?
        // 每次请求需要几个token
		int requestedTokens = routeConfig.getRequestedTokens();

		try {
            // redis key, lua脚本参数
			List<String> keys = getKeys(id);

			// The arguments to the LUA script. time() returns unixtime in seconds.
            // lua参数
			List<String> scriptArgs = Arrays.asList(replenishRate + "", burstCapacity + "", "", requestedTokens + "");
			// allowed, tokens_left = redis.eval(SCRIPT, keys, args)
            // 执行lua限流脚本
			Flux<List<Long>> flux = this.redisTemplate.execute(this.script, keys, scriptArgs);
			// .log("redisratelimiter", Level.FINER);
			return flux.onErrorResume(throwable -> {
				if (log.isDebugEnabled()) {
					log.debug("Error calling rate limiter lua", throwable);
				}
				return Flux.just(Arrays.asList(1L, -1L));
			}).reduce(new ArrayList<Long>(), (longs, l) -> {
				longs.addAll(l);
				return longs;
			}).map(results -> {
                // lua执行结果
				boolean allowed = results.get(0) == 1L;
				Long tokensLeft = results.get(1);
				// 设置结果，和header参数
				Response response = new Response(allowed, getHeaders(routeConfig, tokensLeft));

				if (log.isDebugEnabled()) {
					log.debug("response: " + response);
				}
				return response;
			});
		}
		catch (Exception e) {
			/*
			 * We don't want a hard dependency on Redis to allow traffic. Make sure to set
			 * an alert so you know if this is happening too much. Stripe's observed
			 * failure rate is 0.01%.
			 */
			log.error("Error determining if user allowed from redis", e);
		}
		return Mono.just(new Response(true, getHeaders(routeConfig, -1L)));
	}

```

![image-20230529221734434](images/image-20230529221734434.png)

#### redis key 定义

```java
	static List<String> getKeys(String id) {
		// use `{}` around keys to use Redis Key hash tags
		// this allows for using redis cluster

		// Make a unique key per user.
		String prefix = "request_rate_limiter.{" + id;

		// You need two Redis keys for Token Bucket.
		String tokenKey = prefix + "}.tokens";
		String timestampKey = prefix + "}.timestamp";
		return Arrays.asList(tokenKey, timestampKey);
	}
```

#### lua 脚本

![image-20230529221500619](images/image-20230529221500619.png)

```lua
redis.replicate_commands()

-- request_rate_limiter.{id}.tokens
local tokens_key = KEYS[1]
-- request_rate_limiter.{id}.timestamp
local timestamp_key = KEYS[2]
--redis.log(redis.LOG_WARNING, "tokens_key " .. tokens_key)

-- 每秒执行的请求数
local rate = tonumber(ARGV[1])
-- 一秒内执行的最大请求数
local capacity = tonumber(ARGV[2])
local now = redis.call('TIME')[1]
-- 每个请求从令牌桶中获取的令牌数
local requested = tonumber(ARGV[4])

local fill_time = capacity/rate
local ttl = math.floor(fill_time*2)

--redis.log(redis.LOG_WARNING, "rate " .. ARGV[1])
--redis.log(redis.LOG_WARNING, "capacity " .. ARGV[2])
--redis.log(redis.LOG_WARNING, "now " .. now)
--redis.log(redis.LOG_WARNING, "requested " .. ARGV[4])
--redis.log(redis.LOG_WARNING, "filltime " .. fill_time)
--redis.log(redis.LOG_WARNING, "ttl " .. ttl)

local last_tokens = tonumber(redis.call("get", tokens_key))
if last_tokens == nil then
  last_tokens = capacity
end
--redis.log(redis.LOG_WARNING, "last_tokens " .. last_tokens)

local last_refreshed = tonumber(redis.call("get", timestamp_key))
if last_refreshed == nil then
  last_refreshed = 0
end
--redis.log(redis.LOG_WARNING, "last_refreshed " .. last_refreshed)

local delta = math.max(0, now-last_refreshed)
local filled_tokens = math.min(capacity, last_tokens+(delta*rate))
local allowed = filled_tokens >= requested
local new_tokens = filled_tokens
local allowed_num = 0
if allowed then
  new_tokens = filled_tokens - requested
  allowed_num = 1
end

--redis.log(redis.LOG_WARNING, "delta " .. delta)
--redis.log(redis.LOG_WARNING, "filled_tokens " .. filled_tokens)
--redis.log(redis.LOG_WARNING, "allowed_num " .. allowed_num)
--redis.log(redis.LOG_WARNING, "new_tokens " .. new_tokens)

if ttl > 0 then
  redis.call("setex", tokens_key, ttl, new_tokens)
  redis.call("setex", timestamp_key, ttl, now)
end

-- return { allowed_num, new_tokens, capacity, filled_tokens, requested, new_tokens }
return { allowed_num, new_tokens }

```

### 自定义 KeyResolver

```java
@Component
public class HostAddrKeyResolver implements KeyResolver {
    @Override
    public Mono<String> resolve(ServerWebExchange exchange) {
        return Mono.just(exchange.getRequest().getRemoteAddress().getAddress().getHostAddress());
    }
}
```

![image-20230529222800615](images/image-20230529222800615.png)

```yaml
          filters:
            # /service-api/getTwo -> /service-two/getTwo
            - RewritePath=/service-api(?<segment>.*), /service-two$\{segment}
            - name: RequestRateLimiter
              args:
                # 允许用户每秒执行的请求数
                redis-rate-limiter.replenishRate: 1
                # 允许用户在一秒钟内执行的最大请求数
                redis-rate-limiter.burstCapacity: 1
                # 每个请求从存储桶中获取的令牌数
                redis-rate-limiter.requestedTokens: 1
                # 自定义key
                key-resolver: "#{@hostAddrKeyResolver}"
```

### 自定义rate-limiter

```yaml
spring:
  cloud:
    gateway:
      routes:
      - id: requestratelimiter_route
        uri: https://example.org
        filters:
        - name: RequestRateLimiter
          args:
            rate-limiter: "#{@myRateLimiter}"
            key-resolver: "#{@userKeyResolver}"
```

