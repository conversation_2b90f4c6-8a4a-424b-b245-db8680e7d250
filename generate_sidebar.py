import os
import urllib.parse

docs_dir = "java-learning-knowledge-base"
sidebar_file = os.path.join(docs_dir, "_sidebar.md")

def generate_sidebar():
    sidebar_content = []
    processed_dirs = set()

    for root, _, files in os.walk(docs_dir):
        files = sorted(f for f in files if f.endswith(".md") and f != "_sidebar.md")
        if not files:
            continue

        depth = root.count(os.sep) - docs_dir.count(os.sep)
        indent = "  " * depth

        # 添加目录名称（不带链接）
        if root != docs_dir and root not in processed_dirs:
            sidebar_content.append(f"{indent}- {os.path.basename(root)}")
            processed_dirs.add(root)

        # 添加 Markdown 文件（不带 ./，并进行 URL 编码）
        for file in files:
            rel_path = os.path.relpath(os.path.join(root, file), docs_dir)
            title = file.replace(".md", "").capitalize()
            encoded_path = urllib.parse.quote(rel_path)
            sidebar_content.append(f"{indent}  - [{title}]({encoded_path})")

    with open(sidebar_file, "w", encoding="utf-8") as f:
        f.write("\n".join(sidebar_content))

generate_sidebar()
print("✅ _sidebar.md 生成完成！")

