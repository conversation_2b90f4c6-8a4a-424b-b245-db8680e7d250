## 0604

## 0603

- [ ] SpringBoot 过滤器、拦截器、监听器使用与原理
- [ ] OpenFeign 整合 Sentinel 实现服务降级
- [ ] OpenFeign 拦截器使用

## 0602

-   [ ] ES 倒排索引理解
-   [x] Gateway 集成 Sentinel 实现限流
-   [x] Sentinel 限流等规则初始化（nacos）

## 0601

-   [x] docker-compose 部署 SpringBoot 服务整理

## 0531

-   [x] Gateway 超时配置和 metadata 使用整理
-   [x] Gateway 内置全局过滤器 GlobalFilter

## 0530

## 0529

-   [x] Spring Cloud Gateway 集成 Redis 实现限流 RequestRateLimiterGatewayFilterFactory
-   [x] ES Bulk 批量操作方式
