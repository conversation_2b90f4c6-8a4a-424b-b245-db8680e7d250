## 0528

- [ ] MyBatis 一级缓存、二级缓存整理

## 0527

- [x] SpringBoot整合单机锁、各种分布式锁实现 50%
- [ ] Spring Cloud Gateway 内置全局过滤器GlobalFilter了解整理
- [ ] Spring Cloud Gateway 内置局部过滤器GatewayFilter了解整理
- [ ] Spring Cloud Gateway 获取请求体 json 参数和响应结果参数
- [ ] Spring Cloud Gateway 收到设置 route 转发
- [ ] Spring Cloud Gateway 路由配置实现方式（yaml、Java代码硬编码、动态路由）
- [ ] Spring Cloud Gateway 动态路由实现（nacos、redis、mysql存储）
- [ ] Spring Cloud Gateway超时配置和metadata使用整理

## 0526

- [x] Spring Cloud Gateway 使用 Grafana 监控
- [x] Spring Cloud Gateway 内置路由断言 Predicate 使用

## 0525

## 0524

- [x] SpringBoot validation 使用分析
- [x] Maven 生命周期命令使用
- [x] Git 常用命令整理

## 0523

- [x] ES 分页查询方法整理
- [x] SpringBoot 使用 Redis bitmap 实现签到功能
- [x] easy excel 实现简单文件导入导出

## 0522

- [x] SpringBoot 集成 flyway 数据库版本
- [x] 唯一索引和普通索引，插入，查询的效率对比如何 ​
- [x] 百万数据，分页查询优化
