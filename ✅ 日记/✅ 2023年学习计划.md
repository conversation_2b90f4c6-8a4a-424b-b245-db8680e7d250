

# 202310

- [ ] Springboot logback log4j2 异步日志配置整理
- [ ] 火焰图了解  如何生成

#  算法

常用算法学习与实现

# 排序

- [ ] 堆排序（Heap Sort）
- [ ] 整理插入、归并排序算法笔记
- [ ] 整理冒泡、插入排序算法笔记

# 负载均衡

# Java 基础知识点深入

- [ ] HashMap 扩容机制，源码理解
- [ ] JUC 一些类使用及原理深入

# 常用设计模式

- [x] pie 实现责任链模式（知道就行）

# MySQL 完整笔记整理

- [ ] 主从复制原理整理
- [x] 唯一索引和普通索引，插入，查询的效率对比如何 0522
- [x] 百万数据，分页查询优化 0522

# Spring 深入理解

- [ ] https://developer.jdcloud.com/article/2808 0 源码基础学习 Spring 源码系列（一）——Bean 注入流程

# SpringBoot 原理深入

- [ ] SpringBoot run 方法启动流程整理
- [ ] Spring Bean refresh 方法
- [x] SpringBoot 集成 flyway 数据库版本 0522
- [ ] SpringBoot 过滤器、拦截器、监听器使用与原理
- [x] SpringBoot 接口加解密（RSA、AES 等）demo
- [x] SpringBoot 使用 gRPC 实现 rpc 接口调用-入门
- [x] 整理 Springboot 一些统一设置，如统一返回值，全局异常等
- [x] SpringBoot 整合 JWT 使用
- [ ] SpringBoot 实现接口幂等性
- [x] SpringBoot 使用 Redis bitmap 实现签到功能
- [x] 测试下 SpringBoot Tomcat undertow 请求数超过线程数后会怎样
- [x] SpringBoot 参数校验，注解实现，手动调用 API 实现
- [ ] SpringBoot 整合 drools 使用
- [x] SpringBoot 整合单机锁、各种分布式锁实现
- [ ] Spring Batch 使用 https://mp.weixin.qq.com/s/Ol9dZR6FOHwHjwMFJdeEtw
- [ ] SpringBoot 实现 Actuator 接口重新刷新上下文，实现 Bean 刷新（如 mybatis 数据源修改刷新相关 bean，不用重启服务）

# Dubbo3.x

- [ ] SpringBoot集成 yml配置了解， 常用属性
- [ ] Dubbo集成Nacos配置中心使用
- [x] Dubbo集成Nacos注册中心，
- [x] Nacos管理页面动态上下线服务测试
- [x] Dubbo官网文档 从头跟着了解一遍 https://cn.dubbo.apache.org/zh-cn/overview/tasks/
- [ ] Prometheus 和grafana监控Dubbo的信息
- [ ] Dubbo 应用级和服务级 服务发现有什么区别?  只注册应用, 能否行?
- [ ] Dubbo使用Nacos实现配置中心, 整个配置文件放到Nacos yaml
- [ ] Dubbot元数据中心有什么用? 不使用行不行? 3.1.10版本关闭元数据中心没问题
- [ ] Dubbo自定义Filter和ClusterFilter
- [ ] Dubbo各个负载均衡算法了解
- [x] Dubbo 多注册中心, ZoneAware
- [ ] Dubbo优雅停机

# Spring Cloud

## Gateway

- [x] Gateway 内置路由断言 Predicate 使用
- [x] Gateway 内置全局过滤器 GlobalFilter 了解整理
- [ ] Gateway 内置局部过滤器 GatewayFilter 了解整理
- [x] Gateway 获取请求体 json 参数和响应结果参数
- [ ] Gateway 手动设置 route 转发
- [x] Gateway 路由配置实现方式（yaml、Java 代码硬编码、动态路由）
- [x] Gateway 动态路由实现（nacos、redis、mysql 存储）
- [x] Gateway 超时配置和 metadata 使用整理
- [x] Gateway 集成 Redis 实现限流 RequestRateLimiterGatewayFilterFactory
- [x] Gateway 集成 Sentinel 实现限流

## Ribbon

- [ ] Ribbon 各种超时时间配置，其他配置，负载均衡算法
- [ ] Ribbon + RestTemplate 使用原理分析
- [ ] Ribbon 如何获取服务实例列表

## OpenFeign

- [ ] OpenFeign 整合 Sentinel 实现服务降级
- [ ] OpenFeign 整合 Hystrix 实现服务降级
- [ ] OpenFeign 拦截器使用
- [ ] OpenFeign 是如何获取服务实例列表

## Hystrix

- [ ] hystrix 熔断器使用

## Sentinel

- [x] Sentinel 限流规则初始化（nacos）

## Nacos

- [ ] Nacos2.2.x版本搭建

## 链路追踪

- [ ] 服务追踪组件 zipkin，Spring Cloud Sleuth 集成了 zipkin 组件

# MyBatis

- [ ] MyBatis 一级缓存、二级缓存整理
- [ ] SpringBoot 多数据源实现，主从，读写分离实现
- [x] SpringBoot 使用 Druid 连接池，监控等
- [ ] MyBatis 拦截器实现
  - [x] 字段自动填充
  - [ ] 字段脱敏
  - [x] 主键自动生成
  - [ ] 字段加密
  - [ ] SQL 日志打印、耗时打印
  - [ ] 分页功能

# RocketMQ 系统学习

- [ ] github 仓库 md 文档
- [x] RocketMQ延时消息发送, 消费入库, 看下是否可以实时入库
- [x] 本地开发时, 消息发送成功后, 消费者有时候不会立即消费, 管理页面上队列没有消费终端?
- [x] RocketMQ连接ak sk鉴权

# ES 学习笔记整理

- [ ] https://pdai.tech/md/db/nosql-es pdai 网站 es 部分
- [ ] ES 高级 API Springboot 使用，聚合，搜索等
- [x] ES 分页查询方法整理
- [x] ES Bulk 批量操作方式
- [ ] ES 倒排索引理解

# Kafka 学习笔记整理

- [ ] https://kafka.apachecn.org/documentation.html#过一遍

# Redis

- [ ] redis 跳表 skiplist 理解，如 zset 实现原理，如何查询前 3 原理



# 扩展、设计

- [x] easy excel 实现简单文件导入导出
- [ ] excel 导入导出 poi easy excel 对比大文件 内存占用
- [ ] 分布式事务 Seata demo
- [x] ThreadLocal、TransmittableThreadLocal 等高级使用，多线程数据传递
- [ ] 时间轮，多级时间轮实现和原理了解
- [ ] 
- [x] Maven 生命周期命令使用
  - [x] package 命令
- [ ] Maven 插件 demo 编写
- [ ] arthas 常用命令整理笔记
- [x] Git 常用命令整理，命令行使用
- [ ] 微信扫码登录实现 demo
- [ ] 动态线程池简单使用 dynamicTp
- [ ] 流量录制、回放
- [x] Spring Cloud Allbaba 集成 Dubbo 父子工程项目模板搭建

# Netty+WebSocket

- [x] Netty 服务端、客户端 demo
- [x] Netty 实现 WebSocket，连接、发消息，Nginx 配置 ws，客户端如何知道请求哪个服务器
- [x] Netty 实现 WebSocket 的 URI 如何带上 Path 参数，如/msg/{userId}
- [ ] Netty 实现 WebSocket 集群，节点挂掉，channel 连接自动关闭，如何解决？channel 和 userId 关联，客户端自动重连，userId 绑定新的 channel？
- [ ] Netty 服务 ws 注册到 Nacos，动态上下线结合 Gateway 转发 ws
- [ ] Spring WebSocket Demo 实现 
- [ ] Spring Websocket 集群 注解实现集群部署  https://juejin.cn/post/7264183910598541367
- [x] Nginx转发Netty 如何获取客户端真实IP
- [x] Netty实现连接鉴权和获取客户端IP

# 运维

- [x] Jenkins 部署 SpringBoot 服务整理-Jar 启动方式
- [x] Jenkins 部署 SpringBoot 服务整理-Docker 启动方式
