| 日期                 | 生活  | 学习                                             | 工作  | 收获  |
| ------------------ | --- | :--------------------------------------------- | --- | --- |
| 2023-06-17<br />周六 | 休息  | Spring Cloud Allbaba 集成 Dubbo 父子工程项目模板, 补充通用模板 | -   |     |
| 2023-06-18<br />周日 | 休息  | -                                              | -   |     |
| 2023-06-19<br />周一 | -   | -                                              | -   |     |
| 2023-06-20<br />周二 | -   | 一些组件starter                                    | -   |     |
| 2023-06-21<br />周三 | -   | Sharding-JDBC5.x使用分表自定义算法                      |     |     |
| 2023-06-22<br />周四 | 端午  |                                                |     |     |
| 2023-06-23<br />周五 | 端午  |                                                |     |     |
| 2023-06-24<br />周六 | 端午  |                                                |     |     |
| 2023-06-25<br />周日 | -   |                                                |     |     |
| 2023-06-26<br />周一 | -   |                                                |     |     |
| 2023-06-27<br />周二 |     | dubbo多注册中心和服务分钟使用                              |     |     |
| 2023-06-28<br />周三 | -   |                                                |     |     |
| 2023-06-29<br />周四 | -   |                                                |     |     |
| 2023-06-30<br />周五 | -   |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |
|                    |     |                                                |     |     |

