# Linux服务器屏蔽国外IP方法

## 获取国内ip列表

```shell
wget -q --timeout=60 -O- 'http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest' | awk -F\| '/CN\|ipv4/ { printf("%s/%d\n", $4, 32-log($5)/log(2)) }' > /root/china_ip.txt

```

## 将下面脚本保存为/root/allcn.sh

```shell

mmode=$1
CNIP="/root/china_ip.txt"
gen_iplist() {
        cat <<-EOF
                $(cat ${CNIP:=/dev/null} 2>/dev/null)
EOF
}

flush_r() {
iptables  -F ALLCNRULE 2>/dev/null
iptables -D INPUT -p tcp -j ALLCNRULE 2>/dev/null
iptables  -X ALLCNRULE 2>/dev/null
ipset -X allcn 2>/dev/null
}

mstart() {
ipset create allcn hash:net 2>/dev/null
ipset -! -R <<-EOF
$(gen_iplist | sed -e "s/^/add allcn /")
EOF

iptables -N ALLCNRULE
iptables -I INPUT -p tcp -j ALLCNRULE
iptables -A ALLCNRULE -s *********/8 -j RETURN
iptables -A ALLCNRULE -s ***********/16 -j RETURN
iptables -A ALLCNRULE -s *********/4 -j RETURN
iptables -A ALLCNRULE -s *************** -j RETURN
iptables -A ALLCNRULE -m set --match-set allcn  src -j RETURN
iptables -A ALLCNRULE -p tcp -j DROP
}

if [ "$mmode" == "stop" ] ;then
flush_r
exit 0
fi

flush_r
sleep 1
mstart
```

## 将该脚本设置为可执行

```shell
chmod +x  allcn.sh
```

## 执行如下命令即可开始屏蔽国外 IP 访问。

```shell
/root/allcn.sh
```

## 执行如下命令即可停止屏蔽国外 IP 访问。

```shell
/root/allcn.sh stop
```
