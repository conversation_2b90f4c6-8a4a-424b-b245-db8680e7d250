## ntp

```
 yum -y install ntp
 
systemctl enable ntpd
systemctl start ntpd

ntpdate -u cn.pool.ntp.org

echo "*/20 * * * * /usr/sbin/ntpdate -u ntp1.aliyun.com >/dev/null &" >> /var/spool/cron/root

cn.pool.ntp.org  中国开源免费NTP服务器
ntp1.aliyun.com 阿里云NTP服务器
ntp2.aliyun.com 阿里云NTP服务器
time1.aliyun.com 阿里云NTP服务器
time2.aliyun.com 阿里云NTP服务器

time1.apple.com 苹果NTP服务器
time2.apple.com 苹果NTP服务器
time3.apple.com 苹果NTP服务器
time4.apple.com 苹果NTP服务器
time5.apple.com 苹果NTP服务器
time1.google.com 谷歌NTP服务器
time2.google.com 谷歌NTP服务器
time3.google.com 谷歌NTP服务器
time4.google.com 谷歌NTP服务器
pool.ntp.org 开源免费NTP服务器
```

