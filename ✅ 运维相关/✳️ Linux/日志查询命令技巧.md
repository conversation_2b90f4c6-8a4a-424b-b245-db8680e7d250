# 日志查询命令技巧

## tail

> 过滤delay后面数字大于5000

```shell
tail -100f /home/<USER>/logs/cloudcc-call-control/cloudcc-call-control-warn.log | grep delay | awk -F'delay: ' '$2 > 5000'

```

## 按某个值统计数量

```shell
 awk '/delay: [0-9]+/ {match($0, /delay: ([0-9]+)/, arr); delay=int(arr[1]); range_start=int(delay/1000)*1000; range_end=range_start+999; if(range_start == 0){counts["0-999"]++} else {counts[range_start "-" range_end]++}} END {for (range in counts) print range ": " counts[range]}' /home/<USER>/logs/cloudcc-call-control/cloudcc-call-control-warn.log

```
