# 1. 日志常用命令

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875897-881b389d-861e-49d8-a258-5224be18f01f.png#align=left&display=inline&height=756&originHeight=756&originWidth=1043&size=0&status=done&style=shadow&width=1043)

# 2. 查询端口

```shell
1. 查找被占用的端口
netstat -tln | grep 端口号
2. 查看被占用端口的PID
lsof -i:8000
```

# 3. shell 脚本格式转 unix

```shell
# 查看文件格式
vim
:set ff

fileformat=unix

:set ff=unix
```

# 4. 文本内容批量替换

oldStr替换为newStr

```shell
sed -i 's/oldStr/newStr/g' start*
```

# 5. 查询文件夹大小

```shell
# 查看当前目录下所有文件夹大小
du -sh *

```

![image-20230722214108598](images/image-20230722214108598.png)

```shell
# 查询特定目录的大小
du -h /path/to/your/directory

# 仅显示总大小，不显示子目录大小：
du -sh /path/to/your/directory

# 列出目录中每个子目录的大小
du -h --max-depth=1 /path/to/your/directory

```

# 6. 网卡速率, 进程网速查询

```
yunm install -y jnettop 
jnettop

# 执行以下命令，查看占用内网带宽的进程。
nethogs eth0

# 查询间隔(-d)5秒
nethogs eth1 -d 5
```

## 7. Linux释放内存

释放网页缓存(To free pagecache):

```ruby
sync; echo 1 > /proc/sys/vm/drop_caches
```

释放目录项和索引(To free dentries and inodes):

```ruby
sync; echo 2 > /proc/sys/vm/drop_caches
```

释放网页缓存，目录项和索引（To free pagecache, dentries and inodes）:

```ruby
sync; echo 3 > /proc/sys/vm/drop_caches
```

# 删除软连接

```shell
 unlink link
 unlink docker
```

centos系统，如果使用 rm -rf 软连接/ 的时候会将软连接指定的目录下的文件递归删除，不带提示！！！

罪魁祸首有两个：

rm 的f参数
软连接后面的 /
所以正确 安全 的删除软连接的方式是 unlink 软连接，这个时候如果 shell 自动补全后面的 / ，unlink 命令会提示你删除不了。
其次使用rm 删除的时候不要加参数 f ，这个时候即使你带上了后面的 / ，rm也会提醒你没有这个目录。
![](./images/1706687708937.png)

# top显示不全

```shell
top -b -n 1 -w 512 -c
```

# 清空日志文件内容

```shell
cat /dev/null > cloudcc-call-control-info.log

docker exec lc-cloudcc-call-control cat /dev/null > /home/<USER>/logs/cloudcc-call-control/cloudcc-call-control-warn.log
```
