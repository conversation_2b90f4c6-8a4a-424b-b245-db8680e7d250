## 查询本地端口连接数 3300端口

```shell
netstat -tpn | grep 3300 | grep -i estab | awk '{print $4}' | cut -d ':' -f 1 | awk '{a[$1]=a[$1] + 1} END {for (i in a) if (a[i] > 0) print i " " a[i]}'
```

```shell
172.16.251.72 2233
```

- netstat -tpn：显示系统的网络连接信息，包括TCP连接的相关信息。
- grep 3300：过滤出本地端口为3300的连接信息。
- grep -i estab：过滤出状态为ESTABLISHED的连接，表示已建立的连接。
- awk '{print $4}'：提取出连接的本地地址和端口信息。
- cut -d ':' -f 1：使用冒号分割取出本地地址部分。
- awk '{a[$1]=a[$1] + 1} END {for (i in a) if (a[i] > 0) print i " " a[i]}'：统计每个本地地址的连接数，并输出结果。

> 执行这个命令后，会列出连接到本地端口3300且状态为ESTABLISHED的连接的来源IP地址以及每个IP地址的连接数。

## 查询本地端口被哪些服务器连接 每个IP的连接数

```shell
 netstat -ntp | grep ':3300' | grep ESTABLISHED | awk '{print $5}' | cut -d ':' -f 1 | sort | uniq -c
```

```shell
     101 ************
     20 *************
    100 *************
     10 *************
     20 *************
     30 *************
     10 **************
     10 **************
      1 *************
     10 *************
     14 *************
    480 *************
    425 *************
     30 *************
    580 *************
    380 *************
     10 *************
      2 *************
```

> 解释一下这个命令的各部分含义：

- netstat -ntp：显示系统的网络连接信息，并以数字格式显示端口和进程信息。
- grep ':3300'：过滤出本地端口为3300的连接。
- grep ESTABLISHED：过滤出状态为ESTABLISHED的连接，表示已建立连接的状态。
- awk '{print $5}'：提取出连接的远程地址和端口信息。
- cut -d ':' -f 1：根据冒号分割取出远程地址部分，即IP地址部分。
- sort：对输出的IP地址进行排序，以便后续的计数操作。
- uniq -c：统计相邻行相同的行数，即统计每个IP地址的连接数，并在结果中显示连接数和IP地址。

> 这样，执行这个命令后就可以列出连接了本地端口3300的连接的来源IP地址。
