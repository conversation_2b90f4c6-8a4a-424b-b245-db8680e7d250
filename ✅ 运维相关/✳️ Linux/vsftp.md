# 1. vsftp登录时提示：“vsftpd 500 OOPS: chroot”
解决方法（一）：
/usr/sbin/setsebool -P ftp_home_dir 1
重启服务器

解决方法（二）：
[root@localhost ~]# vim /etc/selinux/config
设置SELINUX=disabled
重启服务器

解决方法 （三）：
**setenforce 0**
不用重启
指定的用户家目录在home下的原家目录，这样如果设置nologin shell的话，也会提示此错误。

# 2. vsftp多用户指定目录
##  2.1 配置
![image.png](https://cdn.nlark.com/yuque/0/2020/png/684952/1578070069419-f16503c1-fdfe-401f-9d31-56000722b57d.png#align=left&display=inline&height=175&originHeight=231&originWidth=714&size=26667&status=done&style=none&width=541)
![image.png](https://cdn.nlark.com/yuque/0/2020/png/684952/1578070143539-8ea046f9-3d01-4587-b7ed-8311f16168ed.png#align=left&display=inline&height=204&originHeight=289&originWidth=768&size=31552&status=done&style=none&width=543)
##  2.2 配置用户目录

```java
useradd -d /home/<USER>/sbin/nologin
-d ftp绑定目录 用户名 -s 用户不能登录, 只能访问ftp
linewell目录不需要手动建
linewll-out添加到chroot_list里

passwd linewell-out

userdel linewell-out

service vsftpd start/stop/restart/status
```

