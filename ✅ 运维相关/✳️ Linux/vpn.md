```
docker pull v2ray/official

```



```json
{
  "log": {
    "access": "/var/log/v2ray/access.log",
    "error": "/var/log/v2ray/error.log",
    "loglevel": "warning"
  },
  "dns": {},
  "stats": {},
  "inbounds": [
    {
      "port": 30999,
      "protocol": "vmess",
      "settings": {
        "clients": [
          {
            "id": "[0e894be1-748e-396b-7973-550843fe2971](https://www.uuidgenerator.net/)",
            "alterId": 32
          }
        ]
      },
      "tag": "in-0",
      "streamSettings": {
        "network": "tcp",
        "security": "none",
        "tcpSettings": {}
      }
    }
  ],
  "outbounds": [
    {
      "tag": "direct",
      "protocol": "freedom",
      "settings": {}
    },
    {
      "tag": "blocked",
      "protocol": "blackhole",
      "settings": {}
    }
  ],
  "routing": {
    "domainStrategy": "AsIs",
    "rules": [
      {
        "type": "field",
        "ip": [
          "geoip:private"
        ],
        "outboundTag": "blocked"
      }
    ]
  },
  "policy": {},
  "reverse": {},
  "transport": {}
}
```



```
docker run -it -d \
--name v2ray \
-v /etc/v2ray:/etc/v2ray \
-p 30999:30999 \
v2ray/official v2ray \
-config=/etc/v2ray/config.json

```





```
docker pull teddysun/shadowsocks-r

docker run -d -p 18388:8388 -p 18388:8388/udp --name ssr teddysun/shadowsocks-r
```

