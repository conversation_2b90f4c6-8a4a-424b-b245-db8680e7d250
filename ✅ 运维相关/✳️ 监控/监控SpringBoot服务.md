## TP99

> https://blog.csdn.net/lp19861126/article/details/106309546
>
> https://akisaya.github.io/2020/%E4%BD%BF%E7%94%A8-springboot-actuator-prometheus-grafana-%E7%9B%91%E6%8E%A7%E4%BD%A0%E7%9A%84%E5%BA%94%E7%94%A8/

```yaml
management:
  metrics:
    distribution:
      percentiles-histogram:
        http.server.requests: true
        recommend.timer.recall: true
        recommend.timer.profile: true
        recommend.timer.predict: true
    tags:
      application: ${spring.application.name}
  endpoint:
    shutdown:
      enabled: true
    health:
      probes:
        enabled: true
      show-details: never
  endpoints:
    web:
      exposure:
        include: health,shutdown,metrics,prometheus

```

```bash

# 统计访问 /recommend 的 qps
irate(http_server_requests_seconds_count{uri='/recommend'}[1m])

# 统计平均时间
rate(http_server_requests_seconds_sum{uri="/recommend"}[1m])/rate(http_server_requests_seconds_count{uri="/recommend"}[1m])

# 计算最大时间
http_server_requests_seconds_max{uri="/recommend"}

## 计算TP95
histogram_quantile(0.95, http_server_requests_seconds_bucket{uri='/recommend'})
```

## skywalking

```shell
-javaagent:/skywalking-agent/skywalking-agent.jar -DSW_AGENT_NAMESPACE=hjzx -DSW_AGENT_NAME=cloudcc-client-server -Dskywalking.collector.backend_service=10.255.1.250:11800
```

