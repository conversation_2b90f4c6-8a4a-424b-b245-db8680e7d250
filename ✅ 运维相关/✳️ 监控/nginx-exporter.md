```shell
docker run -p 9113:9113 nginx/nginx-prometheus-exporter:1.3.0 \
--nginx.scrape-uri=http://***********:8080/nginx_status

```

```shell
    server {
      listen 8080;
    location /nginx_status {
        stub_status;

        access_log off;
    }
    }


```

```shell
curl --request PUT --data @nginx_exporter.json http://************:8500/v1/agent/service/register
```

```shell
  {
    "id": "nginx",
    "name": "nginx_exporter",
    "address": "***********",
    "port": 9113,
    "tags": ["nginx"],
    "meta": {
      "job": "nginx_exporter",
      "instance": "dev服务器"
    },
    "checks": [{
      "http": "http://***********:9113/metrics",
      "interval": "5s"
    }]
  } 

```