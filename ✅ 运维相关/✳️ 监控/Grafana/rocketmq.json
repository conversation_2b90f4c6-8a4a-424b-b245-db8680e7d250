{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": 10477, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"description": "客户端拉取消息的TPS", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "stepBefore", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["cloudcc_business.cloudcc"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 40, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_client_consumer_pull_tps{topic=\"cloudcc\"}", "interval": "", "legendFormat": "{{group}}.{{topic}}.{{clientId}}", "refId": "A"}], "title": "rocketmq_client_consumer_pull_tps-客户端拉取消息的TPS", "transparent": true, "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 1, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["rocketmq_client_consumer_pull_rt{Cluster=\"local\", Env=\"develop\", clientAddr=\"************:23144\", clientId=\"************@1717741919759-1\", group=\"cloudcc_business\", instance=\"**************:5557\", job=\"rocketmq-exporter\", topic=\"cloudcc\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 38, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_client_consumer_pull_rt{topic=\"cloudcc\"}", "interval": "", "legendFormat": "{{clientAddr}}-{{group}}", "refId": "A"}], "title": "rocketmq_client_consumer_pull_rt-客户端拉取消息的时间", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}, "id": 36, "options": {"legend": {"calcs": ["last", "max", "min"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_client_consume_rt{topic=\"cloudcc\"}", "interval": "", "legendFormat": "{{clientAddr}}-{{group}}", "refId": "A"}], "title": "rocketmq_client_consume_rt-消息从拉取到被消费的时间", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "消费tps", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_consumer_tps{topic=\"cloudcc\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{broker}}.{{group}}.{{topic}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_consumer_tps-消费tps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:466", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:467", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 14}, "id": 32, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_group_diff{topic=\"cloudcc\"}", "interval": "", "legendFormat": "{{group}}.{{topic}}", "refId": "A"}], "title": "rocketmq_group_diff-消费组消息堆积消息数", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_producer_tps{topic=\"cloudcc\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{broker}}.{{group}}.{{topic}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_producer_tps-生产tps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:361", "format": "short", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:362", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["rocketmq_client_consume_fail_msg_count{Cluster=\"local\", Env=\"develop\", clientAddr=\"************:26740\", clientId=\"************@************:9876;************:9876;************:9876@7@1284325618462202\", group=\"cloudcc_ext_status\", instance=\"**************:5557\", job=\"rocketmq-exporter\", topic=\"cloudcc_ext_status\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 20}, "id": 34, "options": {"legend": {"calcs": ["last", "min", "max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_client_consume_fail_msg_count{topic=\"cloudcc\"}", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{group}}.{{topic}}.{{clientAddr}}", "refId": "A"}], "title": "rocketmq_client_consume_fail_msg_count-消费失败数", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_broker_tps", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{broker}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "hji00tEIk"}, "exemplar": true, "expr": "rocketmq_broker_qps", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{broker}}", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "broker tps & broker qps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:177", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:178", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 26}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_producer_offset", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_producer_offset", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 26}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_consumer_message_size", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_consumer_message_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 32}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_consumer_offset", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_consumer_offset", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 32}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_producer_message_size", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_producer_message_size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 38}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rocketmq_producer_offset) by (topic) - on(topic)  group_right  sum(rocketmq_consumer_offset) by (group,topic)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_message_accumulation", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 9, "x": 7, "y": 39}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_group_get_latency_by_storetime", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "rocketmq_group_get_latency_by_storetime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 44}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_client_consume_fail_msg_count", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_client_consume_fail_msg_tps", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_client_consume_ok_msg_tps", "format": "time_series", "intervalFactor": 1, "refId": "C"}, {"expr": "rocketmq_client_consume_rt", "format": "time_series", "intervalFactor": 1, "refId": "D"}, {"expr": "rocketmq_client_consumer_pull_rt", "format": "time_series", "intervalFactor": 1, "refId": "E"}, {"expr": "rocketmq_client_consumer_pull_tps", "format": "time_series", "intervalFactor": 1, "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "consume client info", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 45}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_brokeruntime_pmdt_0ms", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_brokeruntime_pmdt_0to10ms", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_brokeruntime_pmdt_10to50ms", "format": "time_series", "intervalFactor": 1, "refId": "C"}, {"expr": "rocketmq_brokeruntime_pmdt_50to100ms", "format": "time_series", "intervalFactor": 1, "refId": "D"}, {"expr": "rocketmq_brokeruntime_pmdt_100to200ms", "format": "time_series", "intervalFactor": 1, "refId": "E"}, {"expr": "rocketmq_brokeruntime_pmdt_200to500ms", "format": "time_series", "intervalFactor": 1, "refId": "F"}, {"expr": "rocketmq_brokeruntime_pmdt_500to1s", "format": "time_series", "intervalFactor": 1, "refId": "G"}, {"expr": "rocketmq_brokeruntime_pmdt_1to2s", "format": "time_series", "intervalFactor": 1, "refId": "H"}, {"expr": "rocketmq_brokeruntime_pmdt_2to3s", "format": "time_series", "intervalFactor": 1, "refId": "I"}, {"expr": "rocketmq_brokeruntime_pmdt_3to4s", "format": "time_series", "intervalFactor": 1, "refId": "J"}, {"expr": "rocketmq_brokeruntime_pmdt_4to5s", "format": "time_series", "intervalFactor": 1, "refId": "K"}, {"expr": "rocketmq_brokeruntime_pmdt_5to10s", "format": "time_series", "intervalFactor": 1, "refId": "L"}, {"expr": "rocketmq_brokeruntime_pmdt_10stomore", "format": "time_series", "intervalFactor": 1, "refId": "M"}], "thresholds": [], "timeRegions": [], "title": "PutMessageDistributeTime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 45}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_brokeruntime_pull_threadpoolqueue_headwait_timemills", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_brokeruntime_query_threadpoolqueue_headwait_timemills", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_brokeruntime_send_threadpoolqueue_headwait_timemills", "format": "time_series", "intervalFactor": 1, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "threadpoolqueue_headwait_timemills", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 51}, "hiddenSeries": false, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_brokeruntime_msg_put_total_today_now", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_brokeruntime_msg_gettotal_today_now", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_brokeruntime_dispatch_behind_bytes", "format": "time_series", "intervalFactor": 1, "refId": "C"}, {"expr": "rocketmq_brokeruntime_put_message_size_total", "format": "time_series", "intervalFactor": 1, "refId": "D"}, {"expr": "rocketmq_brokeruntime_put_message_average_size", "format": "time_series", "intervalFactor": 1, "refId": "E"}, {"expr": "rocketmq_brokeruntime_msg_gettotal_yesterdaymorning", "format": "time_series", "intervalFactor": 1, "refId": "F"}, {"expr": "rocketmq_brokeruntime_msg_puttotal_yesterdaymorning", "format": "time_series", "intervalFactor": 1, "refId": "G"}, {"expr": "rocketmq_brokeruntime_msg_gettotal_todaymorning", "format": "time_series", "intervalFactor": 1, "refId": "H"}, {"expr": "rocketmq_brokeruntime_msg_puttotal_todaymorning", "format": "time_series", "intervalFactor": 1, "refId": "I"}], "thresholds": [], "timeRegions": [], "title": "broker runtime info", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 52}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_brokeruntime_getfound_tps10", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_brokeruntime_gettotal_tps10", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_brokeruntime_gettransfered_tps10", "format": "time_series", "intervalFactor": 1, "refId": "C"}, {"expr": "rocketmq_brokeruntime_getmiss_tps10", "format": "time_series", "intervalFactor": 1, "refId": "D"}, {"expr": "rocketmq_brokeruntime_put_tps10", "format": "time_series", "intervalFactor": 1, "refId": "E"}], "thresholds": [], "timeRegions": [], "title": "runtime tps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 52}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rocketmq_brokeruntime_commitlog_disk_ratio", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"expr": "rocketmq_brokeruntime_consumequeue_disk_ratio", "format": "time_series", "intervalFactor": 1, "refId": "B"}, {"expr": "rocketmq_brokeruntime_commitlogdir_capacity_free", "format": "time_series", "intervalFactor": 1, "refId": "C"}, {"expr": "rocketmq_brokeruntime_commitlogdir_capacity_total", "format": "time_series", "intervalFactor": 1, "refId": "D"}, {"expr": "rocketmq_brokeruntime_commitlog_maxoffset", "format": "time_series", "intervalFactor": 1, "refId": "E"}, {"expr": "rocketmq_brokeruntime_commitlog_minoffset", "format": "time_series", "intervalFactor": 1, "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "disk space", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Rocketmq_dashboard", "uid": "zkVx1w_iz", "version": 37, "weekStart": ""}