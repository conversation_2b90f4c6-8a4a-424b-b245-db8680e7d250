{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": "Prometheus", "enable": true, "expr": "resets(process_uptime_seconds{application=\"$application\", instance=\"$instance\"}[1m]) > 0", "iconColor": "rgba(255, 96, 96, 1)", "name": "Restart Detection", "showIn": 0, "step": "1m", "tagKeys": "restart-tag", "textFormat": "uptime reset", "titleFormat": "<PERSON><PERSON>"}]}, "description": "Dashboard for Micrometer instrumented applications (Java, Spring Boot, Micronaut)", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 4701, "graphTooltip": 1, "id": 1, "iteration": 1693746727680, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 125, "panels": [], "title": "Quick Facts", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 63, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "process_uptime_seconds{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "id": 92, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "process_start_time_seconds{application=\"$application\", instance=\"$instance\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Start time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 1}, "id": 65, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "title": "Heap used", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"from": -1e+32, "result": {"text": "N/A"}, "to": 0}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 75, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "title": "Non-Heap used", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 126, "panels": [], "title": "I/O Overview", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 18, "x": 0, "y": 5}, "hiddenSeries": false, "id": 111, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "HTTP", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", uri=\"/api/v1/bind/axe/binding/{vccId}\"}[1m]))", "hide": false, "interval": "", "legendFormat": "/api/v1/bind/axe/binding", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", uri=\"/api/v1/bind/axe/unbind/{vccId}\"}[1m]))", "hide": false, "interval": "", "legendFormat": "/api/v1/bind/axe/unbind", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", uri=\"/api/v1/bind/getBindInfo\"}[1m]))", "hide": false, "interval": "", "legendFormat": "/api/v1/bind/getBindInfo", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", uri=\"/api/v1/query/bindInfo\"}[1m]))", "hide": false, "interval": "", "legendFormat": "/api/v1/query/bindInfo", "refId": "E"}], "thresholds": [{"$$hashKey": "object:771", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "yaxis": "left"}], "timeRegions": [{"$$hashKey": "object:781", "colorMode": "background6", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time"}], "title": "HTTP接口QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:727", "format": "ops", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:728", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"HTTP": "#890f02", "HTTP - 5xx": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 5}, "hiddenSeries": false, "id": 112, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", status=~\"5..\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "HTTP - 5xx", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "HTTP - 5xx", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1373", "format": "ops", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:1374", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 18, "x": 0, "y": 14}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "sum(rate(http_server_requests_seconds_sum{application=\"$application\", instance=\"$instance\", status!~\"5..\"}[1m]))/sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", status!~\"5..\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "HTTP - AVG", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "expr": "max(http_server_requests_seconds_max{application=\"$application\", instance=\"$instance\", status!~\"5..\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - MAX", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "HTTP接口耗时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1136", "format": "s", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:1137", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 14}, "hiddenSeries": false, "id": 119, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tomcat_threads_busy_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - BSY", "refId": "A"}, {"expr": "tomcat_threads_current_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - CUR", "refId": "B"}, {"expr": "tomcat_threads_config_max_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - MAX", "refId": "C"}, {"expr": "jetty_threads_busy{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - BSY", "refId": "D"}, {"expr": "jetty_threads_current{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - CUR", "refId": "E"}, {"expr": "jetty_threads_config_max{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - MAX", "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "Utilisation", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 127, "panels": [], "title": "JVM Memory", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 24}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "JVM <PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 24}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "JVM Non-Heap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 24}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "JVM Total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 24}, "hiddenSeries": false, "id": 86, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_memory_vss_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "vss", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_memory_rss_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "rss", "refId": "B"}, {"expr": "process_memory_swap_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "swap", "refId": "C"}, {"expr": "process_memory_rss_bytes{application=\"$application\", instance=\"$instance\"} + process_memory_swap_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "JVM Process Memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}, "id": 128, "panels": [], "title": "JVM Misc", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 32}, "hiddenSeries": false, "id": 106, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_cpu_usage{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "system", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_cpu_usage{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process", "refId": "B"}, {"expr": "avg_over_time(process_cpu_usage{application=\"$application\", instance=\"$instance\"}[1h])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process-1h", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "percentunit", "label": "", "logBase": 1, "max": "1", "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 32}, "hiddenSeries": false, "id": 93, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_load_average_1m{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "system-1m", "metric": "", "refId": "A", "step": 2400}, {"expr": "system_cpu_count{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "cpus", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Load", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "short", "label": "", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 32}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_threads_live_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "live", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_threads_daemon_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "daemon", "metric": "", "refId": "B", "step": 2400}, {"expr": "jvm_threads_peak_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "peak", "refId": "C", "step": 2400}, {"expr": "process_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "process", "refId": "D", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Threads", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"blocked": "#bf1b00", "new": "#fce2de", "runnable": "#7eb26d", "terminated": "#511749", "timed-waiting": "#c15c17", "waiting": "#eab839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 32}, "hiddenSeries": false, "id": 124, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_threads_states_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{state}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Thread States", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"debug": "#1F78C1", "error": "#BF1B00", "info": "#508642", "trace": "#6ED0E0", "warn": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 39}, "height": "", "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:132", "alias": "error", "yaxis": 1}, {"$$hashKey": "object:133", "alias": "warn", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "increase(log4j2_events_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{level}}", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Log Events ops/m", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"$$hashKey": "object:146", "decimals": 0, "format": "opm", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:147", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 39}, "hiddenSeries": false, "id": 61, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_files_open_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "open", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_files_max_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "File Descriptors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 10, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"debug": "#1F78C1", "error": "#BF1B00", "info": "#508642", "trace": "#6ED0E0", "warn": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 46}, "height": "", "hiddenSeries": false, "id": 146, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:132", "alias": "error", "yaxis": 1}, {"$$hashKey": "object:133", "alias": "warn", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "log4j2_events_total{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{level}}", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Log Events Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"$$hashKey": "object:146", "decimals": 0, "format": "none", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:147", "format": "none", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 53}, "id": 129, "panels": [], "repeat": "persistence_counts", "title": "JVM Memory Pools (Heap)", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 54}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_heap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "expr": "jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "expr": "jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_heap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"$$hashKey": "object:1590", "format": "bytes", "logBase": 1, "min": 0, "show": true}, {"$$hashKey": "object:1591", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 61}, "id": 130, "panels": [], "title": "JVM Memory Pools (Non-Heap)", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 62}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_nonheap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"expr": "jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"expr": "jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_nonheap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"$$hashKey": "object:314", "format": "bytes", "logBase": 1, "min": 0, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 69}, "id": 131, "panels": [], "title": "Garbage Collection", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 70}, "hiddenSeries": false, "id": 98, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(jvm_gc_pause_seconds_count{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{action}} ({{cause}})", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Collections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 70}, "hiddenSeries": false, "id": 101, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "exemplar": true, "expr": "rate(jvm_gc_pause_seconds_sum{application=\"$application\", instance=\"$instance\"}[1m])/rate(jvm_gc_pause_seconds_count{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "avg {{action}} ({{cause}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "expr": "jvm_gc_pause_seconds_max{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "max {{action}} ({{cause}})", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Pause Du<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:551", "format": "s", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:552", "format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 70}, "hiddenSeries": false, "id": 99, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(jvm_gc_memory_allocated_bytes_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "allocated", "refId": "A"}, {"expr": "rate(jvm_gc_memory_promoted_bytes_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "promoted", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Allocated/Promoted", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 77}, "id": 132, "panels": [], "title": "Classloading", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 78}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_classes_loaded_classes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "loaded", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Classes loaded", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 78}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "delta(jvm_classes_loaded_classes{application=\"$application\",instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "delta-1m", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Class delta", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["ops", "short"], "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 85}, "id": 133, "panels": [], "title": "Buffer Pools", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 86}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_buffer_total_capacity_bytes{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 86}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_count_buffers{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 86}, "hiddenSeries": false, "id": 85, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_buffer_total_capacity_bytes{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 86}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_count_buffers{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "5s", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "private-number-hmyc", "value": "private-number-hmyc"}, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "definition": "label_values(application)", "hide": 0, "includeAll": false, "label": "Application", "multi": false, "name": "application", "options": [], "query": {"query": "label_values(application)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "************:16600", "value": "************:16600"}, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "definition": "label_values(jvm_memory_used_bytes{application=\"$application\"}, instance)", "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "multiFormat": "glob", "name": "instance", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "definition": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"},id)", "hide": 0, "includeAll": true, "label": "JVM Memory Pools Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_heap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"},id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "Uop05Ba4z"}, "definition": "", "hide": 0, "includeAll": true, "label": "JVM Memory Pools Non-Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_nonheap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"},id)", "refId": "Prometheus-jvm_memory_pool_nonheap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "/**", "value": "/**"}, "definition": "label_values(uri)", "hide": 0, "includeAll": false, "multi": false, "name": "uri", "options": [], "query": {"query": "label_values(uri)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "JVM (Micrometer)", "uid": "q62BcB-Vk", "version": 18, "weekStart": ""}