version: '3.8'
services:
  skywalking-oap:
    image: *************:11000/docker/apache/skywalking-oap-server:9.3.0
    container_name: skywalking-oap
    hostname: skywalking-oap
    ports:
      - "11800:11800" # agent 上报数据的端口，这是 gRPC 端口
      - "12800:12800" # ui 读取数据的端口， 这是 http 端口
    healthcheck:
      test: [ "CMD-SHELL", "/skywalking/bin/swctl ch" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # depends_on:
    #   elasticsearch:
    #     condition: service_healthy
    environment:
      TZ: Asia/Shanghai
      SW_CORE_RECORD_DATA_TTL: 2 #记录数据的有效期，单位天
      SW_CORE_METRICS_DATA_TTL: 2 #分析指标数据的有效期，单位天
      SW_ENABLE_UPDATE_UI_TEMPLATE: "true" # 开启dashboard编辑修改功能
      SW_HEALTH_CHECKER: default
      # SW_STORAGE: mysql
      # SW_JDBC_URL: "*****************************************"
      # SW_DATA_SOURCE_USER: root
      # SW_DATA_SOURCE_PASSWORD: cqt@fj889977
      SW_STORAGE: elasticsearch
      SW_STORAGE_ES_CLUSTER_NODES: ************:9200
      SW_STORAGE_ES_HTTP_PROTOCOL: http
      JAVA_OPTS: "-Xms2g -Xmx2g"
    volumes:
      - ./mysql-connector-j-8.0.33.jar:/skywalking/oap-libs/mysql-connector-j-8.0.33.jar

  skywalking-ui:
    image: *************:11000/docker/apache/skywalking-ui:9.3.0
    container_name: skywalking-ui
    hostname: skywalking-ui
    depends_on:
      skywalking-oap:
        condition: service_healthy
    links:
      - skywalking-oap
    ports:
      - "28080:8080"
    environment:
      TZ: Asia/Shanghai
      SW_HEALTH_CHECKER: default
      SW_OAP_ADDRESS: http://skywalking-oap:12800
      # SW_ZIPKIN_ADDRESS: http://skywalking-oap:9412

  # elasticsearch:
  #   image: *************:11000/docker/elasticsearch:8.13.0
  #   environment:
  #     - discovery.type=single-node
  #     - bootstrap.memory_lock=true
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ports:
  #     - "9200:9200"
  #   volumes:
  #     - ./data:/usr/share/elasticsearch/data
  #   healthcheck:
  #     test: [ "CMD-SHELL", "curl --silent --fail localhost:9200/_cluster/health || exit 1" ]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 10s