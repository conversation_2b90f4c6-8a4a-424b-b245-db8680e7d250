# docker-compose 部署 SpringBoot 服务

## maven 插件

> 将各模块 target 里的 jar 包移到一个父工程的 docker 目录, 方便构建镜像

```shell
<!-- 复制，删除，打包，重命名文件等... -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-antrun-plugin</artifactId>
    <version>1.8</version>
    <executions>
        <execution>
            <!-- maven生命周期阶段 -->
            <phase>package</phase>
            <goals>
                <goal>run</goal>
            </goals>
            <configuration>
                <!-- 任务 -->
                <tasks>
                    <!-- 复制jar包到指定目录 -->
                    <!--suppress UnresolvedMavenProperty -->
                    <copy overwrite="true"
                          tofile="../../docker/${project.artifactId}.jar"
                          file="${project.build.directory}/${project.artifactId}-${revision}.jar"/>
                    <!--<copy todir="${project.basedir}/docker">
                        <fileset dir="${project.build.directory}">
                            <include name="${project.artifactId}-${project.version}.jar"/>
                        </fileset>
                    </copy>-->
                </tasks>
            </configuration>
        </execution>
    </executions>
</plugin>
```

![image.png](https://cdn.nlark.com/yuque/0/2022/png/684952/1666785725821-933a6133-77ed-4ed6-adef-b3c573e32945.png#clientId=uaad7837a-8099-4&from=paste&height=161&id=u5845723a&originHeight=322&originWidth=572&originalType=binary&ratio=1&rotation=0&showTitle=false&size=32976&status=done&style=none&taskId=udc92545f-0e19-4b79-a739-5b3bcbecd39&title=&width=286)

## 镜像私服 harbor 登录

```bash
# 添加/etc/hosts
************* harbor.cqt.com

# 登录
docker login -u admin --password cqt@1234 *************:11000

# registries配置
cat /etc/docker/daemon.json
{
	"registry-mirrors": ["https://b9pmyelo.mirror.aliyuncs.com/"],
    "insecure-registries" : [ "*************:11000" ]
}
```

## Dockerfile

```dockerfile

FROM openjdk:8-jre-alpine

# 维护者信息
MAINTAINER cqt

# 构建镜像时传参数据
ARG APP_NAME
ARG APP_PORT
ARG JAVA_OPTS
ARG XJAR_PASSWORD

# 设置环境变量
ENV APP_NAME ${APP_NAME}
ENV APP_JAR ${APP_NAME}.jar
ENV APP_PORT ${APP_PORT}
ENV JAVA_OPTS ${JAVA_OPTS}
ENV XJAR_PASSWORD ${XJAR_PASSWORD}

# 添加jar包到容器中
ADD ${APP_JAR} /home/<USER>/${APP_JAR}

# 对外暴漏的端口号
EXPOSE ${APP_PORT}

CMD java -jar ${JAVA_OPTS} /home/<USER>/${APP_JAR} --xjar.password=${XJAR_PASSWORD}

```

## 构建镜像脚本 build.sh

```shell
#!/bin/bash

harbor='*************:11000'
versions='v1.2.0'
project='fsbc'
XJAR_PASSWORD='cqt@2023'

# build
# gateway
buildGateway(){
   docker build -f Dockerfile \
   --build-arg JAVA_OPTS="-XX:+UseG1GC -Xms1024m -Xmx1024m -Xmn512m \
   -XX:MetaspaceSize=512m \
   -XX:MaxMetaspaceSize=512m \
   -XX:MaxGCPauseMillis=200 \
   -XX:ParallelGCThreads=8 \
   -Ddefault.client.encoding=UTF-8 \
   -Dfile.encoding=UTF-8 \
   -Duser.language=Zh \
   -Duser.region=CN" \
   --build-arg APP_NAME="f-sbc-sip-ms-gateway" \
   --build-arg APP_PORT="57500" \
   --build-arg XJAR_PASSWORD=${XJAR_PASSWORD} \
   -t "f-sbc-sip-ms-gateway:${versions}" .

   echo "====================docker build f-sbc-sip-ms-gateway finish===================="

   # 打标签, 推送镜像到私服
   docker tag f-sbc-sip-ms-gateway:${versions} ${harbor}/${project}/f-sbc-sip-ms-gateway:${versions}
   echo docker tag f-sbc-sip-ms-gateway:${versions} ${harbor}/${project}/f-sbc-sip-ms-gateway:${versions}
   echo "====================tag gateway finish===================="

   docker push ${harbor}/${project}/f-sbc-sip-ms-gateway:${versions}

   echo "====================push gateway finish===================="
   echo docker push ${harbor}/${project}/f-sbc-sip-ms-gateway:${versions}

}

# system
buildSystem(){
    docker build -f Dockerfile \
    --build-arg JAVA_OPTS="-XX:+UseG1GC -Xms1024m -Xmx1024m -Xmn512m \
    -XX:MetaspaceSize=512m \
    -XX:MaxMetaspaceSize=512m \
    -XX:MaxGCPauseMillis=200 \
    -XX:ParallelGCThreads=8 \
    -Ddefault.client.encoding=UTF-8 \
    -Dfile.encoding=UTF-8 \
    -Duser.language=Zh \
    -Duser.region=CN" \
    --build-arg APP_NAME="f-sbc-sip-ms-system" \
    --build-arg APP_PORT="57501" \
    --build-arg XJAR_PASSWORD=${XJAR_PASSWORD} \
    -t "f-sbc-sip-ms-system:${versions}" .

     echo "====================docker build f-sbc-sip-ms-system finish===================="

     # 打标签, 推送镜像到私服
     docker tag f-sbc-sip-ms-system:${versions} ${harbor}/${project}/f-sbc-sip-ms-system:${versions}
     echo "====================tag system finish===================="

     docker push ${harbor}/${project}/f-sbc-sip-ms-system:${versions}

     echo "====================push system finish===================="
}

# setting
buildSetting(){
    docker build -f Dockerfile \
    --build-arg JAVA_OPTS="-XX:+UseG1GC -Xms1024m -Xmx1024m -Xmn512m \
    -XX:MetaspaceSize=512m \
    -XX:MaxMetaspaceSize=512m \
    -XX:MaxGCPauseMillis=200 \
    -XX:ParallelGCThreads=8 \
    -Ddefault.client.encoding=UTF-8 \
    -Dfile.encoding=UTF-8 \
    -Duser.language=Zh \
    -Duser.region=CN" \
    --build-arg APP_NAME="f-sbc-sip-ms-base-setting" \
    --build-arg APP_PORT="57502" \
    --build-arg XJAR_PASSWORD=${XJAR_PASSWORD} \
    -t "f-sbc-sip-ms-base-setting:${versions}" .

     echo "====================docker build f-sbc-sip-ms-base-setting finish===================="

     # 打标签, 推送镜像到私服
     docker tag f-sbc-sip-ms-base-setting:${versions} ${harbor}/${project}/f-sbc-sip-ms-base-setting:${versions}
     echo "====================tag base-setting finish===================="

     docker push ${harbor}/${project}/f-sbc-sip-ms-base-setting:${versions}

     echo "====================push base-setting finish===================="
}

# report
buildReport(){
    docker build -f Dockerfile \
    --build-arg JAVA_OPTS="-XX:+UseG1GC -Xms1024m -Xmx1024m -Xmn512m \
    -XX:MetaspaceSize=512m \
    -XX:MaxMetaspaceSize=512m \
    -XX:MaxGCPauseMillis=200 \
    -XX:ParallelGCThreads=8 \
    -Ddefault.client.encoding=UTF-8 \
    -Dfile.encoding=UTF-8 \
    -Duser.language=Zh \
    -Duser.region=CN" \
    --build-arg APP_NAME="f-sbc-sip-ms-report" \
    --build-arg APP_PORT="57503" \
    --build-arg XJAR_PASSWORD=${XJAR_PASSWORD} \
    -t "f-sbc-sip-ms-report:${versions}" .

     echo "====================docker build f-sbc-sip-ms-report finish===================="

     # 打标签, 推送镜像到私服
     docker tag f-sbc-sip-ms-report:${versions} ${harbor}/${project}/f-sbc-sip-ms-report:${versions}
     echo "====================tag report finish===================="

     docker push ${harbor}/${project}/f-sbc-sip-ms-report:${versions}

     echo "====================push report finish===================="
}

buildAll() {
  buildGateway
  buildSystem
  buildReport
  buildSetting
}

#启动时带参数，根据参数执行
if [ ${#} -ge 1 ]
then
    echo "current verion: ${versions}"
    case ${1} in
        "gateway")
            buildGateway
        ;;
        "system")
            buildSystem
        ;;
        "setting")
            buildSetting
        ;;
        "report")
            buildReport
        ;;
       "all")
            buildAll
        ;;
        *)
            echo "${1}无任何操作"
        ;;
    esac
else
    echo "
    command如下命令：
    gateway：gateway
    system：system
    setting：setting
    report：report
    all: all

    示例命令如：./build.sh gateway
    "
fi
```

## docker-compose.yml

注意点：

-   restart: always 自动重启
-   network_mode: host host 方式使用宿主机网络，注册到 nacos 的 Ip 才是宿主机的 ip，和其他宿主机才可以通讯。

```yaml
version: '1'
services:
  f-sbc-sip-ms-gateway:
    container_name: f-sbc-sip-ms-gateway    # 容器名
    image: *************:11000/fsbc/f-sbc-sip-ms-gateway:v1.2.0  # 镜像名
    restart: always                                                      # 指定容器退出后的重启策略为始终重启
    privileged: true
    volumes: # 数据卷挂载路径设置,将本机目录映射到容器目录
      - /home/<USER>/logs/f-sbc-sip-ms-gateway:/home/<USER>/logs/f-sbc-sip-ms-gateway
    environment: # 设置环境变量,相当于docker run命令中的-e
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
      NACOS_SERVER: *************:8848
    # ports: # 映射端口
    #   - 57500:57500
    network_mode: host # 容器使用宿主机网络 ( tips:此配置和`ports`/`links`不可结合使用 )

  f-sbc-sip-ms-system:
    container_name: f-sbc-sip-ms-system
    image: *************:11000/fsbc/f-sbc-sip-ms-system:v1.2.0
    restart: always
    privileged: true
    volumes:
      - /home/<USER>/logs/f-sbc-sip-ms-system:/home/<USER>/logs/f-sbc-sip-ms-system
    environment:
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
      NACOS_SERVER: *************:8848
    # ports:
    #   - 57501:57501
    network_mode: host

  f-sbc-sip-ms-base-setting:
    container_name: f-sbc-sip-ms-base-setting
    image: *************:11000/fsbc/f-sbc-sip-ms-base-setting:v1.2.0
    restart: always
    privileged: true
    volumes:
      - /home/<USER>/logs/f-sbc-sip-ms-base-setting:/home/<USER>/logs/f-sbc-sip-ms-base-setting
    environment:
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
      NACOS_SERVER: *************:8848
    # ports:
    #   - 57502:57502
    network_mode: host

  f-sbc-sip-ms-report:
    container_name: f-sbc-sip-ms-report
    image: *************:11000/fsbc/f-sbc-sip-ms-report:v1.2.0
    restart: always
    privileged: true
    volumes:
      - /home/<USER>/logs/f-sbc-sip-ms-report:/home/<USER>/logs/f-sbc-sip-ms-report
    environment:
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
      NACOS_SERVER: *************:8848
    # ports:
    #   - 57503:57503
    network_mode: host

```

## docker-compose 部署命令

```shell
启动全部容器: docker-compose -f docker-compose.yml up -d
拉取新镜像(代码更新): docker-compose pull
重建某个容器: docker-compose up --detach --build f-sbc-sip-ms-gateway
重建所有容器: docker-compose up --detach --build
停止容器: docker-compose stop f-sbc-sip-ms-gateway
启动容器: docker-compose start f-sbc-sip-ms-gateway
重启容器: docker-compose restart f-sbc-sip-ms-gateway
容器如何更新: 先拉取 再重建
```

### docker-compose 帮助命令

```shell
启动容器
# -p：项目名称
# -f：指定docker-compose.yml文件路径
# -d：后台启动
docker-compose -f docker-compose.yml -p fsbc up -d


Usage:  docker compose [OPTIONS] COMMAND

Docker Compose

Options:
      --ansi string                Control when to print ANSI control characters ("never"|"always"|"auto") (default "auto")
      --compatibility              Run compose in backward compatibility mode
      --env-file string            Specify an alternate environment file.
  -f, --file stringArray           Compose configuration files
      --profile stringArray        Specify a profile to enable
      --project-directory string   Specify an alternate working directory
                                   (default: the path of the Compose file)
  -p, --project-name string        Project name

Commands:
  build       Build or rebuild services
  convert     Converts the compose file to platform's canonical format
  cp          Copy files/folders between a service container and the local filesystem
  create      Creates containers for a service.
  down        Stop and remove containers, networks
  events      Receive real time events from containers.
  exec        Execute a command in a running container.
  images      List images used by the created containers
  kill        Force stop service containers.
  logs        View output from containers
  ls          List running compose projects
  pause       Pause services
  port        Print the public port for a port binding.
  ps          List containers
  pull        Pull service images
  push        Push service images
  restart     Restart containers
  rm          Removes stopped service containers
  run         Run a one-off command on a service.
  start       Start services
  stop        Stop services
  top         Display the running processes
  unpause     Unpause services
  up          Create and start containers
  version     Show the Docker Compose version information

```

## 一些常用命令

```shell

# 删除空镜像
docker rmi $(docker images -f "dangling=true" -q) docker

# 查看镜像
docker images

# 查看进程
docker ps

# 查看日志
docker logs -f f-sbc-sip-ms-gateway

# 查看容器详情
docker inspect f-sbc-sip-ms-gateway

# 列出网络
docker network ls

# 进入容器内
docker exec -it f-sbc-sip-ms-gateway /bin/sh

```

## 不同服务器之间的 Docker 网络如何通讯

1. network_mode: host 使用宿主机网络
