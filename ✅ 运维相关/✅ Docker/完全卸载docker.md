# 完全卸载docker

```shell
docker kill $(docker ps -a -q)
docker rm $(docker ps -a -q)
docker rmi $(docker images -q)
```

```shell
docker kill $(docker ps -a -q)
docker rm $(docker ps -a -q)
docker rmi $(docker images -q)
systemctl stop docker
sudo yum remove -y docker \
                  docker-client \
                  docker-client-latest \
                  docker-common \
                  docker-latest \
                  docker-latest-logrotate \
                  docker-logrotate \
                  docker-engine
                  
rm -rf /etc/systemd/system/docker.service
sudo rm -rf /var/lib/docker
sudo rm -rf /var/lib/containerd

sudo rm -rf /etc/docker/
sudo rm -rf /home/<USER>/
sudo rm -rf /var/run/docker
sudo rm -rf /var/run/dockershim.sock
 
```
