```java
curl -L https://github.com/docker/compose/releases/download/v2.2.3/docker-compose-`uname -s`-`uname -m` > /usr/local/bin/docker-compose

curl -L https://gh2.yanqishui.work/https://github.com/docker/compose/releases/download/v2.2.3/docker-compose-`uname -s`-`uname -m` > /usr/local/bin/docker-compose

chmod 755 /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose --version
 
https://github.com/docker/compose/releases/tag/v2.2.3

构建镜像
docker-compose启动后台    
docker-compose up -d
```
