user root;
worker_processes  4;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  56500;
}

stream {
    upstream nacos-grpcserver {
       server 10.255.1.14:9848 fail_timeout=10s max_fails=2;
       server 10.255.1.11:9848 fail_timeout=10s max_fails=2;
       server 10.255.1.12:9848 fail_timeout=10s max_fails=2;
    }

    server {
        listen 9848;
        proxy_pass nacos-grpcserver;
    }

    upstream rocketmq-grpcserver {
       server ***********:8081 fail_timeout=10s max_fails=2;
       server ***********:8081 fail_timeout=10s max_fails=2;
    }

    server {
        listen 18081;
        proxy_pass rocketmq-grpcserver;
    }
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  log_json escape=json  '{"@timestamp":"$time_iso8601",'
                                '"host":"$server_addr",'
                                '"clientip":"$remote_addr",'
                                '"http_user_agent":"$http_user_agent",'
                                '"http_x_forwarded_for":"$http_x_forwarded_for",'
                                '"size":$body_bytes_sent,'
                                '"responsetime":$request_time,'
                                '"upstreamtime":"$upstream_response_time",'
                                '"upstreamhost":"$upstream_addr",'
                                '"http_host":"$host",'
                                '"request":"$request",'
                                '"request_body": "$request_body",'
                                '"url":"$uri",'
                                '"xff":"$http_x_forwarded_for",'
                                '"referer":"$http_referer",'
                                '"agent":"$http_user_agent",'
                                '"status":"$status"}';

    access_log  /home/<USER>/logs/access_80.log  log_json;
    error_log /home/<USER>/logs/error_80.log debug;
    client_max_body_size 500m;
    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  0;

    gzip  on;

    #禁止目录浏览
    autoindex off;
    #隐藏版本信息
    server_tokens off;
    server_tag off;
    #移除header
    #proxy_hide_header X-Powered-By;
    #proxy_hide_header Server;

    server {
        listen       54093;
        # ws
        location /msg {
            client_max_body_size 100M;
            proxy_http_version 1.1;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade; #支持wss
            proxy_set_header Connection "upgrade"; #支持wssi
            proxy_pass http://cti-netty-server/msg; #代理路由
            root   html;
            index  index.html index.htm;
        }

    }

}
