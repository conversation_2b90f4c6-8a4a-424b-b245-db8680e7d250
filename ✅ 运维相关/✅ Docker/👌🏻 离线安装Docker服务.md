# 离线安装Docker

> 在某些情况下，无法通过在线方式直接安装 Docker，特别是在网络受限或隔离的环境下。本文将详细介绍如何在这种情况下完成 Docker 的离线安装。

## 下载 Docker 安装包到/home目录

> 自行选择对应操作系统的安装包

> https://download.docker.com/linux/


> https://download.docker.com/linux/static/stable/x86_64/docker-27.0.3.tgz

## 下载docker-compose包到/home目录

> 自行选择对应操作系统的安装包

> https://github.com/docker/compose/releases/download/v2.29.5/docker-compose-linux-x86_64

## 安装Docker

```shell

tar -zxvf /home/<USER>
cp -p /home/<USER>/* /usr/bin
rm -rf /home/<USER>/*

mkdir -p /etc/docker
tee /etc/docker/daemon.json <<-'EOF'
{
    "data-root": "/home/<USER>",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    }
}
EOF


tee /usr/lib/systemd/system/docker.service <<-'EOF'
[Unit]
Description=Docker Application Container Engine
Documentation=http://docs.docker.com
After=network.target docker.socket
[Service]
Type=notify
EnvironmentFile=-/run/flannel/docker
WorkingDirectory=/usr/local/bin
ExecStart=/usr/bin/dockerd \
                -H tcp://0.0.0.0:4243 \
                -H unix:///var/run/docker.sock \
                --selinux-enabled=false 
ExecReload=/bin/kill -s HUP $MAINPID
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
TimeoutStartSec=0
Delegate=yes
KillMode=process
Restart=on-failure
[Install]
WantedBy=multi-user.target
EOF


systemctl daemon-reload
systemctl restart docker
systemctl enable docker
docker info


```

## 安装docker-compose

```shell
mv /home/<USER>/usr/local/bin/docker-compose
chmod 755 /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose --version
```

## 导入镜像

```shell
# 导出镜像
docker save -o [压缩包名称].tar 镜像名称:版本号
docker save -o <image-name>.tar <image-name>:<image-tag>

# 导入镜像
docker load -i <image-name>.tar
```

