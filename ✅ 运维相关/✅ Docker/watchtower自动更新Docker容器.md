# watchtower自动更新容器

> Watchtower 是一个开源项目，用于自动更新运行 Docker 镜像的容器。当 Watchtower
> 发现镜像有更新时，它会自动拉取最新的镜像，并重新启动容器以应用更新。Watchtower
> 支持多种 Docker 镜像仓库，包括 Docker Hub、Quay.io、Google Container Registry 等。

> https://containrrr.dev/watchtower
>
> https://github.com/containrrr/watchtower

## 启动

```
docker run -d \
    --name watchtower \
    -v /var/run/docker.sock:/var/run/docker.sock \
    containrrr/watchtower
```

## 指定容器

```
docker run -d \
    --name watchtower \
    -v /var/run/docker.sock:/var/run/docker.sock \
    containrrr/watchtower \
    [容器名称]   \
    --cleanup   \
    --interval 5
    
```

## 仓库账号

> --interval Poll interval 秒
>
> --rolling-restart true 每次重新启动一个映像，而不是一次停止并启动所有映像

```shell

docker stop watchtower
docker rm watchtower
docker run -d \
  --restart always \
  --name watchtower \
  -e REPO_USER=admin \
  -e REPO_PASS=cqt@1234 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  58.220.49.186:11000/docker/containrrr/watchtower:latest  \
    --cleanup \
    --interval 10
```

```shell
docker stop watchtower
docker rm watchtower
docker run -d \
  --name watchtower \
  -e REPO_USER=admin \
  -e REPO_PASS=cqt@1234 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  58.220.49.186:11000/docker/containrrr/watchtower:latest  \
    --cleanup \
    --interval 10 \
    ai-communication-assistant-cti \
    ai-communication-assistant-app 
```

## 测试

```shell
docker exec -it watchtower /watchtower --debug --run-once common-charge-business
```
