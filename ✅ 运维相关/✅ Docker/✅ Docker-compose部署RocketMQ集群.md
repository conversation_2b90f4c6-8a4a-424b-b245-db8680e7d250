# docker
```
docker pull dyrnq/rocketmq:4.9.4

// 保存aliyun仓库
docker tag dyrnq/rocketmq:4.9.4 registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
docker push registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4

docker pull candice0630/rocketmq-console-ng:2.0
docker tag candice0630/rocketmq-console-ng:2.0 registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq-console-ng:2.0
docker push registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq-console-ng:2.0

```
# 集群搭建

### 目录创建
```yaml
mkdir -p /home/<USER>/rocketmq/logs/nameserver-a
mkdir -p /home/<USER>/rocketmq/store/nameserver-a

mkdir -p /home/<USER>/rocketmq/logs/broker-a
mkdir -p /home/<USER>/rocketmq/store/broker-a
touch /home/<USER>/rocketmq/broker-a.conf

mkdir -p /home/<USER>/rocketmq/logs/broker-a-s
mkdir -p /home/<USER>/rocketmq/store/broker-a-s
touch /home/<USER>/rocketmq/broker-a-s.conf

mkdir -p /home/<USER>/rocketmq/logs/nameserver-b
mkdir -p /home/<USER>/rocketmq/store/nameserver-b

mkdir -p /home/<USER>/rocketmq/logs/broker-b
mkdir -p /home/<USER>/rocketmq/store/broker-b
touch /home/<USER>/rocketmq/broker-b.conf

mkdir -p /home/<USER>/rocketmq/logs/broker-b-s
mkdir -p /home/<USER>/rocketmq/store/broker-b-s
touch /home/<USER>/rocketmq/broker-b-s.conf
```
### broker-a.conf
```properties
# 所属集群名称，如果节点较多可以配置多个
brokerClusterName = DefaultCluster
#broker名称，master和slave使用相同的名称，表明他们的主从关系
brokerName = broker-a
#0表示Master，大于0表示不同的slave
brokerId = 0
#表示几点做消息删除动作，默认是凌晨4点
deleteWhen = 04
#在磁盘上保留消息的时长，单位是小时
fileReservedTime = 48
#有三个值：SYNC_MASTER，ASYNC_MASTER，SLAVE；同步和异步表示Master和Slave之间同步数据的机制；
brokerRole = SYNC_MASTER
#刷盘策略，取值为：ASYNC_FLUSH，SYNC_FLUSH表示同步刷盘和异步刷盘；SYNC_FLUSH消息写入磁盘后才返回成功状态，ASYNC_FLUSH不需要；
flushDiskType = SYNC_FLUSH
# 设置broker节点所在服务器的ip地址（**这个非常重要,主从模式下，从节点会根据主节点的brokerIP2来同步数据，如果不配置，主从无法同步，brokerIP1设置为自己外网能访问的ip，服务器双网卡情况下必须配置，比如阿里云这种，主节点需要配置ip1和ip2，从节点只需要配置ip1即可）
#brokerIP1 = *************
brokerIP1 = host.docker.internal
#nameServer地址，分号分割
namesrvAddr=rmq-namesrv:9876

#是否允许Broker自动创建Topic
autoCreateTopicEnable = true
#是否允许 Broker 自动创建订阅组
autoCreateSubscriptionGroup = true
#linux开启epoll
useEpollNativeSelector = true

#数据存放的根目录
#storePathRootDir = /root/store/path
#commit log保存目录
#storePathCommitLog = /root/store/path/commitlog
#消费队列存储路径存储路径
#storePathConsumerQueue = /root/store/path/consumequeue

slaveReadEnable = true

#Broker 对外服务的监听端口
listenPort=10911

#主要用于slave同步master
fastListenPort=10909

#haService中使用
haListenPort=10912
```

### broker-a-s.conf
```properties
# 所属集群名称，如果节点较多可以配置多个
brokerClusterName = DefaultCluster
#broker名称，master和slave使用相同的名称，表明他们的主从关系
brokerName = broker-a
#0表示Master，大于0表示不同的slave
brokerId = 1
#表示几点做消息删除动作，默认是凌晨4点
deleteWhen = 04
#在磁盘上保留消息的时长，单位是小时
fileReservedTime = 48
#有三个值：SYNC_MASTER，ASYNC_MASTER，SLAVE；同步和异步表示Master和Slave之间同步数据的机制；
brokerRole = SLAVE
#刷盘策略，取值为：ASYNC_FLUSH，SYNC_FLUSH表示同步刷盘和异步刷盘；SYNC_FLUSH消息写入磁盘后才返回成功状态，ASYNC_FLUSH不需要；
flushDiskType = SYNC_FLUSH
# 设置broker节点所在服务器的ip地址（**这个非常重要,主从模式下，从节点会根据主节点的brokerIP2来同步数据，如果不配置，主从无法同步，brokerIP1设置为自己外网能访问的ip，服务器双网卡情况下必须配置，比如阿里云这种，主节点需要配置ip1和ip2，从节点只需要配置ip1即可）
#brokerIP1 = *************
brokerIP1 = host.docker.internal
#nameServer地址，分号分割
namesrvAddr=rmq-namesrv:9876

#是否允许Broker自动创建Topic
autoCreateTopicEnable = true
#是否允许 Broker 自动创建订阅组
autoCreateSubscriptionGroup = true
#linux开启epoll
useEpollNativeSelector = true

#数据存放的根目录
#storePathRootDir = /root/store/path
#commit log保存目录
#storePathCommitLog = /root/store/path/commitlog
#消费队列存储路径存储路径
#storePathConsumerQueue = /root/store/path/consumequeue

slaveReadEnable = true

#Broker 对外服务的监听端口
listenPort=11911

#主要用于slave同步master
fastListenPort=11909

#haService中使用
haListenPort=11912
```
### broker-b.conf
```properties
# 所属集群名称，如果节点较多可以配置多个
brokerClusterName = DefaultCluster
#broker名称，master和slave使用相同的名称，表明他们的主从关系
brokerName = broker-b
#0表示Master，大于0表示不同的slave
brokerId = 0
#表示几点做消息删除动作，默认是凌晨4点
deleteWhen = 04
#在磁盘上保留消息的时长，单位是小时
fileReservedTime = 48
#有三个值：SYNC_MASTER，ASYNC_MASTER，SLAVE；同步和异步表示Master和Slave之间同步数据的机制；
brokerRole = SYNC_MASTER
#刷盘策略，取值为：ASYNC_FLUSH，SYNC_FLUSH表示同步刷盘和异步刷盘；SYNC_FLUSH消息写入磁盘后才返回成功状态，ASYNC_FLUSH不需要；
flushDiskType = SYNC_FLUSH
# 设置broker节点所在服务器的ip地址（**这个非常重要,主从模式下，从节点会根据主节点的brokerIP2来同步数据，如果不配置，主从无法同步，brokerIP1设置为自己外网能访问的ip，服务器双网卡情况下必须配置，比如阿里云这种，主节点需要配置ip1和ip2，从节点只需要配置ip1即可）
#brokerIP1 = *************
brokerIP1 = host.docker.internal
#nameServer地址，分号分割
namesrvAddr=rmq-namesrv:9876

#是否允许Broker自动创建Topic
autoCreateTopicEnable = true
#是否允许 Broker 自动创建订阅组
autoCreateSubscriptionGroup = true
#linux开启epoll
useEpollNativeSelector = true

#数据存放的根目录
#storePathRootDir = /root/store/path
#commit log保存目录
#storePathCommitLog = /root/store/path/commitlog
#消费队列存储路径存储路径
#storePathConsumerQueue = /root/store/path/consumequeue

slaveReadEnable = true

#Broker 对外服务的监听端口
listenPort=13911

#主要用于slave同步master
fastListenPort=13909

#haService中使用
haListenPort=13912
```
### broker-b-s.conf
```properties
# 所属集群名称，如果节点较多可以配置多个
brokerClusterName = DefaultCluster
#broker名称，master和slave使用相同的名称，表明他们的主从关系
brokerName = broker-b
#0表示Master，大于0表示不同的slave
brokerId = 3
#表示几点做消息删除动作，默认是凌晨4点
deleteWhen = 04
#在磁盘上保留消息的时长，单位是小时
fileReservedTime = 48
#有三个值：SYNC_MASTER，ASYNC_MASTER，SLAVE；同步和异步表示Master和Slave之间同步数据的机制；
brokerRole = SLAVE
#刷盘策略，取值为：ASYNC_FLUSH，SYNC_FLUSH表示同步刷盘和异步刷盘；SYNC_FLUSH消息写入磁盘后才返回成功状态，ASYNC_FLUSH不需要；
flushDiskType = SYNC_FLUSH
# 设置broker节点所在服务器的ip地址（**这个非常重要,主从模式下，从节点会根据主节点的brokerIP2来同步数据，如果不配置，主从无法同步，brokerIP1设置为自己外网能访问的ip，服务器双网卡情况下必须配置，比如阿里云这种，主节点需要配置ip1和ip2，从节点只需要配置ip1即可）
#brokerIP1 = *************
brokerIP1 = host.docker.internal
#nameServer地址，分号分割
namesrvAddr=rmq-namesrv:9876

#是否允许Broker自动创建Topic
autoCreateTopicEnable = true
#是否允许 Broker 自动创建订阅组
autoCreateSubscriptionGroup = true
#linux开启epoll
useEpollNativeSelector = true

#数据存放的根目录
#storePathRootDir = /root/store/path
#commit log保存目录
#storePathCommitLog = /root/store/path/commitlog
#消费队列存储路径存储路径
#storePathConsumerQueue = /root/store/path/consumequeue

slaveReadEnable = true

#Broker 对外服务的监听端口
listenPort=14911

#主要用于slave同步master
fastListenPort=14909

#haService中使用
haListenPort=14912
```
### docker-compose-rocketmq-4.9.4.yml mac
> docker-compose -f docker-compose-rocketmq.yml up -d

```yaml
version: '3'
services:
  rmq-namesrv:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
    container_name: rmq-namesrv
    hostname: rmq-namesrv
    privileged: true
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms128M -Xmx128M -Xmn64M"
    volumes:
      - /home/<USER>/rocketmq/logs/nameserver-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/nameserver-a:/home/<USER>/store
    networks:
      rmq:
        aliases:
          - rmq-namesrv

  rmq-broker-a:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
    container_name: rmq-broker-a
    hostname: rmq-broker-a
    privileged: true
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a
    depends_on:
      - rmq-namesrv

  rmq-broker-a-s:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
    container_name: rmq-broker-a-s
    hostname: rmq-broker-a-s
    privileged: true
    ports:
      - "11911:11911"
      - "11909:11909"
      - "11912:11912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a-s:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a-s:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a-s.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-a

  rmq-broker-b:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
    container_name: rmq-broker-b
    hostname: rmq-broker-b
    privileged: true
    ports:
      - "13911:13911"
      - "13909:13909"
      - "13912:13912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-b:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-b:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-b.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-b
    depends_on:
      - rmq-namesrv

  rmq-broker-b-s:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq:4.9.4
    container_name: rmq-broker-b-s
    hostname: rmq-broker-b-s
    privileged: true
    ports:
      - "14911:14911"
      - "14909:14909"
      - "14912:14912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-b-s:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-b-s:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-b-s.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-b-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-b
        
  rocketmq-console-ng:
    image: registry.cn-hangzhou.aliyuncs.com/kk0/rocketmq-console-ng:2.0
    container_name: rocketmq-console-ng
    hostname: rocketmq-console-ng
    privileged: true
    ports:
      - 18080:8080
    environment:
      JAVA_OPTS: -Xms32m -Xmx32m -Xmn16m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      rmq:
        aliases:
          - rocketmq-console-ng
    depends_on:
      - rmq-namesrv
      - rmq-broker-a
      - rmq-broker-a-s
networks:
  rmq:
    name: rmq
    driver: bridge

# docker network create --subnet=**********/16 --driver bridge rmq

# networks:
#   work:
#     external: true

# docker-compose -f docker-compose-rocketmq.yml down

# docker network create --subnet=***********/24 work
# docker network create --subnet ***********/16 --driver bridge work
# docker network create --subnet ***********/16 --driver bridge --gateway *********** work
# docker exec -it rocketmq-dashboard /bin/bash
# docker exec -it rmq-namesrv /bin/bash
```
### docker-compose-rocketmq-4.9.4.yml  centos
#### 创建子网
> docker network create --subnet=**********/16 --driver bridge --gateway ********** rmq

#### 启动
> docker-compose -f docker-compose-rocketmq.yml up -d
> docker-compose -f docker-compose-rocketmq.yml down
> docker-compose -f docker-compose-rocketmq.yml up --build

#### console地址
> *************:18080

#### 使用
> 

#### 4.9.4.yml
```yaml
version: '3'
services:
  rmq-namesrv:
    image: dyrnq/rocketmq:4.9.4
    container_name: rmq-namesrv
    hostname: rmq-namesrv
    privileged: true
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms128M -Xmx128M -Xmn64M"
    volumes:
      - /home/<USER>/rocketmq/logs/nameserver-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/nameserver-a:/home/<USER>/store
    networks:
      rmq:
        aliases:
          - rmq-namesrv

  rmq-broker-a:
    image: dyrnq/rocketmq:4.9.4
    container_name: rmq-broker-a
    hostname: rmq-broker-a
    privileged: true
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a
    depends_on:
      - rmq-namesrv

  rmq-broker-a-s:
    image: dyrnq/rocketmq:4.9.4
    container_name: rmq-broker-a-s
    hostname: rmq-broker-a-s
    privileged: true
    ports:
      - "11911:11911"
      - "11909:11909"
      - "11912:11912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms128m -Xmx128m -Xmn64m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-4.9.4/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a-s:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a-s:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a-s.conf:/home/<USER>/rocketmq-4.9.4/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-a

  # rocketmq-dashboard:
  #   image: abcdelf/rocketmq-dashboard:1.0.0
  #   container_name: rocketmq-dashboard
  #   hostname: rocketmq-dashboard
  #   privileged: true
  #   healthcheck:
  #     test: "nc -z localhost 8080"
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5
  #   environment:
  #     - ROCKETMQ_CONFIG_NAMESRVADDR=rmq-namesrv:9876
  #   ports:
  #     - 18080:8080
  #   networks:
  #     rmq:
  #       aliases:
  #         - rocketmq-dashboard
  #   depends_on:
  #     - rmq-namesrv
  #     - rmq-broker-a
  #     - rmq-broker-a-s
        
  rocketmq-console-ng:
    image: styletang/rocketmq-console-ng:1.0.0
    container_name: rocketmq-console-ng
    hostname: rocketmq-console-ng
    privileged: true
    ports:
      - 18080:8080
    environment:
      JAVA_OPTS: -Xms32m -Xmx32m -Xmn16m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      rmq:
        aliases:
          - rocketmq-console-ng
    depends_on:
      - rmq-namesrv
      - rmq-broker-a
      - rmq-broker-a-s
networks:
  rmq:
    name: rmq
    driver: bridge

# docker network create --subnet=**********/16 --driver bridge rmq

# networks:
#   work:
#     external: true

# docker-compose -f docker-compose-rocketmq.yml down

# docker network create --subnet=***********/24 work
# docker network create --subnet ***********/16 --driver bridge work
# docker network create --subnet ***********/16 --driver bridge --gateway *********** work
# docker exec -it rocketmq-dashboard /bin/bash
# docker exec -it rmq-namesrv /bin/bash
```
#### 5.1.0.yml
```yaml
version: '5.1.0'
services:
  rmq-namesrv:
    image: apache/rocketmq:5.1.0
    container_name: rmq-namesrv
    hostname: rmq-namesrv
    privileged: true
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    volumes:
      - /home/<USER>/rocketmq/logs/nameserver-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/nameserver-a:/home/<USER>/store
    networks:
      rmq:
        aliases:
          - rmq-namesrv

  rmq-broker-a:
    image: apache/rocketmq:5.1.0
    container_name: rmq-broker-a
    hostname: rmq-broker-a
    privileged: true
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: sh mqbroker -c /home/<USER>/conf/2m-2s-async/broker-a.conf --enable-proxy  &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a.conf:/home/<USER>/conf/2m-2s-async/broker-a.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a
    depends_on:
      - rmq-namesrv

  rmq-broker-a-s:
    image: apache/rocketmq:5.1.0
    container_name: rmq-broker-a-s
    hostname: rmq-broker-a-s
    privileged: true
    ports:
      - "11911:11911"
      - "11909:11909"
      - "11912:11912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: sh mqbroker -c /home/<USER>/conf/2m-2s-async/broker-a-s.conf --enable-proxy  &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-a-s:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-a-s:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-a-s.conf:/home/<USER>/conf/2m-2s-async/broker-a-s.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-a

  rmq-broker-b:
    image: apache/rocketmq:5.1.0
    container_name: rmq-broker-b
    hostname: rmq-broker-b
    privileged: true
    ports:
      - "12911:12911"
      - "12909:12909"
      - "12912:12912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: sh mqbroker -c /home/<USER>/conf/2m-2s-async/broker-b.conf --enable-proxy  &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-b:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-b:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-b.conf:/home/<USER>/conf/2m-2s-async/broker-b.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-b
    depends_on:
      - rmq-namesrv

  rmq-broker-b-s:
    image: apache/rocketmq:5.1.0
    container_name: rmq-broker-b-s
    hostname: rmq-broker-b-s
    privileged: true
    ports:
      - "13911:13911"
      - "13909:13909"
      - "13912:13912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: sh mqbroker -c /home/<USER>/conf/2m-2s-async/broker-b-s.conf --enable-proxy  &
    volumes:
      - /home/<USER>/rocketmq/logs/broker-b-s:/home/<USER>/logs
      - /home/<USER>/rocketmq/store/broker-b-s:/home/<USER>/store
      - /home/<USER>/rocketmq/broker-b-s.conf:/home/<USER>/conf/2m-2s-async/broker-b-s.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-b-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-b

  rocketmq-dashboard:
    image: apacherocketmq/rocketmq-dashboard:latest
    container_name: rocketmq-dashboard
    hostname: rocketmq-dashboard
    privileged: true
    ports:
      - 18080:8080
    environment:
      JAVA_OPTS: -Xms32m -Xmx32m -Xmn16m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      rmq:
        aliases:
          - rocketmq-dashboard
    depends_on:
      - rmq-namesrv
      - rmq-broker-a
      - rmq-broker-a-s   
      - rmq-broker-b
      - rmq-broker-b-s    

networks:
  rmq:
    name: rmq
    driver: bridge

# docker network create --subnet=**********/16 --driver bridge rmq

# networks:
#   work:
#     external: true

# docker-compose -f docker-compose-rocketmq.yml down

# docker network create --subnet=***********/24 work
# docker network create --subnet ***********/16 --driver bridge work
# docker network create --subnet ***********/16 --driver bridge --gateway *********** work
# docker exec -it rocketmq-dashboard /bin/bash
# docker exec -it rmq-namesrv /bin/bash
```



# test

```yaml
version: '3'
services:
  rmq-namesrv:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-namesrv
    hostname: rmq-namesrv
    privileged: true
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms256M -Xmx256M -Xmn128M"
    volumes:
      - /home/<USER>/logs/nameserver-a:/home/<USER>/logs
      - /home/<USER>/store/nameserver-a:/home/<USER>/store
    networks:
      rmq:
        aliases:
          - rmq-namesrv

  rmq-broker-a:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-a
    hostname: rmq-broker-a
    privileged: true
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms256m -Xmx256m -Xmn128m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-a:/home/<USER>/logs
      - /home/<USER>/store/broker-a:/home/<USER>/store
      - /home/<USER>/broker-a.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a
    depends_on:
      - rmq-namesrv

  rmq-broker-a-s:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-a-s
    hostname: rmq-broker-a-s
    privileged: true
    ports:
      - "11911:11911"
      - "11909:11909"
      - "11912:11912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms256m -Xmx256m -Xmn128m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-a-s:/home/<USER>/logs
      - /home/<USER>/store/broker-a-s:/home/<USER>/store
      - /home/<USER>/broker-a-s.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-a

  rmq-broker-b:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-b
    hostname: rmq-broker-b
    privileged: true
    ports:
      - "12911:12911"
      - "12909:12909"
      - "12912:12912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms256m -Xmx256m -Xmn128m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-b:/home/<USER>/logs
      - /home/<USER>/store/broker-b:/home/<USER>/store
      - /home/<USER>/broker-b.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf
    networks:
      rmq:
        aliases:
          - rmq-broker-b
    depends_on:
      - rmq-namesrv

  rmq-broker-b-s:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-b-s
    hostname: rmq-broker-b-s
    privileged: true
    ports:
      - "13911:13911"
      - "13909:13909"
      - "13912:13912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms256m -Xmx256m -Xmn128m -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=32m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-b-s:/home/<USER>/logs
      - /home/<USER>/store/broker-b-s:/home/<USER>/store
      - /home/<USER>/broker-b-s.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf
    networks:
      rmq:
        aliases:
          - rmq-broker-b-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-b


  rocketmq-dashboard:
    image: *************:11000/docker/rocketmq-dashboard:latest
    container_name: rocketmq-dashboard
    hostname: rocketmq-dashboard
    privileged: true
    ports:
      - 18080:8080
    environment:
      JAVA_OPTS: -Xms64m -Xmx64m -Xmn32m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      rmq:
        aliases:
          - rocketmq-dashboard
    depends_on:
      - rmq-namesrv
      - rmq-broker-a
      - rmq-broker-a-s   
      - rmq-broker-b
      - rmq-broker-b-s
        
  # rocketmq-console-ng:
  #   image: styletang/rocketmq-console-ng:1.0.0
  #   container_name: rocketmq-console-ng
  #   hostname: rocketmq-console-ng
  #   privileged: true
  #   ports:
  #     - 18080:8080
  #   environment:
  #     JAVA_OPTS: -Xms64m -Xmx64m -Xmn32m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
  #   networks:
  #     rmq:
  #       aliases:
  #         - rocketmq-console-ng
  #   depends_on:
  #     - rmq-namesrv
  #     - rmq-broker-a
  #     - rmq-broker-a-s
networks:
  rmq:
    name: rmq
    driver: bridge

# docker network create --subnet=**********/16 --driver bridge rmq

# networks:
#   work:
#     external: true

# docker-compose -f docker-compose-rocketmq.yml down

# docker network create --subnet=***********/24 work
# docker network create --subnet ***********/16 --driver bridge work
# docker network create --subnet ***********/16 --driver bridge --gateway *********** work
# docker exec -it rocketmq-dashboard /bin/bash
# docker exec -it rmq-namesrv /bin/bash

```

# 8g

```yaml
version: '3'
services:
  rmq-namesrv:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-namesrv
    hostname: rmq-namesrv
    privileged: true
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms1g -Xmx1g -Xmn512M"
    volumes:
      - /home/<USER>/logs/nameserver-a:/home/<USER>/logs
      - /home/<USER>/store/nameserver-a:/home/<USER>/store
    networks:
      rmq:
        aliases:
          - rmq-namesrv

  rmq-broker-a:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-a
    hostname: rmq-broker-a
    privileged: true
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms1g -Xmx1g -Xmn512M -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-a:/home/<USER>/logs
      - /home/<USER>/store/broker-a:/home/<USER>/store
      - /home/<USER>/broker-a.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a
    depends_on:
      - rmq-namesrv

  rmq-broker-a-s:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-a-s
    hostname: rmq-broker-a-s
    privileged: true
    ports:
      - "11911:11911"
      - "11909:11909"
      - "11912:11912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms1g -Xmx1g -Xmn512M -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-a-s:/home/<USER>/logs
      - /home/<USER>/store/broker-a-s:/home/<USER>/store
      - /home/<USER>/broker-a-s.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf 
    networks:
      rmq:
        aliases:
          - rmq-broker-a-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-a

  rmq-broker-b:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-b
    hostname: rmq-broker-b
    privileged: true
    ports:
      - "12911:12911"
      - "12909:12909"
      - "12912:12912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms1g -Xmx1g -Xmn512M -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-b:/home/<USER>/logs
      - /home/<USER>/store/broker-b:/home/<USER>/store
      - /home/<USER>/broker-b.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf
    networks:
      rmq:
        aliases:
          - rmq-broker-b
    depends_on:
      - rmq-namesrv

  rmq-broker-b-s:
    image: *************:11000/docker/rocketmq:${ROCKET_VETSION}
    container_name: rmq-broker-b-s
    hostname: rmq-broker-b-s
    privileged: true
    ports:
      - "13911:13911"
      - "13909:13909"
      - "13912:13912"
    environment:
      NAMESRV_ADDR: rmq-namesrv:9876
      JAVA_OPT_EXT: "-server -Xms1g -Xmx1g -Xmn512M -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
    command: sh mqbroker -c /home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf  autoCreateTopicEnable=true &
    volumes:
      - /home/<USER>/logs/broker-b-s:/home/<USER>/logs
      - /home/<USER>/store/broker-b-s:/home/<USER>/store
      - /home/<USER>/broker-b-s.conf:/home/<USER>/rocketmq-${ROCKET_VETSION}/conf/broker.conf
    networks:
      rmq:
        aliases:
          - rmq-broker-b-s
    depends_on:
      - rmq-namesrv
      - rmq-broker-b


  rocketmq-dashboard:
    image: *************:11000/docker/rocketmq-dashboard:latest
    container_name: rocketmq-dashboard
    hostname: rocketmq-dashboard
    privileged: true
    ports:
      - 18080:8080
    environment:
      JAVA_OPTS: -Xms512m -Xmx512m -Xmn256m -Drocketmq.namesrv.addr=rmq-namesrv:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      rmq:
        aliases:
          - rocketmq-dashboard
    depends_on:
      - rmq-namesrv
      - rmq-broker-a
      - rmq-broker-a-s   
      - rmq-broker-b
      - rmq-broker-b-s
        
networks:
  rmq:
    name: rmq
    driver: bridge


```

