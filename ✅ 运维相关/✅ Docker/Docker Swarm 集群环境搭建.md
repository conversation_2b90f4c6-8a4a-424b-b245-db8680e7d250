# Docs

> https://yeasy.gitbook.io/docker_practice/swarm_mode/deploy

# Docker Swarm 集群环境搭建

角色 IP HOSTNAME Docker 版本
Manager ************** manager1 19.03.12
Manager ************** manager2 19.03.12
Manager ************** manager3 19.03.12
Worker ************** worker1 19.03.12
Worker ************** worker2 19.03.12

```shell

# 创建集群
docker swarm init --advertise-addr **************
# 管理节点执行查看管理节点的令牌信息
docker swarm join-token manager

# 其他管理节点加入集群
docker swarm join --token SWMTKN-1-5s9qg9qn372vask9qz1xnxdicia3u1n80lem1eau5nyn7yulls-85n4inwwvhrnljya2d3l46pah **************:2377

# 工作节点加入集群
# 管理节点先运行 docker swarm join-token worker 命令查看工作节点的令牌信息

# 工作节点执行
docker swarm join --token SWMTKN-1-5s9qg9qn372vask9qz1xnxdicia3u1n80lem1eau5nyn7yulls-f1izea7joj7sm9ihzh32yrshs **************:2377

# 变更角色
docker node ls # 查看节点
docker node update --role [manager|worker] [node_id]
```

## 查看集群docker info

![](./images/1706690434639.png)

## 查看集群节点

```shell
docker node ls
```

## 节点标签

```shell
docker node update --label-add [label_key]=[label_value] [node_id]
docker node inspect [node_id] # 节点详情
docker node update --label-rm auth --label-rm [label_key] [node_id] # 删除标签
```

## 部署服务

```shell
# 创建docker网桥
docker network create -d overlay --attachable swarm-net

 docker stack deploy -c docker-compose-nacos-cluster.yml nacos
 docker stack deploy -c docker-compose-xxl-job-admin.yml xxl-job-admin
 
 docker stack rm nacos
  docker stack rm xxl-job-admin
  
 docker stack services nacos
 
 docker service logs -f nacos_nacos02
```
