# docker-composes部署xxljob集群

## docker-compose-xxl-job-admin.yml

```yaml
version: "2.4.0"
services:
  xxl-job-admin:
    restart: always
    image: 58.220.49.186:11000/docker/xxl-job-admin:2.4.0
    container_name: xxl-job-admin
    volumes:
      - ./logs:/data/applogs
    environment:
      PARAMS: '
      --server.port=8800
      --server.servlet.context-path=/xxl-job-admin
      --spring.datasource.url=********************************************************************************************************************************
      --spring.datasource.username=root
      --spring.datasource.password=cqt@fj889977
      --xxl.job.accessToken=Lpoms_xxljob_default_token'
    network_mode: host
```

```
docker create --name xxl-job-admin -p 8080:8080 \
-e PARAMS="\
--spring.datasource.url=******************************************************************************** \
--spring.datasource.username=【数据库账号】 \
--spring.datasource.password=【数据库密码】" \
-v /usr/local/xxl-job/admin/logs:/data/applogs \
--privileged=true \
xuxueli/xxl-job-admin:2.3.0
```

```
version: "3.9"
services:
  xxl-job-admin:
    restart: always
    # docker 镜像
    image: xuxueli/xxl-job-admin:${NACOS_VERSION}
    # 容器名称
    container_name: xxl-job-admin
    volumes:
      # 日志目录映射到主机目录
      - /xxl-job-admin/logs:/data/applogs
    environment:
      # 设置启动参数
      PARAMS: '
      --server.port=8800
      --server.servlet.context-path=/xxl-job-admin
      --spring.datasource.url=********************************************************************************************************************************
      --spring.datasource.username=root
      --spring.datasource.password=cqt@fj889977
      --spring.mail.host=smtp.qq.com
      --spring.mail.port=465
      --spring.mail.username=<EMAIL>
      --spring.mail.from=<EMAIL>
      --spring.mail.password=password123456
      --spring.mail.properties.mail.smtp.starttls.enable=true
      --spring.mail.properties.mail.smtp.starttls.required=true
      --xxl.job.accessToken=Lpoms_xxljob_default_token'
    network_mode: host
```



