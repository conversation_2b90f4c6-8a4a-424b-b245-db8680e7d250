# 离线安装Docker

## 下载Docker镜像

> http://*************:9999/common/docker/docker-27.0.3.tgz
>
> http://*************:9999/common/docker/docker-compose-linux-x86_64
>
> 复制到服务器/home目录下

```shell
/home/<USER>
/home/<USER>
```

## 安装Docker

```shell

tar -zxvf /home/<USER>
cp -p /home/<USER>/* /usr/bin
rm -rf /home/<USER>/*

mkdir -p /etc/docker
tee /etc/docker/daemon.json <<-'EOF'
{
    "data-root": "/home/<USER>",
    "insecure-registries" : [ "*************:11000", "harbor.cqt.com:11000" ],
    "log-driver":"json-file",
    "log-opts": {"max-size":"50m", "max-file":"3"},
    "data-root": "/home/<USER>"
}
EOF


tee /usr/lib/systemd/system/docker.service <<-'EOF'
[Unit]
Description=Docker Application Container Engine
Documentation=http://docs.docker.com
After=network.target docker.socket
[Service]
Type=notify
EnvironmentFile=-/run/flannel/docker
WorkingDirectory=/usr/local/bin
ExecStart=/usr/bin/dockerd \
                -H unix:///var/run/docker.sock \
                --selinux-enabled=false 
ExecReload=/bin/kill -s HUP $MAINPID
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
TimeoutStartSec=0
Delegate=yes
KillMode=process
Restart=on-failure
[Install]
WantedBy=multi-user.target
EOF


systemctl daemon-reload
systemctl restart docker
systemctl enable docker
docker info

mv /home/<USER>/usr/local/bin/docker-compose
chmod 755 /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose --version
 
```

# 导出服务镜像

```shell

# 导出镜像
docker save -o [压缩包名称].tar 镜像名称:版本号
docker save -o lc-cloudcc-cdr-inside.tar *************:11000/cloudcc-prod/lc-cloudcc-cdr-inside:v7.0.1.10

scp -P 56022 lc-cloudcc-cdr-inside.tar yunwei@10.254.12.101:/home

# 导入镜像
docker load -i /home/<USER>

```

