# Docker-compose限制容器cpu

> https://docs.docker.com/compose/compose-file/deploy/#resources

## 限制yml配置

```yaml
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1g 
```

```yaml
    deploy:
      resources:
        limits:
          cpus: '2000M'
          memory: 2G
        reservations:
          cpus: '200M'
          memory: 1g 
```

### 模拟CPU 飙高

```shell
docker exec -it lc-cloudcc-monitor /bin/sh
for i in $(seq `grep -c ^proc /proc/cpuinfo`); do (yes > /dev/null &); done

docker inspect lc-cloudcc-monitor | grep Cpu

docker stats lc-cloudcc-monitor
```

![](./images/1706681154052.png)

