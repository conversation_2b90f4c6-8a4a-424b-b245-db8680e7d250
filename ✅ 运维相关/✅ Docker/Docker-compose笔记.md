## restart属性

在 docker-compose.yaml 文件的服务定义中，可以通过 restart 属性来指定容器的重启策略。常见的选项包括：

- no：不自动重启容器，即使容器退出或者 Docker 守护进程重启。
- always：无论容器的结束状态是什么，容器始终应该自动重新启动。
- on-failure：只有在容器非正常退出时才会重新启动。此时还可以通过 restart: 选项中的 max-retries 子选项指定最大重启次数，以避免无限尝试重启运行失败的容器。
- unless-stopped：容器只有在用户显式地停止或删除时才会停止，否则 Docker Compose 将自动重新启动容器。

总之，docker-compose restart: always 表示在使用 Docker Compose 启动容器时，无论容器是何种状态，都应该始终自动重新启动。这种重启策略适用于那些需要保持服务连续性和可用性的情况。
