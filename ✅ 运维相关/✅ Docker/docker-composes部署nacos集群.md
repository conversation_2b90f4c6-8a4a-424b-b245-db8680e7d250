# docker-composes部署nacos集群

##  docker-compose-nacos.yml

```yaml
version: "3.8"
services:
  nacos:
    image: *************:11000/docker/nacos-server:${NACOS_VERSION}
    container_name: nacos
    restart: always
    volumes:
      - ./nacos/logs:/home/<USER>/logs
      - ./nacos/data:/home/<USER>/data
    env_file:
      - ./nacos.env
    network_mode: host
```

## nacos.env

```properties
NACOS_APPLICATION_PORT=8846
# 这个端口配置没有生效!!!
NACOS_SERVER_PORT=8848
NACOS_SERVERS=**************:8848 **************:8848 **************:8848
SPRING_DATASOURCE_PLATFORM=mysql
MYSQL_SERVICE_HOST=**************
MYSQL_SERVICE_DB_NAME=nacos_config
MYSQL_SERVICE_PORT=3300
MYSQL_SERVICE_USER=root
MYSQL_SERVICE_PASSWORD=cqt@fj889977
MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true

# 开启账号密码登录
NACOS_AUTH_ENABLE=true
# 这几个配置暂时不是怎么用???
NACOS_AUTH_IDENTITY_KEY=2222
NACOS_AUTH_IDENTITY_VALUE=2xxx
NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789

JVM_XMS=2g
JVM_XMX=2g
JVM_XMN=1g
JVM_MS=256M
JVM_MMS=512M
```

## .env

```properties
NACOS_VERSION=v2.2.3
```

![image-20230722165905159](images/image-20230722165905159.png)