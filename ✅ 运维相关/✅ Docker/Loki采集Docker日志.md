# 采集Docker日志

## 安装lock server

## 安装loki插件

```shell
docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
docker plugin disable loki --force
docker plugin upgrade loki grafana/loki-docker-driver:latest --grant-all-permissions
docker plugin enable loki
systemctl restart docker
```

## 修改配置文件

> /etc/docker/daemon.json
>

```json
{
  "registry-mirrors": [
    "https://hub-mirror.c.163.com",
    "https://docker.mirrors.ustc.edu.cn"
  ],
  "insecure-registries": [
    "*************:11000"
  ],
  "log-driver": "loki",
  "log-opts": {
    "loki-url": "http://*************:3100/loki/api/v1/push",
    "loki-batch-size": "400"
  },
  "data-root": "/home/<USER>"
}
```

```shell
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://hub-mirror.c.163.com",
    "https://docker.mirrors.ustc.edu.cn"
  ],
  "insecure-registries": [
    "*************:11000"
  ],
  "log-driver": "loki",
  "log-opts": {
    "loki-url": "http://*************:3100/loki/api/v1/push",
    "loki-batch-size": "400",
    "loki-retries": "5",
    "max-size": "50m",
    "max-file": "10"
  },
  "data-root": "/home/<USER>"
}
EOF

systemctl restart docker
 
```

## 需要重建容器

```shell
docker inspect -f '{{.HostConfig.LogConfig}}' nacos

{json-file map[max-file:10 max-size:100m]}
```

## docker-compose配置loki采集

```yaml
x-logging:
  &loki-logging
  driver: loki
  options:
    loki-url: "http://************:3100/loki/api/v1/push"
    max-size: "50m"
    max-file: "3"
    labels:
      - dev
services:
  ai-quality-inspection:
  image: ${HARBOR}/rcsp-${ENV}/ai-quality-inspection:${AI_QUALITY_INSPECTION_VERSION}
  hostname: ai-quality-inspection
  container_name: ai-quality-inspection
  restart: always
  privileged: true
  volumes:
    - /home/<USER>/logs/ai-quality-inspection:/home/<USER>/logs/ai-quality-inspection
    - /home/<USER>/home/<USER>
    - ./oom.sh:/home/<USER>
  environment:
    TZ: ${TZ}
    NACOS_SERVER: ${NACOS_SERVER}
    NACOS_NAMESPACE: ${NACOS_NAMESPACE}
    NACOS_USERNAME: ${NACOS_USERNAME}
    NACOS_PASSWORD: ${NACOS_PASSWORD}
    SERVER_IP: ${SERVER_IP}
    ROBOT_URL: ${ROBOT_URL}
    JAVA_OPTS: -XX:+UseZGC
      -XX:+ZGenerational
      -XX:+UnlockExperimentalVMOptions
      -XX:+UseContainerSupport
      -XX:MinRAMPercentage=${HEAP_PERCENTAGE}
      -XX:MaxRAMPercentage=${HEAP_PERCENTAGE}
      -XX:-OmitStackTraceInFastThrow
      -XX:+EnableDynamicAgentLoading
      -Djdk.virtualThreadScheduler.parallelism=32
      -Djdk.virtualThreadScheduler.maxPoolSize=32
      -Djdk.virtualThreadScheduler.minRunnable=32
  network_mode: host
  deploy:
    resources:
      limits:
        cpus: ${AI_QUALITY_INSPECTION_CPUS}
        memory: ${AI_QUALITY_INSPECTION_MEMORY}
  healthcheck:
    test: [ "CMD-SHELL", "curl --silent --fail http://127.0.0.1:7009/ai-quality-inspection-api/actuator/health || exit 1" ]
    interval: 15s
    timeout: 5s
    start_period: 90s
    retries: 3
  logging: *loki-logging

```