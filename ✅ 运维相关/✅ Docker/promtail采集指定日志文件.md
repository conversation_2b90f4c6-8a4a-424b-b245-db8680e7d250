# promtail采集指定日志文件

> https://grafana.com/docs/loki/latest/send-data/promtail/stages/regex/
## docker-compose
```yaml
version: '3.8'

networks:
  loki:

services:
  promtail:
    image: grafana/promtail:2.8.2
    container_name: promtail
    restart: unless-stopped
    volumes:
      - ./promtail/logs:/var/logs
      - ./config:/etc/promtail/config
      - ./data/log:/logs
      - /home/<USER>/logs:/home/<USER>/logs
    user: "0"
    command: -config.file=/etc/promtail/config/promtail-config.yaml
    networks:
      - loki

```
### promtail-config.yaml
```yaml
server:
  http_listen_port: 9081
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://*************:3100/loki/api/v1/push
    batchwait: 10s
    batchsize: 3145728
scrape_configs:
  - job_name: tengine-access_80
    pipeline_stages:
      - match:
          selector: '{job="tengine-access_80"}'
          action: drop
          regex: '.*xxl-job-admin.*'
    static_configs:
    - targets:
        - localhost
      labels:
        os: centos
        log_name: nginx79_access
        host: nginx79
        __path__: /home/<USER>/logs/access_80.log

  - job_name: tengine-error_80
    static_configs:
    - targets:
        - localhost
      labels:
        os: centos
        log_name: nginx79_error
        host: nginx79
        __path__: /home/<USER>/logs/error_80.log

```
