
```java
docker pull registry

docker run -d -p 5000:5000 --name registry docker.io/registry

vim /etc/docker/daemon.json

"insecure-registries": ["172.16.251.53:5000"]
systemctl restart docker

http://172.16.251.53:5000/v2/_catalog
```
# 测试

```java
docker pull hello-world

# 标记hello-world该镜像需要推送到私有仓库
docker tag hello-world:latest 172.16.251.53:5000/hello-world:latest

# 通过push指令推送到私有仓库
docker push 172.16.251.53:5000/hello-world:latest


# 标记hello-world该镜像需要推送到私有仓库
docker tag hello-world:latest 127.0.0.1:5000/hello-world:latest

# 通过push指令推送到私有仓库
docker push 127.0.0.1:5000/hello-world:latest

docker pull 127.0.0.1:5000/hello-world:latest

```
```java
docker build -t k8s-demo:v1 .
    
    docker tag k8s-demo:v1 172.16.251.53:5000/k8s-demo:v1.0

    docker push 172.16.251.53:5000/k8s-demo:v1.0
        
        docker pull 172.16.251.53:5000/k8s-demo:v1.0
```
