# 准备
```java
************ k8smaster
************ k8snode1
************* k8snode2

```
```java
# 关闭防火墙
systemctl stop firewalld
systemctl disable firewalld

# 关闭selinux
sed -i 's/enforcing/disabled/' /etc/selinux/config  # 永久
setenforce 0  # 临时

# 关闭swap
swapoff -a  # 临时
sed -ri 's/.*swap.*/#&/' /etc/fstab    # 永久

# 根据规划设置主机名
hostnamectl set-hostname <hostname>

# 在master添加hosts
cat >> /etc/hosts << EOF
************ k8smaster
************ k8snode1
************* k8snode2
EOF

# 将桥接的IPv4流量传递到iptables的链
cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
EOF
sysctl --system  # 生效

# 时间同步
yum install ntpdate -y
ntpdate time.windows.com
```
# 安装docker
```java
 wget https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo -O /etc/yum.repos.d/docker-ce.repo
 yum -y install docker-ce
systemctl enable docker && systemctl start docker
 docker --version
 
 cat > /etc/docker/daemon.json << EOF
{
  "registry-mirrors": ["https://b9pmyelo.mirror.aliyuncs.com"]
}
EOF
```
# k8s阿里yun源
```java
cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://mirrors.aliyun.com/kubernetes/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=0
repo_gpgcheck=0
gpgkey=https://mirrors.aliyun.com/kubernetes/yum/doc/yum-key.gpg https://mirrors.aliyun.com/kubernetes/yum/doc/rpm-package-key.gpg
EOF
```
# 安装k8s
```java
yum install -y kubelet-1.18.0 kubeadm-1.18.0 kubectl-1.18.0
 systemctl enable kubelet
```
# 部署k8s master
> ************ k8smaster

```java
kubeadm init \
  --apiserver-advertise-address=************ \
  --image-repository registry.aliyuncs.com/google_containers \
  --kubernetes-version v1.18.0 \
  --service-cidr=*********/12 \
  --pod-network-cidr=**********/16
    
    以上命令完成之后
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config
```
# 节点加入集群
> ************ k8snode1
> ************* k8snode2

```java
节点执行
kubeadm join ************:6443 --token 59yxrl.1807c7a78knfji9i \
    --discovery-token-ca-cert-hash sha256:5199171849cf2fda659d9c3d1a837c8c40767cfe211e0f80b3eabaca7fab411a
```
# 部署CNI网络插件
> master节点执行

```java
wget https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

默认镜像地址无法访问，sed命令修改为docker hub镜像仓库。
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

kubectl get pods -n kube-system

```
# 测试
```java
 kubectl create deployment nginx --image=nginx
 kubectl expose deployment nginx --port=80 --type=NodePort
 kubectl get pod,svc
```
# 命令
```java
查看集群 kubectl get nodes

删除pod
kubectl delete pod POD_NAME -n NAMESPACE_NAME
kubectl get pod
kubectl delete pod nginx-f89759699-gfwhk 

删除deployment
kubectl delete deployment DEPLOYMENT_NAME -n NAMESPACE_NAME
kubectl get deployment
kubectl delete deployment nginx

删除service
kubectl delete svc SERVICE_NAME -n NAMESPACE_NAME


删除所有
kubectl delete deployment -all
kubectl delete pod --all

```
# 安装 **kuboard**
> master节点执行

```java
kubectl apply -f https://kuboard.cn/install-script/kuboard.yaml
kubectl apply -f https://addons.kuboard.cn/metrics-server/0.3.7/metrics-server.yaml
 
查看运行状态  kubectl get pods -l k8s.kuboard.cn/name=kuboard -n kube-system


# 如果您参考 www.kuboard.cn 提供的文档安装 Kuberenetes，可在第一个 Master 节点上执行此命令
echo $(kubectl -n kube-system get secret $(kubectl -n kube-system get secret | grep ^kuboard-user | awk '{print $1}') -o go-template='{{.data.token}}' | base64 -d)
 
    
    token:
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


```
```java
访问  http://************:32567/dashboard
```
# springboot 
> [https://segmentfault.com/a/****************](https://segmentfault.com/a/****************)

```java
apiVersion: apps/v1
kind: Deployment #部署
metadata:
  name: k8s-demo
spec:
  replicas: 2 #2个副本
  selector:
    matchLabels:
      app: k8s-demo
  template:
    metadata:
      labels:
        app: k8s-demo
    spec:
      containers:
      - name: k8s-demo
        image: *************:5000/k8s-demo:v1.0 #刚刚push到阿里云上的镜像地址
        ports:
        - containerPort: 8900 #默认springboot端口 

---

apiVersion: networking.k8s.io/v1
kind: Service
metadata:
  name: k8s-demo
spec:
  selector:
    app: k8s-demo #选中上面的 Deployment
  ports:
  - port: 7003 #对外7003端口
    targetPort: 8900


--- 
#Ingress 
apiVersion: v1
kind: Ingress
metadata:
  name: myingress
  labels:
    name: myingress
spec:
  IngressClass: nginx
  rules:
  - host: k8s-demo.com #所有的host这个域名请求 转发到上面的 Service= springboot-app
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: k8s-demo # 转发到 这个Service 
            port: 
              number: 7003 

```
