# 1. 下载地址
[https://download.docker.com/linux/static/stable/x86_64/](https://download.docker.com/linux/static/stable/x86_64/)
```shell
wget https://download.docker.com/linux/static/stable/x86_64/docker-20.10.9.tgz
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612489378478-0057c9a7-f195-437f-a534-6d350472e8c7.png#height=400&id=VNJAs&originHeight=400&originWidth=1135&originalType=binary&ratio=1&rotation=0&showTitle=false&size=68111&status=done&style=none&title=&width=1135)
# 2. 一键安装脚本
### install.sh 安装

```shell
#!/bin/sh
echo '解压tar包...'
tar -xvf $1

echo '将docker目录移到/usr/bin目录下...'
cp docker/* /usr/bin/

echo '将docker.service 移到/etc/systemd/system/ 目录...'
cp docker.service /etc/systemd/system/

echo '添加文件权限...'
chmod +x /etc/systemd/system/docker.service

echo '重新加载配置文件...'
systemctl daemon-reload

echo '启动docker...'
systemctl start docker

echo '设置开机自启...'
systemctl enable docker.service

echo 'docker安装成功...'
docker -v
```

### uninstall.sh 卸载

```shell
#!/bin/sh

echo '删除docker.service...'
rm -f /etc/systemd/system/docker.service

echo '删除docker文件...'
rm -rf /usr/bin/docker*

echo '重新加载配置文件'
systemctl daemon-reload

echo '卸载成功...'
```
### docker.service

```shell
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
# the default is not to use systemd for cgroups because the delegate issues still
# exists and systemd currently does not support the cgroup feature set required
# for containers run by docker
ExecStart=/usr/bin/dockerd
ExecReload=/bin/kill -s HUP $MAINPID
# Having non-zero Limit*s causes performance problems due to accounting overhead
# in the kernel. We recommend using cgroups to do container-local accounting.
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
# Uncomment TasksMax if your systemd version supports it.
# Only systemd 226 and above support this version.
#TasksMax=infinity
TimeoutStartSec=0
# set delegate yes so that systemd does not reset the cgroups of docker containers
Delegate=yes
# kill only the docker process, not all processes in the cgroup
KillMode=process
# restart the docker process if it exits prematurely
Restart=on-failure
StartLimitBurst=3
StartLimitInterval=60s

[Install]
WantedBy=multi-user.target
```

# 3. 安装
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/*************-c4b2d95e-b707-44f9-8fe7-04b7cb96d733.png#height=466&id=qx8h7&originHeight=466&originWidth=931&originalType=binary&ratio=1&rotation=0&showTitle=false&size=43561&status=done&style=none&title=&width=931)
