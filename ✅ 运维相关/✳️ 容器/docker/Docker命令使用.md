# 
# [https://yeasy.gitbook.io/docker_practice/](https://yeasy.gitbook.io/docker_practice/)![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612491781719-e42dd248-07f7-4b3a-bdf5-d9e9c33ae815.png#align=left&display=inline&height=417&originHeight=417&originWidth=892&size=42703&status=done&style=none&width=892)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612491797780-9dd35aba-d76c-4062-b8da-259937887f5e.png#align=left&display=inline&height=296&originHeight=296&originWidth=608&size=22179&status=done&style=none&width=608)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612491909142-06173fd9-1ff3-4db8-a709-ff1370ae2789.png#align=left&display=inline&height=875&originHeight=875&originWidth=943&size=104191&status=done&style=none&width=943)

```shell
# 删除none镜像
docker images  | grep none | awk '{print $3}' | xargs docker rmi
```

# 1. docker pull

```shell
到仓库拉取镜像
docker pull [选项] [Docker Registry 地址[:端口号]/]仓库名[:标签]

镜像是多层存储, 下载也是一层层pull, 每一层都有唯一id  显示前12位
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612491397358-db77175b-a2b6-4afb-85ed-97dd9484eab2.png#align=left&display=inline&height=323&originHeight=323&originWidth=846&size=34791&status=done&style=none&width=846)
# 2. docker run 运行容器

```shell
docker run -it ubuntu:18.04 bash

-it：这是两个参数，一个是 -i：交互式操作，一个是 -t 终端。我们这里打算进入 bash 执行一些命令并查看返回结果，因此我们需要交互式终端。
--rm：这个参数是说容器退出后随之将其删除。默认情况下，为了排障需求，退出的容器并不会立即删除，除非手动 docker rm。我们这里只是随便执行个命令，看看结果，不需要排障和保留结果，因此使用 --rm 可以避免浪费空间。
ubuntu:18.04：这是指用 ubuntu:18.04 镜像为基础来启动容器。
bash：放在镜像名后的是 命令，这里我们希望有个交互式 Shell，因此用的是 bash。


```
## nginx

```shell
运行一个nginx
docker run --name webserver -d -p 80:80 nginx
	docker exec -it webserver bash 进入bash命令
  	安装目录 /usr/share/nginx
	docker diff webserver 查看变动
  
 -d 参数默认不会进入容器, 后台运行
 -P:将容器内部使用的网络端口随机映射到我们使用的主机上。  
 			外:内  默认80, 映射到 80 端口
      -p <宿主端口>:<容器端口>
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612492042480-f789d7ec-54cf-4006-8a54-059dd21b777f.png#align=left&display=inline&height=125&originHeight=125&originWidth=675&size=12622&status=done&style=none&width=675)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612492175455-4f8c8dfb-5755-4f72-ae43-d4ea0cae4bdc.png#align=left&display=inline&height=334&originHeight=334&originWidth=896&size=36349&status=done&style=none&width=896)
# 3. docker images  镜像列表

```shell
docker image ls		列出所有顶层镜像
docker images
仓库名、标签、镜像 ID、创建时间 以及 所占用的空间

docker image ls -a
	如果希望显示包括中间层镜像在内的所有镜像的话，需要加 -a 参数。

docker image ls ubuntu   指定镜像

 docker image ls -f since=mongo:3.2
 	过滤器参数 --filter，或者简写 -f     之前before

docker image ls -q  列出镜像id  
docker image ls --format "table {{.ID}}\t{{.Repository}}\t{{.Tag}}"  表格

docker image ls --digests  镜像摘要

```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612492237897-0025645e-53e9-4fde-a8da-b02263e7fa68.png#align=left&display=inline&height=150&originHeight=150&originWidth=923&size=19147&status=done&style=none&width=923)

```shell
查看镜像、容器、数据卷所占用的空间。
docker system df
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612492537614-7e63541c-e6e0-4bc0-9f1a-5828572e6b67.png#align=left&display=inline&height=125&originHeight=125&originWidth=930&size=12567&status=done&style=none&width=930)
```shell
虚悬镜像
docker image ls -f dangling=true
删除虚悬镜像
docker image prune  
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612492509363-383dd29b-6e68-4076-9f41-07ec3aafb8d5.png#align=left&display=inline&height=126&originHeight=126&originWidth=911&size=15067&status=done&style=none&width=911)
# 4. docker image rm  删除本地镜像

```shell
docker image rm [选项] <镜像1> [<镜像2> ...]
<镜像> 可以是 镜像短 ID、镜像长 ID、镜像名 或者 镜像摘要
id可以是短id 前几位

删除虚悬镜像
docker image prune 
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612493041897-5f2fed1d-3bf8-426e-9041-c1d60a926c41.png#align=left&display=inline&height=148&originHeight=148&originWidth=602&size=11310&status=done&style=none&width=602)

# 5. docker commit

```shell
docker commit [选项] <容器ID或容器名> [<仓库名>[:<标签>]]
将容器保存为镜像：
docker commit \
    --author "Tao Wang <<EMAIL>>" \
    --message "修改了默认网页" \
    webserver \
    nginx:v2
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612496602966-dfb057c4-6421-4502-af98-2994c8eb0e2d.png#align=left&display=inline&height=325&originHeight=325&originWidth=881&size=40741&status=done&style=none&width=881)
# 6. docker ps  查看进程
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612496480677-fda580cc-8ac7-4333-a7af-7c4f6642a156.png#align=left&display=inline&height=75&originHeight=75&originWidth=1366&size=11982&status=done&style=none&width=1366)


# 7. Dockerfile 

```shell
Dockerfile 是一个文本文件，其内包含了一条条的 指令(Instruction)，每一条指令构建一层，因此每一条指令的内容，就是描述该层应当如何构建。
```

## 创建dockerfile
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612497357927-ebbe8abf-9f72-427e-b1d6-9a97e13241f5.png#align=left&display=inline&height=90&originHeight=90&originWidth=792&size=8834&status=done&style=none&width=792)
## 构建
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612497392647-e0847d4b-bc93-4c73-be90-12deca1c1ae1.png#align=left&display=inline&height=331&originHeight=331&originWidth=885&size=42403&status=done&style=none&width=885)
## 运行
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612497597103-cb15a008-c986-4c2e-81d0-d321398ea4a1.png#align=left&display=inline&height=69&originHeight=69&originWidth=805&size=8320&status=done&style=none&width=805)

# 8. docker export 导出导入

```shell
docker export ecd36bce6fdd > nginx.tar
导出容器快照到本地文件。
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612506006001-df6acfba-26a2-4d17-aa4c-1503d3f3ae2e.png#align=left&display=inline&height=299&originHeight=299&originWidth=1514&size=50627&status=done&style=none&width=1514)

```shell
cat nginx.tar | docker import - test/nginx:v4
从容器快照文件中再导入为镜像

docker import http://example.com/exampleimage.tgz example/imagerepo

```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612506182211-b4fea4fa-f915-4093-ab75-4d51ca0d00ea.png#align=left&display=inline&height=461&originHeight=461&originWidth=1510&size=86888&status=done&style=none&width=1510)

# 9. docker container rm 删除容器

```shell
 docker container rm b7191abf1fe6
 
 docker container prune  清理掉所有处于终止状态的容器。
```

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612506335797-ffad456f-a815-4faf-80ff-fc8b0d0e3699.png#align=left&display=inline&height=329&originHeight=329&originWidth=1530&size=68670&status=done&style=none&width=1530)

# 10. docker search 搜索镜像

```shell
docker search redis

```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612506479275-caab8aa2-33ad-448b-8d38-f2ddcb90ceef.png#align=left&display=inline&height=459&originHeight=459&originWidth=1450&size=80309&status=done&style=none&width=1450)

# 11. docker push 将自己的镜像推送到 Docker Hub

```shell
docker tag ubuntu:18.04 username/ubuntu:18.04

docker push username/ubuntu:18.04
```

# 12. docker volume 数据卷

```shell
 docker volume create my-vol  创建
 docker volume ls 查看所有
 docker volume inspect my-vol 具体信息
  docker volume rm my-vol 删除
  docker volume prune 删除全部无主的
  使用  --mount 标记来将 数据卷 挂载到容器里
  docker run -d -P \
    --name web \
    --mount source=my-vol,target=/usr/share/nginx/html \
    nginx:alpine
    
 docker inspect web 查看信息
 
 挂在主机目录
 https://yeasy.gitbook.io/docker_practice/data_management/bind-mounts
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612507005631-fcfc6c58-b1b6-4c70-a5e3-16b1aea538d5.png#align=left&display=inline&height=370&originHeight=370&originWidth=995&size=33921&status=done&style=none&width=995)
# other

```shell
 docker history nginx:v2     查看镜像内的历史记录
```

