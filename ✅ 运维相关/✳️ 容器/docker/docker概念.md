[https://zhuanlan.zhihu.com/p/53260098](https://zhuanlan.zhihu.com/p/53260098)
[https://yeasy.gitbook.io/docker_practice/introduction/why](https://yeasy.gitbook.io/docker_practice/introduction/why)
# 1. docker架构
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1612489755343-240fae31-917f-4e64-affe-2ea9a1509184.png#align=left&display=inline&height=1015&originHeight=1015&originWidth=1488&size=0&status=done&style=none&width=1488)

# 2. 概念
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612489975957-439a0470-56d7-4890-bfd5-9bc1ccfa6198.png#align=left&display=inline&height=239&originHeight=239&originWidth=474&size=12260&status=done&style=none&width=474)
```shell
镜像Image 是静态的 就像是个文件
容器Container  镜像被运行, 是个进程, 活的, 容器可以被创建、启动、停止、删除、暂停等
镜像和容器都是 分层存储
	数据一般存哪里?  应该使用 数据卷（Volume）、或者 绑定宿主目录
Windows系统  iso是镜像, 安装成windows  是容器?
仓库 repository
 	<仓库名>:<标签>    ubuntu:16.04
  	仓库名是两段式名称，即 <用户名>/<软件名>
    不写用户名, 默认为 library，也就是官方镜像
  
Docker Registry
	https://hub.docker.com/
  https://hub.daocloud.io/
```
