# 安装node-exporter

## tar
```shell

mkdir /home/<USER>
wget https://gh-proxy.com/https://github.com/prometheus/node_exporter/releases/download/v1.8.2/node_exporter-1.8.2.linux-amd64.tar.gz --no-check-certificate
tar -zxvf node_exporter-1.8.2.linux-amd64.tar.gz
cd node_exporter-1.8.2.linux-amd64
nohup ./node_exporter --web.listen-address=":9100" &

nohup /home/<USER>/node_exporter-1.8.2.linux-amd64/node_exporter --web.listen-address=":9100" &
# nohup ./node_exporter --web.listen-address=":9110" &

```

```shell
8919

https://prometheus.io/docs/instrumenting/exporters/
```

## docker-compose

```yaml
services:
  node_exporter:
    image: quay.io/prometheus/node-exporter:latest
    container_name: node_exporter
    command:
      - '--path.rootfs=/host'
    network_mode: host
    pid: host
    restart: unless-stopped
    volumes:
      - '/:/host:ro,rslave'
```