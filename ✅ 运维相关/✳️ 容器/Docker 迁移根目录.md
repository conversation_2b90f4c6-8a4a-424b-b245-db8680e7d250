```shell
 docker info
 # 停止 
 systemctl stop docker
 # 创建目录 
 mkdir -p /home/<USER>
# 迁移数据
mv /var/lib/docker /home/<USER>
# 通过软连接把两个目录关联起来 
 ln -s /home/<USER>/var/lib/docker
# 启动 
 systemctl start docker
# 查看状态 
 systemctl status docker

```
![image.png](https://cdn.nlark.com/yuque/0/2022/png/684952/1659875343569-f257cd72-03ee-48f9-b4fa-31b051f8f81f.png#clientId=u1d7271da-af74-4&from=paste&height=381&id=uc3bdc738&originHeight=381&originWidth=474&originalType=binary&ratio=1&rotation=0&showTitle=false&size=84276&status=done&style=none&taskId=u082244c6-40e2-4537-b3e3-70b1f4a1f53&title=&width=474)
