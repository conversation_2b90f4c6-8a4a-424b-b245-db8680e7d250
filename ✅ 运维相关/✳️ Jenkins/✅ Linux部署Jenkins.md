# 环境准备
> git maven java node

```csharp
mkdir -p /home/<USER>
cd /home/<USER>
// 初始化安装环境 jdk git maven node
sh jenkins.sh init

java -version
git version
mvn -v
node -v
```
### maven settings.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>


<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <!-- localRepository
  | The path to the local repository maven will use to store artifacts.
  |
  | Default: ${user.home}/.m2/repository
  <localRepository>/path/to/local/repo</localRepository>
  -->
  <localRepository>/home/<USER>/maven/repository</localRepository>


  <pluginGroups>

  </pluginGroups>

  <!-- proxies
  | This is a list of proxies which can be used on this machine to connect to the network.
  | Unless otherwise specified (by system property or command-line switch), the first proxy
  | specification in this list marked as active will be used.
  |-->
  <proxies>

  </proxies>


  <servers>

    <server>
      <id>cqt</id>
      <username>admin</username>
      <password>cqt@1234</password>
    </server>

  </servers>


  <mirrors>


    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>public</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public/</url>
    </mirror>

    <mirror>
      <id>central</id>
      <mirrorOf>central</mirrorOf>
      <name>central</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>    

    <mirror>
      <id>maven2</id>
      <mirrorOf>central</mirrorOf>
      <name>maven2</name>
      <url>https://repo1.maven.org/maven2/</url>
    </mirror>

    <mirror>
      <id>aliyunmavenspring</id>
      <mirrorOf>spring</mirrorOf>
      <name>阿里云公共仓库spring</name>
      <url>https://maven.aliyun.com/repository/spring</url>
    </mirror>

    <mirror>
      <id>mirror</id>
      <mirrorOf>central,jcenter,!rdc-releases,!rdc-snapshots</mirrorOf>
      <name>mirror</name>
      <url>https://maven.aliyun.com/nexus/content/groups/public</url>
    </mirror>

    <mirror>
      <id>cqt</id>
      <name>cqt</name>
      <mirrorOf>cqt</mirrorOf>
      <url>http://172.16.252.130:7077/repository/maven-public/</url>
    </mirror>

  </mirrors>


  <profiles>
    <profile>
      <id>maven</id>
      <repositories>
        <repository>
          <id>central</id>
          <url>https://maven.aliyun.com/nexus/content/groups/public</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>  

        <repository>
          <id>maven2</id>
          <url>https://repo1.maven.org/maven2/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository> 

        <repository>
          <id>cqt</id>
          <url>http://cqt</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </repository>
        </repositories>
          <pluginRepositories>
          <pluginRepository>
          <id>central</id>
          <url>https://maven.aliyun.com/nexus/content/groups/public</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>

          <pluginRepository>
          <id>maven2</id>
          <url>https://repo1.maven.org/maven2/</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>

          <pluginRepository>
          <id>cqt</id>
          <url>http://cqt</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
        </pluginRepositories>
        </profile>
        </profiles>


          <activeProfiles>
          <activeProfile>maven</activeProfile>
        </activeProfiles>
        </settings>

```
# 部署脚本
```yaml
#!/bin/bash

####################################
# @description jenkins运行脚本
# @params $? => 代表上一个命令执行后的退出状态: 0->成功,1->失败
#         $1 => 脚本第一个参数-操作类型(init|start|stop|restart)
# @example => sh jenkins.sh init & source /etc/profile
# <AUTHOR>
# @date 2021/7/17 3:59 下午
####################################


# 在执行过程中若遇到使用了未定义的变量或命令返回值为非零，将直接报错退出
# set -eu

# 检查参数个数
if [ "${#}" -lt 1 ]; then
	echo "\033[41;37m 脚本使用示例: sh jenkins.sh init(初始化环境)|start(运行)|stop(停止)|restart(重启)  \033[0m"
	exit
fi

# SOFT_DIR='/home/<USER>'

# 获取脚本第一个参数
APP_OPT=${1}
# 端口
APP_PORT=10000
# 名称
APP_NAME=jenkins
# jar名 | war名
APP_JAR=${APP_NAME}.war
# 程序根目录
APP_JAR_HOME=.
# 日志名
APP_LOG_NAME=jenkins
# 日志根目录
APP_LOG_HOME=.
# 程序运行参数
JAVA_OPTS="--ajp13Port=-1 --httpPort=${APP_PORT} --prefix=/jenkins"

echo "本次操作服务名：[${APP_NAME}]"
echo "本次操作选择：[${APP_OPT}]"

# 初始化环境
function init() {
    install_git
    install_jdk
    install_maven
    install_nodejs
    # 使配置生效
    source /etc/profile
}

function install_git() {
  echo "*******************************************************************************"
  echo "            ↓↓↓↓↓↓ [检查环境：git] ↓↓↓↓↓↓         "
  which git
  # $?: 指上一次命令执行的状态 成功：0 失败：1
  if [ "$?" -eq 1 ]; then
    echo "安装git..."
    yum install -y git
  fi
}

function install_jdk() {
    echo "*******************************************************************************"
    echo "            ↓↓↓↓↓↓ [检查环境：java] ↓↓↓↓↓↓         "
    which java
    if [ "$?" -eq 1 ]; then
    	echo "安装jdk..."
    	yum -y install java-1.8.0-openjdk*
      # 配置环境变量 -- 注意$前加上\ 避免shell中获取其变量值追加到配置文件中
cat>> /etc/profile <<EOF

############################## ↓↓↓↓↓↓ set java environment ↓↓↓↓↓↓ #############################
JAVA_HOME=/usr/lib/jvm/java
CLASSPATH=.:\$JAVA_HOME/lib/dt.jar:\$JAVA_HOME/lib/tools.jar:\$JAVA_HOME/jre/lib/rt.jar
PATH=\$PATH:\$JAVA_HOME/bin
export JAVA_HOME CLASSPATH PATH
###############################################################################################

EOF
      # 使配置生效
      . /etc/profile
    fi
}

function install_maven() {
    echo "*******************************************************************************"
    echo "            ↓↓↓↓↓↓ [检查环境：maven] ↓↓↓↓↓↓         "
    which mvn
    if [ "$?" -eq 1 ]; then
    	echo "安装maven..."
    	mkdir -p /home/<USER>/maven/repository
      cd /home/<USER>/maven
    	wget --no-check-certificate https://mirrors.bfsu.edu.cn/apache/maven/maven-3/3.6.3/binaries/apache-maven-3.6.3-bin.tar.gz
      # 解压
      tar -zxvf apache-maven-3.6.3-bin.tar.gz
      # 修改配置文件
      isExist=$(cat /home/<USER>/maven/apache-maven-3.6.3/conf/settings.xml | grep "<localRepository>/home/<USER>/maven/repository</localRepository>")
      if [[ "$isExist" == "" ]]
      then
          # 不包含
          # 在第55行插入
          sed -i '55i <localRepository>/home/<USER>/maven/repository</localRepository>' /home/<USER>/maven/apache-maven-3.6.3/conf/settings.xml
          # 在第160行插入
          sed -i '160i \
              <!-- 国内中央仓库的配置-阿里云中央仓库 --> \
              <mirror> \
                  <id>nexus-aliyun</id> \
                  <mirrorOf>central</mirrorOf> \
                  <name>Nexus aliyun</name> \
                  <url>http://maven.aliyun.com/nexus/content/groups/public</url> \
              </mirror> \
          ' /home/<USER>/maven/apache-maven-3.6.3/conf/settings.xml
      fi
      # 配置环境变量
cat>> /etc/profile <<EOF

############################## ↓↓↓↓↓↓ set maven environment ↓↓↓↓↓↓ #############################
MAVEN_HOME=/home/<USER>/maven/apache-maven-3.6.3
PATH=\$PATH:\$JAVA_HOME/bin:\$MAVEN_HOME/bin
export MAVEN_HOME PATH
################################################################################################

EOF
      # 使配置生效
      source /etc/profile
    fi
}

function install_nodejs() {
    echo "*******************************************************************************"
    echo "            ↓↓↓↓↓↓ [检查环境：NodeJS] ↓↓↓↓↓↓         "
    which npm
    if [ "$?" -eq 1 ]; then
    	echo "安装NodeJS..."
    	mkdir -p /home/<USER>
      cd /home/<USER>
    	wget https://nodejs.org/dist/v12.18.3/node-v12.18.3-linux-arm64.tar.xz
      # 解压
      tar -xvf node-v12.18.3-linux-arm64.tar.xz
      # 配置环境变量
cat>> /etc/profile <<EOF

############################## ↓↓↓↓↓↓ set nodejs environment ↓↓↓↓↓↓ #############################
NODEJS_HOME=/home/<USER>/node-v12.18.3-linux-arm64
PATH=\$PATH:\$NODEJS_HOME/bin
export NODEJS_HOME PATH
#################################################################################################

EOF
      # 使配置生效
      source /etc/profile
      # 将npm软连接到`/usr/bin`目录下 => 解决`sudo: npm：找不到命令`问题
      sudo ln -s /home/<USER>/node-v12.18.3-linux-arm64/bin/node /usr/bin/node
      sudo ln -s /home/<USER>/node-v12.18.3-linux-arm64/bin/npm /usr/bin/npm
      sudo ln -s /home/<USER>/node-v12.18.3-linux-arm64/bin/cnpm /usr/bin/cnpm
      sudo ln -s /home/<USER>/node-v12.18.3-linux-arm64/bin/npx /usr/lib/npx
      # 设置淘宝`NPM`镜像
      npm install -g cnpm --registry=https://registry.npm.taobao.org
    fi
}

# 停止
function stop(){
  echo "<-------------------------------------->"
  echo "[${APP_NAME}] ... stop ..."
  # 查看该jar进程
  pid=`ps -ef | grep ${APP_JAR} | grep -v 'grep' | awk '{print $2}'`
  echo "[${APP_NAME}] pid="${pid}
  # 存在则kill,不存在打印一下吧
  if [ "${pid}" ]; then
    kill -9 ${pid}
      # 检查kill是否成功
      if [ "$?" -eq 0 ]; then
          echo "[${APP_NAME}] stop success"
      else
          echo "[${APP_NAME}] stop fail"
      fi
  else
    echo "[${APP_NAME}] 进程不存在"
  fi
}


# 运行
function start(){
  echo "<-------------------------------------->"
  echo "[${APP_NAME}] ... start ..."
  cd ${APP_JAR_HOME}
  echo "当前路径:`pwd`"
  # 赋予可读可写可执行权限
  chmod 777 ${APP_JAR}
  echo "启动命令: nohup java -jar ${APP_JAR} ${JAVA_OPTS} >> ${APP_LOG_HOME}/${APP_NAME}.log 2>&1 &"
  #nohup java -jar ${APP_JAR} ${JAVA_OPTS} >> ${APP_LOG_HOME}/${APP_NAME}.log 2>&1 &
  nohup java -jar -Xmx256M -Xms256M /home/<USER>/jenkins/jenkins.war --ajp13Port=-1 --httpPort=10000 --prefix=/jenkins >> ./jenkins.log 2>&1 &
  if [ "$?" -eq 0 ]; then
    echo "[${APP_NAME}] start success"
  else
    echo "[${APP_NAME}] start fail"
  fi
}


# 重启
function restart(){
  echo "<-------------------------------------->"
  echo "[${APP_NAME}] ... restart ..."
	stop
	start
}


# 多分支条件判断执行参数
case "${APP_OPT}" in
	"init")
		init
		;;
	"stop")
		stop
		;;
	"start")
		start
		;;
	"restart")
		restart
		;;
	*)
	echo "\033[41;37m 提示:不支持参数 命令 -> ${APP_OPT} \033[0m"
	;;
esac

```
# 安装jenkins.war
```bash
mkdir -p /home/<USER>/jenkins
cd /home/<USER>/jenkins

# 下载 【 根据自己需要的版本下载 http://mirrors.jenkins.io/war-stable/ 】
wget https://mirrors.jenkins.io/war-stable/2.346.1/jenkins.war

# 启动war
sh jenkins.sh start
```
# 插件升级加速
```csharp
http://mirror.xmission.com/jenkins/updates/update-center.json
```
# 安装插件
```
Maven Integration	构建maven项目插件
Extended Choice Parameter	参数化构建
Git Parameter	Git参数化
Publish Over SSH	远程服务器部署（执行操作命令和传输文件）
GitLab	GitLab
Email Extension Plugin	电子邮件通知
NodeJS	部署前端
Throttle Concurrent Builds	任务同时build个数控制 (或不使用此插件，直接勾选任务配置中的Do not allow concurrent builds即不允许并发构建)
ThinBackup	备份配置信息
Blue Ocean	pipeline 的可视化UI
```
