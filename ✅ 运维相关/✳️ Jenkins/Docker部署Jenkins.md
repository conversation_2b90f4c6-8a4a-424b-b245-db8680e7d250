```shell
docker run -d  \
--name jenkins -u root \
-p 9090:8080  \
-v /home/<USER>/jenkins_home:/var/jenkins_home  \
harbor.cqt.com:11000/docker/jenkins/jenkins:jdk21
```

```yml
services:
  jenkins:
    image: harbor.cqt.com:11000/docker/jenkins/jenkins:jdk21
    container_name: jenkins
    user: root
    ports:
      - "9090:8080"
    volumes:
      - /home/<USER>/jenkins_home:/home/<USER>/jenkins_home
      - /home/<USER>/apache-maven-3.9.6:/home/<USER>/apache-maven-3.9.6
      - /home/<USER>/repository:/home/<USER>/repository
    restart: unless-stopped
```