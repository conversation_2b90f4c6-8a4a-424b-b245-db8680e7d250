## 模拟占用某个端口5000

```shell
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:5000/")
$listener.Start()
Write-Output "Listening on port <port_number>..."
$context = $listener.GetContext()
$request = $context.Request
$response = $context.Response
$writer = New-Object System.IO.StreamWriter($response.OutputStream)
$writer.Write("Hello from PowerShell listener!")
$writer.Close()
$listener.Stop()
```
