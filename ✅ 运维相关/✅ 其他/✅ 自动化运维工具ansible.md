## 安装

```
yum install ansible
```

## 机器列表

> /etc/ansible/hosts

## 密码方式连接

> yum install sshpass

```
[as]
*************:22 ansible_ssh_user=root ansible_ssh_pass=cqt@2020
*************:22 ansible_ssh_user=root ansible_ssh_pass=cqt@2020
*************:22 ansible_ssh_user=root ansible_ssh_pass=cqt@2020
*************:22 ansible_ssh_user=root ansible_ssh_pass=cqt@2020

[as]
*************:22
*************:22
*************:22
*************:22
```

### 其他参数

```
ansible_ssh_host
    将要连接的远程主机名.与你想要设定的主机的别名不同的话,可通过此变量设置.
ansible_ssh_port
    ssh端口号.如果不是默认的端口号,通过此变量设置.
ansible_ssh_user
    默认的 ssh 用户名
ansible_ssh_pass
    ssh 密码(这种方式并不安全,我们强烈建议使用 --ask-pass 或 SSH 密钥)
ansible_sudo_pass
    sudo 密码(这种方式并不安全,我们强烈建议使用 --ask-sudo-pass)
ansible_sudo_exe (new in version 1.8)
    sudo 命令路径(适用于1.8及以上版本)
ansible_connection
    与主机的连接类型.比如:local, ssh 或者 paramiko. Ansible 1.2 以前默认使用 paramiko.1.2 以后默认使用 'smart','smart' 方式会根据是否支持 ControlPersist, 来判断'ssh' 方式是否可行.
ansible_ssh_private_key_file
    ssh 使用的私钥文件.适用于有多个密钥,而你不想使用 SSH 代理的情况.
ansible_shell_type
    目标系统的shell类型.默认情况下,命令的执行使用 'sh' 语法,可设置为 'csh' 或 'fish'.
ansible_python_interpreter
    目标主机的 python 路径.适用于的情况: 系统中有多个 Python, 或者命令路径不是"/usr/bin/python",比如  \*BSD, 或者 /usr/bin/python
```

## 基于证书认证添加机器

```
ssh-keygen -t rsa
ssh-copy-id -i ~/.ssh/id_rsa.pub root@*************
ssh-copy-id -i ~/.ssh/id_rsa.pub root@*************
ssh-copy-id -i ~/.ssh/id_rsa.pub root@*************
ssh-copy-id -i ~/.ssh/id_rsa.pub root@*************
```

## 使用

```
ansible as -a "ls "

是否ping通
ansible as -m ping
```

### 其他命令

```
# 执行远程命令
# ansible as -m command -a 'uptime'

# 执行主控端脚本
# ansible as -m script -a '/home/<USER>/auto-start.sh'

# 执行远程主机的脚本
# ansible as -m shell -a 'ps aux|grep zabbix'

# 类似shell
# ansible as -m raw -a "ps aux|grep third|awk '{print \$2}'"

# 创建软链接
# ansible as -m file -a "src=/etc/resolv.conf dest=/tmp/resolv.conf state=link"

# 删除软链接
# ansible as -m file -a "path=/tmp/resolv.conf state=absent"

# 复制文件到远程服务器
# ansible as -m copy -a "src=/etc/ansible/ansible.cfg dest=/tmp/ansible.cfg owner=root group=root mode=0644"
```

## playbook
