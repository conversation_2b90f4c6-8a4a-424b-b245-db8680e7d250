```shell

LTAI5tH72223wCwv1BDy
QTSTzmtA7333EXPwtRD3Paa6

export DP_Id="LTAI5tH7i333wCwv1BDy"        
export DP_Key="QTSTzmtA7U1r222RD3Paa6"  
acme.sh --issue --dns dns_dp -d "*.kk0.xyz" -d "kk0.xyz" 
acme.sh --install-cert -d "*.kk0.xyz" -d "kk0.xyz" \
--cert-file /root/nginx/ssl/cert1.pem \
--key-file /root/nginx/ssl/key1.pem \
--fullchain-file /root/nginx/ssl/fullchain1.pem 
\
--reloadcmd "systemctl force-reload nginx"

```

```shell
[Service]
Environment="HTTP_PROXY=http://192.210.226.48:600000"
Environment="HTTPS_PROXY=https://192.210.226.48:600000"
Environment="NO_PROXY=dockerhub.kk0.xyz,10.10.10.10,*.kk0.xyz"
```
