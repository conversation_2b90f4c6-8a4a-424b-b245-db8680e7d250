# Dubbo3.1.10 多注册中心-异地双活

## Dubbo版本

> 3.1.10

## 多注册中心 nacos

> 消费者只有配置了多个注册中心, 订阅多注册中心的服务, 集群模式就会切换为zone-aware

### 消费者bootstrap.yml

```yaml
NACOS_SERVER: @server-addr@
BACK_NACOS_SERVER: @back-server-addr@
NACOS_USERNAME: @username@
NACOS_PASSWORD: @password@
NACOS_NAMESPACE: @namespace@
NACOS_GROUP: @group@
spring:
  application:
    name: @project.artifactId@
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      config:
        file-extension: yaml
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_GROUP}
  main:
    allow-bean-definition-overriding: true
dubbo:
  application:
    name: ${spring.application.name}
    # 设置优雅停机超时时间，缺省超时时间是 10 秒，如果超时则强制关闭。
    shutwait: 10000
  scan:
    base-packages: com.cqt.rpc
  #  config:
  #    multiple: true
  # 多注册中心
  registries:
    # A机房注册中心
    registry-a:
      protocol: nacos
      address: nacos://${NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      group: ${NACOS_GROUP}
      parameters:
        namespace: ${NACOS_NAMESPACE}
        # 消费者注册到nacos注册中心
        register-consumer-url: true
      use-as-config-center: false
      use-as-metadata-center: false
      preferred: true
      # 设置全局默认注册中心
      default: true
    # B机房注册中心
    registry-b:
      protocol: nacos
      address: nacos://${BACK_NACOS_SERVER}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
      group: ${NACOS_GROUP}
      parameters:
        namespace: ${NACOS_NAMESPACE}
        # 消费者注册到nacos注册中心
        register-consumer-url: true
      use-as-config-center: false
      use-as-metadata-center: false
      default: false
      register: false
  protocol:
    name: dubbo
  provider:
    # 服务需要预热时间，比如初始化缓存，等待相关资源就位等，延迟暴露。如果你不需要延迟暴露服务，无需配置 delay
    delay: 3000
    timeout: 3000
  consumer:
    # 延迟连接用于减少长连接数。当有调用发起时，再创建长连接。
    lazy: true
    # 不检查生产者服务是否可用
    check: false
    # 失败重试次数
    retries: 0
    # 消费者请求生产者接口超时时间ms
    timeout: 5050

management:
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    shutdown:
      enabled: true
    health:
      probes:
        enabled: true
      show-details: never
  endpoints:
    web:
      exposure:
        include: health,shutdown,metrics,prometheus


```

### 调用指定注册中心

![image-20230701215535282](images/image-20230701215535282.png)

## 工作原理

> https://cn.dubbo.apache.org/zh-cn/overview/mannual/java-sdk/reference-manual/registry/multiple-registry/

负载均衡选址:

1. 注册中心集群间选址，选定一个集群
2. 注册中心集群内选址，在集群内进行地址筛选

<img src="images/image-20230701215028116.png" alt="image-20230701215028116" style="zoom:50%; float: left" />

![image-20230701215421733](images/image-20230701215421733.png)

## 源码分析

### **org.apache.dubbo.config.ReferenceConfig**

> 创建服务引用的配置类

#### createInvokerForRemote()

```java
	/**
     * Make a remote reference, create a remote reference invoker
     * 创建远程引用，创建远程引用调用程序
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void createInvokerForRemote() {
        // 注册中心个数判断
        if (urls.size() == 1) {
            // 只配置一个, 直接执行集群负载均衡算法
            URL curUrl = urls.get(0);
            invoker = protocolSPI.refer(interfaceClass, curUrl);
            // registry url, mesh-enable and unloadClusterRelated is true, not need Cluster.
            if (!UrlUtils.isRegistry(curUrl) &&
                    !curUrl.getParameter(UNLOAD_CLUSTER_RELATED, false)) {
                List<Invoker<?>> invokers = new ArrayList<>();
                invokers.add(invoker);
                invoker = Cluster.getCluster(getScopeModel(), Cluster.DEFAULT).join(new StaticDirectory(curUrl, invokers), true);
            }
        } else {
            // 多注册中心
            List<Invoker<?>> invokers = new ArrayList<>();
            URL registryUrl = null;
            for (URL url : urls) {
                // For multi-registry scenarios, it is not checked whether each referInvoker is available.
                // Because this invoker may become available later.
                // 创建远程调用程序, 传递给集群策略执行
                invokers.add(protocolSPI.refer(interfaceClass, url));

                // 判断url是否为注册中心
                if (UrlUtils.isRegistry(url)) {
                    // use last registry url
                    registryUrl = url;
                }
            }

            if (registryUrl != null) {
                // registry url is available
                // for multi-subscription scenario, use 'zone-aware' policy by default
                // 多注册中心, 集群cluster使用zone-aware策略
                String cluster = registryUrl.getParameter(CLUSTER_KEY, ZoneAwareCluster.NAME);
                // The invoker wrap sequence would be: ZoneAwareClusterInvoker(StaticDirectory) -> FailoverClusterInvoker
                // (RegistryDirectory, routing happens here) -> Invoker
                // 先找到注册中心, 再找具体实例
                invoker = Cluster.getCluster(registryUrl.getScopeModel(), cluster, false).join(new StaticDirectory(registryUrl, invokers), false);
            } else {
                // not a registry url, must be direct invoke.
                // 注册url不是注册中心, 一定是直接实例ip调用
                if (CollectionUtils.isEmpty(invokers)) {
                    throw new IllegalArgumentException("invokers == null");
                }
                URL curUrl = invokers.get(0).getUrl();
                String cluster = curUrl.getParameter(CLUSTER_KEY, Cluster.DEFAULT);
                invoker = Cluster.getCluster(getScopeModel(), cluster).join(new StaticDirectory(curUrl, invokers), true);
            }
        }
    }
```

### org.apache.dubbo.rpc.cluster.support.registry.ZoneAwareCluster

```java
public class ZoneAwareCluster extends AbstractCluster {

    public final static String NAME = "zone-aware";

    @Override
    protected <T> AbstractClusterInvoker<T> doJoin(Directory<T> directory) throws RpcException {
        return new ZoneAwareClusterInvoker<T>(directory);
    }

}
```

#### 集群负载均衡策略

![image-20230701220642305](images/image-20230701220642305.png)

### org.apache.dubbo.rpc.cluster.support.registry.ZoneAwareClusterInvoker

> 当有多个注册中心可供订阅时。
> 这个扩展提供了一种策略来决定如何在其中分配流量:
>
> 1. 标记为“preferred=true”的注册表具有最高优先级。
> 2. 检查当前请求所属的区域zone，首先选择具有相同区域的注册中心。
> 3. .根据每个注册中心的权重weight在所有注册中心之间均衡流量。
> 4. 随便挑一个有空的人

```java
/**
 * When there are more than one registry for subscription.
 * <p>
 * This extension provides a strategy to decide how to distribute traffics among them:
 * 1. registry marked as 'preferred=true' has the highest priority.
 * 2. check the zone the current request belongs, pick the registry that has the same zone first.
 * 3. Evenly balance traffic between all registries based on each registry's weight.
 * 4. Pick anyone that's available.
 */
public class ZoneAwareClusterInvoker<T> extends AbstractClusterInvoker<T> {

    private static final Logger logger = LoggerFactory.getLogger(ZoneAwareClusterInvoker.class);

    private ZoneDetector zoneDetector;

    public ZoneAwareClusterInvoker(Directory<T> directory) {
        super(directory);
        ExtensionLoader<ZoneDetector> loader = directory.getConsumerUrl().getOrDefaultApplicationModel().getExtensionLoader(ZoneDetector.class);
        if (loader.hasExtension("default")) {
            zoneDetector = loader.getExtension("default");
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public Result doInvoke(Invocation invocation, final List<Invoker<T>> invokers, LoadBalance loadbalance) throws RpcException {
        // First, pick the invoker (XXXClusterInvoker) that comes from the local registry, distinguish by a 'preferred' key.
        // 1. 注册中心preferred=true配置
        for (Invoker<T> invoker : invokers) {
            ClusterInvoker<T> clusterInvoker = (ClusterInvoker<T>) invoker;
            if (clusterInvoker.isAvailable() && clusterInvoker.getRegistryUrl()
                    .getParameter(PREFERRED_KEY, false)) {
                return clusterInvoker.invoke(invocation);
            }
        }

        RpcContext rpcContext = RpcContext.getClientAttachment();
        // registry_zone配置
        String zone = rpcContext.getAttachment(REGISTRY_ZONE);
        String force = rpcContext.getAttachment(REGISTRY_ZONE_FORCE);
        if (StringUtils.isEmpty(zone) && zoneDetector != null) {
            zone = zoneDetector.getZoneOfCurrentRequest(invocation);
            force = zoneDetector.isZoneForcingEnabled(invocation, zone);
        }

        // providers in the registry with the same zone
        // 2. 注册区域
        if (StringUtils.isNotEmpty(zone)) {
            for (Invoker<T> invoker : invokers) {
                ClusterInvoker<T> clusterInvoker = (ClusterInvoker<T>) invoker;
                if (clusterInvoker.isAvailable() && zone.equals(clusterInvoker.getRegistryUrl().getParameter(ZONE_KEY))) {
                    return clusterInvoker.invoke(invocation);
                }
            }
            if (StringUtils.isNotEmpty(force) && "true".equalsIgnoreCase(force)) {
                throw new IllegalStateException("No registry instance in zone or no available providers in the registry, zone: "
                        + zone
                        + ", registries: " + invokers.stream().map(invoker -> ((ClusterInvoker<T>) invoker).getRegistryUrl().toString()).collect(Collectors.joining(",")));
            }
        }


        // load balance among all registries, with registry weight count in.
        // 3. 权重
        Invoker<T> balancedInvoker = select(loadbalance, invocation, invokers, null);
        if (balancedInvoker!=null && balancedInvoker.isAvailable()) {
            return balancedInvoker.invoke(invocation);
        }

        // If none of the invokers has a preferred signal or is picked by the loadbalancer, pick the first one available.
        // 4. 如果所有调用者都没有首选信号或被负载均衡器选中，则选择第一个可用的。
        for (Invoker<T> invoker : invokers) {
            ClusterInvoker<T> clusterInvoker = (ClusterInvoker<T>) invoker;
            // 注册中心可用
            if (clusterInvoker.isAvailable()) {
                return clusterInvoker.invoke(invocation);
            }
        }

        //if none available,just pick one
        return invokers.get(0).invoke(invocation);
    }

}
```

