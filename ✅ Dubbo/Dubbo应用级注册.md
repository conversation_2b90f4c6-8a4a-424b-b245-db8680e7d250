# Dubbo 应用级注册

## 提供者

```
-Ddubbo.application.register-mode=all
# 可选值 interface、instance、all，默认是 all，即接口级地址、应用级地址都注册

# 仅应用级注册
dubbo.application.register-mode=instance
```



## 消费者

```
dubbo.application.service-discovery.migration=APPLICATION_FIRST
# 可选值 
# FORCE_INTERFACE，只消费接口级地址，如无地址则报错，单订阅 2.x 地址
# APPLICATION_FIRST，智能决策接口级/应用级地址，双订阅
# FORCE_APPLICATION，只消费应用级地址，如无地址则报错，单订阅 3.x 地址

System.setProperty("dubbo.application.service-discovery.migration", "FORCE_APPLICATION");

```



## 版本

> dubbo 3.2.4
>
> SpringBoot 2.7.13
>
> 