# Dubbo实际项目使用注意点

# 启动info大量Throw classNotFound (com/google/protobuf/GeneratedMessageV3)

```
2023-08-02 21:30:44,255 [Dubbo-cloudcc-call-control-service-export-thread-1raceId] INFO  [Dubbo-cloudcc-call-control-service-export-thread-1]
[org.apache.dubbo.metadata.definition.TypeDefinitionBuilder.getGenericTypeBuilder(TypeDefinitionBuilder.java:70)] []  
[DUBBO] Throw classNotFound (com/google/protobuf/GeneratedMessageV3) in class org.apache.dubbo.metadata.definition.protobuf.ProtobufTypeBuilder,
dubbo version: 3.2.4, current host: ************
```

![image-20230802213258806](images/image-20230802213258806.png)

引入依赖解决

```xml
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-metadata-definition-protobuf</artifactId>
        </dependency>
```

