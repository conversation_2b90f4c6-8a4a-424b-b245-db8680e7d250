# Dubbo特性和用法

## 参数校验

### 依赖

```xml
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
```

### 生效

字段校验注解

```java
    @NotNull // 不允许为空
    @Size(min = 1, max = 20) // 长度或大小范围
    private String name;

    @Pattern(regexp = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$")
    private String email;

    @Min(18) // 最小值
    @Max(100) // 最大值
    private int age;

    @Past // 必须为一个过去的时间
    private Date loginDate;

    @Future // 必须为一个未来的时间
    private Date expiryDate;
```



在生产者端声明接口需要校验

```java
@DubboService(validation = "true")
```

## 服务分组+版本号

```java
// 生产者端设置分组
@DubboService(group="g1", version = "1.1.0", validation = "true")

// 消费者端
@DubboReference(group = "g1", version = "1.1.0")
// * 代表所有分组，
@DubboReference(group = "*", version = "1.1.0")
```

## 启动时检查

```
// 检查生产者服务是否启动可用
check: false
```

## 集群容错, 失败重试

![image-20230618205633620](images/image-20230618205633620.png)

```java
@DubboReference(group = "g1", version = "1.1.0", cluster = "failover")
```

### Failover Cluster - 默认

> 失败自动切换，当出现失败，重试其它服务器。通常用于读操作，但重试会带来更长延迟。可通过 `retries="2"` 来设置重试次数(不含第一次)。

### Failfast Cluster

> 快速失败，只发起一次调用，失败立即报错。通常用于非幂等性的写操作，比如新增记录。

### Failsafe Cluster

> 失败安全，出现异常时，直接忽略。通常用于写入审计日志等操作。

### Failback Cluster

> 失败自动恢复，后台记录失败请求，定时重发。通常用于消息通知操作。

### Forking Cluster

> 并行调用多个服务器，只要一个成功即返回。通常用于实时性要求较高的读操作，但需要浪费更多服务资源。可通过 `forks="2"` 来设置最大并行数。

### Broadcast Cluster

> 广播调用所有提供者，逐个调用，任意一台报错则报错。通常用于通知所有提供者更新缓存或日志等本地资源信息。
>
> 现在广播调用中，可以通过 broadcast.fail.percent 配置节点调用失败的比例，当达到这个比例后，BroadcastClusterInvoker 将不再调用其他节点，直接抛出异常。 broadcast.fail.percent 取值在 0～100 范围内。默认情况下当全部调用失败后，才会抛出异常。 broadcast.fail.percent 只是控制的当失败后是否继续调用其他节点，并不改变结果(任意一台报错则报错)。broadcast.fail.percent 参数 在 dubbo2.7.10 及以上版本生效。
>
> Broadcast Cluster 配置 broadcast.fail.percent。
>
> broadcast.fail.percent=20 代表了当 20% 的节点调用失败就抛出异常，不再调用其他节点。

```java
@reference(cluster = "broadcast", parameters = {"broadcast.fail.percent", "20"})
```

### Available Cluster

> 调用目前可用的实例（只调用一个），如果当前没有可用的实例，则抛出异常。通常用于不需要负载均衡的场景。

### Mergeable Cluster

> 将集群中的调用结果聚合起来返回结果，通常和group一起配合使用。通过分组对结果进行聚合并返回聚合后的结果，比如菜单服务，用group区分同一接口的多种实现，现在消费方需从每种group中调用一次并返回结果，对结果进行合并之后返回，这样就可以实现聚合菜单项。

### ZoneAware Cluster - 多注册中心

> 多注册中心订阅的场景，注册中心集群间的负载均衡。对于多注册中心间的选址策略有如下四种

1. 指定优先级：`preferred="true"`注册中心的地址将被优先选择

```xml
<dubbo:registry address="zookeeper://127.0.0.1:2181" preferred="true" />
```

2. 同中心优先：检查当前请求所属的区域，优先选择具有相同区域的注册中心

```xml
<dubbo:registry address="zookeeper://127.0.0.1:2181" zone="beijing" />
```

3. 权重轮询：根据每个注册中心的权重分配流量

```xml
<dubbo:registry id="beijing" address="zookeeper://127.0.0.1:2181" weight="100" />

<dubbo:registry id="shanghai" address="zookeeper://127.0.0.1:2182" weight="10" />
```

4. 缺省值：选择一个可用的注册中心

## 服务降级

> 服务降级是指服务在非正常情况下进行降级应急处理。

### 使用场景

- 某服务或接口负荷超出最大承载能力范围，需要进行降级应急处理，避免系统崩溃
- 调用的某非关键服务或接口暂时不可用时，返回模拟数据或空，业务还能继续可用
- 降级非核心业务的服务或接口，腾出系统资源，尽量保证核心业务的正常运行
- 某上游基础服务超时或不可用时，执行能快速响应的降级预案，避免服务整体雪崩

### 使用sentinel

## 异步调用

> https://cn.dubbo.apache.org/zh-cn/overview/mannual/java-sdk/advanced-features-and-usage/service/async-call/

> 生产者接口逻辑处理慢, 生产者开一个异步线程处理业务逻辑
>
> 消费者 异步调用生产者服务

## 线程池隔离

> 按接口设置线程池, 不同接口互相隔离. 不会因为某个接口线程池用完导致其他接口不可用.
>
> 没找到在哪配置