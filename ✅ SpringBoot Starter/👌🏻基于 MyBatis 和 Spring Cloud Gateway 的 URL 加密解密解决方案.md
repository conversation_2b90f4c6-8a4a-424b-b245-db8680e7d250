# 需求背景

为了提高系统安全性，防止内部 URL 被外部直接访问，需求是：

1. **在数据库查询结果返回给前端之前**，通过自定义注解对特定字段进行加密。
2. **前端接收到加密的 URL** 后，访问网关提供的路由接口，网关通过解密该 URL 将请求转发到内部服务。



# 解决方案

## 加密内部URL

> 我们通过自定义注解和 MyBatis 拦截器，在查询时加密特定字段，确保内部 URL 的安全性。

### 1. 自定义注解

> 用于标记需要加密的字段。

```java
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SecretUrl {
}
```

### 2.  mybatis拦截器

> 在查询结果返回时，拦截器会遍历实体类的字段，对带有 `@SecretUrl` 注解的字段进行加密。采用 SM4 对称加密算法，确保数据的安全传输。

```java
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
@Component
@RequiredArgsConstructor
public class ResourceSecretSelectInterceptor implements Interceptor {

    private final CustomeProperties customProperties;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 拦截查询结果
        Object result = invocation.proceed();
        String secretKey = customProperties.getSecretKey();

        if (Objects.isNull(result)) {
            return null;
        }

        // 如果是 List 则表示是多条记录查询
        if (result instanceof List) {
            List<?> resultList = (List<?>) result;
            if (CollUtil.isEmpty(resultList)) {
                return result;
            }

            SM4 sm4 = new SM4(Base64.decode(secretKey));
            for (Object entity : resultList) {
                encryptSecretFields(sm4, entity);
            }
        }
        return result;
    }

    private void encryptSecretFields(SM4 sm4, Object entity) throws Exception {
        // 获取所有字段
        Field[] fields = entity.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 检查是否有 @SecretUrl 注解
            if (field.isAnnotationPresent(SecretUrl.class)) {
                Object value = ReflectUtil.getFieldValue(entity, field);
                if (Objects.isNull(value) || StrUtil.isEmptyIfStr(value)) {
                    continue;
                }
                // 对字段进行加密
                String encryptedValue = sm4.encryptHex(String.valueOf(value));
                ReflectUtil.setFieldValue(entity, field.getName(), encryptedValue);
            }
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(java.util.Properties properties) {
    }
}
```

## 定义一个SpringCloudGateway路由

> 接下来，通过 Spring Cloud Gateway 提供的转发接口，解密并将请求转发到内部服务。
>
> 接口 : https://domain/gateway/api/proxy-resource?resource=xxxxx
>
> resource的值就是使用sm4加密后的字符串, 在mybatis拦截器加密. 在gateway过滤器解密, 解密之后是个URL就转发到这个URL

### 1. 定义路由网关

**动态路由配置**（通过 Nacos 等配置中心管理）：

```json
	{
		"id": "dynamic-route",
		"order": 2,
		"predicates": [{
			"name": "Path",
			"args": {
				"_genkey_0": "/gateway/api/proxy-resource"
			}
		}],
		"uri": "lb://localhost"
	}
```

**基于 `RouteLocator` 的路由配置**：

```java
	@Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            .route("dynamic-route", r -> r
                .path("/gateway/api/proxy-resource") 
                .uri("lb://localhost") // 目标服务占位符
            )
            .build();
    }
```

### 2. 自定义全局过滤器 `DynamicResourceGatewayFilter`

> 在过滤器中对 `resource` 参数进行解密，然后将请求转发到解密后的 URL。
>
> 同时根据 URL 的文件类型设置合适的 `Content-Type` 和文件名。

```java
@RefreshScope
@Component
public class DynamicResourceGatewayFilter implements GlobalFilter, Ordered {

    @Value("${sm4.secret-key:}")
    private String secretKey;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        boolean equals = exchange.getRequest().getURI().getPath().equals("/gateway/api/proxy-resource");
        if (!equals) {
            return chain.filter(exchange);
        }

        // 从请求参数中获取url
        String resource = exchange.getRequest().getQueryParams().getFirst("resource");

        // 检查url是否存在，并且是内网地址
        if (StrUtil.isNotEmpty(resource)) {

            String targetUrl;
            try {
                SM4 sm4 = new SM4(Base64.decode(secretKey));
                targetUrl = sm4.decryptStr(resource);
            } catch (Exception e) {
                exchange.getResponse().setStatusCode(HttpStatus.BAD_REQUEST);
                return exchange.getResponse().setComplete();
            }

            URI uri = URI.create(targetUrl);
            exchange.getAttributes().put(ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR, uri);
            String contentType = getContentType(targetUrl);
            exchange.getResponse().getHeaders().set("Content-Type", contentType);

            String fileName = getFileName(targetUrl);
            exchange.getResponse().getHeaders().set(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"");
            return chain.filter(exchange);
        }
        
        // 如果url参数无效，返回400错误
        exchange.getResponse().setStatusCode(HttpStatus.BAD_REQUEST);
        return exchange.getResponse().setComplete();
    }

    @Override
    public int getOrder() {
        // 需要大于1000
        return 10001; 
    }


    private String getFileName(String path) {
        return path.substring(path.lastIndexOf('/') + 1);
    }

    private String getFileExtension(String path) {
        int dotIndex = path.lastIndexOf('.');
        return (dotIndex != -1) ? path.substring(dotIndex + 1).toLowerCase() : "";
    }

    private String getContentType(String path) {
        String extension = getFileExtension(path);
        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "mp4":
                return "video/mp4";
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "pdf":
                return "application/pdf";
            case "json":
                return "application/json";
            default:
                return "application/octet-stream";
        }
    }
}
```