# EasyExcel 简介

> https://easyexcel.opensource.alibaba.com/

> EasyExcel是一个基于Java的、快速、简洁、解决大文件内存溢出的Excel处理工具。
> 他能让你在不用考虑性能、内存的等因素的情况下，快速完成Excel的读、写等功能。

# 导出思路

为了实现大数据的高效导出，我们需要关注以下几个方面：

1. **分页查询**：由于数据量庞大，无法一次性将所有数据加载到内存中，因此需要通过分页的方式分批次加载数据。
2. **多线程处理**：为了提高导出效率，可以采用多线程并发处理，多个线程同时写入不同的 Excel 文件。
3. **导出后处理**：在导出完成后对文件进行后续处理，例如压缩、上传OSS等操作。

# maven依赖

```xml
<easyexcel.version>4.0.1</easyexcel.version>

<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>easyexcel</artifactId>
    <version>${easyexcel.version}</version>
</dependency>
```

# 代码实现

## 导出逻辑抽象类

> 为了实现大数据导出的通用性，可以通过设计一个抽象类 `AbstractExcelExporter`，在该类中封装导出的核心逻辑，不同导出场景可以通过继承该类进行具体实现。

```java
@Slf4j
public abstract class AbstractExcelExporter<T, R> {

    public ExporterVO writeExcel(T queryDTO) {
        Long count = getCount(queryDTO);
        if (count == 0) {
            throw new BizException("查询数量为空!");
        }
        Integer maxSheetCount = getMaxSheetCount();
        Integer batchPageSize = getBatchPageSize();
        int totalPage = PageUtil.totalPage(count, batchPageSize);

        long startTotal = System.currentTimeMillis();

        // 分几个excel key excelIndex ; value  table index
        Map<Integer, List<Integer>> excelIndexMap = getExcelIndex(totalPage, maxSheetCount, batchPageSize);

        // 唯一id 临时目录
        String uniqueId = IdUtil.fastUUID();
        String fileDir = getFileDir(uniqueId);
        File firstExcel = null;
        CountDownLatch latch = new CountDownLatch(excelIndexMap.size());
        for (Map.Entry<Integer, List<Integer>> entry : excelIndexMap.entrySet()) {
            Integer excelIndex = entry.getKey();
            List<Integer> tableIndexList = entry.getValue();
            String fileName = getFileFullPath(uniqueId, excelIndex);
            File excelFile = FileUtil.file(fileName);
            if (!excelFile.exists()) {
                try {
                    FileUtil.mkParentDirs(excelFile);
                } catch (Exception e) {
                    log.error("create excel file error: ", e);
                }
            }

            if (Objects.isNull(firstExcel)) {
                firstExcel = excelFile;
            }

            getExecutor().execute(() -> {
                try (ExcelWriter excelWriter = EasyExcel.write(excelFile, getReturnClass()).build()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet(getSheetName(excelIndex))
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .build();
                    for (Integer index : tableIndexList) {
                        cn.hutool.core.util.PageUtil.setFirstPageNo(1);
                        long startTime = System.currentTimeMillis();
                        int start = PageUtil.getStart(index, batchPageSize);
                        List<R> recordList = getDataPageList(queryDTO, batchPageSize, start);
                        long endTime = System.currentTimeMillis();
                        log.info("查询sql fileName: {}, pageCount: {}, excelIndex: {}, tableIndex: {}, cost: {}ms",
                                fileName, recordList.size(), excelIndex, index, endTime - startTime);

                        excelWriter.write(recordList, writeSheet);
                        long endTime2 = System.currentTimeMillis();
                        log.info("写excel fileName: {}, pageCount: {}, excelIndex: {}, tableIndex: {}, cost: {}ms",
                                fileName, recordList.size(), excelIndex, index, endTime2 - endTime);
                    }
                } catch (Exception e) {
                    log.info("fileName: {}, excelIndex: {}, error: ", fileName, excelIndex, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch.await error: ", e);
        }

        long endTotal = System.currentTimeMillis();
        log.info("总数: {}, excel导出总耗时: {}ms", count, endTotal - startTotal);

        ExporterResultDTO exporterResultDTO = ExporterResultDTO.builder()
                .uniqueId(uniqueId)
                .fileDir(fileDir)
                .excelCount(excelIndexMap.size())
                .firstExcel(firstExcel)
                .build();

        try {
            return afterProcess(exporterResultDTO);
        } finally {
            // del tmp file
            FileUtil.del(fileDir);
        }
    }

    /**
     * 线程池
     */
    protected abstract Executor getExecutor();

    /**
     * 批量分页查询数量
     */
    protected abstract Integer getBatchPageSize();

    /**
     * 每个sheet多大数量
     */
    protected abstract Integer getMaxSheetCount();

    /**
     * 查询总数
     *
     * @param queryDTO 查询条件
     * @return 总数
     */
    protected abstract Long getCount(T queryDTO);

    /**
     * 分页数据
     *
     * @param queryDTO      查询条件
     * @param batchPageSize 每页数量
     * @param start         开始偏移量
     * @return 分页数据
     */
    protected abstract List<R> getDataPageList(T queryDTO, Integer batchPageSize, Integer start);

    /**
     * 数据类型class
     *
     * @return 数据类型class
     */
    protected abstract Class<R> getReturnClass();

    /**
     * shell的名称
     *
     * @param excelIndex excel索引 1开始
     * @return shell的名称
     */
    protected abstract String getSheetName(Integer excelIndex);

    /**
     * 获取excel临时目录
     *
     * @param uniqueId 当前导出唯一id 作为目录
     * @return excel文件绝对目录  /home/<USER>/{uniqueId}
     */
    protected abstract String getFileDir(String uniqueId);

    /**
     * excel文件 临时存放目录
     *
     * @param uniqueId   当前导出唯一id 作为目录
     * @param excelIndex excel索引 1开始
     * @return excel文件绝对目录  /home/<USER>/{uniqueId}/{excelIndex}.xlsx
     */
    protected abstract String getFileFullPath(String uniqueId, Integer excelIndex);

    /**
     * excel写入成功后的操作, excel暂存于磁盘
     */
    protected abstract ExporterVO afterProcess(ExporterResultDTO exporterResultDTO);

    /**
     * 每个分页索引 和 Excel的索引对应关系
     *
     * @param totalPage     总的分页数
     * @param maxSheetCount 一个excel 最大数量
     * @param batchPageSize 分页查询 每页数量
     * @return Map
     */
    private Map<Integer, List<Integer>> getExcelIndex(int totalPage, Integer maxSheetCount, Integer batchPageSize) {
        Map<Integer, Integer> indexMap = new HashMap<>();
        Integer totalCount = 0;
        int sheetIndex = 1;
        for (int page = 1; page <= totalPage; page++) {

            if (totalCount < sheetIndex * maxSheetCount) {
                indexMap.put(page, sheetIndex);
                totalCount += batchPageSize;
                continue;
            }
            sheetIndex++;
            totalCount += batchPageSize;
            indexMap.put(page, sheetIndex);
        }
        return indexMap.entrySet()
                .stream()
                .collect(Collectors.groupingBy(
                        Map.Entry::getValue,  // 根据value进行分组
                        Collectors.mapping(Map.Entry::getKey, Collectors.toList())  // 将key放入列表
                ));
    }
}

```

## 具体实现子类

> 我们通过继承 `AbstractExcelExporter` 类，创建一个导出服务类 `RecordExcelExporterService`，并实现导出逻辑中的细节，如线程池配置、数据分页查询等。

```java
@Slf4j
@Service
public class RecordExcelExporterService extends AbstractExcelExporter<RecordQueryDTO, RecordDTO> {

    public ExporterVO exporter(RecordQueryDTO recordQueryDTO) {
        return writeExcel(recordQueryDTO);
    }

    @Override
    protected Executor getExecutor() {
        return ThreadUtil.newExecutor(8);
    }

    @Override
    protected Integer getBatchPageSize() {
        return 100000;
    }

    @Override
    protected Integer getMaxSheetCount() {
        return 500000;
    }

    @Override
    protected Long getCount(RecordQueryDTO queryDTO) {
        return 2000000L;
    }

    @Override
    protected List<RecordDTO> getDataPageList(RecordQueryDTO queryDTO, Integer batchPageSize, Integer start) {
        List<RecordDTO> list = new ArrayList<>();

        for (int i = 0; i < batchPageSize; i++) {
            RecordDTO recordDTO = new RecordDTO();
            recordDTO.setData1(IdUtil.fastSimpleUUID());
            recordDTO.setData2(IdUtil.fastSimpleUUID());
            recordDTO.setData3(IdUtil.fastSimpleUUID());
            recordDTO.setData4(IdUtil.fastSimpleUUID());
            recordDTO.setData5(IdUtil.fastSimpleUUID());
            recordDTO.setData6(IdUtil.fastSimpleUUID());
            recordDTO.setData7(IdUtil.fastSimpleUUID());
            recordDTO.setData8(IdUtil.fastSimpleUUID());
            recordDTO.setData9(IdUtil.fastSimpleUUID());
            list.add(recordDTO);
        }

        return list;
    }

    @Override
    protected Class<RecordDTO> getReturnClass() {
        return RecordDTO.class;
    }

    @Override
    protected String getSheetName(Integer excelIndex) {
        return String.format("excelSheet-%s", excelIndex);
    }

    @Override
    protected String getFileDir(String uniqueId) {
        return String.format("/home/<USER>/%s", uniqueId);
    }

    @Override
    protected String getFileFullPath(String uniqueId, Integer excelIndex) {
        return String.format("/home/<USER>/%s/excel-%s.xlsx", uniqueId, excelIndex);
    }

    @Override
    protected ExporterVO afterProcess(ExporterResultDTO exporterResultDTO) {
        Integer excelCount = exporterResultDTO.getExcelCount();
        if (excelCount == 1) {
            File firstExcel = exporterResultDTO.getFirstExcel();
            return ExporterVO.builder()
                    .url("http://")
                    .fileName("xxx.xlsx")
                    .build();
        }
        String fileDir = exporterResultDTO.getFileDir();
        // 上传oss
        log.info("afterProcess....");
        ZipUtil.zip(fileDir);
        return ExporterVO.builder()
                .url("http://")
                .fileName("xxx.zip")
                .build();
    }
}

```

## 接口调用

```java
@RestController
public class ExportController {

    private final RecordExcelExporterService recordExcelExporterService;

    public ExportController(RecordExcelExporterService recordExcelExporterService) {
        this.recordExcelExporterService = recordExcelExporterService;
    }

    @PostMapping("exporter")
    public ExporterVO exporter(@RequestBody RecordQueryDTO recordQueryDTO) {
        return recordExcelExporterService.exporter(recordQueryDTO);
    }
}
```

# 进阶

1. 后续可以到EasyExcel操作也由子类控制, 这样就可以自定义使用不同的excel操作包

# 总结

通过 EasyExcel 结合多线程和分页查询，能够高效解决大数据导出时的内存占用和性能问题。