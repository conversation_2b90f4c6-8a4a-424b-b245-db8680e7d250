# Maven 构建生命周期

## 三个构建生命周期

![](images/Pasted%20image%2020230524200701.png)

## build 阶段

![](images/Pasted%20image%2020230524195800.png)
| 阶段 | 处理 | 描述 |
| :------------ | :------- | :------------------------------------------------------- |
| 验证 validate | 验证项目 | 验证项目是否正确且所有必须信息是可用的 |
| 编译 compile | 执行编译 | 源代码编译在此阶段完成 |
| 测试 Test | 测试 | 使用适当的单元测试框架（例如 JUnit）运行测试。 |
| 包装 package | 打包 | 创建 JAR/WAR 包如在 pom.xml 中定义提及的包 |
| 检查 verify | 检查 | 对集成测试的结果进行检查，以保证质量达标 |
| 安装 install | 安装 | 安装打包的项目到本地仓库，以供其他项目使用 |
| 部署 deploy | 部署 | 拷贝最终的工程包到远程仓库中，以共享给其他开发人员和工程 |

## 发布 jar 到私服

> maven settings.xml 要配置私服的账号密码

```xml
    <distributionManagement>
        <repository>
            <id>cqt</id>
            <name>Releases</name>
            <url>http://172.16.252.130:7077/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>cqt</id>
            <name>Snapshot</name>
            <url>http://172.16.252.130:7077/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
```

## Maven settings.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>


<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <localRepository>/usr/local/software/maven/repository</localRepository>

  <pluginGroups>

  </pluginGroups>

  <proxies>

  </proxies>

  <servers>
        <server>
          <id>cqt</id>
          <username>admin</username>
          <password>cqt@1234</password>
        </server>
  </servers>

  <mirrors>

    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>public</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public/</url>
    </mirror>

    <mirror>
      <id>central</id>
      <mirrorOf>central</mirrorOf>
      <name>central</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>

    <mirror>
      <id>maven2</id>
      <mirrorOf>central</mirrorOf>
      <name>maven2</name>
      <url>https://repo1.maven.org/maven2/</url>
    </mirror>

    <mirror>
      <id>aliyunmavenspring</id>
      <mirrorOf>spring</mirrorOf>
      <name>阿里云公共仓库spring</name>
      <url>https://maven.aliyun.com/repository/spring</url>
    </mirror>

    <mirror>
      <id>mirror</id>
      <mirrorOf>central,jcenter,!rdc-releases,!rdc-snapshots</mirrorOf>
      <name>mirror</name>
      <url>https://maven.aliyun.com/nexus/content/groups/public</url>
    </mirror>

    <mirror>
        <id>cqt</id>
        <name>cqt</name>
        <mirrorOf>cqt</mirrorOf>
        <url>http://172.16.252.130:7077/repository/maven-public/</url>
    </mirror>

  </mirrors>

  <profiles>
      <profile>
        <id>maven</id>
        <repositories>
            <repository>
                <id>central</id>
                <url>https://maven.aliyun.com/nexus/content/groups/public</url>
                <releases>
                    <enabled>true</enabled>
                </releases>
                <snapshots>
                    <enabled>false</enabled>
                </snapshots>
            </repository>

            <repository>
                <id>maven2</id>
                <url>https://repo1.maven.org/maven2/</url>
                <releases>
                    <enabled>true</enabled>
                </releases>
                <snapshots>
                    <enabled>false</enabled>
                </snapshots>
            </repository>

              <repository>
                <id>cqt</id>
                <url>http://cqt</url>
                <releases><enabled>true</enabled></releases>
                <snapshots><enabled>true</enabled></snapshots>
              </repository>
        </repositories>
       <pluginRepositories>
          <pluginRepository>
            <id>central</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
          </pluginRepository>

          <pluginRepository>
            <id>maven2</id>
            <url>https://repo1.maven.org/maven2/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
          </pluginRepository>

          <pluginRepository>
            <id>cqt</id>
            <url>http://cqt</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
          </pluginRepository>
        </pluginRepositories>
    </profile>
  </profiles>


  <activeProfiles>
        <activeProfile>maven</activeProfile>
  </activeProfiles>
</settings>
```
