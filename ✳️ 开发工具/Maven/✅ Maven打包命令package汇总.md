# Maven 打包汇总

```bash
mvn clean package  -P prod -DskipTests

mvn clean package  -P prod -DskipTests -am -pl module1,module1/module1-child
```

## Maven 打包命令 mvn package 可用的参数

```abap
Usage: mvn [options] [<goal(s)>] [<phase(s)>]

Options:
 -am,--also-make                        如果当前项目发生变更，则同时构建相关模块
 -amd,--also-make-dependents            如果有依赖于当前每个模块的项目，则同时构建它们
 -B,--batch-mode                        批量模式（不输出颜色）
 -C,--strict-checksums                  必须验证下载的插件和本地文件的校验和
 -cpu,--check-plugin-updates            检查潜在更新的插件
 -D,--define <arg>                      定义系统属性
 -e,--errors                            显示错误信息
 -emp,--encrypt-master-password <arg>   加密Maven主密码
 -ep,--encrypt-password <arg>           加密新的Maven密码
 -f,--file <arg>                        指定POM文件路径名称，默认为pom.xml
 -ff,--fail-fast                        快速失败：当构建中遇到第一个错误时停止
 -fn,--fail-never                       从不失败，但要将结果标记为失败的状态
 -gs,--global-settings <arg>            海星配置文件路径
 -h,--help                              输出帮助信息
 -l,--log-file <arg>                    指定文件日志输出路径
 -N,--non-recursive                     不使用递归模式构建，并且仅构建当前模块
 -o,--offline                           离线模式，不通过互联网下载任何插件或依赖项
 -P,--activate-profiles <arg>           激活分离的profile
 -pl,--projects <arg>                   仅构建所述模块(和所有关联的模块)
 -q,--quiet                             简化输出模式
 -rf,--resume-from <arg>                从指定阶段(和目标)的项目中恢复构建
 -s,--settings <arg>                    指定Maven设置文件路径
 -T,--threads <arg>                     执行并行构建，其中“N”是并行线程数，默认使用CPU核心数
 -t,--toolchains <arg>                  指定工具链文件路径
 -U,--update-snapshots                  强制更新快照依赖和插件
 -v,--version                           输出版本信息
 -V,--show-version                      输出版本号信息并退出
 -X,--debug                             输出调试信息
```

常用参数：

- -Dproperty=value：定义系统属性
- -f pom.xml：指定 POM 文件
- -o：离线模式
- -P profile_id：激活指定的 profile
- -pl comma-delimited-list-of-artifact-ids：仅构建指定模块
- -am：同时构建相关模块
- -DskipTests：跳过单元测试

这些参数可以帮助您根据需要自定义 Maven 构建行为。

## 父子工程如何单独 package 某个服务

使用 Maven 进行 Spring Boot 父子工程打包时，您可以使用 -pl 或者 -am 参数来指定打包某个子模块及其下的所有子模块。
例如，假设您有一个名为 parent 的父项目，其中有两个子模块 module1 和 module2。如果您只想打包 module1 以及它的子模块 module1-child，可以使用以下命令：
mvn clean package -pl module1,module1/module1-child
或者
mvn clean package -am -pl module1,module1/module1-child
其中，**-pl 参数指定了要打包的子模块路径，逗号分隔多个子模块**；**-am 参数表示自动化构建依赖关系并同时构建子模块**， 而 -pl 指定实际执行构建的子模块列表。

```bash
mvn clean package -am -pl module1,module1/module1-child
```

## 打包时跳过单元测试

在使用 Maven 进行打包时，可以使用-DskipTests 参数来跳过单元测试。该参数控制 Maven 是否执行 unit test，从而加快构建时间。
您可以使用以下命令启动 Maven 构建并跳过单元测试：

```bash
mvn package -DskipTests
mvn package -Dmaven.test.skip=true
```

如果您使用的是 Spring Boot，可以使用以下命令：

```bash
mvn spring-boot:run -DskipTests
```

其中，**-D 参数用于传递系统属性**，**skipTests 参数表示跳过单元测试**。使用这个选项后，则会自动忽略 maven test phase 中所有标识@Test 的测试类和方法

## 打包时指定环境 profile

在使用 Maven 进行打包时，可以使用**-P 参数来指定执行的环境 profile**。通过使用 profile，我们可以根据不同的环境需求分别配置应用程序的依赖和属性值。
假设我们有一个名为 dev 的环境 profile 和一个名为 prod 的环境 profile，可以使用以下命令指定打包所需的环境 profile：

```bash
mvn package -P prod
```

上述命令将指定 prod 环境的配置信息用于构建/打包操作。我们需要在 pom.xml 文件中定义对应的 profile，例如：

```xml
<profiles>
  <profile>
    <id>dev</id>
    <activation>
      <activeByDefault>true</activeByDefault>
    </activation>
    <properties>
      <spring.profiles.active>dev</spring.profiles.active>
      <!-- dev 环境的属性 -->
    </properties>
  </profile>
  <profile>
    <id>prod</id>
    <properties>
      <spring.profiles.active>prod</spring.profiles.active>
      <!-- prod 环境的属性 -->
    </properties>
  </profile>
</profiles>
```

在上面的示例中，我们定义了两个 profile：dev 和 prod，并设置了每个 profile 对应的 spring.profiles.active 属性的值。这个属性用于告诉 Spring Boot 启动哪个环境。比如如果 dev 配置了 spring.profiles.active=dev，那么在运行时执行配置是应用到 dev 环境。
使用这种方式，可以根据环境的不同来放置配置文件、指定 properties 文件等操作，以达到更好的应用程序控制效果。
