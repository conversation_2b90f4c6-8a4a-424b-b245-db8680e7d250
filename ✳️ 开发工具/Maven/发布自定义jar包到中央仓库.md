# gpg生成公钥
```shell
gpg --gen-key
gpg --list-keys
```

# 上传公钥
```shell
keyserver.ubunut.com
keys.openpgp.org
pgp.mit.edu

gpg --keyserver keyserver.ubuntu.com --send-keys <你的公钥>
gpg --keyserver keys.openpgp.org --send-keys 44FBF59630BE59EE34B185DF7920B917796A2943
gpg --keyserver pgp.mit.edu --send-keys 44FBF59630BE59EE34B185DF7920B917796A2943

gpg --keyserver keys.openpgp.org --send-keys 077EDDEB1A0B41B92439416BAE53F37BD280C93B
gpg --keyserver pgp.mit.edu --send-keys 077EDDEB1A0B41B92439416BAE53F37BD280C93B

```

# 验证
```shell
gpg --keyserver keys.openpgp.org --recv-keys 44FBF59630BE59EE34B185DF7920B917796A2943
gpg --keyserver kpgp.mit.edu --recv-keys 44FBF59630BE59EE34B185DF7920B917796A2943


gpg --keyserver keys.openpgp.org --recv-keys 077EDDEB1A0B41B92439416BAE53F37BD280C93B
gpg --keyserver kpgp.mit.edu --recv-keys 077EDDEB1A0B41B92439416BAE53F37BD280C93B

```

# 发布
```shell
 killall gpg-agent
mvn deploy -e
mvn -s /Users/<USER>/sofeware/maven/apache-maven-3.8.6/conf/settings-local.xml deploy
```
