# arthas 常用命令

## 安装

```shell
docker exec -it lc-cloudcc-call-control /bin/sh
curl -O https://arthas.aliyun.com/arthas-boot.jar
java -jar arthas-boot.jar
```

## docker attach

```shell
docker exec -it  lc-cloudcc-call-control /bin/sh -c "curl -O https://arthas.aliyun.com/arthas-boot.jar && java -jar arthas-boot.jar"

docker exec -it  lc-cloudcc-queue-control /bin/sh -c "curl -O https://arthas.aliyun.com/arthas-boot.jar && java -jar arthas-boot.jar"

docker exec -it  lc-cloudcc-sdk-interface /bin/sh -c "curl -O https://arthas.aliyun.com/arthas-boot.jar && java -jar arthas-boot.jar"

```

# 关闭session

> 不用记得退出, 如果之前已经有attach成功的话，会直接连接之前的 3658端口。

```shell
stop
```

## trace 命令

```shell
trace --skipJDKMethod false -E com.cqt.call.strategy.event.callstatus.impl.InviteCallStatusStrategyImpl|com.cqt.call.strategy.event.callstatus.impl.BridgeCallStatusStrategyImpl|com.cqt.call.strategy.event.callstatus.impl.AnswerCallStatusStrategyImpl|com.cqt.call.strategy.event.callstatus.impl.MediaCallStatusStrategyImpl|com.cqt.call.strategy.event.callstatus.impl.HangupCallStatusStrategyImpl deal '#cost > 100'

trace --skipJDKMethod false -E com.cqt.sdk.client.strategy.client.impl.CheckinClientRequestStrategyImpl checkin '#cost > 500'

```


