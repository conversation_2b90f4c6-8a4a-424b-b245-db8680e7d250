# 代码同时推送到github和gitee

> 在工程 .git目录的config文件
> 配置多个git地址

```shell
[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[submodule]
	active = .
[remote "origin"]
	url = **************:kk01001/simple-im.git
	url = https://gitee.com/wellq/simple-im.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[user]
        name = kk
        email = <EMAIL>

```

## 推送

```shell
git push
```
