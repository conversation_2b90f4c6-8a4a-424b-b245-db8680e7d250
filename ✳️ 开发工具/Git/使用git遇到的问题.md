# 1. 在IDEA中用git提交代码时的detached HEAD问题
[https://blog.csdn.net/weixin_42035397/article/details/92772497](https://blog.csdn.net/weixin_42035397/article/details/92772497)
```java
1.首先在自己的代码的目录下打开git bash
2.然后通过 git branch ,查看当前项目中的所有分支
3.先查看自己的当前分支的提交状态 git status
4.如果发现了别的分支 , 记录下那个版本号(比如我当时需要提交的分支是master,可是新创建的分支为head),
	记下最新提交的版本号
5.然后创建一个临时分支
	$git branch temp + 上面记录下的版本号
6.然后切换到你需要提交到的分支
	$git checkout master
7.将之前新增加的临时分支与你要提交的分支合并
	$git merge temp
	这个时候已经将临时分支的内容合并好了
8.删除临时创建的分支
	$git branch -d temp
```

# 2. 不小心点了git cherry-pick

```
git cherry-pick --abort 
撤销操作
```

