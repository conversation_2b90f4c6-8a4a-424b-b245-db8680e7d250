## Git配置多个SSH-Key
## 背景
> 有gitee，github，公司gitlab，多个git账号时，本机同时配置多个SSH-Key


## 配置
### 先生成ssh-key
```bash
ssh-keygen -t rsa -C '<EMAIL>' -f ~/.ssh/gitee_id_rsa
ssh-keygen -t rsa -C '<EMAIL>' -f ~/.ssh/github_id_rsa
ssh-keygen -t rsa -C '<EMAIL>' -f ~/.ssh/gitlab_id_rsa
```
### ~/.ssh 目录下新建一个config文件
> Host和HostName填写git服务器的域名，IdentityFile指定私钥的路径

```git
# gitee
Host gitee.com
HostName gitee.com
PreferredAuthentications publickey
IdentityFile ~/.ssh/gitee_id_rsa
# github
Host github.com
HostName github.com
PreferredAuthentications publickey
IdentityFile ~/.ssh/github_id_rsa
```
### 在各个的网站上添加公钥
### 验证
```bash
ssh -T **************
ssh -T *************    
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683290832415-b4ca903c-0b11-4655-aeb2-3c0924bc4f57.png#averageHue=%23093340&clientId=u573166d8-506a-4&from=paste&height=93&id=u5d9f7435&originHeight=186&originWidth=1440&originalType=binary&ratio=2&rotation=0&showTitle=false&size=37099&status=done&style=none&taskId=ueb0e36dd-458c-4ade-aa2d-854604ba6e8&title=&width=720)
