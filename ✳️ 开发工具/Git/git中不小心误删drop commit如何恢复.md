# git中不小心误删drop commit如何恢复
```java
git reflog
git reset --hard {s}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1684326165534-420b3465-4878-472e-9093-7b5efc66d513.png#averageHue=%230e7e54&clientId=u380b7797-3357-4&from=paste&height=375&id=udfeac29a&originHeight=750&originWidth=1768&originalType=binary&ratio=2&rotation=0&showTitle=false&size=304415&status=done&style=none&taskId=ueeffebac-456a-4245-8d23-7e74b340b5a&title=&width=884)
