> springboot2.6

# 1. 启动类注解@SpringBootApplication

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(excludeFilters = { @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
		@Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class) })
public @interface SpringBootApplication {
}
```
#  2. @EnableAutoConfiguration 开启自动配置

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@AutoConfigurationPackage
@Import(AutoConfigurationImportSelector.class)
public @interface EnableAutoConfiguration {

	String ENABLED_OVERRIDE_PROPERTY = "spring.boot.enableautoconfiguration";

	Class<?>[] exclude() default {};

	String[] excludeName() default {};
}
```
# 3. @Import(AutoConfigurationImportSelector.class)
## AutoConfigurationImportSelector 自动配置选择类
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682430843983-e75f3d51-6c68-4944-a78c-ba88a495e8bf.png#averageHue=%23888b8f&clientId=ucf203895-9589-4&from=paste&height=304&id=u45f04f33&originHeight=608&originWidth=2662&originalType=binary&ratio=2&rotation=0&showTitle=false&size=69253&status=done&style=none&taskId=u900ecea3-dd8a-43e8-b230-f0e51cfcc82&title=&width=1331)
```java
public class AutoConfigurationImportSelector implements DeferredImportSelector, BeanClassLoaderAware,
		ResourceLoaderAware, BeanFactoryAware, EnvironmentAware, Ordered {

}
```
## 加载META-INF下的spring.factories
```java
public final class SpringFactoriesLoader {
	public static final String FACTORIES_RESOURCE_LOCATION = "META-INF/spring.factories";
	static final Map<ClassLoader, Map<String, List<String>>> cache = new ConcurrentReferenceHashMap<>();

public static List<String> loadFactoryNames(Class<?> factoryType, @Nullable ClassLoader classLoader) {
		ClassLoader classLoaderToUse = classLoader;
		if (classLoaderToUse == null) {
			classLoaderToUse = SpringFactoriesLoader.class.getClassLoader();
		}
		String factoryTypeName = factoryType.getName();
		return loadSpringFactories(classLoaderToUse).getOrDefault(factoryTypeName, Collections.emptyList());
	}

	private static Map<String, List<String>> loadSpringFactories(ClassLoader classLoader) {
        // 根据类加载器，从缓存中取
		Map<String, List<String>> result = cache.get(classLoader);
		if (result != null) {
			return result;
		}

		result = new HashMap<>();
		try {
            // 获取META-INF/spring.factories文件内的全限定类名
			Enumeration<URL> urls = classLoader.getResources(FACTORIES_RESOURCE_LOCATION);
			while (urls.hasMoreElements()) {
				URL url = urls.nextElement();
				UrlResource resource = new UrlResource(url);
				Properties properties = PropertiesLoaderUtils.loadProperties(resource);
				for (Map.Entry<?, ?> entry : properties.entrySet()) {
					String factoryTypeName = ((String) entry.getKey()).trim();
					String[] factoryImplementationNames =
							StringUtils.commaDelimitedListToStringArray((String) entry.getValue());
					for (String factoryImplementationName : factoryImplementationNames) {
						result.computeIfAbsent(factoryTypeName, key -> new ArrayList<>())
								.add(factoryImplementationName.trim());
					}
				}
			}

			// Replace all lists with unmodifiable lists containing unique elements
			result.replaceAll((factoryType, implementations) -> implementations.stream().distinct()
					.collect(Collectors.collectingAndThen(Collectors.toList(), Collections::unmodifiableList)));
			cache.put(classLoader, result);
		}
		catch (IOException ex) {
			throw new IllegalArgumentException("Unable to load factories from location [" +
					FACTORIES_RESOURCE_LOCATION + "]", ex);
		}
		return result;
	}

}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682431621137-7b20ac7f-9569-4f58-94a1-2779ed1a6fda.png#averageHue=%232d3239&clientId=ucf203895-9589-4&from=paste&height=516&id=u05ebab36&originHeight=1032&originWidth=2280&originalType=binary&ratio=2&rotation=0&showTitle=false&size=469261&status=done&style=none&taskId=u302b93d3-6b2f-4b8a-a6bc-1a4d02ba17c&title=&width=1140)
## 获取配置类全路径, 默认有273个AutoConfiguration
> _Return the auto-configuration class names that should be considered_

```java
protected List<String> getCandidateConfigurations(AnnotationMetadata metadata, AnnotationAttributes attributes) {
    List<String> configurations = SpringFactoriesLoader.loadFactoryNames(getSpringFactoriesLoaderFactoryClass(),
            getBeanClassLoader());
    Assert.notEmpty(configurations, "No auto configuration classes found in META-INF/spring.factories. If you "
            + "are using a custom packaging, make sure that file is correct.");
    return configurations;
}
```
![image.png](https://cdn.nlark.com/yuque/0/2022/png/684952/1667021799845-96d41acd-0651-4d73-8b24-a0dfe0e2ce8e.png#averageHue=%234a4e4d&clientId=ue9b6b151-3443-4&from=paste&height=460&id=u9cfe43a0&originHeight=920&originWidth=1828&originalType=binary&ratio=1&rotation=0&showTitle=false&size=351126&status=done&style=none&taskId=ubbe133b9-e78a-46ce-b4e5-e201bae3ecd&title=&width=914)
## 筛选要加载的配置类
```java
protected AutoConfigurationEntry getAutoConfigurationEntry(AnnotationMetadata annotationMetadata) {
    if (!isEnabled(annotationMetadata)) {
        return EMPTY_ENTRY;
    }
    AnnotationAttributes attributes = getAttributes(annotationMetadata);
    // 获取所有配置类
	List<String> configurations = getCandidateConfigurations(annotationMetadata, attributes);
    // 去重
	configurations = removeDuplicates(configurations);
    // 不加载的配置类
	Set<String> exclusions = getExclusions(annotationMetadata, attributes);
    checkExcludedClasses(configurations, exclusions);
    // 移除不加载的配置类
	configurations.removeAll(exclusions);
    configurations = getConfigurationClassFilter().filter(configurations);
    fireAutoConfigurationImportEvents(configurations, exclusions);
    return new AutoConfigurationEntry(configurations, exclusions);
}
```

## 通过配置类上的@ConditionalXXX注解
> 根据注解条件判断是否加到IOC容器内
> - @ConditionalOnClass  依赖存在类才加载
>    - @ConditionalOnClass({ DataSource.class, JdbcTemplate.class })
> - @ConditionalOnMissingBean  容器中不存在bean才加载
>    - @ConditionalOnMissingBean(WebMvcConfigurationSupport.class)
> - @ConditionalOnProperty  某个属性存在才加载
>    - @ConditionalOnProperty(prefix = "spring.mvc.formcontent.filter", name = "enabled", matchIfMissing = true)
>    - 存在enabled, 默认为true
> 
....
>  

## 通过@EnableConfigurationProperties加载配置信息
> @EnableConfigurationProperties(RabbitProperties.class)

```java
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({EnableConfigurationPropertiesRegistrar.class})
public @interface EnableConfigurationProperties {
    String VALIDATOR_BEAN_NAME = "configurationPropertiesValidator";

    Class<?>[] value() default {};
}

```
## 最后bean自动创建完成

# 4. 总结

- 条件化装配：通过@Condition注解决定是否装配
- SpringApplication：启动类
- Starter依赖管理：可以快速接入各种组件
- 自定义属性：通过@ConfigurationProperties注解
- 使用条件注解：@ConditionalOnClass、@ConditionalOnMissingBean、@ConditionalOnProperty 控制是否zhuceBean
- 自定义装配：可以通过实现BeanDefinitionRegistry接口并使用@Bean来进行定制扩展。



