# 开启WebSocket
```java
@Configuration
public class WebSocketConfig {
    /**
     * 注入ServerEndpointExporter，
     * 这个bean会自动注册使用了@ServerEndpoint注解声明的Websocket endpoint
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
```
# 定义端点
```java
@Slf4j
@Component
@ServerEndpoint(value = "/ctd-status-websocket/{callId}")
public class CtdStatusWebSocketServer {

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 呼叫id
     */
    private String callId;

    /**
     * 用来存放每个客户端对应的WebSocket对象。
     */
    private static final Cache<String, CtdStatusWebSocketServer> WEB_SCOKET_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1024)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 建立连接成功
     *
     * @param session 会话
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("callId") String callId) {
        this.session = session;
        this.callId = callId;
        WEB_SCOKET_CACHE.put(callId, this);
        log.info("【websocket消息】 有新的连接: {}，总数{}", callId, WEB_SCOKET_CACHE.size());
    }

    /**
     * 连接关闭
     */
    @OnClose
    public void onClose() {
        WEB_SCOKET_CACHE.invalidate(this.callId);
        log.info("【websocket消息】 连接: {}, 断开，总数{}", this.callId, WEB_SCOKET_CACHE.size());
    }

    /**
     * 接收客户端消息
     *
     * @param message 消息
     */
    @OnMessage
    public void onMessage(String message) {
        log.info("【websocket消息】 收到客户端发来的消息：{}", message);
    }

    /**
     * 发送自定义消息
     *
     * @param message 消息
     * @param callId  呼叫id
     * @throws IOException 异常
     */
    public void sendSingle(String message, @PathParam("callId") String callId) throws IOException {
        log.info("发送消息到:" + callId + "，报文:" + message);
        CtdStatusWebSocketServer server = WEB_SCOKET_CACHE.getIfPresent(callId);
        if (server == null) {
            log.error("用户: " + callId + ",不在线！");
            return;
        }
        server.sendMessage(message);
    }


    /**
     * 发送消息
     *
     * @param message 消息
     */
    public void sendMessage(String message) throws IOException {
        log.info("【websocket消息】 发送消息：{}", message);
        this.session.getBasicRemote().sendText(message);
    }
}
```
# 使用
```java
ws://ip:port/ctd-status-websocket/{callId}
```
# vue使用
```vue
// 初始化weosocket
    initWebSocket(){
      getAction(this.url.getWsIp + this.model.vccId).then((res) => {
        if (res.success) {
          if ( res.result !== '') {
            const wsUri = 'ws://' + res.result + '/private-number/report-api/ctd-status-websocket/' + this.model.callId;
            console.log(wsUri)
            this.websock = new WebSocket(wsUri);
            this.websock.onopen = this.websocketOnOpen;
            this.websock.onerror = this.websocketOnError;
            this.websock.onmessage = this.websocketOnMessage;
            this.websock.onclose = this.websocketClose;
          }
        }
      })
    },
    websocketOnOpen() {
    },
    websocketOnError(e) { //错误
    },
    websocketOnMessage(e){ //数据接收
      console.log(e.data);
      this.showStatus()
    },
    websocketClose(e){ //关闭
      this.websock.close();
    },
    open(){
      this.initWebSocket();
    },
```

