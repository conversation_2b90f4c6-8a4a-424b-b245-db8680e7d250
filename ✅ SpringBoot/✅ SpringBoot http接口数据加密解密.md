# 1. 接口准备

接口请求体使用 AES 的秘钥加密, 将密文丢 body 里

# 2. 实现, 解密

### 2. 1 @ControllerAdvice 在拦截器之后执行, 

```java
@Slf4j
@ControllerAdvice(basePackages = "com.kk0.system.controller")
public class DecodeRequestBodyAdvice implements RequestBodyAdvice {

    @Autowired
    private RedisUtil redisUtil;

    private String tokenHead = "Bearer ";

    private String tokenHeader = "Authorization";

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return methodParameter.getMethodAnnotation(SecurityParameter.class) != null;
    }


    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type,
                                           Class<? extends HttpMessageConverter<?>> aClass) throws IOException {

        try {
            boolean encode = false;
            // 判断是否有解密注解
            if (methodParameter.getMethod().isAnnotationPresent(SecurityParameter.class)) {
                // 获取注解配置的包含和去除字段
                SecurityParameter serializedField = methodParameter.getMethodAnnotation(SecurityParameter.class);
                // 入参是否需要解密
                encode = serializedField.inDecode();
            }
            if (encode) {
                log.info("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行解密");
                List<String> strings = httpInputMessage.getHeaders().get(tokenHeader);
                if (CollUtil.isNotEmpty(strings)) {
                    String tokenValue = strings.get(0).substring(tokenHead.length());
                    Map<String, Object> token = (Map<String, Object>) redisUtil.hget("token", tokenValue);
                    String publicKey = token.get("public_key").toString();
                    //String decrypt = AESUtils.decrypt(IoUtil.read(httpInputMessage.getBody(), CharsetUtil.CHARSET_UTF_8), publicKey);
                    // 获取解密后的输出流返回,
                    return new MyHttpInputMessage(httpInputMessage, publicKey);
                }
            }else{
                return httpInputMessage;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("对方法method :【" + methodParameter.getMethod().getName() + "】返回数据进行解密出现异常："+e.getMessage());
            return httpInputMessage;
        }
        return httpInputMessage;
    }

    @Override
    public Object afterBodyRead(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }

    @Override
    public Object handleEmptyBody(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }
}
```

### 2.2 自定义 HttpInputMessage, 通过秘钥解密密文, 再将解密后的数据丢输出流., 

```java
public class MyHttpInputMessage implements HttpInputMessage {

    private HttpHeaders headers;

    private InputStream body;

    private String publicKey;

    public MyHttpInputMessage(HttpInputMessage inputMessage, String publicKey) throws Exception {
        this.headers = inputMessage.getHeaders();
        this.body = getNewBody(inputMessage, publicKey);
    }


    @Override
    public InputStream getBody() throws IOException {
        return body;
    }

    @Override
    public HttpHeaders getHeaders() {
        return headers;
    }

    private InputStream getNewBody(HttpInputMessage inputMessage, String publicKey) throws IOException {
        InputStream body = inputMessage.getBody();
        String ciphertext = "";
        try {
            // 密文
            ciphertext = IOUtils.toString(inputMessage.getBody(), "utf-8");
            String decrypt = AESUtils.decrypt(ciphertext, publicKey);
            InputStream inputStream = IOUtils.toInputStream(decrypt);
            return inputStream;
        } catch (Exception e) {
            //return IOUtils.toInputStream(ciphertext, CharsetUtil.CHARSET_UTF_8);
            return null;
        }
    }
}
```

###  2.3 自定义注解, SecurityParameter

接口使用这个注解, 表明数据需要解密

```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Mapping
@Documented
public @interface SecurityParameter {

    /**
     - 入参是否解密，默认解密
     */
    boolean inDecode() default true;

    /**
     - 出参是否加密，默认加密
     */
    //boolean outEncode() default false;
}
```
