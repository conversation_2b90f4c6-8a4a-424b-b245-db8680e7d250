# SpringBoot 使用 Druid 连接池

## Druid 连接池介绍

https://github.com/alibaba/druid/wiki/Druid%E8%BF%9E%E6%8E%A5%E6%B1%A0%E4%BB%8B%E7%BB%8D#3-%E4%B8%BA%E7%9B%91%E6%8E%A7%E8%80%8C%E7%94%9F

## Druid 基本配置参数介绍

|     | 配置                                      | 缺省值               | 说明                                                                                                                                                                                                                                                                                                              |
| --- | :---------------------------------------- | -------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
|     | name                                      |                      | 配置这个属性的意义在于，如果存在多个数据源，监控的时候可以通过名字来区分开来。如果没有配置，将会生成一个名字，格式是："DataSource-" + System.identityHashCode(this). 另外配置此属性至少在 1.0.5 版本中是不起作用的，强行设置 name 会出错。[详情-点此处](http://blog.csdn.net/lanmo555/article/details/41248763)。 |
|     | url                                       |                      | 连接数据库的 url，不同数据库不一样。例如： mysql : ************************************** oracle : *******************************************                                                                                                                                                                    |
|     | username                                  |                      | 连接数据库的用户名                                                                                                                                                                                                                                                                                                |
|     | password                                  |                      | 连接数据库的密码。如果你不希望密码直接写在配置文件中，可以使用 ConfigFilter。[详细看这里](https://github.com/alibaba/druid/wiki/使用ConfigFilter)                                                                                                                                                                 |
|     | driverClassName                           | 根据 url 自动识别    | 这一项可配可不配，如果不配置 druid 会根据 url 自动识别 dbType，然后选择相应的 driverClassName                                                                                                                                                                                                                     |
|     | initialSize                               | 0                    | 初始化时建立物理连接的个数。初始化发生在显示调用 init 方法，或者第一次 getConnection 时                                                                                                                                                                                                                           |
|     | maxActive                                 | 8                    | 最大连接池数量                                                                                                                                                                                                                                                                                                    |
|     | maxIdle                                   | 8                    | 已经不再使用，配置了也没效果                                                                                                                                                                                                                                                                                      |
|     | minIdle                                   |                      | 最小连接池数量                                                                                                                                                                                                                                                                                                    |
|     | maxWait                                   |                      | 获取连接时最大等待时间，单位毫秒。配置了 maxWait 之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置 useUnfairLock 属性为 true 使用非公平锁。                                                                                                                                                          |
|     | poolPreparedStatements                    | false                | 是否缓存 preparedStatement，也就是 PSCache。PSCache 对支持游标的数据库性能提升巨大，比如说 oracle。在 mysql 下建议关闭。                                                                                                                                                                                          |
|     | maxPoolPreparedStatementPerConnectionSize | -1                   | 要启用 PSCache，必须配置大于 0，当大于 0 时，poolPreparedStatements 自动触发修改为 true。在 Druid 中，不会存在 Oracle 下 PSCache 占用内存过多的问题，可以把这个数值配置大一些，比如说 100                                                                                                                         |
|     | validationQuery                           |                      | 用来检测连接是否有效的 sql，要求是一个查询语句，常用 select 'x'。如果 validationQuery 为 null，testOnBorrow、testOnReturn、testWhileIdle 都不会起作用。                                                                                                                                                           |
|     | validationQueryTimeout                    |                      | 单位：秒，检测连接是否有效的超时时间。底层调用 jdbc Statement 对象的 void setQueryTimeout(int seconds)方法                                                                                                                                                                                                        |
|     | testOnBorrow                              | true                 | 申请连接时执行 validationQuery 检测连接是否有效，做了这个配置会降低性能。                                                                                                                                                                                                                                         |
|     | testOnReturn                              | false                | 归还连接时执行 validationQuery 检测连接是否有效，做了这个配置会降低性能。                                                                                                                                                                                                                                         |
|     | testWhileIdle                             | false                | 建议配置为 true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于 timeBetweenEvictionRunsMillis，执行 validationQuery 检测连接是否有效。                                                                                                                                                          |
|     | keepAlive                                 | false （1.0.28）     | 连接池中的 minIdle 数量以内的连接，空闲时间超过 minEvictableIdleTimeMillis，则会执行 keepAlive 操作。                                                                                                                                                                                                             |
|     | timeBetweenEvictionRunsMillis             | 1 分钟（1.0.14）     | 有两个含义： 1) Destroy 线程会检测连接的间隔时间，如果连接空闲时间大于等于 minEvictableIdleTimeMillis 则关闭物理连接。 2) testWhileIdle 的判断依据，详细看 testWhileIdle 属性的说明                                                                                                                               |
|     | numTestsPerEvictionRun                    | 30 分钟（1.0.14）    | 不再使用，一个 DruidDataSource 只支持一个 EvictionRun                                                                                                                                                                                                                                                             |
|     | minEvictableIdleTimeMillis                |                      | 连接保持空闲而不被驱逐的最小时间                                                                                                                                                                                                                                                                                  |
|     | connectionInitSqls                        |                      | 物理连接初始化的时候执行的 sql                                                                                                                                                                                                                                                                                    |
|     | exceptionSorter                           | 根据 dbType 自动识别 | 当数据库抛出一些不可恢复的异常时，抛弃连接                                                                                                                                                                                                                                                                        |
|     | filters                                   |                      | 属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有： 监控统计用的 filter:stat 日志用的 filter:log4j 防御 sql 注入的 filter:wall                                                                                                                                                                           |
|     | proxyFilters                              |                      | 类型是 List<com.alibaba.druid.filter.Filter>，如果同时配置了 filters 和 proxyFilters，是组合关系，并非替换关系                                                                                                                                                                                                    |

## maven 依赖导入

```xml
        <!--引入druid数据源-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.18</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
```

## 三方组件集成

### MyBatis 使用 Druid 配置

```yaml
spring:
  datasource:
    url: ***********************************************************
    username: root
    password: cqt@1234
    #    hikari:
    #      connection-timeout: 10000
    type: com.alibaba.druid.pool.DruidDataSource
    # https://github.com/alibaba/druid/wiki/DruidDataSource%E9%85%8D%E7%BD%AE%E5%B1%9E%E6%80%A7%E5%88%97%E8%A1%A8
    druid:
      name: db
      # 连接超时时间ms
      connect-timeout: 5000
      # 初始化连接数
      initial-size: 3
      # 最大连接数
      max-active: 5
      # 最小连接数
      min-idle: 1
      # 获取连接时最大等待时间ms
      max-wait: 3000
      # 使用非公平锁
      use-unfair-lock: true
      pool-prepared-statements: false
      max-open-prepared-statements: 3
      validation-query: select 1 from dual
      # 获取连接时检测
      test-on-borrow: false
      # 归还连接时检测
      test-on-return: false
      # 检测连接
      test-while-idle: true
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 5000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 180000
      max-evictable-idle-time-millis: 360000
      filter:
        wall:
          enabled: true
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        log4j2:
          enabled: true
          statement-execute-after-log-enabled: true
      filters: stat,log4j2,wall
      # 监控过滤器
      web-stat-filter:
        enabled: true
        exclusions:
          - "*.js"
          - "*.gif"
          - "*.jpg"
          - "*.png"
          - "*.css"
          - "*.ico"
          - "/druid/*"
      # 开启监控页面
      stat-view-servlet:
        enabled: true
        reset-enable: false
        login-username: admin
        login-password: 123456
        url-pattern: /druid/*

```

### MyBatis Plus 多数据源使用 Druid 配置

### Sharing-JDBC 使用 Druid 配置

## 监控页面

http://127.0.0.1:5001/druid/sql.html
![](images/Pasted%20image%2020230521153007.png)
![](images/Pasted%20image%2020230521153040.png)
