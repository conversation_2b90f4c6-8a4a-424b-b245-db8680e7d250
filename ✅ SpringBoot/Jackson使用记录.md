# Jackson 使用记录

## 首字母小写的字段

> 序列化, 使用声明的字段名称.

## demo

> 转 json 结果为 {"aname":"dd"}
> 使用注解@JsonAutoDetect后结果: {"aName":"dd"}

```java
@Data
public class JacksonDTO {

    private String aName;
}
```

### 解决

> getterVisibility = JsonAutoDetect.Visibility.NONE 才有效

```java
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
```

### @JsonAutoDetect 解释

- getterVisibility
  - 自动检测 getter 方法所需的最低可见性
- isGetterVisibility
  - 自动检测 is-getter 方法所需的最小可见性
- setterVisibility
  - 自动检测 setter 方法所需的最低可见度
- creatorVisibility
  - 自动检测 creator 方法所需的最低可见性，除了无参数构造函数(无论如何总是检测到它)
- fieldVisibility
  - 自动检测成员字段所需的最低可见性。

```java
// 可见性阈值(最小可见性)，可用于限制自动检测哪些方法(和字段)
public enum Visibility {
        /**
         * private to public, 所有
         */
        ANY,

        /**
         * 除了private
         */
        NON_PRIVATE,

        /**
         * 'protected' and 'public'
         */
        PROTECTED_AND_PUBLIC,

        /**
         * 只有public
         */
        PUBLIC_ONLY,
        /**
         * 没有
         */
        NONE,

        /**
         * 该值指示要使用默认可见性级别(无论它是什么，取决于上下文)。这通常意味着要使用继承值(来自父可见性设置)。
         */
        DEFAULT;

    }
```

## Date 字段格式化

```java
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private Date aCallEndTime;
```

## 序列化忽略 null 值

```java
@JsonInclude(JsonInclude.Include.NON_NULL)
```

## 序列化忽略某个字段

```java
@JsonIgnore
```
