# SpringBoot 接口使用Date类型接收时间参数

## 使用Date类型接收时间参数

```java
    @RequestMapping("/page2")
public List<Log> page2(Date start,Date end){
        LambdaQueryWrapper<Log> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.between(Log::getCreateTime,start,end);
        queryWrapper.last("limit 10");
        return logMapper.selectList(queryWrapper);
        }
```

## error

![](./images/1699691119340.png)

```txt
2023-11-11 16:23:19.035  WARN 11624 --- [io-13011-exec-3] .w.s.m.s.DefaultHandlerExceptionResolver : 
Resolved [org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: 
Failed to convert value of type 'java.lang.String' to required type 'java.util.Date'; 
nested exception is org.springframework.core.convert.ConversionFailedException:
Failed to convert from type [java.lang.String] to type [java.util.Date] for value '2021-01-10 17:36:27'; 
nested exception is java.lang.IllegalArgumentException]
```

## 解决方法一

在Spring Boot中，我们可以使用@DateTimeFormat注解来指定日期格式。

例如，我们有一个接口，用于获取当前时间。

```java

@RestController
public class TimeController {
    @GetMapping("/time")
    public String getTime(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date) {
        return date.toString();
    }
}
```

在上面的代码中，我们使用了@DateTimeFormat注解，并指定了日期格式为yyyy-MM-dd HH:mm:ss。

当我们访问/time接口时，返回的时间格式为：Sun Jan 10 17:36:27 CST 2021。

## 解决方法二 全局设置

