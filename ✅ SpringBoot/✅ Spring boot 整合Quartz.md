# 1. 依赖包

```xml
<dependency>
  	<groupId>org.springframework.boot</groupId>
 		<artifactId>spring-boot-starter-quartz</artifactId>
</dependency>
```

# 2. 配置

```java
@Configuration
public class QuartzConfig {

    //这个地方如果需要使用自定义的executor，可以在别的地方配置好，然后这里注入
    //@Autowired
    //private ThreadPoolTaskExecutor taskExecutor;


    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setAutoStartup(true);
        //这里如果不配置任务池，它就会默认加载SimpleThreadPool
        //factory.setTaskExecutor();
        return factory;
    }

    @Bean
    public Scheduler scheduler() throws IOException {
        return schedulerFactoryBean().getScheduler();
    }
}
```
# 3. 使用:  工具Service

```java
@Service
public class JobScheduleService {

    /**
     * 因为在配置中设定了这个bean的名称，这里就需要指定bean的名称，不然启动就会报错
     */
    @Autowired
    private Scheduler scheduler;

    /**
     * 功能描述: 添加简单任务
     *
     * @param
     * @return: void
     */
    public void addSimpleJob(Class taskClass, String jobName, String jobGroup, Date startTime, Date endTime) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        SimpleTrigger simpleTrigger = TriggerBuilder.newTrigger()
                .withIdentity(jobName, jobGroup)
                .startAt(startTime)
                .endAt(endTime)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInSeconds(3)
                        .withRepeatCount(5))
                .build();
        try {
            scheduler.scheduleJob(jobDetail, simpleTrigger);
        } catch (SchedulerException e) {
            StaticLog.error("addSimpleJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 添加定时任务
     *
     * @param
     * @return: void
     */
    public void addCronJob(Class taskClass, String jobName, String jobGroup, String cronExpression) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        // 触发器
        try {
            CronTrigger trigger = new CronTriggerImpl(jobName, jobGroup, jobName, jobGroup, cronExpression);// 触发器名,触发器组
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException | ParseException e) {
            StaticLog.error("addCronJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 添加定时任务, 可传参map到Job
     * @param taskClass
     * @param jobName
     * @param jobGroup
     * @param cronExpression
     * @param params
     */
    public void addCronJob(Class taskClass, String jobName, String jobGroup, String cronExpression,
                           Map<String, Object> params) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        // 向job传参
        jobDetail.getJobDataMap().putAll(params);
        try {
            // 触发器
            CronTrigger trigger = new CronTriggerImpl(jobName, jobGroup, jobName, jobGroup, cronExpression);// 触发器名,触发器组
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException | ParseException e) {
            StaticLog.error("addCronJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 修改任务Trigger，即修改任务的定时机制
     *
     * @param
     * @return: void
     */
    public void modifyJob(String jobName, String jobGroup, String cronExpression) {
        TriggerKey oldKey = new TriggerKey(jobName, jobGroup);
        //表达式调度构建器(即任务执行的时间)
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExpression);
        //按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger().
                withIdentity(jobName, jobGroup).withSchedule(scheduleBuilder).build();
        try {
            scheduler.rescheduleJob(oldKey, trigger);
        } catch (SchedulerException e) {
            StaticLog.error("modifyJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 暂停任务，只支持定时任务的暂停，不支持单次任务，单次任务需要interrupt
     *
     * @param
     * @return: void
     */
    public void pauseJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            if (StringUtils.isEmpty(jobDetail)) {
                System.out.println("没有这个job");
            }
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("pauseJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 从暂停状态中恢复定时任务运行
     *
     * @param
     * @return:  void
     */
    public void resumeJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("resumeJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 删除任务
     *
     * @param
     * @return: void
     */
    public void deleteJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            scheduler.deleteJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("deleteJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 检查任务是否启动
     * @param jobName
     * @param jobGroup
     * @return
     */
    public boolean checkJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            return scheduler.checkExists(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("deleteJob catch {}", e.getMessage());
        }
        return false;
    }
}
```
# 4. 使用: 执行任务

```java
    // 注入 bean
    @Autowired
    private JobScheduleService jobScheduleService;
	
	// 执行添加Job
    jobScheduleService.addCronJob(MonitorJob.class, appId, appId, "0/1 * * * * ?", params);

```

# 5. 实现Job注入Bean

```java
// 定义一个Quartz工厂
@Component
public class QuartzJobFactory extends AdaptableJobFactory {

    /**
     * AutowireCapableBeanFactory接口是BeanFactory的子类
     * 可以连接和填充那些生命周期不被Spring管理的已存在的bean实例
     */
    @Autowired
    private AutowireCapableBeanFactory autowireCapableBeanFactory;

    @Override
    protected Object createJobInstance(TriggerFiredBundle bundle) throws Exception {
        // 调用父类的方法
        Object jobInstance = super.createJobInstance(bundle);
        // 进行注入
        autowireCapableBeanFactory.autowireBean(jobInstance);
        return jobInstance;
    }
}

// 在 Quartz配置 注入工厂
@Configuration
public class QuartzConfig {

    //这个地方如果需要使用自定义的executor，可以在别的地方配置好，然后这里注入
    //@Autowired
    //private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private QuartzJobFactory quartzJobFactory;


    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setAutoStartup(true);

        // 设置工厂
        factory.setJobFactory(quartzJobFactory);

        //这里如果不配置任务池，它就会默认加载SimpleThreadPool
        //factory.setTaskExecutor();
        return factory;
    }

    @Bean
    public Scheduler scheduler() throws IOException {
        return schedulerFactoryBean().getScheduler();
    }
}

// 在Job就可以注入Bean
@Autowired
private ReqLogDao reqLogDao;

```
# 6. Quartz工具类实现

```java
@Service
public class JobScheduleService {

    /**
     * 因为在配置中设定了这个bean的名称，这里就需要指定bean的名称，不然启动就会报错
     */
    @Autowired
    private Scheduler scheduler;

    /**
     * 功能描述: 添加简单任务
     *
     * @param
     * @return: void
     */
    public void addSimpleJob(Class taskClass, String jobName, String jobGroup, Date startTime, Date endTime) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        SimpleTrigger simpleTrigger = TriggerBuilder.newTrigger()
                .withIdentity(jobName, jobGroup)
                .startAt(startTime)
                .endAt(endTime)
                .withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInSeconds(3)
                        .withRepeatCount(5))
                .build();
        try {
            scheduler.scheduleJob(jobDetail, simpleTrigger);
        } catch (SchedulerException e) {
            StaticLog.error("addSimpleJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 添加定时任务
     *
     * @param
     * @return: void
     */
    public void addCronJob(Class taskClass, String jobName, String jobGroup, String cronExpression) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        // 触发器
        try {
            CronTrigger trigger = new CronTriggerImpl(jobName, jobGroup, jobName, jobGroup, cronExpression);// 触发器名,触发器组
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException | ParseException e) {
            StaticLog.error("addCronJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 添加定时任务, 可传参map到Job
     * @param taskClass
     * @param jobName
     * @param jobGroup
     * @param cronExpression
     * @param params
     */
    public void addCronJob(Class taskClass, String jobName, String jobGroup, String cronExpression,
                           Map<String, Object> params) {
        JobDetail jobDetail = JobBuilder.newJob(taskClass).withIdentity(jobName, jobGroup).build();
        // 向job传参
        jobDetail.getJobDataMap().putAll(params);
        try {
            // 触发器
            CronTrigger trigger = new CronTriggerImpl(jobName, jobGroup, jobName, jobGroup, cronExpression);// 触发器名,触发器组
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException | ParseException e) {
            StaticLog.error("addCronJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 修改任务Trigger，即修改任务的定时机制
     *
     * @param
     * @return: void
     */
    public void modifyJob(String jobName, String jobGroup, String cronExpression) {
        TriggerKey oldKey = new TriggerKey(jobName, jobGroup);
        //表达式调度构建器(即任务执行的时间)
        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExpression);
        //按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger().
                withIdentity(jobName, jobGroup).withSchedule(scheduleBuilder).build();
        try {
            scheduler.rescheduleJob(oldKey, trigger);
        } catch (SchedulerException e) {
            StaticLog.error("modifyJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 暂停任务，只支持定时任务的暂停，不支持单次任务，单次任务需要interrupt
     *
     * @param
     * @return: void
     */
    public void pauseJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            if (StringUtils.isEmpty(jobDetail)) {
                System.out.println("没有这个job");
            }
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("pauseJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 从暂停状态中恢复定时任务运行
     *
     * @param
     * @return:  void
     */
    public void resumeJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("resumeJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 删除任务
     *
     * @param
     * @return: void
     */
    public void deleteJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            scheduler.deleteJob(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("deleteJob catch {}", e.getMessage());
        }
    }

    /**
     * 功能描述: 检查任务是否启动
     * @param jobName
     * @param jobGroup
     * @return
     */
    public boolean checkJob(String jobName, String jobGroup) {
        JobKey jobKey = new JobKey(jobName, jobGroup);
        try {
            return scheduler.checkExists(jobKey);
        } catch (SchedulerException e) {
            StaticLog.error("deleteJob catch {}", e.getMessage());
        }
        return false;
    }
}

```

