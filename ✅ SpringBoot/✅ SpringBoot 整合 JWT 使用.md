# ​SpringBoot 整合 JWT 使用

## JWT 是什么

https://jwt.io/
JSON Web Token （JWT） 是一种开放标准 （RFC 7519），它定义了一种紧凑且独立的方式，用于在各方之间以 JSON 对象的形式安全地传输信息。此信息可以验证和信任，因为它是经过数字签名的。JWT 可以使用密钥（使用 HMAC 算法）或使用 RSA 或 ECDSA 的公钥/私钥对进行签名。

## JWT 结构

JSON Web Token 是由点 （.） 分隔的三个部分组成。
Header.Payload.Signature

- Header
- Payload
- Signature

![](images/Pasted%20image%2020230521161237.png)

### Header

标头通常由两部分组成：令牌的类型（即 JWT）和正在使用的签名算法，例如 HMAC SHA256 或 RSA。此 JSON 被 Base64Url 编码以形成 JWT 的第一部分。

```json
{
    "alg": "HS256",
    "typ": "JWT"
}
```

### Payload

令牌的第二部分是有效负载，其中包含声明。声明是有关实体（通常是用户）和其他数据的语句.
There are three types of claims: registered, public, and private claims

#### **Registered Claim**

https://datatracker.ietf.org/doc/html/rfc7519#section-4.1
官方定义的 7 个字段

```
"iss" (issuer)   签发人
"sub" (subject)	  主题
"aud" (audience)  受众
"exp" (expiration time) 过期时间
"nbf" (not before) 生效时间
"iat" (Issued At) 签发时间
"jti" (JWT ID) 编号
```

![](images/Pasted%20image%2020230521161753.png)
也可已自定义字段

```json
{
    "iss": "John Wu JWT",
    "iat": 1441593502,
    "exp": 1441594722,
    "aud": "www.example.com",
    "sub": "<EMAIL>",
    "from_user": "B",
    "target_user": "A"
}
```

#### Public Claim

#### Private Claim

### Signature

签名，对 header，payload 进行签名，使用 header 指定的签名算法。

```java
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret)
```

![](images/Pasted%20image%2020230521161023.png)

## 优缺点比较

```
1. 去中心化的JWT token
    优点：
        1. 去中心化，便于分布式系统使用
        2. 基本信息可以直接放在token中。 username，nickname，role
        3. 功能权限较少的话，可以直接放在token中。用bit位表示用户所具有的功能权限
    缺点：服务端不能主动让token失效

2. 中心化的 redis token / memory session等
    优点：服务端可以主动让token失效
    缺点：
       1. 依赖内存或redis存储。
       2. 分布式系统的话，需要redis查询/接口调用增加系统复杂性。

```

## 如何使用

### Maven 依赖

```xml
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>
```

### 生成 token

```java
public class JwtUtil {
    private static final RSA rsa = SecureUtil.rsa();

    public static String createToken() {
        String compact = Jwts.builder()
                // 自定义参数
                .claim("name", "lisi")
                .claim("age", 16)
                // rsa加密
                .signWith(SignatureAlgorithm.RS256, rsa.getPrivateKey())
                // 有效期30分钟
                .setExpiration(new Date(System.currentTimeMillis() + 3600000))
                // 早于此时间的token不可用
                .setNotBefore(new Date())
                // 发布时间
                .setIssuedAt(new Date())
                // 唯一标识
                .setId(UUID.randomUUID().toString())
                // The payload and claims properties are mutually exclusive - only one of the two may be used
                // Both 'payload' and 'claims' cannot both be specified. Choose either one.
//                .setPayload("1")
                .compact();
        return compact;
    }

    public static Claims parseToken(String token) {
        return Jwts
                .parser()
                .setSigningKey(rsa.getPublicKey())
                .parseClaimsJws(token)
                .getBody();
    }

    public static void main(String[] args) {
        String privateKeyBase64 = rsa.getPrivateKeyBase64();
        System.out.println(privateKeyBase64);
        String publicKeyBase64 = rsa.getPublicKeyBase64();
        System.out.println(publicKeyBase64);
        String token = createToken();
        System.out.println(token);
        // {name=lisi, age=16, exp=1684663616, nbf=1684660016, iat=1684660016, jti=07b86e8b-9a64-42d0-ba7c-01b8cce175c0}
        System.out.println(parseToken(token));
    }
}
```
