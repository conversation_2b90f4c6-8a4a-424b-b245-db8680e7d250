# SpringBoot 集成 flyway 数据库版本

## maven 依赖

spring-boot-starter 2.6.11

```xml
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>8.5.13</version>
        </dependency>

```

## Spring Boot 和 Flyway 版本关系

flyway-core 8.2.0 版本之后不支持 MySQL
![](images/Pasted%20image%2020230522204647.png)
将 MySQL 代码提取到插件。这将需要作为新的依赖项添加

```xml
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
            <version>8.5.13</version>
        </dependency>
```

SpringBoot2.6.11 使用 9.0.0 以上版本报错，可能需要 SpringBoot3.0
![](images/Pasted%20image%2020230522205639.png)

## Flyway 更新记录

https://flywaydb.org/documentation/learnmore/releaseNotes#8.5.13

## yml 配置

```yaml
spring:
  datasource:
    url: *****************************************************
    username: root
    password: 123456
    #    hikari:
    #      connection-timeout: 10000
    type: com.alibaba.druid.pool.DruidDataSource
    # https://github.com/alibaba/druid/wiki/DruidDataSource%E9%85%8D%E7%BD%AE%E5%B1%9E%E6%80%A7%E5%88%97%E8%A1%A8
    druid:
      name: db
      # 连接超时时间ms
      connect-timeout: 5000
      # 初始化连接数
      initial-size: 3
      # 最大连接数
      max-active: 5
      # 最小连接数
      min-idle: 1
      # 获取连接时最大等待时间ms
      max-wait: 3000
      # 使用非公平锁
      use-unfair-lock: true
      pool-prepared-statements: false
      max-open-prepared-statements: 3
      validation-query: select 1 from dual
      # 获取连接时检测
      test-on-borrow: false
      # 归还连接时检测
      test-on-return: false
      # 检测连接
      test-while-idle: true
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 5000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 180000
      max-evictable-idle-time-millis: 360000
      filter:
        wall:
          enabled: true
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        log4j2:
          enabled: true
          statement-execute-after-log-enabled: true
      filters: stat,log4j2,wall
      # 监控过滤器
      web-stat-filter:
        enabled: true
        exclusions:
          - "*.js"
          - "*.gif"
          - "*.jpg"
          - "*.png"
          - "*.css"
          - "*.ico"
          - "/druid/*"
      # 开启监控页面
      stat-view-servlet:
        enabled: true
        reset-enable: false
        login-username: admin
        login-password: 123456
        url-pattern: /druid/*
  flyway:
    enabled: true
    # 禁止清理数据库表
    clean-disabled: true
    # 如果数据库不是空表，需要设置成 true，否则启动报错
    baseline-on-migrate: true
    # 与 baseline-on-migrate: true 搭配使用
    baseline-version: 0
    locations:
      - classpath:db/migration****
```

![](images/Pasted%20image%2020230522202731.png)

## 版本文件规则

- 版本迁移(Versioned Migrations)以 V 开头，**只会执行一次**；
- 回退迁移(Undo Migrations)以 U 开头，执行一旦发生破坏性更改，就会很麻烦，项目中一般不用；
- 可重复执行迁移(Repeatable Migrations)以 R 开头，**每次修改后都会重新执行**。
- ![](images/Pasted%20image%2020230522203604.png)
    自动生成此表，记录版本
    ![](images/Pasted%20image%2020230522203700.png)

### 注意点

版本号要唯一，版本号和版本描述之间，使用两个下划线分隔。
![](images/Pasted%20image%2020230522204040.png)

版本迁移 V，文件内容执行一次后不能修改，否则 checksum 会变化报错。
![](images/Pasted%20image%2020230522203852.png)
V 开头的 SQL 执行优先级要比 R 开头的 SQL 优先级高。

### 文件样例

V20230522_1\_\_create_table.sql

```sql
CREATE TABLE IF NOT EXISTS `test_flyway_table`
(
    `id`   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `time` datetime   NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

```

V20230522_2\_\_insertTable.sql

```sql
INSERT INTO `test_flyway_table`
VALUES ('1', '2021-06-28 17:48:48');

```

R\_\_addTable.sql

```sql
update `test_flyway_table`
set time = '2023-09-23 17:48:48'
where id = 2;

```

## 遇到问题
### 1.  Flyway Teams Edition or MySQL upgrade required: MySQL 5.7 is no longer supported by Flyway Community Edition, but still supported by Flyway Teams Edition.
mysql5.7 不在支持MySQL 5.7
![](images/Pasted%20image%2020230527134228.png)
![](images/Pasted%20image%2020230527134319.png)