# 1. 使用的注解

```java
@RestControllerAdvice
```

# 2. 配置实现

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    // 捕获全局异常,处理所有不可知的异常, 可以使用多个ExceptionHandler
    @ExceptionHandler(value = Exception.class)
    public Object handleException(Exception e, HttpServletRequest request) {
        String message = e.getMessage();
        // 根据异常结果判断, 返回不同的信息
        if (message.startsWith("Required request body is missing")) {
            return ResponseData.fail(ResponseEnum.REQUEST_BODY_EMPTY.getMsg(), ResponseEnum.REQUEST_BODY_EMPTY.getCode());
        }
        return ResponseData.fail(e.getMessage(), ResponseEnum.UNKNOWN_EXCEPTION.getCode());
    }
}

```
