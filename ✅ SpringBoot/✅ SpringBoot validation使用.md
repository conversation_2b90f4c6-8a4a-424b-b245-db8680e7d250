# SpringBoot validation 使用分析

## 使用方法

### 实体类定义

![](images/Pasted%20image%2020230524151041.png)

### 请求体参数

![](images/Pasted%20image%2020230524151023.png)

### 表单参数和路径参数

![](images/Pasted%20image%2020230524151005.png)

## @Valid 和@Validated 区别

![](./images/1684912774767.png)

```java

@Target({METHOD, FIELD, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
public @interface Valid {
}
```

```java

@Target({ElementType.TYPE, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Validated {

    /**
     - group类
     */
    Class<?>[] value() default {};

}
```

## 全局异常捕获

```java

@RestControllerAdvice
public class ValidationHandlers {

    /**
     - 表单请求校验结果处理
     *
     - @param bindException
     - @return
     */
    @ExceptionHandler(value = BindException.class)
    public List<ObjectError> errorHandler(BindException bindException) {
        BindingResult bindingResult = bindException.getBindingResult();
        return bindingResult.getAllErrors();
    }

    /**
     - JSON请求校验结果，也就是请求中对实体标记了@RequestBody
     *
     - @param methodArgumentNotValidException
     - @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public List<ObjectError> errorHandler(MethodArgumentNotValidException methodArgumentNotValidException) {
        BindingResult bindingResult = methodArgumentNotValidException.getBindingResult();
        return bindingResult.getAllErrors();
    }

    /**
     - 表单参数和路径参数异常
     *
     - @param constraintViolationException
     - @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public String errorHandler(ConstraintViolationException constraintViolationException) {
        String message = constraintViolationException.getMessage();
        Set<ConstraintViolation<?>> constraintViolations = constraintViolationException.getConstraintViolations();
        return message;
    }
}

```

## 分组

```java
public interface UpdateValidation {
}

public interface AddValidation {
}
```

![](./images/1684912351999.png)
![](images/1684912401468.png)
![](./images/1684912401468.png)

## 自定义规则

```java

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
// 指定校验处理类
@Constraint(validatedBy = {TelValidator.class})
public @interface Tel {

    String message() default "must be tel.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
```

```java
public class TelValidator implements ConstraintValidator<Tel, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StrUtil.isEmpty(value)) {
            context.buildConstraintViolationWithTemplate("value is null");
            return false;
        }
        return ReUtil.isMatch(PatternPool.MOBILE, value);
    }

    @Override
    public void initialize(Tel constraintAnnotation) {
        // 获取注解的值, 进行初始化变量
        ConstraintValidator.super.initialize(constraintAnnotation);
    }
}
```

## 作为 bean 使用

```java

@Configuration
public class ValidatorConfig {

    @Bean
    public Validator validator() {
        ValidatorFactory factory = Validation.byProvider(HibernateValidator.class)
                .configure()
                // 快速失败, 遇到一个失败就结束
                .failFast(false)
                .buildValidatorFactory();
        return factory.getValidator();
    }
}
```

![](./images/1684912561888.png)

## 原理分析

## 参考

https://juejin.cn/post/6919344314267287565#heading-19
