[TOC]

# SpringBoot 整合单机锁、各种分布式锁实现

## 单机锁

### synchronized

[✅ synchronized 关键字](../✅%20Java相关知识/✅%20Java并发编程/✅%20synchronized关键字.md)

### Lock 接口

## 分布式锁

### 1. MySQL 悲观锁

悲观锁的实现方式是在对数据行进行操作前，先获取该行数据的锁。如果该行数据已经被其他用户加锁，则当前用户需要等待锁被释放后才能对该行数据进行操作。

#### 实现方式

1. SELECT ... FOR UPDATE 行锁
2. LOCK TABLES 表锁
3. 表插入主键，只有插入成功的才获取到锁，其他线程只能丢弃请求。

### 2. MySQL 乐观锁

乐观锁基于一个版本号机制，当一个线程对数据进行修改时，会为该数据增加一个版本号。当其他线程尝试访问该数据时，MySQL 会检查该数据的版本号是否与当前版本号相同。如果版本号相同，则允许访问；如果版本号不同，则拒绝访问，直到当前用户提交了修改。

#### 测试表结构

```sql
CREATE TABLE IF NOT EXISTS `optimistic_lock`
(
    `id`      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `time`    datetime   NOT NULL COMMENT '创建时间',
    `count`   bigint(20) NOT NULL COMMENT '数量',
    `version` int(1)     NOT NULL COMMENT '版本号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
```

#### 代码实现

![](images/Pasted%20image%2020230527205048.png)

![](images/Pasted%20image%2020230527204143.png)

#### 结果

> 初始化 count=1000，多线程情况下，最多 1000 个请求成功。

![image-20230527204912757](images/image-20230527204912757.png)

![](images/Pasted%20image%2020230527204858.png)

### 3. redis setnx

setnx 命令，只有一个线程能执行成功，其他线程可以等待重试或抛出异常。
注意点：

1. 需要设置超时时间，防止设置锁后异常未解锁，导致后续都无法获取到锁。
2. 若任务执行时间大于超时时间，应延长锁超时，不然可能被其他线程删除锁
   1. 看门狗机制，监视锁
3. 最终要释放锁（删除 key）。

### 4. redisson-可重入锁

#### redisson 版本

![](images/Pasted%20image%2020230527220329.png)

#### 两种方式

- 阻塞等待获取锁 lock 无返回值
- 尝试获取锁 tryLock 是否获取成功

##### 代码 demo

阻塞等待获取锁

![](images/Pasted%20image%2020230527214146.png)

尝试获取锁
两个时间：
waitTime：等待获取时间超时时间
leaseTime：锁的超时时间（默认 30 秒）

![](images/Pasted%20image%2020230527220447.png)

#### 源码

![](images/Pasted%20image%2020230527212420.png)

![](images/Pasted%20image%2020230527212438.png)

![](images/Pasted%20image%2020230527212707.png)

#### 参数解释

- KEYS[1] 分布式锁 key
- ARGV[1] 过期时间 ms
- ARGV[2] uuid:线程 id hash 项
- value 重入次数，调用几次 lock

#### lua

只要满足一个条件：

- key 存在，说明线程已经获取到锁，后续再次重入锁
- 指定元素（uuid:线程 id）的值=1，说明线程第一次获取锁，线程可以重入锁

```lua
# KEYS[1]存在 或 hash项 ARGV[2]的值为1
if ((redis.call('exists', KEYS[1]) == 0)  or (redis.call('hexists', KEYS[1], ARGV[2]) == 1)) then
    # hash项 ARGV[2]的值递增1
    redis.call('hincrby', KEYS[1], ARGV[2], 1);
    # 设置key的超时时间为ARGV[1]
    redis.call('pexpire', KEYS[1], ARGV[1]);
    return nil;
end;
    # 存在则返回key的剩余过期时间
    return redis.call('pttl', KEYS[1]);
```

![](images/Pasted%20image%2020230527212523.png)

![](images/Pasted%20image%2020230527214940.png)

### 5. zookeeper 临时索引
