随机权重算法（Weighted Random Algorithm）是一种在N个元素中按照各自权重进行随机选择的算法，通常使用在计算机负载均衡、广告投放、搜索引擎、推荐系统等领域中。
其实现原理是将每个元素按照其权重大小转换为一段连续的区间，在[0,1)的随机数据范围内产生一个随机数，根据该随机数在各自区间内进行选择。例如有三个元素A、B、C，它们的权重分别为3、4、5，那么可以将其对应的区间划分为[0,0.3)、[0.3,0.7)、[0.7,1.0)，产生的随机数如果在[0,0.3)范围内，则选择A，如果在[0.3,0.7)范围内，则选择B，如果在[0.7,1.0)范围内，则选择C。
下面给出Java代码实现：
```java
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

class Element {
    public String name;   // 元素名称
    public int weight;    // 元素权重
    public double weightRatio;   // 权重比例

    public Element(String name, int weight) {
        this.name = name;
        this.weight = weight;
    }
}

public class WeightedRandom {

    public static void main(String[] args) {
        // 初始化元素列表
        List<Element> elements = new ArrayList<>();
        elements.add(new Element("A", 3));
        elements.add(new Element("B", 4));
        elements.add(new Element("C", 5));

        // 计算元素总权重
        int totalWeight = 0;
        for (Element element : elements) {
            totalWeight += element.weight;
        }

        // 计算每个元素的权重比例
        for (Element element : elements) {
            element.weightRatio = element.weight * 1.0 / totalWeight;
        }

        // 随机选择元素
        Random random = new Random();
        double randomValue = random.nextDouble();  // 产生[0,1)范围内的随机数
        double sumRatio = 0;
        for (Element element : elements) {
            sumRatio += element.weightRatio;
            if (randomValue < sumRatio) {
                System.out.println("选择的元素名称：" + element.name);
                break;
            }
        }
    }
}
```
以上代码实现了元素列表的初始化，计算每个元素的权重比例，以及随机选择元素的过程。其中随机数生成使用了Java内置的Random类的nextDouble()方法，产生了一个[0,1)的浮点型随机数。在选择元素的过程中，根据产生的随机数在各个元素的区间内判断是否选中相应元素。
