# 快速排序（Quick Sort）
## gpt
快速排序（Quicksort）是一种常用的基于分治思想的排序算法。它的基本思路是将一个待排序的序列（通常是一个数组）分成两个子序列，其中一个子序列的所有元素都比另一个子序列的元素小，然后再递归地对这两个子序列进行快速排序，直到整个序列有序为止。
具体来说，快排的过程如下：

1. 选取基准值：从待排序序列中选择一个元素作为基准值，通常选择第一个或最后一个元素。
2. 分区操作：将待排序序列中小于基准值的元素移动到基准值的左边，大于等于基准值的元素移动到右边。这个过程称为“分区”（partition）操作。

在分区操作中，我们可以使用两个指针 i 和 j 分别指向序列的首尾元素，然后通过交换元素的方式把比基准值小的元素放到序列前面，比基准值大的元素放到序列后面。具体方法如下：
a. 初始化 i 和 j 指针分别指向序列的首元素和尾元素。
b. 从后往前遍历序列，并向前移动 j 指针，直到找到第一个小于基准值的元素。
c. 从前往后遍历序列，并向后移动 i 指针，直到找到第一个大于等于基准值的元素。
d. 如果 i < j，则交换 i 和 j 所指向的元素。
e. 重复步骤 b 到 d，直到 i >= j。
f. 最后，将基准值和 i 指向的元素互换。这样，在排序过程中，基准值的最终位置就能确定下来。

1. 递归操作：对基准值左边的子序列和右边的子序列分别进行步骤 1 和 2 的操作，直到整个序列有序为止。

需要注意的是，在实现快排算法时还需要考虑一些特殊情况，比如序列为空或只有一个元素的情况、选取基准值的策略、分区操作中 i 和 j 指针的初始位置等。

## demo
