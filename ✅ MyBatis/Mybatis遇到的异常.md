# Mybatis遇到的异常
## org.springframework.jdbc.BadSqlGrammarException 
BadSqlGrammarException 是 Spring Framework 中的一个异常类，它通常在 SQL 语法错误时抛出。具体来说，当 JdbcTemplate 或 NamedParameterJdbcTemplate 无法正确执行 SQL 查询、更新或删除等操作时，就会抛出 BadSqlGrammarException 异常。
通常情况下，SQL 语法错误可能发生在以下几种情况下：

- SQL 查询语句中列名写错了；
- SQL 操作符写错了；
- SQL 语句缺少必要的引号、括号、分号等；
- SQL 语句中的表名、列名大小写不一致；
- SQL 语句中使用了非法的关键字等等。

除此之外，BadSqlGrammarException 异常还可能发生在数据库连接配置错误、数据库对象不存在、表名或列名错误等情况下，因为这些错误也可能导致 SQL 语法错误而引发该异常。
如果发生 BadSqlGrammarException 异常，需要检查 SQL 语句的语法是否正确，以及数据库连接和配置是否正常。如果排除以上问题后仍然出现异常，建议检查数据库和应用程序版本是否兼容，或者将详细的异常信息记录下来，以便进一步诊断和修复问题。
### 表不存在时
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682647684188-705f4b27-3875-43ef-8275-2b71a00201c4.png#averageHue=%23222429&clientId=ued1f6493-8fbb-4&from=paste&height=812&id=u71505983&originHeight=812&originWidth=1373&originalType=binary&ratio=1&rotation=0&showTitle=false&size=284312&status=done&style=none&taskId=u5cfda84b-2675-419e-ac57-6b244b3a75f&title=&width=1373)
### 字段不存在时
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682647807211-ee5883a1-7a55-434b-ba84-9dcad5b8bab9.png#averageHue=%23222529&clientId=ued1f6493-8fbb-4&from=paste&height=815&id=u14cabb35&originHeight=815&originWidth=1387&originalType=binary&ratio=1&rotation=0&showTitle=false&size=285069&status=done&style=none&taskId=u60bd6bbe-879b-4774-8e62-029f471878e&title=&width=1387)
### sql语句语法不对时
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682647901827-60582a83-e153-4672-bfde-025fad4ed665.png#averageHue=%23212429&clientId=ued1f6493-8fbb-4&from=paste&height=798&id=u68f518e7&originHeight=798&originWidth=1370&originalType=binary&ratio=1&rotation=0&showTitle=false&size=268118&status=done&style=none&taskId=uf99dc41f-cca9-474b-a671-8565c8a3471&title=&width=1370)

## org.mybatis.spring.MyBatisSystemException
### 数据库连不上
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682649983916-f715d8e8-b9d1-461b-8e88-0536a5a0748a.png#averageHue=%23212328&clientId=ued1f6493-8fbb-4&from=paste&height=819&id=u77d5e0ae&originHeight=819&originWidth=1402&originalType=binary&ratio=1&rotation=0&showTitle=false&size=204505&status=done&style=none&taskId=u150c889f-5ee3-48b3-8140-bea5afd05bb&title=&width=1402)

## org.springframework.dao.DuplicateKeyException
### 主键重复
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682650136589-7d9f58d3-f97e-49ac-ac34-ec4e0544fa9f.png#averageHue=%2322252a&clientId=ued1f6493-8fbb-4&from=paste&height=715&id=u9722c38a&originHeight=715&originWidth=1385&originalType=binary&ratio=1&rotation=0&showTitle=false&size=253254&status=done&style=none&taskId=ue01fffe6-c1db-4d71-9f99-6e4fecca0c6&title=&width=1385)
### 唯一索引重复
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682650301076-ec1bb30f-8d36-4df3-96a5-ea814e23ce50.png#averageHue=%2322252a&clientId=ued1f6493-8fbb-4&from=paste&height=736&id=u613b5804&originHeight=736&originWidth=1379&originalType=binary&ratio=1&rotation=0&showTitle=false&size=258573&status=done&style=none&taskId=u736953ad-8491-4728-9b2a-0598b5acb02&title=&width=1379)

## org.apache.ibatis.exceptions.TooManyResultsException
> select 行查询 返回多个结果

