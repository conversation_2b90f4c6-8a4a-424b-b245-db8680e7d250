# 1. 什么是Mybatis

```basic
MyBatis 是一款优秀的持久层框架，它支持定制化 SQL、存储过程以及高级映射。MyBatis 避免了几乎所有的 JDBC 代码和手动设置参数以及获取结果集。MyBatis 可以使用简单的 XML 或注解来配置和映射原生信息，将接口和 Java 的 POJOs(Plain Ordinary Java Object,普通的 Java对象)映射成数据库中的记录
```

# 2. Mybatis工作原理

- 先封装SQL，接着调用JDBC操作数据库，最后把数据库返回的表结果封装成Java类。



```basic
JDBC四个核心对象:
（1）DriverManager，用于注册数据库连接
（2）Connection，与数据库连接对象
（3）Statement/PrepareStatement，操作数据库SQL语句的对象
（4）ResultSet，结果集或一张虚拟表
Mybatis四个核心对象:
（1）SqlSession对象，该对象中包含了执行SQL语句的所有方法。类似于JDBC里面的Connection。
（2）Executor接口，它将根据SqlSession传递的参数动态地生成需要执行的SQL语句，同时负责查询缓存的维护。类似于JDBC里面的Statement/PrepareStatement。
（3）MappedStatement对象，该对象是对映射SQL的封装，用于存储要映射的SQL语句的id、参数等信息。
（4）ResultHandler对象，用于对返回的结果进行处理，最终得到自己想要的数据格式或类型。可以自定义返回类型。
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411798036-a24dfb8c-2d60-4f46-ab89-1f1308e3dbc8.png#align=left&display=inline&height=859&originHeight=831&originWidth=733&size=0&status=done&style=none&width=758)
**
# 3. 接口->xml实现原理

```
接口dao -> xml -> sql, 如何实现
  1. 接口全限定名 - namespace
  2. 接口方法名 - id
  3. 接口返回值类型 - resultType/resultMap
  4. 参数类型 - parameterType
  
JDK动态代理
 - Mybatis运行时会使用JDK动态代理为Dao接口生成代理proxy对象，
 - 代理对象proxy会拦截接口方法，
 - 通过接口全限名+方法名拼接字符串作为key值找到MappedStatement对象
 - 转而执行MappedStatement所代表的sql，
 - 然后将sql执行结果返回
```


# 4. 优缺点

```
优点：
（1）基于SQL语句编程，相当灵活，不会对应用程序或者数据库的现有设计造成任何影响，SQL写在
XML里，解除sql与程序代码的耦合，便于统一管理；提供XML标签，支持编写动态SQL语句，并可重
用。
（2）与JDBC相比，减少了50%以上的代码量，消除了JDBC大量冗余的代码，不需要手动开关连接；
（3）很好的与各种数据库兼容（因为MyBatis使用JDBC来连接数据库，所以只要JDBC支持的数据库
MyBatis都支持）。
（4）能够与Spring很好的集成；
（5）提供映射标签，支持对象与数据库的ORM字段关系映射；提供对象关系映射标签，支持对象关系
组件维护。

缺点
（1）SQL语句的编写工作量较大，尤其当字段多、关联表多时，对开发人员编写SQL语句的功底有一定
要求。
（2）SQL语句依赖于数据库，导致数据库移植性差，不能随意更换数据库。
```


# 5. #{}和${}的区别是什么？

```bash
#{}
 - 参数占位符, 替代 ? 
 - 预编译, PreparedStatement
 - 可防止sql注入
 - 效率高
${}
 - 变量占位符, 拼接sql
 - 字符串替换
```

# 6. 接口参数传递

```
1. 一个参数 #{param}
2. 多个参数 , 根据参数位置 #{0}, #{1}, ...
3. 实体类
4. map
5. @param("自定义名称")
```


# 7. 动态sql标签

```sql
<resultMap>、<parameterMap>、<sql>、<include>、<selectKey>
trim|where|set|foreach|if|choose|when|otherwise|bind
原理:
 - 使用OGNL从sql参数对象中计算表达式的值，根据表达式的值动态拼接sql，以此来完成动态sql的功能。
```

# 8. 分页插件原理

```basic
1. Mybatis使用RowBounds对象进行分页，它是针对ResultSet结果集执行的内存分页
2. 使用Mybatis提供的插件接口，实现自定义插件，在插件的拦截方法内拦截待执行的sql，然后重写sql，根据dialect方言，添加对应的物理分页语句和物理分页参数。
 - select * from student，拦截sql后重写为：select t.* from （select * from student）t limit 0，10
```

# 9. Mybatis的缓存, 一级, 二级

```basic
一级缓存
 - sqlSession级别, map
 - key: MapperID+offset+limit+Sql+所有的入参 
 - value: 用户信息
 - 同一会话, 第二次查询就从缓存取, 如果删除, 修改, 添加操作, 在这一session会话, 全部清空缓存, 下一次先查数据库再同步到缓存
 
二级缓存
 - mapper级别(同一个命名空间), map
 - key: MapperID+offset+limit+Sql+所有的入参 
 - mybatis 的二级缓存是通过 CacheExecutor 实现的。 CacheExecutor其实是 Executor 的代理对象 
 - key： MapperID+offset+limit+Sql+所有的入参 
 - 所有的查询select操作，在 CacheExecutor 中都会先匹配缓存中是否存在，不存在则查询数据库 
 - 总配置里的开关cacheEnabled默认是true
<settings>
  <setting name="cacheEnabled" value="false"/>
</settings>
  
配置:
1. Mybatis 全局配置中启用二级缓存配置
2. 在对应的 Mapper.xml 中配置 cache 节点 <cache/>
3. 在对应的 select 查询节点中添加 useCache=true
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411798003-58cfde46-b019-4f79-bbaf-b7d02a5e7540.png#align=left&display=inline&height=727&originHeight=727&originWidth=996&size=0&status=done&style=none&width=996)
# 10. MyBatis有四大核心对象

```basic
（1）ParameterHandler：处理SQL的参数对象
（2）ResultSetHandler：处理SQL的返回结果集
（3）StatementHandler：数据库的处理对象，用于执行SQL语句
（4）Executor：MyBatis的执行器，用于执行增删改查操作
```


# 11. Executor执行器

```basic
SimpleExecutor、
 - 每执行一次update或select，就开启一个Statement对象，用完立刻关闭Statement对象。
  
ReuseExecutor、
 - 执行update或select，以sql作为key查找Statement对象，存在就使用，不存在就创建，用完后，不关闭Statement对象，而是放置于Map<String, Statement>内，供下一次使用。简言之，就是重复使用Statement对象
    
BatchExecutor
 - 执行update（没有select，JDBC批处理不支持select），将所有sql都添加到批处理中（addBatch()），等待统一执行（executeBatch()），它缓存了多个Statement对象，每个Statement对象都是addBatch()完毕后，等待逐一执行executeBatch()批处理。与JDBC批处理相同。
```

# 12. 插件运行原理

```
原理: Mybatis仅可以编写针对ParameterHandler、ResultSetHandler、StatementHandler、Executor这4种接口的插件，Mybatis使用JDK的动态代理，为需要拦截的接口生成代理对象以实现接口方法拦截功能，每当执行这4种接口对象的方法时，就会进入拦截方法，具体就是InvocationHandler的invoke()方法，当然，只会拦截那些你指定需要拦截的方法。
编写插件：实现Mybatis的Interceptor接口并复写intercept()方法，然后在给插件编写注解，指定要拦截哪一个接口的哪些方法即可，记住，别忘了在配置文件中配置你编写的插件。
mybatis的插件属于拦截器插件
```

# 13. Mybatis的配置xml文件 mybatis-config.xml
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611411797992-11eab6c5-7d35-4ef1-91e9-7c19d1b79f04.png#align=left&display=inline&height=446&originHeight=446&originWidth=401&size=0&status=done&style=none&width=401)
# 14. 模糊查询like写法

```
select * from users where name like #{value}
select * from users where name like contact("%", #{value}, "%")
```


