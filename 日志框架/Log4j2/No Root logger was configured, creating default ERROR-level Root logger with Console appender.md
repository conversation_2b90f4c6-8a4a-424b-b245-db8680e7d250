# No Root logger was configured, creating default ERROR-level Root logger with Console appender

> 2024-07-11T01:26:34.493676700Z main WARN No Root logger was configured, creating default ERROR-level Root logger with Console appender
> 2024-07-11T01:26:34.576694500Z main WARN The use of package scanning to locate plugins is deprecated and will be removed in a future release
> 2024-07-11T01:26:34.577974100Z main WARN The use of package scanning to locate plugins is deprecated and will be removed in a future release
> 2024-07-11T01:26:34.578983300Z main WARN The use of package scanning to locate plugins is deprecated and will be removed in a future release
> 2024-07-11T01:26:34.581164800Z main WARN The use of package scanning to locate plugins is deprecated and will be removed in a future release
> 2024-07-11T01:26:34.594328200Z main WARN No Root logger was configured, creating default ERROR-level Root logger with Console appender

# 问题产生

>Nacos不兼容Log4j 2.23.1
>
>https://github.com/alibaba/nacos/issues/12102

# 解决

> 配置了系统属性`nacos.logging.default.config.enabled=false`后，则不报异常警告了

```java
System.setProperty("nacos.logging.default.config.enabled", "false");
```

