# Minio部署

> 注：Minio官方建议生产环境最少4个节点，而且这N个节点，**至少有N/2个节点才能保证可读，至少有N/2+1个节点才能保证可写**。比如，一个8节点的Minio集群，每个节点一块盘，即便4个节点同时宕机，这个集群仍然是可读的，不过需要5个节点才能写数据。
>
> https://min.io/docs/minio/linux/operations/concepts/architecture.html

## 硬件

> https://min.io/docs/minio/linux/operations/checklists/hardware.html

<img src="images/image-20240714122834248.png" alt="image-20240714122834248" style="zoom:67%;" />

## 二进制部署

> https://wanghaiyang.cc/archives/tOX5AfHw

## docker-compose部署

> host
> minio-1
> minio-2

```yaml
# 所有容器通用的设置和配置
x-minio-common: &minio-common
  image: harbor.cqt.com:11000/docker/minio/minio:RELEASE.2024-07-04T14-25-45Z-cpuv1
  #image: harbor.cqt.com:11000/docker/minio/minio:RELEASE.2024-08-29T01-40-52Z
  restart: always
  command: server --console-address '0.0.0.0:9999' http://minio-{1...2}/data-{1...4}
  network_mode: host
  environment:
    - MINIO_ROOT_USER=admin
    - MINIO_ROOT_PASSWORD=cqt@2020
    - MINIO_PROMETHEUS_AUTH_TYPE=public
  healthcheck:
    test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
    interval: 30s
    timeout: 20s
    retries: 3

# 启动4个docker容器运行minio服务器实例
# 使用nginx反向代理9000端口，负载均衡, 你可以通过9001、9002、9003、9004端口访问它们的web console
services:
  minio-1:
    <<: *minio-common
    container_name: minio-1
    hostname: minio-1
    volumes:
      - ./data-1:/data-1
      - ./data-2:/data-2
      - ./data-3:/data-3
      - ./data-4:/data-4

  minio-2:
    <<: *minio-common
    container_name: minio-2
    hostname: minio-2
    volumes:
      - ./data-1:/data-1
      - ./data-2:/data-2
      - ./data-3:/data-3
      - ./data-4:/data-4

```



## Minio命令行工具

> https://wanghaiyang.cc/archives/JxsXjVvS

## Ngnix代理

> https://min.io/docs/minio/linux/integrations/setup-nginx-proxy-with-minio.html

```nginx
upstream minio_s3 {
   least_conn;
   server minio-01.internal-domain.com:9000;
   server minio-02.internal-domain.com:9000;
   server minio-03.internal-domain.com:9000;
   server minio-04.internal-domain.com:9000;
}

upstream minio_console {
   least_conn;
   server minio-01.internal-domain.com:9001;
   server minio-02.internal-domain.com:9001;
   server minio-03.internal-domain.com:9001;
   server minio-04.internal-domain.com:9001;
}

server {
   listen       80;
   listen  [::]:80;
   server_name  minio.example.net;

   # Allow special characters in headers
   ignore_invalid_headers off;
   # Allow any size file to be uploaded.
   # Set to a value such as 1000m; to restrict file size to a specific value
   client_max_body_size 0;
   # Disable buffering
   proxy_buffering off;
   proxy_request_buffering off;

   location / {
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_connect_timeout 300;
      # Default is HTTP/1, keepalive is only enabled in HTTP/1.1
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;

      proxy_pass http://minio_s3; # This uses the upstream directive definition to load balance
   }
}

server {

   listen       80;
   listen  [::]:80;
   server_name  console.example.net;

   # Allow special characters in headers
   ignore_invalid_headers off;
   # Allow any size file to be uploaded.
   # Set to a value such as 1000m; to restrict file size to a specific value
   client_max_body_size 0;
   # Disable buffering
   proxy_buffering off;
   proxy_request_buffering off;

   location / {
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-NginX-Proxy true;

      # This is necessary to pass the correct IP to be hashed
      real_ip_header X-Real-IP;

      proxy_connect_timeout 300;

      # To support websocket
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";

      chunked_transfer_encoding off;

      proxy_pass http://minio_console/; # This uses the upstream directive definition to load balance
   }
}
```

# ak权限

```json
{
	"Version": "2012-10-17",
	"Statement": [

		{
			"Effect": "Allow",
			"Action": [
				"s3:GetObject",
				"s3:ListAllMyBuckets",
				"s3:ListBucket",
				"s3:PutObject",
				"s3:DeleteObject",
				"s3:GetBucketLocation"
			],
			"Resource": [
				"arn:aws:s3:::*"
			]
		}
	]
}
```





## 参考

> https://meethigher.top/blog/2022/minio/