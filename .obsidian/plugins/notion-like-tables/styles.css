/* src/react/shared/button/styles.css */
.NLT__button {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  white-space: nowrap;
  color: var(--text-normal);
  margin-right: 0;
  cursor: pointer;
  width: unset;
}
.NLT__button--link {
  color: var(--link-color);
  text-decoration-line: var(--link-decoration);
  cursor: var(--cursor-link);
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}
.NLT__button--link:hover {
  box-shadow: var(--input-shadow) !important;
}
.NLT__button--icon {
  background-color: transparent !important;
  box-shadow: none !important;
}
.NLT__button--icon:hover {
  box-shadow: var(--input-shadow) !important;
}
.NLT__button--simple {
  padding: 0;
  height: 1px;
}
.NLT__button--simple:hover {
  box-shadow: none !important;
}

/* src/react/table-app/row-options/styles.css */
.NLT__row-options {
  padding-left: var(--nlt-spacing--md);
}

/* src/react/shared/menu/styles.css */
.NLT__menu {
  width: 0;
  height: 0;
}
.NLT__menu-container {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  box-shadow: 0 2px 8px var(--background-modifier-box-shadow);
  z-index: var(--layer-menu);
  position: absolute;
  border-radius: 4px;
  font-weight: 400;
}

/* src/react/shared/text/styles.css */
.NLT__p {
  white-space: nowrap;
  margin: 0;
}
.NLT__text-muted {
  color: var(--text-muted);
}
.NLT__text-faint {
  color: var(--text-faint);
}
.NLT__text-semibold {
  font-weight: var(--font-semibold);
}

/* src/react/shared/menu-item/styles.css */
.NLT__menu-item {
  display: flex;
  align-items: center;
  padding: var(--nlt-spacing--sm) var(--nlt-spacing--lg);
  width: 100%;
}

/* src/react/shared/switch/styles.css */
.NLT__switch {
  width: calc(var(--toggle-width) * 0.75);
  height: calc((var(--toggle-thumb-height) * 0.75) + (var(--toggle-border-width) * 2 * 0.75));
}
.NLT__switch:after {
  width: calc(var(--toggle-thumb-width) * 0.75);
  height: calc(var(--toggle-thumb-height) * 0.75);
}
.NLT__switch.is-enabled:after {
  transform: translate3d(calc((var(--toggle-width) - var(--toggle-thumb-width) - var(--toggle-border-width)) * 0.75), 0, 0);
}
.NLT__switch input {
  width: calc(var(--checkbox-size) * 0.75);
  height: calc(var(--checkbox-size) * 0.75);
}
.NLT__switch:active:after {
  width: calc((var(--toggle-thumb-width) * 0.75) + (var(--toggle-border-width)));
}

/* src/react/table-app/option-bar/filter/styles.css */
.react-select {
  max-width: 250px !important;
}
.react-select input:focus {
  border: 0 !important;
  box-shadow: none !important;
}
.react-select input:focus-visible {
  border: 0 !important;
  box-shadow: none !important;
}

/* src/react/table-app/option-bar/styles.css */
.NLT__option-bar {
  width: 100%;
  padding: var(--nlt-spacing--md) var(--nlt-spacing--lg);
  border-top: 1px solid var(--background-modifier-border);
  border-bottom: 1px solid var(--background-modifier-border);
}

/* src/react/table-app/function-cell/styles.css */
.NLT__function-cell {
  padding: var(--nlt-cell-spacing-x) var(--nlt-cell-spacing-y);
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  overflow: hidden;
}

/* src/react/table-app/text-cell/styles.css */
.NLT__text-cell {
  width: 100%;
}
.NLT__text-cell p {
  margin: 0;
}
.NLT__text-cell ul {
  padding: 0 var(--nlt-spacing--lg);
  margin: 0;
}

/* src/react/shared/tag/styles.css */
.NLT__tag {
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding: var(--nlt-spacing--xs) var(--nlt-spacing--md);
  width: max-content;
  color: var(--text-normal);
}

/* src/react/table-app/tag-cell/styles.css */
.NLT__tag-cell {
  width: 100%;
}

/* src/react/table-app/checkbox-cell/styles.css */
.NLT__checkbox-cell {
  width: 100%;
  padding: var(--nlt-cell-spacing);
}

/* src/react/table-app/date-cell/styles.css */
.NLT__date-cell {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: var(--nlt-cell-spacing);
}

/* src/react/table-app/number-cell/styles.css */
.NLT__number-cell {
  width: 100%;
  text-align: right;
}

/* src/react/table-app/number-cell-edit/styles.css */
.NLT__number-cell-edit input {
  text-align: right;
  width: 100%;
  height: 100%;
  border: 0;
  font-size: var(--font-ui-medium) !important;
}

/* src/react/table-app/text-cell-edit/styles.css */
.NLT__text-cell-edit {
  width: 100%;
  height: 100%;
}
.NLT__text-cell-edit textarea {
  width: 100%;
  height: 100%;
  border: 0 !important;
  overflow: hidden;
  padding: var(--nlt-cell-spacing-x) var(--nlt-cell-spacing-y);
  resize: none;
  font-size: var(--font-ui-medium) !important;
}

/* src/react/table-app/tag-color-menu/components/color-item/styles.css */
.NLT__color-item {
  display: flex;
  align-items: center;
  padding: var(--nlt-spacing--xs) var(--nlt-spacing--lg);
  width: 100%;
}
.NLT__color-item-square {
  width: 10px;
  height: 10px;
  padding: 0;
  margin: 0 10px 0 0;
}

/* src/react/table-app/tag-color-menu/styles.css */

/* src/react/table-app/date-cell-edit/styles.css */
.NLT__date-cell-edit input {
  width: 100%;
  height: 100%;
  border: 1px solid var(--background-modifier-border);
  padding: 5px;
  background-color: var(--background-secondary);
}
.NLT__date-cell-edit input[aria-invalid=true] {
  outline: 2px solid var(--background-modifier-error);
  outline-offset: -2px;
}

/* src/react/table-app/multi-tag-cell/styles.css */
.NLT__multi-tag-cell {
  display: flex;
  flex-direction: column;
}

/* src/react/table-app/body-cell/styles.css */
.NLT__td-container {
  display: flex;
  width: 100%;
  min-height: var(--nlt-cell-min-height);
  height: 100%;
  padding: var(--nlt-cell-spacing-x) var(--nlt-cell-spacing-y);
  cursor: pointer;
}

/* src/react/table-app/currency-cell/styles.css */
.NLT__currency-cell {
  width: 100%;
  text-align: right;
}

/* src/react/table-app/currency-cell-edit/styles.css */
.NLT__currency-cell-edit input {
  text-align: right;
  width: 100%;
  height: 100%;
  border: 0;
  font-size: var(--font-ui-medium) !important;
}

/* src/react/table-app/new-row-button/styles.css */
.NLT__new-row {
  padding: var(--nlt-spacing--md);
}

/* src/react/table-app/new-column-button/styles.css */
.NLT__new-column {
  padding-left: var(--nlt-spacing--md);
}

/* src/react/table-app/header-cell/components/HeaderMenu/styles.css */
.NLT__header-menu {
  color: var(--text-normal);
}
.NLT__header-menu input {
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  padding: 4px 10px;
  font-size: 0.95rem;
  width: 100%;
}

/* src/react/table-app/header-cell/styles.css */
.NLT__th-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: var(--nlt-cell-min-height);
}
.NLT__th-content {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  user-select: none;
  padding: var(--nlt-cell-spacing-x) var(--nlt-cell-spacing-y);
}
.NLT__th-resize-container {
  position: relative;
}
.NLT__th-resize {
  position: absolute;
  left: -5px;
  cursor: col-resize;
  width: 5px;
  height: 100%;
}
.NLT__th-resize:hover {
  background-color: var(--interactive-accent);
}
.NLT__th-resize--active {
  background-color: var(--interactive-accent);
}

/* src/react/table-app/styles.css */
:root {
  --nlt-spacing--xs: 2px;
  --nlt-spacing--sm: 4px;
  --nlt-spacing--md: 8px;
  --nlt-spacing--lg: 12px;
  --nlt-spacing--xl: 16px;
  --nlt-spacing--2xl: 24px;
  --nlt-cell-spacing-x: var(--nlt-spacing--sm);
  --nlt-cell-spacing-y: var(--nlt-spacing--lg);
  --nlt-cell-min-height: 1.9rem;
  --nlt-font-size--xs: 0.8rem;
  --nlt-font-size--sm: 0.9rem;
  --nlt-font-size--md: 1rem;
  --nlt-font-size--lg: 1.1rem;
}
.NLT__light-gray--light {
  background-color: hsl(0, 3%, 94%);
}
.NLT__light-gray--dark {
  background-color: hsl(0, 0%, 22%);
}
.NLT__gray--light {
  background-color: hsl(40, 5%, 88%);
}
.NLT__gray--dark {
  background-color: hsl(0, 0%, 35%);
}
.NLT__brown--light {
  background-color: hsl(18, 31%, 89%);
}
.NLT__brown--dark {
  background-color: hsl(19, 32%, 27%);
}
.NLT__orange--light {
  background-color: hsl(28, 67%, 88%);
}
.NLT__orange--dark {
  background-color: hsl(28, 52%, 32%);
}
.NLT__yellow--light {
  background-color: hsl(43, 82%, 89%);
}
.NLT__yellow--dark {
  background-color: hsl(37, 43%, 36%);
}
.NLT__green--light {
  background-color: hsl(113, 30%, 89%);
}
.NLT__green--dark {
  background-color: hsl(138, 23%, 28%);
}
.NLT__blue--light {
  background-color: hsl(205, 41%, 89%);
}
.NLT__blue--dark {
  background-color: hsl(218, 38%, 30%);
}
.NLT__purple--light {
  background-color: hsl(272, 29%, 90%);
}
.NLT__purple--dark {
  background-color: hsl(266, 34%, 28%);
}
.NLT__pink--light {
  background-color: hsl(330, 36%, 91%);
}
.NLT__pink--dark {
  background-color: hsl(330, 31%, 30%);
}
.NLT__red--light {
  background-color: hsl(11, 64%, 91%);
}
.NLT__red--dark {
  background-color: hsl(8, 35%, 30%);
}
.NLT__selectable {
  cursor: pointer;
}
.NLT__selectable:hover {
  background-color: var(--color-base-30);
}
.NLT__selected {
  background-color: var(--color-base-20);
}
.NLT__wrap-overflow {
  overflow-wrap: break-word;
}
.NLT__hide-overflow {
  overflow: hidden;
  overflow-wrap: normal;
  white-space: nowrap;
}
.NLT__hide-overflow-ellipsis {
  overflow: hidden;
  overflow-wrap: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.workspace-leaf-content[data-type=notion-like-tables] .view-content {
  padding: 0;
}
.NLT__app {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.NLT__default-cursor {
  cursor: default !important;
}
.NLT__focusable:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: -2px;
}
.NLT__focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: -2px;
}
.NLT__button:not(.NLT__button--icon):focus-visible {
  outline-offset: 0;
}

/* src/react/import-app/styles.css */
.NLT__import-app {
  margin-top: 20px;
}
.NLT__import-app .error-text {
  font-size: 12px;
  color: var(--text-error);
  visibility: hidden;
}
.NLT__import-app .error-text--visible {
  visibility: visible;
}
.NLT__import-app label {
  font-size: 0.8rem;
  font-weight: 600;
}
