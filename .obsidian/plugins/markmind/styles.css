/* @font-face{
  font-family: 'myFont'; 
  src:url('http://cdn.ghost-jack.top/chinese.ttf');
} */
.mm-handdraw-theme {
  font-family: 'myFont';
}

.mm-app-container {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  font-family: Helvetica, Tahoma, Arial, "PingFang SC", STXihei, "Microsoft yahei", "WenQuanYi Micro Hei", sans-serif;
}

.mm-mindmap-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.mm-pdf-container {
  flex: auto;
  height: 100%;
  overflow: hidden;
  position: relative;
  left: 0;
  right: 0;
  top: 0px;
  padding-top: 36px;
  user-select: text !important;
  -webkit-user-select: text !important;
  font-size: 10px;
  display: none;
}

.mm-mobile-app .mm-pdf-container {
  padding-top: 50px;
}

.mm-pdf-container .pdf-container {
  padding: 0;
}

.mm-pdf-container .pdf-container .pdf {
  height: 100%;
  width: 100%;
}

.pdf * {
  box-sizing: content-box;
}

.mm-mindmap {
  color: #666;
  font-size: 16px;
  width: 8000px;
  height: 8000px;
  transition: all 0.15s linear;

}

.theme-dark .mm-node {
  color: #f5f5f5;
}

.theme-dark .mm-node.mm-root {
  color: #333;
}

.theme-light .mm-node.mm-root>.mm-node-content {
  color: #fff;
  background-color: rgb(0, 170, 255);
}

.mm-node {
  position: absolute;
  cursor: pointer;
  box-sizing: border-box;
}

.mm-node .mm-node-content {
  padding: 2px 4px;
  max-width: 600px;
  word-break: break-word;
  white-space: normal;
  display: flex;
  align-items: center;
  min-height: 24px;
  min-width: 10px;
  border-radius: 4px;
}

.mm-node .mm-node-content>p,
.mm-node .mm-node-content>h1,
.mm-node .mm-node-content>h2,
.mm-node .mm-node-content>h3,
.mm-node .mm-node-content>h4,
.mm-node .mm-node-content>h5,
.mm-node .mm-node-content>h6,
.mm-node .mm-node-content>ol,
.mm-node .mm-node-content>ul {
  padding: 0;
  margin: 0;
}

.mm-node-edit>p,
.mm-node-edit>h1,
.mm-node-edit>h2,
.mm-node-edit>h3,
.mm-node-edit>h4,
.mm-node-edit>h5,
.mm-node-edit>h6,
.mm-node-edit>ol,
.mm-node-edit>ul {
  padding: 0;
  margin: 0;
}

.mm-node-edit>ol,
.mm-node-edit>ul {
  padding-left: 20px;
}


.mm-node .mm-node-content table th {
  font-weight: 600;
}

.mm-node .mm-node-content table th,
.mm-node .mm-node-content table td {
  padding: 6px 13px;
  border: 1px solid #d0d7de;
}

.mm-node .mm-node-content table tr {
  background-color: #ffffff;
  border-top: 1px solid hsla(210, 18%, 87%, 1);
}

.mm-node .mm-node-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.mm-node .mm-node-content table img {
  background-color: transparent;
}

/* .mm-node .mm-node-content img[align=right] {
  padding-left: 20px;
}

.mm-node .mm-node-content img[align=left] {
  padding-right: 20px;
} */


.mm-node-edit>.callout {
  margin: 0;
}

.mm-node.mm-root>.mm-node-content {
  font-size: 1.6em;
  padding: 14px 20px;
  border-radius: 0.25rem;
  background: white;
}

.mm-node.mm-node-second>.mm-node-content {
  padding: 8px 10px;
  font-size: 1.2em;
}

.mm-node.mm-node-select {
  border: 2px solid var(--interactive-accent);
  border-radius: 0.25rem;
}

.mm-node-bar {
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  padding: 0 3px;
  bottom: -8px;
  box-sizing: border-box;
}

.mm-node-bar:hover {
  transform: scale(1.2);
}



.mm-node-right>.mm-node-bar,
.mm-node.mm-root>.mm-node-bar {
  right: -10px;
}

.mm-node-right.mm-mindmap2-node>.mm-node-bar {
  right: -20px;
  top: 50%;
  margin-top: -5px;
}

.mm-node-right.mm-mindmap1-node>.mm-node-bar {
  right: -20px;
  top: 50%;
  margin-top: -5px;
}


.mm-root>.mm-node-bar,
.mm-node-second>.mm-node-bar {
  top: 50%;
  margin-top: -5px;
  bottom: inherit;
}

.mm-node-down.mm-multipleTree-node>.mm-node-bar {
  left: 50% !important;
  margin-left: -5px !important;
  top: 100% !important;
  margin-top: 4px !important;
}

.mm-node-down>.mm-node-bar {
  left: 50%;
  margin-left: -5px;
  top: 100%;
  margin-top: 4px;
}

.mm-node-up>.mm-node-bar {
  left: 50%;
  margin-left: -5px;
  top: -15px;
}

.mm-node-left>.mm-node-bar {
  left: -10px;
}

.mm-node-left.mm-mindmap2-node>.mm-node-bar {
  left: -20px;
  top: 50%;
  margin-top: -5px;
}

.mm-node-left.mm-mindmap1-node>.mm-node-bar {
  left: -20px;
  top: 50%;
  margin-top: -5px;
}

.mm-node-right.mm-fish-node>.mm-node-bar {
  bottom: -6px !important;
  right: -12px !important;
  top: auto;
}

.mm-node-left.mm-fish-node>.mm-node-bar {
  bottom: -6px !important;
  left: -12px !important;
  top: auto;
}

.mm-node-collapse>.mm-node-bar {
  display: block !important;
  box-sizing: border-box;
  border: 0px;
  background-color: #fff !important;
  border: 2px solid #ccc;
}

.mm-root>.mm-node-bar,
.mm-node-leaf>.mm-node-bar,
.mm-node-induce>.mm-node-bar {
  display: none !important;
}

.mm-node.mm-node-induce .mm-node-content {
  background-color: var(--background-primary);
}

.mm-vertical-node {
  background-color: var(--background-primary);
}



/* node indicate */
.mm-node-layout-indicate {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border: 20px solid transparent;
  border-bottom: 40px solid var(--interactive-accent);
  transform-origin: center center;
  z-index: 100;
}

.mm-node-layout-indicate.mm-arrow-left {
  transform: rotate(-90deg)
}

.mm-node-layout-indicate.mm-arrow-down {
  transform: rotate(180deg)
}

.mm-node-layout-indicate.mm-arrow-right {
  transform: rotate(-270deg)
}


/* edit node style */
.mm-node.mm-edit-node {
  z-index: 50;
}

.mm-node.mm-edit-node .mm-node-content {
  background-color: #333 !important;
  color: white !important;
}

.theme-light .mm-node.mm-edit-node .mm-node-content {
  background-color: white !important;
  color: #333 !important;
}

/* menu */
.mm-node-menu {
  position: absolute;
  background: #333;
  border: 1px solid #000;
  width: 212px;
  border-radius: 0.25rem;
  z-index: 6000;
  padding-top: 5px;
  height: 28px;
  box-sizing: border-box;
}

.theme-light .mm-node-menu {
  background: white;
  border: 1px solid #666;
}

.mm-node-menu span {
  vertical-align: middle;
  cursor: pointer;
  margin: 0 5px
}

.mm-node-menu span svg {
  fill: #ccc;
}

.mm-node-menu span svg:hover {
  opacity: 0.8;
}

.theme-light .mm-node-menu svg {
  fill: #333;
}


.mm-drag {
  position: absolute;
  z-index: 5000;
  left: 2000px;
  top: 2000px;
  color: #f5f5f5;
  min-height: 20px;
  min-width: 60px;
  border: 1px solid rgb(0, 170, 255);
  background: none;
  pointer-events: none;
}

.drag-top {
  background-color: rgb(0, 170, 255);
  height: 10px;
  width: 100%;
  top: -10px;
  left: 0;
  cursor: n-resize;
  position: absolute;
  pointer-events: all;
}

.drag-top .d3 {
  margin: 0 auto;
  margin-top: -20px;
  width: 0;
  height: 0;
  border-width: 10px;
  border-style: solid;
  border-color: transparent rgb(0, 170, 255) transparent transparent;
  transform: rotate(90deg);
  /*顺时针旋转90°*/
}

.drag-bottom {
  background-color: rgb(0, 170, 255);
  height: 10px;
  width: 100%;
  bottom: -10px;
  left: 0;
  cursor: s-resize;
  position: absolute;
  pointer-events: all;
}

.drag-bottom .d4 {
  margin: 0 auto;
  width: 0;
  height: 0;
  top: 0;
  border-width: 10px;
  border-style: solid;
  border-color: rgb(0, 170, 255) transparent transparent;
  margin-top: 10px;
}

.mm-drag.model-right .drag-top {
  background-color: rgb(0, 170, 255);
  height: 100%;
  width: 10px;
  top: 0;
  left: -10px;
  cursor: w-resize;
  position: absolute;
  pointer-events: all;
}

.mm-drag.model-right .drag-top .d3 {
  margin: 0 auto;
  margin-left: -20px;
  width: 0;
  height: 0;
  border-width: 10px;
  border-style: solid;
  border-color: transparent rgb(0, 170, 255) transparent transparent;
  transform: rotate(0);
  position: absolute;
  top: 50%;
  margin-top: -10px;
}

.mm-drag.model-right .drag-bottom {
  background-color: rgb(0, 170, 255);
  height: 100%;
  width: 10px;
  top: 0;
  right: -10px;
  cursor: e-resize;
  position: absolute;
  pointer-events: all;
  left: 100%;
}

.mm-drag.model-right .drag-bottom .d4 {
  margin: 0 auto;
  width: 0;
  height: 0;
  top: 0;
  border-width: 10px;
  border-style: solid;
  border-color: rgb(0, 170, 255) transparent transparent;
  margin-top: 10px;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  margin-left: 10px;
  transform: rotate(-90deg);
}



.mm-node-assist .mm-node-annotate {
  /* float: left; */
  width: 20px;
  line-height: 16px;
  padding-top: 4px;
}

/* .mm-edit-node .mm-node-assist .mm-node-annotate{
  float: none;
  display: none;
} */


/* relate link text node */
.node-relate .mm-node-bar {
  display: none;
}

.mm-link-board {
  position: absolute;
  background-color: rgb(68 68 68);
  border: 1px solid #000;
  color: white;
  min-width: 300px;
  min-height: 200px;
  max-width: 500px;
  padding: 10px 0;
  border-radius: 3px;
  z-index: 1000;

}

.mm-link-board ul {
  margin: 0;
  padding: 0;
}

.mm-link-board ul li {
  line-height: 24px;
  padding: 0 6px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mm-link-board ul li:hover,
.mm-file-name.active {
  color: #ccc;
  background-color: #000;
}

.mm-link-board .mm-file-ext {
  font-size: 0.6em;
  padding-right: 6px;
}

.mm-block-ext {
  font-size: 0.6em;
  padding-right: 6px;
}

.mm-block {
  position: absolute;
  left: 98%;
  top: 0px;
  width: 300px;
  background-color: rgb(68 68 68);
  border: 1px solid #000;
  color: white;
  min-height: 200px;
  display: none;
  border-radius: 3px;
  padding: 6px 0;
}

.mm-assist-board {
  position: absolute;
  background: #333;
  border: 1px solid #000;
  width: 230px;
  border-radius: 0.25rem;
  z-index: 60;
  color: #ccc;
  padding: 4px;
  box-sizing: border-box;
}

.mm-assist-color {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  cursor: pointer;
}

.mm-assist-board svg {
  fill: #666;
}

.mm-assist-lineType span {
  margin-right: 6px;
  cursor: pointer;
}

.theme-light .mm-assist-board {
  background: white;
  border: 1px solid #666;
}

.mm-color-red {
  background-color: red
}

.mm-color-orange {
  background-color: orange
}

.mm-color-yellow {
  background-color: yellow
}

.mm-color-green {
  background-color: green
}

.mm-color-blue {
  background-color: blue
}

.mm-color-indigo {
  background-color: indigo
}

.mm-color-purple {
  background-color: purple
}

.mm-assist-delete {
  padding: 4px;
}

.mm-color-black {
  background-color: #9e9d9d;
}

.mm-color-white {
  background-color: #fff;
  border: 1px solid #e0dfdf;
}

/* pdf js */
#closePDF::before {
  background-color: transparent !important;
  cursor: pointer;
}

#closePDF:hover {
  background-color: #f5f5f5;
}

.treeItem.selected>a {
  color: #999 !important;
}

.treeItem>a {
  color: #888 !important;
}

.treeItemToggler::before {
  background-color: #666 !important;
}

.thumbnail.selected>.thumbnailSelectionRing {
  background-color: #666 !important;
}

.annoate-btn.rect {
  background-color: transparent !important;
}

.annoate-btn.rect svg {
  fill: #333;
}

.annoate-btn.delete {
  background-color: transparent !important;
}

.annoate-btn.delete svg {
  fill: #333;
}




.cicada-list {
  background: #f2f2f2;
  top: 24px;
  overflow-y: auto;
  text-align: left;
  /* position: fixed; */
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  height: 100%;
}

.mm-list ul {
  margin-left: 10px;
  padding: 0;
  border-left: 1px solid #4a4949;
  line-height: 30px;
}

.mm-list li {
  list-style: none;
  padding-left: 16px;
}

/* .cicada-list ul>li:first-child{
  border-left:1px dotted #ccc;
} */
.mm-list .text {
  /* display: inline-block;  */

  outline: transparent dotted thick;
  /* margin-left:30px; */
}

/* .cicada-list li.select{
    border:1px solid #ccc;
} */
.mm-list {
  border-left: 0;
  max-width: 900px;
  margin: 60px auto;
  min-width: 400px;
  font-size: 16px;
  min-height: 600px;
  padding: 10px;
  box-sizing: border-box;
}

.mm-router {
  max-width: 900px;
  margin: 40px auto 0 auto;
  padding: 10px;
  box-sizing: border-box;
  color: #a0a0a0;
}

.mm-router span {
  font-size: 14px;
  cursor: pointer;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

.mm-router span:hover {
  color: #f5f5f5;
}

.theme-light .mm-router span:hover {
  color: #333;
}

.mm-mindmap-container>.mm-link-board {
  position: fixed;
}

/* .theme-dark .mm-list{
  background: #282828;
  box-shadow: 0 0 4px #333;
} */

/* .rich-edit {
  position: fixed;
  right: 20px;
  top: 20px;
  background: #fff;
  padding: 6px;
  border-radius: 3px;
  border: 1px solid #f5f5f5;
  font-size: 14px;
  z-index: 4000;
  cursor: pointer;
} */
/* .btn-group{
    font-weight: bold;
} */
/* .btn-group a {
  text-decoration: none;
  color: #666;
  display: inline-block;
  min-width: 24px;
  line-height: 24px;
  text-align: center;
  margin: 2px;
  height: 24px;
  border-radius: 50%;
  vertical-align: middle;
}
.btn-group > a:first-child {
  color: #ccc;
} */

.li-node {
  position: relative;
  box-sizing: border-box;
}

.li-node ul {
  top: 0;
  visibility: visible;
}

.li-node ul.hide {
  position: absolute;
  top: -500px;
  visibility: hidden;
  transition: top, visibility 0.5s, 0.5s ease, ease;
}

.li-node .node-open {
  font-size: 12px;
  vertical-align: top;
  padding-right: 6px;
  cursor: pointer;
  position: absolute;
  left: -38px;
  display: none;
  color: #666;
}

.li-node .node-open svg {
  fill: #7b7b7b;
}

.li-node .icon-dott {
  display: inline-block;
  width: 6px;
  background-color: #202020;
  position: absolute;
  left: -20px;
  top: 0px;
  height: 30px;
  cursor: move;
}

.li-node .icon-dott:hover span {
  background-color: #666;
}

.li-node .icon-dott span {
  border-radius: 50%;
  width: 6px;
  height: 6px;
  background-color: #7a7a7a;
  position: absolute;
  left: 1px;
  top: 12px;
  line-height: 30px;
}

.li-node.node-expand>.node-control>.icon-dott span:before {
  display: "block";
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 0;
  left: -3px;
  top: -3px;
}

.li-node>.node-control>.icon-dott span:before {
  display: "block";
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid #444343;
  left: -3px;
  top: -3px;
}

/* .li-node .node-open.icon-iconjia{
    display: block;
} */

.li-node.node-no-border {
  padding-left: 0;
}

.li-node.node-no-border>ul {
  margin-left: 0px;
  padding: 0;
  border-left: 0px dashed #eee;
}

.node-control:hover>.node-open {
  display: block;
}

.node-leaf .node-control:hover>.node-open {
  display: none;
}

.node-leaf .node-open {
  display: none !important;
}

.node-add-top>.node-control:before {
  display: block;
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  background-color: blue;
  width: 100%;
  box-sizing: border-box;
  z-index: 10;
  margin: 2px 0;
}

.node-add-bottom>.node-control:after {
  display: block;
  content: "";
  position: absolute;
  bottom: 0;
  height: 2px;

  background-color: blue;
  width: 100%;
  box-sizing: border-box;
  z-index: 10;
  margin: 2px 0;
}

.node-control {
  position: relative;
}

.route {
  width: 1060px;
  margin: 10px auto;
  /* border-bottom: 1px solid #f6f6f6; */
  cursor: pointer;
  color: #666;
  font-size: 12px;
  margin: 0 auto;
  margin-top: 30px;
}

.route ul {
  border: 0 !important;
  margin: 0;
  line-height: 24px;
}

.route li {
  display: inline-block;
  line-height: 28px;
  padding-left: 0;
}

.route li span {
  display: inline-block;
  vertical-align: middle;
}

.route li span.text {
  max-width: 200px;
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.route li:first-child {
  margin-left: 0;
}

.route li:hover {
  color: #333;
}

.theme-dark .route li {
  color: rgba(255, 255, 255, 0.4);
}

.theme-dark .route li:hover {
  color: rgba(255, 255, 255, 0.6);
}

/* .route-item span{
    margin:0 6px;
} */

.li-node .node-link {
  color: #666;
  font-size: 14px;
}

.li-node .node-remark {
  background: #f5f5f5;
  outline: transparent solid 2px;
  padding: 2px 6px;
  color: inherit;
  font-size: 12px;
  line-height: 20px;
  border-radius: 3px
}

.theme-dark .li-node .node-remark {
  background: #2d2d2d;
}

.theme-sepia .li-node .node-remark {
  background: rgba(255, 246, 237, 0.6);
}

.li-node .node-image {
  padding: 6px;
  background: transparent;
  position: relative;
  display: inline-block;
  border: 1px solid transparent;
}

.li-node .node-image:hover {
  border: 1px solid #ccc;
}

.li-node .node-image:hover .node-resize,
.li-node .node-image:hover .node-delete {
  display: block;
}

.li-node .node-resize {
  position: absolute;
  width: auto;
  height: 20px;
  z-index: 200;
  right: 0;
  bottom: 4px;
  cursor: nw-resize;
  display: none;
}

.li-node .node-delete {
  position: absolute;
  right: 2px;
  top: 0;
  cursor: pointer;
  display: none;
}

.li-node .node-assist {
  height: 30px;
  box-sizing: border-box;
  padding-top: 8px;
}

.linkSetup {
  position: absolute;
  z-index: 120;
  padding: 6px 10px;
  /* border:1px solid #ccc; */
  background: #ffeac3;
  border-radius: 2px;
}

.linkSetup::before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border: 8px solid;
  border-color: transparent transparent #ffeac3;
  top: -16px;
  position: absolute;
  left: 8px;
}

.linkSetup span {
  display: inline-block;
  width: 50px;
  height: 30px;
  line-height: 30px;
  background: #666;
  color: #fff;
  margin: 6px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

.linkSetup span:hover {
  opacity: 0.8;
}

.mm-list li.node-showNode {
  padding-left: 0;
}

.mm-list li.node-showNode>.node-control {
  margin-bottom: 40px;
}

.mm-list li.node-showNode>.node-control .text {
  border-bottom: 2px solid #989898;
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 3px;
}

/* .cicada-list li ul,
.cicada-list li .node-control{
    transition: all 0.6s ease;
} */

.mm-list li.node-showNode>.node-control .node-open,
.mm-list li.node-showNode>.node-control .icon-dott {
  display: none;
}

.text {
  min-height: 30px;
}

.text ul {
  border-left: 0px solid white !important;
  list-style: outside;
}

.text ul li {
  list-style-type: circle;
  padding-left: 0;
}

.text ol li {
  list-style-type: disc;
}

.text>p,
.text>ul,
.text>ol,
.text>h1,
.text>h2,
.text>h3,
.text>h4,
.text>h5,
.text>h6 {
  margin: 0;
  padding: 0;
}

.theme-light .mm-list ul {
  border-left: 1px solid #e0e0e0
}

.theme-light .li-node .icon-dott {
  background-color: white;
}

.theme-light .li-node.node-no-border>ul {
  border-left: 0px solid white;
}

/* mind map theme */

.mm-theme-dark.mm-mindmap {
  background-color: rgb(35, 39, 62);
}

.mm-theme-dark .mm-node .mm-node-content {
  /* background-color: #282828;  */
  background-color: transparent;
  color: rgb(115, 154, 163)
}

.mm-theme-dark .mm-node.mm-root .mm-node-content {
  background-color: rgb(5, 196, 235);
  color: rgba(255, 255, 255, .8)
}


.mm-theme-light.mm-mindmap {
  background-color: #f1f1f1;
}

.mm-theme-light .mm-node .mm-node-content {
  /* background-color: #282828;  */
  background-color: transparent;
  color: #383833
}

.mm-theme-light .mm-node.mm-root .mm-node-content {
  background-color: #c31105;
  color: #fff
}

.mm-theme-light .mm-node :focus {
  color: #666;
}

.mm-theme-card.mm-mindmap {
  background-color: #fff;
}

.mm-theme-card .mm-node .mm-node-content {
  /* background-color: #282828;  */
  background-color: #fff;
  color: #333;
  box-shadow: 0 0 6px #f1f1f1;
  padding: 4px 10px;
  border-radius: 5px;
  border: 1px solid #f5f5f5;
  font-size: 14px;

}

.mm-theme-card .mm-node.mm-root .mm-node-content {
  background-color: #fff;
  color: #333;
  font-size: 18px;
  padding: 10px 18px;
}

.mm-theme-card .mm-node :focus {
  color: #b1b0b0;
}

.mm-theme-card .node-relate .mm-node-content {
  background-color: transparent;
  color: red;
  border: 0;
  box-shadow: 0 0 0 #fff;
}

.mm-mobile-app .mm-node .mm-node-content {
  max-width: 400px;
}

.mm-note {
  position: absolute;
  width: 400px;
  height: 300px;
  background-color: rgb(68 68 68);
  border: 1px solid #000;
  color: white;
  z-index: 6000;
  border-radius: 5px;
}

.mm-note textarea {
  outline: none;
  border: 0px solid #ccc;
  display: block;
  width: 100%;
  height: 100%;
}

.theme-light .mm-note {
  background-color: white;
  border: 1px solid #ccc;
  color: #333;
}

.mm-note-dom svg {
  fill: #d4d4d4;
}

.theme-light .mm-node-dom svg {
  fill: #333;
}

.mm-node-note-close {
  position: absolute;
  right: 6px;
  top: 6px;
  color: red;
  width: 20px;
  height: 20px;
  font-size: 14px;
}

.mm-node-note-tab {
  position: absolute;
  min-width: 400px;
  min-height: 300px;
  max-width: 500px;
  max-height: 400px;
  border-radius: 5px;
  background-color: wheat;
  color: #333;
  overflow: auto;
  z-index: 6000;
  display: none;
}

.mm-node-note-container {
  padding: 10px;
  box-sizing: border-box;
  font-size: 14px;
  white-space: pre-wrap;
}

.block-language-mindmap {
  height: 600px;
}

/* theme hand drawn */
.theme-light .mm-mindmap.mm-handdraw-theme {
  color: #333;
}

.mm-mindmap.mm-handdraw-theme .mm-node-bar {
  display: none;
}

.mm-mindmap.mm-handdraw-theme .mm-node.mm-root>.mm-node-content {
  background: transparent;
}


/* theme black */
.mm-mindmap.mm-theme-black {
  background-color: #f1f1f1;
}

.theme-dark .mm-mindmap.mm-theme-black .mm-node .mm-node-content {
  color: #333;
}

.mm-mindmap.mm-theme-black .mm-node.mm-root .mm-node-content {
  color: white;
  background-color: #c31105;
}

.mm-mindmap.mm-theme-black .mm-node.mm-node-second .mm-node-content {
  background-color: #333;
  color: white;
}


/* theme white */

.mm-mindmap.mm-theme-white {
  background-color: #fff;
}

.theme-dark .mm-mindmap.mm-theme-white .mm-node .mm-node-content {
  color: #333;
}

.mm-mindmap.mm-theme-white .mm-node.mm-root .mm-node-content {
  color: #333;
  background-color: #fff;
  border: 2px solid #333;
}


.mm-mindmap.mm-theme-white .mm-node.mm-node-second .mm-node-content {
  color: #333;
}


/* theme warm */
.mm-mindmap.mm-theme-warm {
  background-color: #FFF8E1;
}

.theme-dark .mm-mindmap.mm-theme-warm .mm-node .mm-node-content {
  color: #333;
}

.mm-mindmap.mm-theme-warm .mm-node.mm-root .mm-node-content {
  color: white;
  background-color: #FFD180;
}

.mm-mindmap.mm-theme-warm .mm-node.mm-node-second .mm-node-content {
  color: white;
  background-color: #4E342E;
}


/* theme cold */
.mm-mindmap.mm-theme-cold {
  background-color: rgb(35, 39, 62);
}

.theme-dark .mm-mindmap.mm-theme-cold .mm-node .mm-node-content {
  color: white;
}

.theme-light .mm-mindmap.mm-theme-cold .mm-node .mm-node-content {
  color: rgb(115, 154, 163);
}

.mm-mindmap.mm-theme-cold .mm-node.mm-root .mm-node-content {
  color: white;
  background-color: rgb(5, 196, 235);
}

.mm-mindmap.mm-theme-cold .mm-node.mm-node-second .mm-node-content {
  color: white;
  background-color: rgb(35, 39, 62);
}

/* theme normal */

.mm-mindmap.mm-theme-normal {
  background-color: #555;
}

.theme-dark .mm-mindmap.mm-theme-normal .mm-node .mm-node-content {
  color: white;
}

.theme-light .mm-mindmap.mm-theme-normal .mm-node .mm-node-content {
  color: white;
}

.mm-mindmap.mm-theme-normal .mm-node.mm-root .mm-node-content {
  color: rgb(82, 50, 0);
  background-color: rgb(232, 222, 153);
}

.mm-mindmap.mm-theme-normal .mm-node.mm-node-second .mm-node-content {
  color: #333;
  background-color: rgb(164, 195, 190);
}

/* theme relax */
.mm-mindmap.mm-theme-relax {
  background-color: #56a3b3;
}

.theme-dark .mm-mindmap.mm-theme-relax .mm-node .mm-node-content {
  color: white;
}

.theme-light .mm-mindmap.mm-theme-relax .mm-node .mm-node-content {
  color: white;
}

.mm-mindmap.mm-theme-relax .mm-node.mm-root .mm-node-content {
  color: #333;
  background-color: #fbffff;
}

.mm-mindmap.mm-theme-relax .mm-node.mm-node-second .mm-node-content {
  color: #333;
  background-color: rgb(255, 235, 204);
}


/* node style */
.mm-node-setup-board {
  position: absolute;
  top: 14000px;
  left: 14000px;
  z-index: 5000;
  padding: 10px;
  background: #333;
  border: 1px solid #000;
  color: #ccc;
  border-radius: 5px;
  min-width: 200px;
  max-width: 300px;
  white-space: pre-wrap;
}


.theme-light .mm-node-setup-board {
  background: white;
  border: 1px solid #666;
  color: #999
}

.mm-node-setup-board:hover {
  z-index: 8000;
}

.mm-node-setup-board svg {
  fill: #ccc
}

.mm-dec {
  font-size: 16px;
  font-weight: bold;
  margin: 4px;
}

.mm-node-setup-board span {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  vertical-align: middle;
  margin: 2px;
  cursor: pointer;

}

.theme-dark .mm-node-setup-board span.mm-color {
  border: 1px solid #000
}

.theme-light .mm-node-setup-board span.mm-color {
  border: 1px solid #666
}

.mm-node-stroke-width {
  border-bottom-color: #ccc;
  border-bottom-style: solid;
  border-radius: 0px !important;
  height: 10px !important;
}

.mm-node-stroke-style {
  border-bottom-color: #ccc;
  border-radius: 0px !important;
  height: 10px !important;

}

.theme-light .mm-mindmap-select {
  background-color: rgb(255, 165, 0, 0.2) !important;
}


/* table css */

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

.mm-table {
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #cbcbcb;
}

.mm-table caption {
  color: #000;
  font: italic 85%/1 arial, sans-serif;
  padding: 1em 0;
  text-align: center;
}

.mm-table td,
.mm-table th {
  border: 1px solid #cbcbcb;
  border-width: 0 0 0 1px;
  font-size: inherit;
  margin: 0;
  overflow: visible;

}

/* .mm-table td:hover,.mm-table th:hover{
   border:1px solid var(--interactive-accent);
} */

.mm-table thead {
  background-color: #e0e0e0;
  color: #000;
  text-align: left;
  vertical-align: bottom;
}

.mm-table td {
  background-color: transparent;
}

.mm-table-bordered td {
  border-bottom: 1px solid #cbcbcb;
}

.mm-table-bordered tbody>tr:last-child>td {
  border-bottom-width: 0;
}

.mm-table>tr>td>.mm-node,
.mm-table>thead>tr>th>.mm-node {
  position: initial !important;
  padding: .5em 1em;
}

.mm-table .mm-node:hover {
  border: 1px solid var(--interactive-accent);
}

.mm-table>tr>td>.mm-node>.mm-node-content {
  max-width: 1200px !important;
}

.mm-table>thead>tr>th>.mm-node>.mm-node-content {
  max-width: initial !important;
}

/* .mm-table .mm-node.mm-root .mm-node-content {
  background-color: transparent;
} */
.mm-table>thead>tr>th>.mm-node.mm-root {
  padding: 0;
}

.mm-table .mm-node.mm-root .mm-node-content {
  font-size: 18px;
  border-radius: 0;
}

/* .mm-table>tr>td>.mm-node>.mm-node-content>.mm-node-edit{
  width: 80%;
} */


/* theme whiteboard */

/* .theme-light .mm-theme-whiteboard{

} */

.theme-light .mm-theme-whiteboard .mm-mindmap-content>.mm-root>.mm-node-content {
  background-color: white;
  box-shadow: 0 0 10px #d6d6d6;
  color: #333;
  font-size: 18px;
}

.mm-theme-whiteboard .mm-mindmap-content>.mm-node>.mm-node-content img {
  border-radius: 6px;
  margin: 10px 0;
}

.theme-dark .mm-theme-whiteboard .mm-mindmap-content>.mm-root>.mm-node-content {
  background-color: #313131;
  box-shadow: 0 0 10px #1b1b1b;
  color: #f5f5f5;
  font-size: 18px;
}

/* .theme-dark .mm-theme-whiteboard .mm-mindmap-content polyline{
  stroke:#9e9d9d
}

.theme-dark .mm-theme-whiteboard .mm-mindmap-content marker circle,
.theme-dark .mm-theme-whiteboard .mm-mindmap-content marker polygon {
  fill:#9e9d9d
} */

/* search box*/

.mm-search {
  position: absolute;
  top: 60px;
  left: 20px;
  z-index: 8000;
  width: 450px;
  border-radius: 6px;
}

.theme-dark .mm-search {
  background-color: #333;
  box-shadow: 0 0 6px #000;
  color: #f5f5f5;
}

.theme-light .mm-search {
  color: #333;
  background-color: #f5f5f5;
  box-shadow: 0 0 10px #d6d6d6;
}

.mm-search .mm-search-head {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  font-size: 14px;
  line-height: 20px;
}

.mm-search .mm-search-head input {
  width: 100%;
  line-height: 30px;
  border: 0;
  outline: none;
  margin-bottom: 6px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  text-indent: 6px;
}

.theme-dark .mm-search .mm-search-head input {
  background-color: #202020;
  color: #ccc;
}


.mm-search {
  padding-top: 70px;
}

.mm-search-list {
  line-height: 28px;
  padding: 0;
  max-height: 400px;
  overflow: auto;
}

.theme-dark .mm-search-item:hover {
  background-color: #000;
  cursor: pointer;
}

.theme-light .mm-search-item:hover {
  background-color: #ccc;
  cursor: pointer;
}

.mm-search-result {
  padding-left: 6px;
}

.mm-search-item {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  padding: 0 6px;
  font-size: 14px;
  line-height: 24px;
}

.theme-dark .mm-search-item {
  border-top: 1px solid #000;
}

.theme-light .mm-search-item {
  border-top: 1px solid #ccc;
}


.mm-scale {
  position: fixed;
  left: 20px;
  bottom: 40px;
  background-color: #fff;
  color: #333;
  padding: 3px 6px;
  border-radius: 3px;
  cursor: pointer;
  user-select: none;
  height: 30px;
}

.mm-scale>span {
  vertical-align: middle;
  margin: 0 2px;
  font-size: 12px;

  text-align: center;
  display: inline-block;
  height: 24px;
  line-height: 24px;
}

.mm-scale span.mm-scale-number {
  width: 32px;
}

.mm-scale>span svg {
  margin-top: 3px;
}

.theme-dark .mm-scale {
  background-color: #000;
  color: #f5f5f5;
}

.theme-dark .mm-scale>span svg {
  fill: #f5f5f5;
}

.mm-scale .mm-center-btn svg {
  margin-top: 5px;
}

.mm-scale .mm-center-btn {
  margin-left: 6px;
}

/* hover edit */

.mm-node .popover.hover-popover {
  position: inherit;
  min-width: 400px;
  height: 300px;
}

.mm-change-layout-board {
  background-color: #000;
  background: #333;
  border: 1px solid #000;
  width: 300px;
  height: 500px;
  overflow: auto;
  border-radius: 0.25rem;
  z-index: 6000;
  padding: 0 20px;

}

.mm-change-layout {
  width: 80%;
  margin: 20px auto;
  cursor: pointer;
  border: 1px solid #555;
  padding: 6px;
  border-radius: 5px;
}

.mm-change-layout img {
  width: 100%;
}

.mm-change-layout:nth-child(3) img,
.mm-change-layout:nth-child(4) img {
  width: 60%;
  margin: 0 auto;
  display: block;
}

.mm-change-layout-delete svg {
  display: block;
  margin: 0 auto;
  fill: white;
}

.mm-change-layout:hover {
  opacity: 0.9;
}

/* 主题切换选框 */
.mm-theme-select {
  position: absolute;
  top: 60px;
  left: 20px;
  z-index: 8000;
  width: 450px;
  border-radius: 6px;
}

.mm-theme-select select {
  outline: none;
  border: 0;
  padding: 4px 6px;
  width: 100px;
}