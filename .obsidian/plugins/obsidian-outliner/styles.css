/* lists and bullets */
.outliner-plugin-better-lists .cm-s-obsidian .HyperMD-list-line {
  padding-top: 0.4em;
}

.outliner-plugin-better-lists .cm-formatting-list-ul {
  margin-right: 0.3em;
}

.outliner-plugin-better-lists .list-bullet::after {
  width: 0.4em;
  height: 0.4em;
  background-color: var(--text-muted);
}

/* lines */
.outliner-plugin-list-lines-scroller {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--file-margins);
  padding-left: 0;
  pointer-events: none;
  overflow: hidden;
}

.outliner-plugin-list-lines-content-container {
  position: relative;
}

.outliner-plugin-list-line {
  pointer-events: auto;
  position: absolute;
  width: 5px;
  margin-left: 0.5ch;
  margin-top: 1em;
  z-index: 1;
  cursor: pointer;
  background: transparent;
  background-image: linear-gradient(
    to right,
    var(--text-faint) 1px,
    transparent 1px
  );
  background-position-x: 2px;
  background-repeat: no-repeat;
}

.outliner-plugin-better-bullets .outliner-plugin-list-line {
  margin-top: 1.4em;
}

.markdown-source-view.mod-cm6.is-readable-line-width
  .outliner-plugin-list-lines-content-container {
  max-width: 700px;
  margin: auto;
}

.outliner-plugin-list-line:hover {
  background: var(--text-faint);
}

.outliner-plugin-vertical-lines
  .markdown-source-view.mod-cm6
  .cm-hmd-list-indent
  .cm-indent::before {
  content: none;
}

/* drag-n-drop */
.outliner-plugin-dropping-line {
  background-color: hsla(var(--interactive-accent-hsl), 0.4);
}

.outliner-plugin-dragging-line {
  opacity: 0.5;
  background-color: hsla(var(--interactive-accent-hsl), 0.2);
}

.outliner-plugin-drop-zone {
  width: 300px;
  height: 4px;
  background: var(--color-accent);
  z-index: 999;
  position: absolute;
  pointer-events: none;
}

.outliner-plugin-drop-zone-padding {
  position: absolute;
  height: 4px;
}

body.outliner-plugin-dnd:not(.outliner-plugin-dragging) .cm-formatting-list,
body.outliner-plugin-dnd:not(.outliner-plugin-dragging)
  .cm-fold-indicator
  .collapse-indicator {
  cursor: grab !important;
}

html body.outliner-plugin-dnd.outliner-plugin-dragging {
  cursor: grabbing !important;
}
