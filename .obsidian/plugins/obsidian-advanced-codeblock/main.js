/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/main.ts
__export(exports, {
  default: () => ObsidianAdvancedCodeblockPlugin
});
var import_obsidian3 = __toModule(require("obsidian"));

// src/postProcessor.ts
var import_obsidian2 = __toModule(require("obsidian"));

// src/util.ts
var paramRegex = /\{.+\}|\w+/g;
var braceSurroundingRegex = /^{.+}$/;

// src/extendPrism.ts
var import_obsidian = __toModule(require("obsidian"));

// lib/prism-line-numbers.js
function extendLineNumberPlugin(Prism) {
  if (typeof Prism === "undefined" || typeof document === "undefined") {
    return;
  }
  var PLUGIN_NAME = "line-numbers";
  var NEW_LINE_EXP = /\n(?!$)/g;
  var config = Prism.plugins.lineNumbers = {
    getLine: function(element, number) {
      if (element.tagName !== "PRE" || !element.classList.contains(PLUGIN_NAME)) {
        return;
      }
      var lineNumberRows = element.querySelector(".line-numbers-rows");
      if (!lineNumberRows) {
        return;
      }
      var lineNumberStart = parseInt(element.getAttribute("data-start"), 10) || 1;
      var lineNumberEnd = lineNumberStart + (lineNumberRows.children.length - 1);
      if (number < lineNumberStart) {
        number = lineNumberStart;
      }
      if (number > lineNumberEnd) {
        number = lineNumberEnd;
      }
      var lineIndex = number - lineNumberStart;
      return lineNumberRows.children[lineIndex];
    },
    resize: function(element) {
      resizeElements([element]);
    },
    assumeViewportIndependence: true
  };
  function resizeElements(elements) {
    elements = elements.filter(function(e) {
      var codeStyles = getStyles(e);
      var whiteSpace = codeStyles["white-space"];
      return whiteSpace === "pre-wrap" || whiteSpace === "pre-line";
    });
    if (elements.length == 0) {
      return;
    }
    var infos = elements.map(function(element) {
      var codeElement = element.querySelector("code");
      var lineNumbersWrapper = element.querySelector(".line-numbers-rows");
      if (!codeElement || !lineNumbersWrapper) {
        return void 0;
      }
      var lineNumberSizer = element.querySelector(".line-numbers-sizer");
      var codeLines = codeElement.textContent.split(NEW_LINE_EXP);
      if (!lineNumberSizer) {
        lineNumberSizer = document.createElement("span");
        lineNumberSizer.className = "line-numbers-sizer";
        codeElement.appendChild(lineNumberSizer);
      }
      lineNumberSizer.innerHTML = "0";
      lineNumberSizer.style.display = "block";
      var oneLinerHeight = lineNumberSizer.getBoundingClientRect().height;
      lineNumberSizer.innerHTML = "";
      return {
        element,
        lines: codeLines,
        lineHeights: [],
        oneLinerHeight,
        sizer: lineNumberSizer
      };
    }).filter(Boolean);
    infos.forEach(function(info) {
      var lineNumberSizer = info.sizer;
      var lines = info.lines;
      var lineHeights = info.lineHeights;
      var oneLinerHeight = info.oneLinerHeight;
      lineHeights[lines.length - 1] = void 0;
      lines.forEach(function(line, index) {
        if (line && line.length > 1) {
          var e = lineNumberSizer.appendChild(document.createElement("span"));
          e.style.display = "block";
          e.textContent = line;
        } else {
          lineHeights[index] = oneLinerHeight;
        }
      });
    });
    infos.forEach(function(info) {
      var lineNumberSizer = info.sizer;
      var lineHeights = info.lineHeights;
      var childIndex = 0;
      for (var i = 0; i < lineHeights.length; i++) {
        if (lineHeights[i] === void 0) {
          lineHeights[i] = lineNumberSizer.children[childIndex++].getBoundingClientRect().height;
        }
      }
    });
    infos.forEach(function(info) {
      var lineNumberSizer = info.sizer;
      var wrapper = info.element.querySelector(".line-numbers-rows");
      lineNumberSizer.style.display = "none";
      lineNumberSizer.innerHTML = "";
      info.lineHeights.forEach(function(height, lineNumber) {
        wrapper.children[lineNumber].style.height = height + "px";
      });
    });
  }
  function getStyles(element) {
    if (!element) {
      return null;
    }
    return window.getComputedStyle ? getComputedStyle(element) : element.currentStyle || null;
  }
  Prism.hooks.add("complete", function(env) {
    if (!env.code) {
      return;
    }
    var code = env.element;
    var pre = code.parentNode;
    if (!pre || !/pre/i.test(pre.nodeName)) {
      return;
    }
    if (code.querySelector(".line-numbers-rows")) {
      return;
    }
    if (!Prism.util.isActive(code, PLUGIN_NAME)) {
      return;
    }
    code.classList.remove(PLUGIN_NAME);
    pre.classList.add(PLUGIN_NAME);
    var match = env.code.match(NEW_LINE_EXP);
    var linesNum = match ? match.length + 1 : 1;
    var lineNumbersWrapper;
    var lines = new Array(linesNum + 1).join("<span></span>");
    lineNumbersWrapper = document.createElement("span");
    lineNumbersWrapper.setAttribute("aria-hidden", "true");
    lineNumbersWrapper.className = "line-numbers-rows";
    lineNumbersWrapper.innerHTML = lines;
    if (pre.hasAttribute("data-start")) {
      pre.style.counterReset = "linenumber " + (parseInt(pre.getAttribute("data-start"), 10) - 1);
    }
    env.element.appendChild(lineNumbersWrapper);
    resizeElements([pre]);
    Prism.hooks.run("line-numbers", env);
  });
  Prism.hooks.add("line-numbers", function(env) {
    env.plugins = env.plugins || {};
    env.plugins.lineNumbers = true;
  });
}

// lib/prism-line-highlight.js
function extendLineHighlightPlugin(Prism) {
  if (typeof Prism === "undefined" || typeof document === "undefined" || !document.querySelector) {
    return;
  }
  var LINE_NUMBERS_CLASS = "line-numbers";
  var LINKABLE_LINE_NUMBERS_CLASS = "linkable-line-numbers";
  var NEW_LINE_REGEX = /\n(?!$)/g;
  function $$(selector, container) {
    return Array.prototype.slice.call((container || document).querySelectorAll(selector));
  }
  function hasClass(element, className) {
    return element.classList.contains(className);
  }
  function callFunction(func) {
    func();
  }
  var isLineHeightRounded = function() {
    var res;
    return function() {
      if (typeof res === "undefined") {
        var d = document.createElement("div");
        d.style.fontSize = "13px";
        d.style.lineHeight = "1.5";
        d.style.padding = "0";
        d.style.border = "0";
        d.innerHTML = "&nbsp;<br />&nbsp;";
        document.body.appendChild(d);
        res = d.offsetHeight === 38;
        document.body.removeChild(d);
      }
      return res;
    };
  }();
  function getContentBoxTopOffset(parent, child) {
    var parentStyle = getComputedStyle(parent);
    var childStyle = getComputedStyle(child);
    function pxToNumber(px) {
      return +px.substr(0, px.length - 2);
    }
    return child.offsetTop + pxToNumber(childStyle.borderTopWidth) + pxToNumber(childStyle.paddingTop) - pxToNumber(parentStyle.paddingTop);
  }
  function isActiveFor(pre) {
    if (!pre || !/pre/i.test(pre.nodeName)) {
      return false;
    }
    if (pre.hasAttribute("data-line")) {
      return true;
    }
    if (pre.id && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS)) {
      return true;
    }
    return false;
  }
  var scrollIntoView = true;
  Prism.plugins.lineHighlight = {
    highlightLines: function highlightLines(pre, lines, classes) {
      lines = typeof lines === "string" ? lines : pre.getAttribute("data-line") || "";
      var ranges = lines.replace(/\s+/g, "").split(",").filter(Boolean);
      var offset = +pre.getAttribute("data-line-offset") || 0;
      var parseMethod = isLineHeightRounded() ? parseInt : parseFloat;
      var lineHeight = parseMethod(getComputedStyle(pre).lineHeight);
      var hasLineNumbers = Prism.util.isActive(pre, LINE_NUMBERS_CLASS);
      var codeElement = pre.querySelector("code");
      var parentElement = hasLineNumbers ? pre : codeElement || pre;
      var mutateActions = [];
      var lineBreakMatch = codeElement.textContent.match(NEW_LINE_REGEX);
      var numberOfLines = lineBreakMatch ? lineBreakMatch.length + 1 : 1;
      var codePreOffset = !codeElement || parentElement == codeElement ? 0 : getContentBoxTopOffset(pre, codeElement);
      ranges.forEach(function(currentRange) {
        var range = currentRange.split("-");
        var start2 = +range[0];
        var end = +range[1] || start2;
        end = Math.min(numberOfLines, end);
        if (end < start2)
          return;
        var line = pre.querySelector('.line-highlight[data-range="' + currentRange + '"]') || document.createElement("div");
        mutateActions.push(function() {
          line.setAttribute("aria-hidden", "true");
          line.setAttribute("data-range", currentRange);
          line.className = (classes || "") + " line-highlight";
        });
        if (hasLineNumbers && Prism.plugins.lineNumbers) {
          var startNode = Prism.plugins.lineNumbers.getLine(pre, start2);
          var endNode = Prism.plugins.lineNumbers.getLine(pre, end);
          if (startNode) {
            var top = startNode.offsetTop + codePreOffset + "px";
            mutateActions.push(function() {
              line.style.top = top;
            });
          }
          if (endNode) {
            var height = endNode.offsetTop - startNode.offsetTop + endNode.offsetHeight + "px";
            mutateActions.push(function() {
              line.style.height = height;
            });
          }
        } else {
          mutateActions.push(function() {
            line.setAttribute("data-start", String(start2));
            if (end > start2) {
              line.setAttribute("data-end", String(end));
            }
            line.style.top = (start2 - offset - 1) * lineHeight + codePreOffset + "px";
            line.textContent = new Array(end - start2 + 2).join(" \n");
          });
        }
        mutateActions.push(function() {
          line.style.width = pre.scrollWidth + "px";
        });
        mutateActions.push(function() {
          parentElement.appendChild(line);
        });
      });
      var id = pre.id;
      if (hasLineNumbers && Prism.util.isActive(pre, LINKABLE_LINE_NUMBERS_CLASS) && id) {
        if (!hasClass(pre, LINKABLE_LINE_NUMBERS_CLASS)) {
          mutateActions.push(function() {
            pre.classList.add(LINKABLE_LINE_NUMBERS_CLASS);
          });
        }
        var start = parseInt(pre.getAttribute("data-start") || "1");
        $$(".line-numbers-rows > span", pre).forEach(function(lineSpan, i) {
          var lineNumber = i + start;
          lineSpan.onclick = function() {
            var hash = id + "." + lineNumber;
            scrollIntoView = false;
            location.hash = hash;
            setTimeout(function() {
              scrollIntoView = true;
            }, 1);
          };
        });
      }
      return function() {
        mutateActions.forEach(callFunction);
      };
    }
  };
  function applyHash() {
    var hash = location.hash.slice(1);
    $$(".temporary.line-highlight").forEach(function(line) {
      line.parentNode.removeChild(line);
    });
    var range = (hash.match(/\.([\d,-]+)$/) || [, ""])[1];
    if (!range || document.getElementById(hash)) {
      return;
    }
    var id = hash.slice(0, hash.lastIndexOf("."));
    var pre = document.getElementById(id);
    if (!pre) {
      return;
    }
    if (!pre.hasAttribute("data-line")) {
      pre.setAttribute("data-line", "");
    }
    var mutateDom = Prism.plugins.lineHighlight.highlightLines(pre, range, "temporary ");
    mutateDom();
    if (scrollIntoView) {
      document.querySelector(".temporary.line-highlight").scrollIntoView();
    }
  }
  var fakeTimer = 0;
  Prism.hooks.add("before-sanity-check", function(env) {
    var pre = env.element.parentElement;
    if (!isActiveFor(pre)) {
      return;
    }
    var num = 0;
    $$(".line-highlight", pre).forEach(function(line) {
      num += line.textContent.length;
      line.parentNode.removeChild(line);
    });
    if (num && /^(?: \n)+$/.test(env.code.slice(-num))) {
      env.code = env.code.slice(0, -num);
    }
  });
  Prism.hooks.add("complete", function completeHook(env) {
    var pre = env.element.parentElement;
    if (!isActiveFor(pre)) {
      return;
    }
    clearTimeout(fakeTimer);
    var hasLineNumbers = Prism.plugins.lineNumbers;
    var isLineNumbersLoaded = env.plugins && env.plugins.lineNumbers;
    if (hasClass(pre, LINE_NUMBERS_CLASS) && hasLineNumbers && !isLineNumbersLoaded) {
      Prism.hooks.add("line-numbers", completeHook);
    } else {
      var mutateDom = Prism.plugins.lineHighlight.highlightLines(pre);
      mutateDom();
      fakeTimer = setTimeout(applyHash, 1);
    }
  });
  window.addEventListener("hashchange", applyHash);
}

// src/extendPrism.ts
(0, import_obsidian.loadPrism)().then((val) => {
  extendLineNumberPlugin(window.Prism);
  extendLineHighlightPlugin(window.Prism);
});

// src/postProcessor.ts
function processParams(element, context, app) {
  var _a;
  const pre = element.querySelector("pre:not(.frontmatter)");
  if (!pre)
    return null;
  const codeBlock = context.getSectionInfo(element);
  if (!codeBlock)
    return null;
  const origin = (_a = app.workspace.getActiveViewOfType(import_obsidian2.MarkdownView)) == null ? void 0 : _a.editor.getLine(codeBlock.lineStart).slice(3);
  if (!origin)
    return null;
  const codeBlockInfo = origin.match(paramRegex);
  const params = codeBlockInfo.slice(1);
  if (!params.length)
    return null;
  return { pre, params };
}
function onMounted(element, onAttachCallback) {
  const observer = new MutationObserver(function() {
    function isAttached(el) {
      if (el.parentNode === document) {
        return true;
      } else if (el.parentNode === null) {
        return false;
      } else {
        return isAttached(el.parentNode);
      }
    }
    if (isAttached(element)) {
      observer.disconnect();
      onAttachCallback();
    }
  });
  observer.observe(document, {
    childList: true,
    subtree: true
  });
}
function handleLineNumbers(pre, params, initHandlers) {
  if (!params.includes("nums"))
    return;
  pre.classList.add("line-numbers");
  const initLineNumbers = () => {
    window.Prism.plugins.lineNumbers.resize(pre);
  };
  initHandlers.push(initLineNumbers);
}
function handleLineHighlight(pre, params, initHandlers) {
  const lineHightlightParamIdx = params.findIndex((param) => braceSurroundingRegex.test(param));
  if (lineHightlightParamIdx === -1)
    return;
  pre.dataset.line = params[lineHightlightParamIdx].slice(1, -1);
  const initLineHighlight = () => {
    window.Prism.plugins.lineHighlight.highlightLines(pre)();
  };
  initHandlers.push(initLineHighlight);
}
function commonCodeblockPostProcessor(element, context, app, plugin) {
  const processResult = processParams(element, context, app);
  if (!processResult)
    return;
  const { pre, params } = processResult;
  const initHandlers = [];
  handleLineNumbers(pre, params, initHandlers);
  handleLineHighlight(pre, params, initHandlers);
  onMounted(pre, () => {
    initHandlers.forEach((handler) => {
      handler();
    });
  });
  plugin.registerEvent(app.workspace.on("resize", () => {
    initHandlers.forEach((handler) => {
      handler();
    });
  }));
}

// src/CM6Extensions.ts
var import_view = __toModule(require("@codemirror/view"));
var import_state = __toModule(require("@codemirror/state"));
var import_language = __toModule(require("@codemirror/language"));
var LineNumberWidget = class extends import_view.WidgetType {
  constructor(idx) {
    super();
    this.idx = idx;
  }
  toDOM() {
    const el = document.createElement("span");
    el.className = "live-preview-codeblock-line-nums";
    el.textContent = "" + this.idx;
    return el;
  }
};
var livePreviewCM6Extension = import_view.ViewPlugin.fromClass(class {
  constructor(view) {
    this.decorations = this.buildDecorations(view);
  }
  update(update) {
    if (update.docChanged || update.viewportChanged)
      this.decorations = this.buildDecorations(update.view);
  }
  destory() {
  }
  buildDecorations(view) {
    const builder = new import_state.RangeSetBuilder();
    const codeblockInfo = {
      showLineNumbers: false,
      highlightLines: null
    };
    let startLineNum;
    for (const { from, to } of view.visibleRanges) {
      try {
        const tree = (0, import_language.syntaxTree)(view.state);
        tree.iterate({
          from,
          to,
          enter: ({ type, from: from2, to: to2 }) => {
            var _a, _b;
            const lineClasses = type.prop(import_language.lineClassNodeProp);
            if (!lineClasses)
              return;
            const classes = new Set(lineClasses.split(" "));
            const isCodeblockBegin = classes.has("HyperMD-codeblock-begin");
            const isCodeblockLine = classes.has("HyperMD-codeblock-bg") && !classes.has("HyperMD-codeblock-begin") && !classes.has("HyperMD-codeblock-end");
            if (isCodeblockBegin) {
              const startLine = view.state.doc.lineAt(from2);
              const codeblockParams = startLine.text.match(paramRegex).slice(1);
              const highlightParam = (_a = codeblockParams.find((param) => braceSurroundingRegex.test(param))) == null ? void 0 : _a.slice(1, -1);
              startLineNum = startLine.number;
              codeblockInfo.showLineNumbers = false;
              codeblockInfo.highlightLines = null;
              if (codeblockParams.includes("nums"))
                codeblockInfo.showLineNumbers = true;
              if (highlightParam)
                codeblockInfo.highlightLines = highlightParam.replace(" ", "").split(",").flatMap((line) => {
                  if (!+line) {
                    const res = [];
                    const [start, end] = line.split("-");
                    for (let i = +start; i <= +end; i++) {
                      res.push(i);
                    }
                    return res;
                  }
                  return [+line];
                });
            }
            if (!isCodeblockLine)
              return;
            const currentLineNum = view.state.doc.lineAt(from2).number;
            if (codeblockInfo.showLineNumbers) {
              const deco = import_view.Decoration.widget({
                widget: new LineNumberWidget(currentLineNum - startLineNum),
                side: -1e4
              });
              builder.add(from2, from2, deco);
            }
            if (codeblockInfo.highlightLines) {
              if (codeblockInfo.highlightLines.includes(currentLineNum - startLineNum)) {
                const line = view.state.doc.lineAt(from2);
                const deco = import_view.Decoration.line({
                  attributes: { class: "live-preview-codeblock-highlight" }
                });
                if ((_b = builder.last) == null ? void 0 : _b.startSide) {
                  deco.startSide = builder.last.startSide;
                  deco.endSide = deco.startSide;
                }
                builder.add(line.from, line.from, deco);
              }
            }
          }
        });
      } catch (error) {
        console.log(error);
      }
    }
    return builder.finish();
  }
}, {
  decorations: (v) => v.decorations
});

// src/main.ts
var ObsidianAdvancedCodeblockPlugin = class extends import_obsidian3.Plugin {
  onload() {
    return __async(this, null, function* () {
      console.log("Loading Advanced Codeblock");
      this.registerEditorExtension([livePreviewCM6Extension]);
      this.registerMarkdownPostProcessor((element, context) => {
        commonCodeblockPostProcessor(element, context, this.app, this);
      });
    });
  }
  onunload() {
  }
};
//# sourceMappingURL=data:application/json;base64,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
