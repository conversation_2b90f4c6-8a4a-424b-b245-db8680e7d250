/* line-number */
code[class*="language-"] {
	overflow: visible;
}

pre[class*="language-"].line-numbers {
	position: relative;
	padding-left: 3.8em;
	counter-reset: linenumber;
	white-space: pre-wrap;
}

pre[class*="language-"].line-numbers > code {
	position: relative;
	white-space: inherit;
}

.line-numbers .line-numbers-rows {
	position: absolute;
	pointer-events: none;
	top: 0;
	margin-top: 1em;
	font-size: 100%;
	left: -3.8em;
	width: 3em; /* works for line-numbers below 1000 lines */
	letter-spacing: -1px;
	border-right: 1px solid #999;

	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.line-numbers-rows > span {
	display: block;
	counter-increment: linenumber;
}

.line-numbers-rows > span:before {
	content: counter(linenumber);
	color: #999;
	display: block;
	padding-right: 0.8em;
	text-align: right;
	white-space: nowrap;
}

span.live-preview-codeblock-line-nums {
	display: inline-block;
	width: 3em;
	margin-right: 1.8em;
	color: #999;
	border-right: 1px solid #999;
	padding-right: 0.8em;
	text-align: right;
	white-space: nowrap;
	user-select: none;
}

/* line-highlight */
pre[data-line] {
	position: relative;
	padding: 1em 0 1em 3em;
}

.line-highlight {
	position: absolute;
	left: 0;
	right: 0;
	padding: inherit 0;
	margin-top: 1em; /* Same as .prism’s padding-top */

	background: hsla(24, 20%, 50%, 0.08);
	background: linear-gradient(
		to right,
		hsla(24, 20%, 50%, 0.1) 70%,
		hsla(24, 20%, 50%, 0)
	);

	pointer-events: none;

	line-height: inherit;
	white-space: pre;
}

code .line-highlight {
	margin-top: 2em;
}

@media print {
	.line-highlight {
		/*
		 * This will prevent browsers from replacing the background color with white.
		 * It's necessary because the element is layered on top of the displayed code.
		 */
		-webkit-print-color-adjust: exact;
		color-adjust: exact;
	}
}

.line-highlight:before,
.line-highlight[data-end]:after {
	content: attr(data-start);
	position: absolute;
	top: 0.4em;
	left: 0.6em;
	min-width: 1em;
	padding: 0 0.5em;
	background-color: hsla(24, 20%, 50%, 0.4);
	color: hsl(24, 20%, 95%);
	font: bold 65%/1.5 sans-serif;
	text-align: center;
	vertical-align: 0.3em;
	border-radius: 999px;
	text-shadow: none;
	box-shadow: 0 1px white;
}

.line-highlight[data-end]:after {
	content: attr(data-end);
	top: auto;
	bottom: 0.4em;
}

.line-numbers .line-highlight:before,
.line-numbers .line-highlight:after {
	content: none;
}

pre[id].linkable-line-numbers span.line-numbers-rows {
	pointer-events: all;
}
pre[id].linkable-line-numbers span.line-numbers-rows > span:before {
	cursor: pointer;
}
pre[id].linkable-line-numbers span.line-numbers-rows > span:hover:before {
	background-color: rgba(128, 128, 128, 0.2);
}

.HyperMD-codeblock-bg.live-preview-codeblock-highlight {
	background: hsla(24, 20%, 50%, 0.08);
	background: linear-gradient(
		to right,
		hsla(24, 20%, 50%, 0.1) 70%,
		hsla(24, 20%, 50%, 0)
	);	
}
