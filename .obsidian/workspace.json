{"main": {"id": "02bf0e1ba8d22305", "type": "split", "children": [{"id": "50d3cfe8a041f3c2", "type": "tabs", "children": [{"id": "1670d1255352910f", "type": "leaf", "pinned": true, "state": {"type": "empty", "state": {}, "pinned": true, "icon": "lucide-file", "title": "新标签页"}}]}], "direction": "vertical"}, "left": {"id": "928b37ca06aaa493", "type": "split", "children": [{"id": "50b1967bfb0b5864", "type": "tabs", "children": [{"id": "dfbcbeec6bbd7444", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "byCreatedTimeReverse", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "d27737f2a0018b29", "type": "leaf", "state": {"type": "search", "state": {"query": "jackson", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "192857b02d2d9f16", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 360.5026054382324}, "right": {"id": "10b2128bd098f714", "type": "split", "children": [{"id": "bf166ece2cd1c312", "type": "tabs", "children": [{"id": "0698bb080db5c6d4", "type": "leaf", "state": {"type": "backlink", "state": {"file": "✅ SpringBoot Starter/👌🏻OSS对象存储, 自定义SpringBoot Starter实现.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "👌🏻OSS对象存储, 自定义SpringBoot Starter实现 的反向链接列表"}}, {"id": "dc660cf47fb85bc7", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "✅ SpringBoot Starter/👌🏻OSS对象存储, 自定义SpringBoot Starter实现.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "👌🏻OSS对象存储, 自定义SpringBoot Starter实现 的出链列表"}}, {"id": "81d398265fc8623d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "95a1d84abd650b71", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "大纲"}}, {"id": "dc8ca894ec616fb6", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}], "currentTab": 3}], "direction": "horizontal", "width": 305.5}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "notion-like-tables:Create Notion-Like table": false, "table-editor-obsidian:Advanced Tables Toolbar": false}}, "active": "1670d1255352910f", "lastOpenFiles": ["✅ 数据存储中间件/✅ Sharding-JDBC/Sharding5.5/ShardingSphere-Nacos-配置中心集成指南.md", "✅ 数据存储中间件/✅ Sharding-JDBC/Sharding5.5/ShardingSphere-Rules-规则配置详解.md", "✅ 数据存储中间件/✅ Sharding-JDBC/Sharding5.5/ShardingSphere-HikariCP-数据源配置指南.md", "✅ 数据存储中间件/✅ Sharding-JDBC/Sharding5.5/ShardingSphere-Props-配置说明.md", "✅ 数据存储中间件/✅ Sharding-JDBC/Sharding5.5", "AI大模型/mcp.md", "✅ 运维相关/✳️ <PERSON>/Docker部署Jenkins.md", "✅ 有趣的东西/WSL.md", "✅ 性能优化/img_1.png", "✅ 性能优化/img.png", "✅ 性能优化/Netty断连问题.md", "✅ 性能优化/Linux 服务器 参数优化.md", "✅ 对象存储/✅ Minio/MC.md", "✅ SpringBoot/✅ SpringBoot 整合 JWT 使用.md", "✅ SpringBoot/✅ SpringBoot 使用 Druid 连接池.md", "✅ SpringBoot/README.md", "✅ Java相关知识/README.md", "✅ Dubbo/README.md", "index.html", "generate_sidebar.py", "_sidebar.md", "_navbar.md", "OpenWRT/防火墙.md", "OpenWRT/Docker安装Openwrt.md", "OpenWRT", "Nginx/Nginx的安全措施.md", "<PERSON><PERSON><PERSON>", "AI大模型/TTS/KokoroTTS.md", "AI大模型/TTS", "AI大模型/README.md", "✅ 日记/week/✅ 2023年-05月 第22周计划.md", "✅ 日记/week/✅ 2023年-06月 第23周计划.md", "✅ 日记/week/✅ 2023年-06月 第24周计划.md", "✅ 日记/week/✅ 2023年-05月 第21周计划.md", "未命名.canvas", "✅ 运维相关/✳️ 监控/images/image-20230902183722536.png", "✅ 运维相关/✳️ 监控/images/image-20230902183719203.png", "✅ 运维相关/✳️ 监控/jvm.json", "✅ Dubbo/images/image-20230802213258806.png", "✅ Dubbo/images/image-20230701220642305.png", "✅ Dubbo/images/image-20230701215535282.png", "✅ Dubbo/images/image-20230701215421733.png", "✅ Dubbo/images/image-20230701215028116.png", "✅ Dubbo/images/image-20230618205633620.png", "✅ Dubbo/images", "✅ Dubbo", "✅ 日记/week", "✅ 日记/未命名.canvas", "日记/未命名.canvas"]}