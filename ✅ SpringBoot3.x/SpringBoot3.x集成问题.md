# SpringBoot3.x集成问题

## 版本

```xml
    <properties>
        <revision>7.0.2</revision>
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${project.build.sourceEncoding}</project.reporting.outputEncoding>
        <spring-boot-dependencies.version>3.2.3</spring-boot-dependencies.version>
        <spring-cloud-dependencies.version>2023.0.0</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2022.0.0.0</spring-cloud-alibaba-dependencies.version>
        <nacos.client.version>2.3.0</nacos.client.version>
    </properties>

	<dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-commons</artifactId>
                <version>4.1.1</version>
                <scope>compile</scope>
            </dependency>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
```

## 配置文件迁移

> 检测配置文件变化
>
> https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-3.0-Configuration-Changelog

```xml
<dependency>
  <groupId>org.springframework.boot</groupId>
  <artifactId>spring-boot-properties-migrator</artifactId>
  <scope>runtime</scope>
</dependency>
```

## starter配置

> https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-2.7-Release-Notes#changes-to-auto-configuration

```
META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

## Mybatis-Plus版本

```xml
           <mybatis.plus.version>3.5.5</mybatis.plus.version>
			<dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
```

## 动态数据源

```xml
            <dynamic.datasource.version>4.3.0</dynamic.datasource.version>
			<dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>
```

## knife4j

```xml
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-dependencies</artifactId>
            <version>${knife4j.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
		<dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
```

## MySQL

```xml
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

```

## RocketMQ

```java
// 启动类
@Import(RocketMQAutoConfiguration.class)
```

## javax -> jakarta

```xml
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
```



## Name for argument of type [java.lang.Integer] not specified, and parameter name information not found in class file either.

```
curl 127.0.0.1:17701/virtual/run?num=1
```

![image-20231226095008002](images/image-20231226095008002.png)

![image-20231226095130427](images/image-20231226095130427.png)

```
https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention
```





## BeanPostProcessors not registered correctly when @LoadBalanced bean instantiated in auto-configuration

> 2024-03-09 21:04:31,524 [mainraceId] WARN  [main] [org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization(PostProcessorRegistrationDelegate.java:437)] [] Bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification' of type [org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.

> https://github.com/spring-cloud/spring-cloud-commons/issues/1315
>
> https://spring.io/blog/2024/01/23/spring-cloud-commons-4-1-1-has-been-released

```xml
<dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-commons</artifactId>
      <version>4.1.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</dependencyManagement>
```

