# demo
```java
    @GetMapping("testHeap-Long")
    public void testHeapLong() {

        Long count = 0L;
        for (long i = 0L; i <= Integer.MAX_VALUE; i++) {
            count +=i;
        }
        System.out.println(count);
    }

    @GetMapping("testHeap-long")
    public void testHeaplong() {

        long count = 0L;
        for (long i = 0L; i <= Integer.MAX_VALUE; i++) {
            count +=i;
        }
        System.out.println(count);
    }
```
# 方法内使用包装类堆内存变化
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680672472729-dfac5ff6-9e80-40e1-9137-2bcd81ea1136.png#averageHue=%232e3137&clientId=ua9404ed8-bb32-4&from=paste&height=323&id=u98a5aa04&originHeight=646&originWidth=2412&originalType=binary&ratio=2&rotation=0&showTitle=false&size=52864&status=done&style=none&taskId=u4f42e782-d5ce-4c22-84a8-5566f768459&title=&width=1206)

# 方法内使用基本变量堆内存变化
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680672597024-b3998583-0ad5-4b25-a4a7-6098cdd0cbd9.png#averageHue=%232d2f33&clientId=ua9404ed8-bb32-4&from=paste&height=315&id=u91092be8&originHeight=630&originWidth=2388&originalType=binary&ratio=2&rotation=0&showTitle=false&size=50028&status=done&style=none&taskId=u757446cb-2c3c-43ab-87e2-4db456d4752&title=&width=1194)

# 总结

- Long包装类是对象，存储在堆内，频繁创建，需使用大量内存。
- 代码方法内使用基本变量，内存消耗小。
