# 堆OOM
## JVM参数
```bash
-Xms64M
-Xmx64M
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/home/<USER>/jvm-heap-oom.hprof
```
## OOM代码
```java
@RestController
public class HeapOomController {

    private static final Map<String, String> HEAP_OOM_MAP = new ConcurrentHashMap<>(10240);

    // curl 127.0.0.1:5020/heap-oom
    @GetMapping("heap-oom")
    public void addMap() {
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            HEAP_OOM_MAP.put(RandomUtil.randomNumbers(11), RandomUtil.randomNumbers(11));
        }
    }

}
```
## 分析
> 执行以上代码结果。

### java.lang.OutOfMemoryError: GC overhead limit exceeded
```java
Exception in thread "http-nio-5020-exec-1" java.lang.OutOfMemoryError: GC overhead limit exceeded
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681043342052-f3eacdeb-c9bb-4148-9dc5-834263c0133b.png#averageHue=%23222427&clientId=u73eda01a-798f-4&from=paste&height=120&id=u71cd1bf5&originHeight=240&originWidth=1888&originalType=binary&ratio=2&rotation=0&showTitle=false&size=49165&status=done&style=none&taskId=uf459e34c-9c46-44c7-a4c9-aa4ed4d42ca&title=&width=944)
> 指定的保存dump文件的目录不会自动创建。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681044331647-561ff439-a3e2-4f8b-ae76-8eb9a86cebaf.png#averageHue=%23252629&clientId=uaf9dbe52-c8e2-4&from=paste&height=92&id=u1c8afb89&originHeight=184&originWidth=1582&originalType=binary&ratio=2&rotation=0&showTitle=false&size=38989&status=done&style=none&taskId=u0f613d40-b086-49b9-aa7b-6c0ab658c64&title=&width=791)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681044726562-97ffaf3b-f2f8-40f5-94c7-d746fa9925ee.png#averageHue=%232c2e31&clientId=uaf9dbe52-c8e2-4&from=paste&height=531&id=u7935d335&originHeight=1062&originWidth=3242&originalType=binary&ratio=2&rotation=0&showTitle=false&size=63896&status=done&style=none&taskId=ufb09c117-32a3-4d08-b4d1-2676b19f970&title=&width=1621)
> mat 查看大对象

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681044446521-3500989f-7453-4c44-984c-8db81dc15c0c.png#averageHue=%23c8b68e&clientId=uaf9dbe52-c8e2-4&from=paste&height=797&id=u5f37bd32&originHeight=1594&originWidth=1776&originalType=binary&ratio=2&rotation=0&showTitle=false&size=673593&status=done&style=none&taskId=u814f304d-1820-49cd-b708-3058f38a8e8&title=&width=888)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681044180067-9ac1d05f-b9f3-4b23-babf-e9d60b83058f.png#averageHue=%23f6f6f4&clientId=uaf9dbe52-c8e2-4&from=paste&height=720&id=ufa487b1f&originHeight=1440&originWidth=2290&originalType=binary&ratio=2&rotation=0&showTitle=false&size=485716&status=done&style=none&taskId=u439859af-2f2e-4b5c-a543-21aeb29cc97&title=&width=1145)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681044217347-3b144b0a-d7fd-4496-9d41-2be642c99ec2.png#averageHue=%23e8e9e8&clientId=uaf9dbe52-c8e2-4&from=paste&height=271&id=ufafa62c2&originHeight=542&originWidth=1586&originalType=binary&ratio=2&rotation=0&showTitle=false&size=292770&status=done&style=none&taskId=ub21404b7-0cec-4e3b-ae43-3c3dbcfad78&title=&width=793)
# 元空间OOM
## JVM参数
```bash
-Xms640M
-Xmx640M
-XX:MaxMetaspaceSize=32M
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/home/<USER>/jvm-metaspace-oom.hprof
```
## OOM代码
```java
@RestController
public class MetaspaceOOM {

    @GetMapping("metaspace-oom")
    public void metaspace() throws IOException, CannotCompileException {
        List<Class<?>> classes = new ArrayList<Class<?>>();
        ClassPool pool = ClassPool.getDefault();
        while (true) {
            CtClass cc = pool.makeClass("com.kk0.Generated" + UUID.randomUUID().toString().replace("-", ""));
            byte[] code = cc.toBytecode();
            Class<?> clazz = cc.toClass();
            classes.add(clazz);
        }
    }
}
```
## 分析
### java.lang.OutOfMemoryError: Metaspace
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681045778334-462dbdc4-537a-4299-a535-c692e385c802.png#averageHue=%23222326&clientId=ue9538b66-a34e-4&from=paste&height=136&id=u191a8d73&originHeight=272&originWidth=2280&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57704&status=done&style=none&taskId=ud18846b7-ea43-4e3d-9484-6f3fa2bda27&title=&width=1140)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681045742584-0f483cd3-a702-49ad-8ccc-0bba6ee89efb.png#averageHue=%232c2e32&clientId=ue9538b66-a34e-4&from=paste&height=476&id=u3ded877b&originHeight=952&originWidth=3244&originalType=binary&ratio=2&rotation=0&showTitle=false&size=67268&status=done&style=none&taskId=ub53fc9c8-d256-47d6-9be0-e5b7eaf702c&title=&width=1622)
# 栈OOM
## JVM参数
```bash
-Xms640M
-Xmx640M
-Xss1024k
-XX:MaxMetaspaceSize=320M
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/home/<USER>/jvm-stack-oom.hprof
```
## OOM代码
```java
/**
* -Xms640M
* -Xmx640M
* -Xss1024k
* -XX:MaxMetaspaceSize=320M
* -XX:+HeapDumpOnOutOfMemoryError
* -XX:HeapDumpPath=/home/<USER>/jvm-stack-oom.hprof
* <p>
* curl 127.0.0.1:5020/stack-oom
*/
@RestController
public class StackOverflowExample {

    @GetMapping("stack-oom")
    public void stackOom() {
        recursiveMethod(1);
    }

    public void recursiveMethod(long i) {
        System.out.println(i);
        recursiveMethod(++i);
    }
}
```
## 启动报错
> The stack size specified is too small, Specify at least 640k
> Error: Could not create the Java Virtual Machine.
> Error: A fatal exception has occurred. Program will exit.
> 不同jdk Xss值不同

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681130457147-7d1c5907-07dd-44b9-b3a1-cd0c223ec1f7.png#averageHue=%23383b40&clientId=ue9538b66-a34e-4&from=paste&height=34&id=ud735176f&originHeight=68&originWidth=1490&originalType=binary&ratio=2&rotation=0&showTitle=false&size=13673&status=done&style=none&taskId=ua4bf18b9-66ff-4c74-b4c9-5d646b9baa9&title=&width=745)
## 分析
### java.lang.StackOverflowError: null
> 递归方法栈太深， 达到-Xss1024k。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681130792807-63dda329-deec-443a-9c63-b0dd19d0bfc0.png#averageHue=%23212225&clientId=ue9538b66-a34e-4&from=paste&height=420&id=uee4383fd&originHeight=840&originWidth=2250&originalType=binary&ratio=2&rotation=0&showTitle=false&size=139485&status=done&style=none&taskId=u37ec1c9c-71db-4bb3-8364-3f455ac9897&title=&width=1125)
# 线程创建太多OOM
## JVM参数
```bash
-Xms64M
-Xmx64M
-Xss1024k
-XX:MaxMetaspaceSize=1g
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/home/<USER>/jvm-thread-oom.hprof
```
## OOM代码
```java
/**
 * <AUTHOR>
 * date:  2023-04-10 15:11
 * <p>
 * -Xms64M
 * -Xmx64M
 * -Xss1024k
 * -XX:MaxMetaspaceSize=1g
 * -XX:+HeapDumpOnOutOfMemoryError
 * -XX:HeapDumpPath=/home/<USER>/jvm-thread-oom.hprof
 * <p>
 * curl 127.0.0.1:5020/thread-oom
 */
@RestController
public class TheadCreateTooMany {

    @GetMapping("thread-oom")
    public void threadCreate() throws InterruptedException {

        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            new Thread(() -> {
                try {
                    Thread.sleep(Long.MAX_VALUE);
                    String id = IdUtil.fastSimpleUUID();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }, "create--")
                    .start();

        }
    }
}
```
## 分析
### java.lang.OutOfMemoryError: unable to create new native thread
> 无法再创建新的线程。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681131427738-4c36e1ed-b867-4f61-b1c6-7d0c4334adb6.png#averageHue=%2325282f&clientId=ue9538b66-a34e-4&from=paste&height=229&id=u391859dc&originHeight=458&originWidth=2188&originalType=binary&ratio=2&rotation=0&showTitle=false&size=133952&status=done&style=none&taskId=ucbf269e8-9303-4c63-8ffd-f9a29027a15&title=&width=1094)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681131401209-eb64bae3-54c0-46ef-8fa1-c7a9992db376.png#averageHue=%232d3033&clientId=ue9538b66-a34e-4&from=paste&height=330&id=u49ece5f5&originHeight=660&originWidth=3254&originalType=binary&ratio=2&rotation=0&showTitle=false&size=58439&status=done&style=none&taskId=u0196e7a7-34d4-4308-8745-f1f2ab46209&title=&width=1627)
# 直接内存（对外内存）OOM
## JVM参数
```bash
-Xms64M
-Xmx64M
-Xss1024k
-XX:MaxDirectMemorySize=64M
-XX:MaxMetaspaceSize=1g
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/home/<USER>/jvm-direct-memory-oom.hprof
```
## OOM代码
```java
/**
* -Xms64M
* -Xmx64M
* -Xss1024k
* -XX:MaxMetaspaceSize=1g
* -XX:MaxDirectMemorySize=64M
* -XX:+HeapDumpOnOutOfMemoryError
* -XX:HeapDumpPath=/home/<USER>/jvm-direct-memory-oom.hprof
* <p>
* curl 127.0.0.1:5020/direct-memory-oom
*/
@RestController
public class DirectMemoryOOM {
    private static final int ONE_MB = 1024 * 1024 * 1024;

    /**
    * java.lang.OutOfMemoryError: Direct buffer memory
    * 默认情况下，直接内存的大小等于最大堆内存大小（-Xmx 参数）
    * 当 JVM 分配的直接内存超过了系统的可用内存时，就会发生直接内存溢出。
    * 分配超过64M 就OOM
    * 小于64M 可以分配,直到直到超出系统可用内存.
    */
    @GetMapping("direct-memory-oom")
    public void directMemory() {
        while (true) {
            ByteBuffer buffer = ByteBuffer.allocateDirect(ONE_MB);
            System.out.println("1");
        }
    }
}
```
## 分析
### java.lang.OutOfMemoryError: Direct buffer memory
> 默认情况下，直接内存的大小等于最大堆内存大小（-Xmx 参数） 
> 当 JVM 分配的直接内存超过了最大直接内存时，就会发生直接内存溢出。
> 如上jvm配置分配超过64M 就OOM
> 小于64M 可以分配。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681131059047-5da647b7-1f45-4d08-af1c-132a1d71b096.png#averageHue=%23242629&clientId=ue9538b66-a34e-4&from=paste&height=302&id=ucc1dd766&originHeight=604&originWidth=2192&originalType=binary&ratio=2&rotation=0&showTitle=false&size=171005&status=done&style=none&taskId=u0f17e0f1-cb00-406b-baf0-3eca5c215ff&title=&width=1096)
