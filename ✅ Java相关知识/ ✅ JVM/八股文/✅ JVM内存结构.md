[https://cloud.tencent.com/developer/article/1698363](https://cloud.tencent.com/developer/article/1698363)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612706997397-0c131871-c09d-4cd5-88ec-b3dadfd008c5.png#averageHue=%23ecf6f8&height=404&id=L0B7W&originHeight=807&originWidth=1464&originalType=binary&ratio=1&rotation=0&showTitle=false&size=349960&status=done&style=none&title=&width=732)
# 区域划分


![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190749-9aa659df-ce2c-426b-9511-344f64032bb8.png#averageHue=%2387b0d9&height=568&id=F3HPl&originHeight=888&originWidth=1280&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=819)
## 程序计数器 - 线程私有

- 占用内存很小，不存在GC
- 正在执行的虚拟机字节码指令
- 下一条指令的地址
- **分支、循环、跳转、异常处理、线程恢复**等基础功能都需要依赖计数器完成
## 虚拟机栈Java Stack - 线程私有

- 每个线程都有一个栈(私有), **生命周期和线程一样**
- -Xss1024k
- 每个方法在执行时都会创建一个栈帧(Stack Frame)
- 用于存储**局部变量、实例方法、引用类型变量、操作数栈、动态链接、方法出口**等信息
   - 如果线程请求的**栈深度大于**虚拟机所允许的深度，将抛出**StackOverflowError**异常；
   - 如果虚拟机栈**动态扩展**时无法申请到足够的内存时会抛出**OutOfMemoryError**异常。
- 每一个方法从调用直至执行完成的过程，就对应着一个栈帧在虚拟机栈中入栈到出栈的过程。
- 栈是运行时单位，解决程序运行的问题, 如何运行, 运行时数据如何处理
- ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612706624255-4de8acaa-bf0e-46c5-afc4-106a82cffbb2.png#averageHue=%2376b8d6&height=335&id=AgZKt&originHeight=587&originWidth=831&originalType=binary&ratio=1&rotation=0&showTitle=false&size=192930&status=done&style=none&title=&width=474)
## 本地方法栈 （Native Method Stacks）

- 保存被**native修饰**的方法，即非java方法。
   - Unsafe类，String.intern()
   - 

- 会抛出**StackOverflowError和OutOfMemoryError异常**。
## 方法区 Method Area - 线程共享

- 存储被 JVM 加载的**类信息、运行时常量池、静态变量、JIT即时编译器编译后的代码**等数据.
- 内存不连续， 线程共享， 可gc
- 当无法再扩展时会抛出OutOfMemoryError异常
- jdk8元空间（非堆）实现，jdk7存放在永久代
- ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612706367793-83c9b948-ec0d-46a4-bf7c-4de3b98d16b4.png#averageHue=%23a1cdf5&height=252&id=daiqT&originHeight=503&originWidth=819&originalType=binary&ratio=1&rotation=0&showTitle=false&size=147611&status=done&style=none&title=&width=409.5)
## 堆Java Heap - 线程共享

- 存储**new创建的对象和数组**
- **线程共享， 可gc**
- **占用内存最大， 内存不连续， 当没有内存创建对象时， 就会抛出OutOfMemoryError异常**
# Java堆的结构

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190784-fecee670-7ed2-43bf-9bd3-e7861bf4a86c.png#averageHue=%23eff8f0&height=471&id=bTp2D&originHeight=694&originWidth=1234&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=837)
## 年轻代 Young 

- 分为**Eden区, From Survivor区(S0)和 To Survivor区(S1)** , 默认比例** 8:1:1**
   - -XX:SurvivorRatio=8   2个Survivor区：1个Eden区比例为2：8
- 默认年轻代占堆的**1/3，-Xmn1g**
   - **年轻代老年代比例 **–XX:NewRatio=4，年轻代/老年代比例为1：4，年轻代/整个堆比例为1：5
- Eden区内存不足时会触发Minor GC
- 垃圾收集Minor GC采用**复制算法**
### Minor GC

- 1. Eden 、 Survivor From  存活对象复制到 Survivor To，年龄+1, 年龄达到老年的标准(15),  就晋升到老年代,  Servicor To内存不够分配，也晋升到老年代。
- 2. 清空 Eden 、 Survivor From中的对象
- 3. Survivor To 和 Survivor F rom 互换
- 4. 继续下一轮gc, 回到1
## 老年代 Old

- 默认占堆**2/3**
- **大对象（需要大量连续空间的对象，如数组，字符串）**
   - -XX:PetenureSizeThreshold=32M，超过这个大小的对象直接分配到老年代
- **生命周期长的对象（年龄达到阈值）**
   - -XX:MaxTenuringThreshold=15
- 内存不足 触发 Major GC
- Major GC 采用**标记-清除算法**
### Major GC

- 扫描一次老年代
- 标记存活的对象
- **清除没有标记**的对象
## 元空间 Metaspace jdk1.8

- 元空间并不在虚拟机中，而是**使用本地内存**。
- -XX:MetaspaceSize：初始空间大小
- -XX:MaxMetaspaceSize：最大空间，默认是没有限制的。
- -XX：MinMetaspaceFreeRatio：在GC之后，**最小**的Metaspace剩余空间容量的百分比，减少为class metadata分配空间导致的垃圾收集。
- -XX:MaxMetaspaceFreeRatio： 在GC之后，**最大**的Metaspace剩余空间容量的百分比，减少为class metadata释放空间导致的垃圾收集。
# 对象动态年龄计算
> hostpot在遍历所有对象时，按照年龄从小到大对其所占用的大小进行排序，当某个年龄的对象大小总和大于survivor区的一半时，取这个年龄和MaxTenuringThreshold中更小的一个值，作为新的晋升年龄阈值

## 为什么引入动态年龄计算

- 若固定按MaxTenuringThreshold值为晋升条件
   - 该值设置过大，本该晋升的对象一直停留在Survivor区，直到Survivor区溢出，一旦溢出发生，Eden和Survivor中的对象将不再依据年龄晋升，而是全部晋升到老年代，这样对象老化的机制就失效了。
   - 该值设置过小，过早晋升，即对象不能在年轻代充分被回收，大量年轻对象被晋升到老年代，老年代空间迅速增长，引发频繁的Major GC，分代回收失去了意义，严重影响GC性能。
# 内存分配策略

- 对象优先进入Eden区
- 大对象直接进入老年代
- 长期存活的对象进入老年代
- 动态对象年龄判断
- 空间分配担保
   - 在发生 Minor GC 之前，虚拟机先检查**老年代最大可用的连续空间**是否大于**新生代所有对象总空间**，
如果条件成立的话，那么 Minor GC 可以确认是安全的。
   - 如果不成立的话虚拟机会查看 **HandlePromotionFailure** 设置值是否允许担保失败，
如果允许那么就会继续检查**老年代最大可用的连续空间**是否大于**历次晋升到老年代对象的平均大小**，
如果大于，将尝试着进行一次 Minor GC；
如果小于，或者 HandlePromotionFailure 设置不允许冒险，那么就要进行一次 Full GC。
# 逃逸分析 Escape Analysis

- 一种可以有效减少 Java 程序中**同步负载**和**内存堆分配压力**的跨函数全局数据流分析算法。
- 通过逃逸分析，Java Hotspot 编译器能够分析出**一个新的对象的引用的使用范围**从而决定是否要将这个对象分配到堆上。
- 分析对象的动态作用域
   - 逃逸
      - 对象被外部对象使用
   - 没逃逸
      - 一个对象在方法中被定义后, 只在方法内部被使用, 没有被外部方法使用, 没有return
- Jdk6之后默认开启** -XX:+DoEscapeAnalysis**
- 编译器对代码的优化
   - 同步消除/锁消除
      - 一个对象只被一个线程访问, 就不同步, 不加锁，如使用synchronized
   - 标量替换
      - 标量: 不能再分解的数据
      - 聚合量: 包括其他聚合量和标量
      - 通过逃逸分析确定**对象不会被外部使用**, 就将对象分解成一个个标量(成员变量), JVM不会创建该对象, 分配在栈帧或寄存器上
      - 开启标量替代	-XX:+EliminateAllocations
      - 查看标量替代情况	-XX:+PrintEliminateAllocations
   - 栈上分配
      - 正常对象时分配在堆上的, 对象没有被引用会触发GC，对象较多时, GC压力大，影响性能。
      - 通过逃逸分析, 确定对象不被外部使用,  **通过标量替代将对象分解, 分配在栈上**，**这些对象所占的内存空间在出栈后会被销毁**，减少GC
# TLAB - Thread Local Allocation Buffer

- 对Eden区继续划分, JVM为每个线程分配一个私有缓存区域。
- 多线程同时分配内存时, 使用TLAB可以避免线程安全问题, 还能提高分配速度。
- 为什么要有TLAB?
   - 堆区线程共享, 对象创建在JVM中非常频繁, 并发环境划分内存空间线程不安全
   - 为避免多线程操作同一地址, 需加锁, 影响分配速度
- 开启TLAB		-XX:UseTLAB
- 默认TLAB空间只占Eden区的1%	-XX:TLABWasteTargetPercent 设置百分比
- TLAB分配失败, 就加锁保证原子性, 在Eden区分配内存。
