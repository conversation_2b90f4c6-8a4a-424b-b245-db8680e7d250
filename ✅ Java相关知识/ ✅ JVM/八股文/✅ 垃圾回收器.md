![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678888905351-0ec63656-4cf6-4adb-ade4-51fcaf21b3a8.png#averageHue=%23d8b9a2&clientId=ud37daa7c-1dfd-4&from=paste&id=u2dd4ba73&originHeight=391&originWidth=646&originalType=binary&ratio=2&rotation=0&showTitle=false&size=101284&status=done&style=none&taskId=u92a51d73-f54a-4217-8ef0-94c30307ebf&title=)
# Concurrent Mark Sweep（CMS）
> 并发标记-清除算法
> 老年代
> -XX:UseConcMarkSweepGC

## 流程

- 初始标记
   - 标记GC Roots可达的对象。会STW（除了GC线程，其他线程都会暂停）
- 并发标记
   - 从上一次标记可达的对象开始，进行GC Roots tracing，扫描所有可达的对象。
- 重新标记
   - 为了修正并发标记期间，因用户程序继续运行而导致标记产生变动的那一部分对象的标记记录。会STW
   - 用户程序运行时，标记可能错乱，变动，需重新标记
   - 比较耗时
- 并发清除
   - 和应用程序并发清除垃圾。
## 缺点

- 吞吐量低
- 无法处理浮点垃圾（并发清除阶段由于用户线程继续运行而产生的垃圾），浮点垃圾只能等待下一次GC。
- 标记清除会产生内存碎片，内存不连续，无法存储大对象。
# Garbage-First收集器（G1）
> 适合多核，大容量的服务
> 使用多个CPU（CPU或者CPU核心）来缩短STW的停顿时间，它满足**短时间停顿**的同时达到一个**高的吞吐量**。
> JDK9开始， 默认G1
> G1把空间划分成很多个块（Region），堆比较大时采用复制算法，碎片化问题不严重。整体上数据标记整理算法，Region之间数据复制算法。
> G1需要记忆集（卡表）来记录年轻代和老年代之间的引用关系，需要占用大量内存，可能达到整个堆内存的20%，甚至更多。
> 小内存CMS
> 大内存G1	6G-8G

# ParNew 收集器
> 用于年轻代
> Serial 收集器的多线程版本
> 默认线程数和cpu核心数一样
> -XX:ParallelGCThreads设置线程数

# Serial 收集器
> 串行单线程，年轻代
> GC时STW

# Serial Old 收集器
> 老年代

# Parallel Scavenge 收集器
> 多线程，年轻代

# Parallel Old 收集器
> 多线程，老年代

