# 如何确认什么是垃圾
## 引用计数法
> 给对象添加一个引用计数器。对象被引用，计数器+1，离开引用-1，为0时可回收。
> 但是难以解决循环引用问题。

## 可达性分析
> 通过**GC Roots**对象为起点搜索, GC Roots到一个对象之间没有可达路径, 那么这个对象是不可达的
> 一个对象经过两次标记是不可达, 就可以回收。
> ![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190752-8d0e14d7-d600-4639-b722-258ef412b423.png#averageHue=%230c0e0b&height=491&id=pQfA7&originHeight=873&originWidth=1248&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=702)

# GC Roots对象有哪些
```java
1. 虚拟机栈(栈帧中的本地变量表)中引用的对象  
public class Test {
    public static  void main(String[] args) {
        // a 是栈帧中的本地变量
        Test a = new Test();
        // a是GC Root, a为null就与new Test()断开联系, new Test()会被回收
        a = null;
    }
}

2. 方法区中类的静态属性引用的对象
public class Test {
	// s 是类静态属性引用
    public static Test s;
    public static  void main(String[] args) {
        // 创建一个对象
        Test a = new Test();
        // 给s赋予一直新对象
        a.s = new Test();
        // a为null就与new Test()断开联系, 但是s指向的对象依然存在
        a = null;
    }
}

3. 方法区中常量引用的对象 
public class Test {
	// s是常量引用的对象, 指向Test, 
	public static final Test s = new Test();
    public static void main(String[] args) {
    	// a指向的对象为null为空被回收, 不影响s指向的对象
	    Test a = new Test();
	    a = null;
     }
}

4. 本地方法栈中 JNI(即一般说的 Native 方法) 引用的对象
Native方法调用的可能不是Java代码, 如C, C++

```
# GC发生区域分类
## Minor GC

- 从**年轻代**空间（包括 Eden 和 Survivor 区域）回收内存
- 复制算法
## Major GC

- **老年代**清理
- 标记-清除算法
## Full GC

- **Minor GC + Major GC**, 是清理整个堆空间—包括年轻代和老年代。
- 触发条件
   - 调用System.gc时
   - 老年代空间不足。
   - 通过Minor GC后进入老年代的平均大小大于老年代的可用内存。
   - 由Eden区、Survivor From区向Survivor To区复制时，对象大小大于Survivor To可能内存，则把对象晋升到老年代，但老年代的可用内存小于该对象大小。（空间分配担保失败）
# 回收算法
## 复制算法 copying
> 新生代 8 1 1 
> 
> 当对象在 Survivor 区躲过一次 GC 后，其年龄就会+1。默认情况下年龄到达 15 的对象会被移到老生代中
> 内存碎片少, 会浪费内存, 新生代 浪费1/10的内存
> ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678888672087-747a015f-e52e-4158-b409-7887437b195f.png#averageHue=%23f4f3ee&clientId=u756a420a-7077-4&from=paste&id=u9e705c7d&originHeight=223&originWidth=684&originalType=binary&ratio=2&rotation=0&showTitle=false&size=75802&status=done&style=none&taskId=uebbf7132-ac1b-488d-8340-548a4fd6803&title=)

## 标记-清除算法 Mark-Sweep
> 老年代
> 
> 1. 标记存活的对象
> 2. 清除未被标记的对象
会出现大量不连续的内存碎片, 大对象可能没地方存放
> 
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678888691800-187d70d3-3297-458b-892f-68fc649fc230.png#averageHue=%23efefef&clientId=u756a420a-7077-4&from=paste&id=u0e80ccf4&originHeight=263&originWidth=607&originalType=binary&ratio=2&rotation=0&showTitle=false&size=46766&status=done&style=none&taskId=u41908271-db50-41a9-80c2-b3f3da708ae&title=)

## 标记-整理算法 Mark-Compact
> 标记后不是清理对象，而是将存活对象移向内存的一端。然后清除端边界外的对象
> ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678888710108-8583d953-d6cf-4f90-8f70-d7614ac21be7.png#averageHue=%23e1e1e1&clientId=u756a420a-7077-4&from=paste&id=u70806bbf&originHeight=231&originWidth=638&originalType=binary&ratio=2&rotation=0&showTitle=false&size=53934&status=done&style=none&taskId=u45c961e6-459a-4566-bccd-c848187cad0&title=)

## 分代算法
> jvm采用的算法
> 新生代 复制算法
> 老年代 标记-清除算法 或标记-整理算法

