# 类加载过程
[https://segmentfault.com/a/1190000037574626](https://segmentfault.com/a/1190000037574626)
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190804-f9f85af0-cba6-4a1c-baa5-4ba3bceb85a2.png#averageHue=%23c2d7ec&height=375&id=wHJzv&originHeight=682&originWidth=1280&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=704)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678879428164-ff0f741d-9ea6-4dfb-babc-97586ebb8be3.png#averageHue=%23d6c17c&clientId=u3db3c6ba-c1b6-4&from=paste&height=559&id=ua8a65626&originHeight=1118&originWidth=1460&originalType=binary&ratio=2&rotation=0&showTitle=false&size=292016&status=done&style=none&taskId=u3d2367d6-7b58-4394-bb3c-1f966fa7e23&title=&width=730)
## 1. 加载

- 从jar, war加载类(.java文件 -> .class)到内存(生成java.lang.Class对象（一个类只有一个Class对象）)
- 加载二进制数据到内存 —> 映射成jvm能识别的结构 —> 在内存中生成class文件
## 2. Linking链接:
> 将上面创建好的class类合并至Java虚拟机中，使之能够执行的过程，分三个阶段：

- 验证
   - 验证class字节流是否符合JVM规范	
- 准备
   - 给静态变量分配内存并设置默认初始值 int a = 123, a默认值为0。
   - final修饰的静态变量不会设置默认值，在编译阶段就设置了。
- 解析
   - 指虚拟机将常量池中的**符号引用**替换为**直接引用**的过程。
   - 符号引用是class文件中CONSTANT_Class_info, CONSTANT_Field_info, CONSTANT_Method_info等类型的常量
## 3. 初始

- 变量设置初始值, int a = 123, 在这一步设置a =123
- 初始化就是执行类的构造器方法init()的过程。这个方法不需要定义，是javac编译器自动收集类中所有类变量的赋值动作和静态代码块中的语句合并来的。 
- 若该类具有父类，jvm会保证父类的init先执行，然后在执行子类的init。
## 4. 使用
## 5. 卸载
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1612703877026-934efea8-1392-459b-b7b4-6000938a6271.png#averageHue=%23f1ea8a&height=220&id=s8scR&originHeight=220&originWidth=623&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=623)
# 类加载器


![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190820-593a6d0c-1c4e-4d6b-b907-4cbc4037324f.png#averageHue=%23fdfdf9&height=362&id=zewn8&originHeight=857&originWidth=1226&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=518)
## 类加载实现
### 1. 全盘负责
> 加载一个类，它的依赖和引用也负责一起加载

### 2. 父类委托
> 加载一个类时，先让父加载器加载，父加载器加载不了，再往下给子加载器加载

### 3. 缓存机制
> 加载过的Class会缓存起来，使用时，先找缓存，缓存没有再重新加载，再写入缓存。


## 哪些类加载器

- 启动类加载器
   - C/C++语言实现的
   - 负责加载`**JAVA_HOME/jre/lib/rt.jar**`**、**`**resources.jar**`**、**`**sun.boot.class.path**`
   - 只加载包名为：**java、javax、sun**开头的类
- 扩展类加载器
   - Java编写，由`**sun.misc.Launcher$ExtClassLoader**`实现
   - 负责加载 JAVA_HOME\lib\ext， `java.ext.dirs`目录中加载类库
- 应用程序类加载器
   - Java语言编写，由`**sun.misc.Launcher$AppClassLoader**`实现    
   - 负责加载 用户classpath下的（程序员编写的类）、 `系统属性java.class.path`指定路径下的类库
   - `**ClassLoader#getSystemClassLoader()**`获取并操作这个加载器
- 自定义类加载器
   - 实现： 继承`**java.lang.ClassLoader**`类，重写**findClass()**方法
## 获取Classloader

```java
public abstract class ClassLoader {}

// 方式一：获取当前类的 ClassLoader
clazz.getClassLoader();
// 方式二：获取当前线程上下文的 ClassLoader
ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
// 方式三：获取系统的 ClassLoader
ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
// 方式四：获取调用者的 ClassLoader
DriverManager.getCallerClassLoader();
```
# 双亲委派机制

- 当一个类加载器收到一个类加载请求, 它首先不会自己去加载, 而是**把请求委派给父类**加载器, **父类加载器加载不了, 子类才会尝试去加载**
- **组合模式**
- 如果子类加载失败就会抛出`**ClassNotFoundException**`异常



![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611285190726-38a57f3d-bfb1-4e3f-8551-2ae3da7db4bf.png#averageHue=%23edecea&height=368&id=n8YBv&originHeight=368&originWidth=923&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=923)
## 优点
> 防止内存出现多份相同的字节码文件，安全，类不容易被篡改。


# 破坏双亲委派的情况
### SPI接口 Service Provider Interface

- 常见的 SPI 有 JDBC、JNDI等， 存在于rt.jar包中， 
- 要Bootstrap类加载器来加载， 但是 Bootstrap类加载器无法加载SPI的实现类
- **所以需要 线程上下文类加载器**（双亲委派模型的破坏者）

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1612705619978-960c83f3-da6b-4cd5-9087-c080d3d211ff.png#averageHue=%23f4f2f2&height=440&id=KTRRr&originHeight=440&originWidth=640&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=640)
