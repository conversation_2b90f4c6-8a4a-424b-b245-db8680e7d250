# 强引用 Strong
> 当一个对象被强引用变量所引用时，垃圾回收器不会回收该对象，即使当前内存空间不足，也不会回收，只有当该对象的强引用都被释放，才会被回收。

```java
Object obj = new Object();

// gc 会回收
obj = null;
```
# 软引用 Soft
> 当内存不足时，垃圾回收器会收集软引用对象所指向的对象，内存不足时，垃圾回收器会回收这些对象。回收之后，内存再不足，就会OOM。
> 当内存充足时，垃圾回收器不会回收这些对象。

```java
Object obj = new Object();

SoftReference<Object> softRef = new SoftReference<>(obj);

// 获取对象，obj被回收时返回null
Object o = softRef.get();

// gc
softRef.clear();

```
```java
public class SoftReference<T> extends Reference<T> {

}
```
# 弱引用 Weak
> 当gc时，会立即回收弱引用对象所指向的对象。不管内存是否充足。

```java
Object obj = new Object();
WeakReference<Object> weakRef = new WeakReference<>(obj);
Object o = weakRef.get();
```
