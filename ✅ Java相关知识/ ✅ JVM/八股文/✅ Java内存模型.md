# 概念
> JMM  Java Memory Model    是一个规定
> 规定所有变量都存储在主内存中，全局变量（实例变量、静态变量）
> 每个线程都有自己的工作内存，**工作内存保存线程用到的变量和主内存的副本拷贝，线程对变量的操作只能在工作内存。**
> **线程不能直接操作主内存中的变量**。
> 各个线程的工作内存互相隔离。
> ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612959355143-89a05df4-a815-4e89-8905-c8dea3122cf4.png#averageHue=%23f4d9d0&height=350&id=Q1bkx&originHeight=350&originWidth=860&originalType=binary&ratio=1&rotation=0&showTitle=false&size=73221&status=done&style=none&title=&width=860)

# 多线程并发三大特性
### 原子性
> 操作不可再分割， 最小单位。
> 一个操作不可被中断，要么全部执行成功，要么全部失败。
> 多线程并发下，如果多个线程同时对同一个变量进行修改，就可能破坏这个变量的原子性，引发数据不一致问题。

- i++；  不是原子性  包括两个操作， i+1, 再赋值i
#### 保证原子性方式
> 加锁。

- synchronized关键字
- Lock
- Atomic类
### 可见性
> 一个线程的对共享变量进行修改， 其他线程能够like看到这个修改。
> 多线程并发情况下，由于CPU缓存的存在，一个线程修改了某个共享变量后，并不一定能让其他线程立即看到这个修改。

- volatile  保证可见性。
   - volatile修改的变量， **一个线程在工作内存对共享变量进行修改， 会立刻刷新到主内存**， 其他线程的工作内存的变量就立即失效， 重新读取主内存的最新的值。
- final也保证可见性。
   - final修饰的字段，一旦初始化完成，如果没有对象逸出（指对象为初始化完成就可以被别的线程使用），那么对于其他线程都是可见的。
- synchronized也保证可见性。
   - 原理： 在执行完，**进入unlock之前，必须将共享变量同步到主内存中**。
#### MESI CPU缓存一致性协议
##### 为什么要有MESI？
> CPU每个核和内存之间增加了一个高速缓存，在多线程情况下，就会出现缓存不一致的问题。

> MESI 就是解决缓存不一致而出现的协议，CPU和内存数据交换要遵守MESI协议。

![CPU多核缓存架构](https://cdn.nlark.com/yuque/0/2023/png/684952/1678800154823-8dcb9388-e07b-41f5-b9ce-723004a3679f.png#averageHue=%23d7df7d&clientId=u82d9b188-acde-4&from=paste&height=586&id=u05975d97&originHeight=1172&originWidth=1130&originalType=binary&ratio=2&rotation=0&showTitle=true&size=289068&status=done&style=none&taskId=ue28161ec-0aec-4ab2-b6d7-15e07f302ab&title=CPU%E5%A4%9A%E6%A0%B8%E7%BC%93%E5%AD%98%E6%9E%B6%E6%9E%84&width=565 "CPU多核缓存架构")
##### 缓存行Cache Line四个状态
> MESI协议用以保证多个CPU缓存中共享数据的一致性，定义了缓存行Cache Line四个状态

1. M 修改 Modify
   - 该行数据有效，数据被修改了，和内存中的数据不一致，数据只能存在于本缓冲区中。
2. E 独享 Exclusive
   - 这行数据有效，数据和内存中的数据一致，数据只存在于本Cache中。
3. S 共享 Shared
   - 这行数据有效，数据和内存中的数据一致，数据存在于很多Cache中。
4. I 无效 Invalid
   - 这行数据无效

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678800427762-fd418a70-3932-451d-85fe-469cd20ac628.png#averageHue=%23e9e9e9&clientId=u82d9b188-acde-4&from=paste&height=382&id=u5f5721ab&originHeight=764&originWidth=1356&originalType=binary&ratio=2&rotation=0&showTitle=false&size=224775&status=done&style=none&taskId=u8dec612a-43ec-4ec2-9237-d801679347b&title=&width=678)
##### MESI状态之间的迁移
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678800997292-ad1656e0-cea7-4642-a6b3-aa8be0c41796.png#averageHue=%23f7f9f7&clientId=u82d9b188-acde-4&from=paste&height=678&id=ud6a7fbba&originHeight=1356&originWidth=2090&originalType=binary&ratio=2&rotation=0&showTitle=false&size=991188&status=done&style=none&taskId=u81e3171d-1ba8-401d-a6ee-e9e4813b3ab&title=&width=1045)

- 当前状态是Modified
   - 本地内核**读**取本地缓存中的值(local read)：从缓存区中读取数据，状态不变，还是修改M
   - 本地内核**写**本地缓存中的值(local write)：从缓存区中修改数据，状态不变，还是修改M
   - 其它内核**读取**其他缓存中的值(remote read)：数据被写入内存，其他内存读取到最新数据，即为共享S
   - 其它内核**更改**其他缓存中的值(remote write)：数据被写入内存，其他内存读取到最新数据，并修改和提交，此缓存区的状态为无效I
- 当前状态是Exclusive
   - 内核读取本地缓存中的值(local read)：从缓存区中读取数据，状态不变，还是独占E
   - 本地内核写本地缓存中的值(local write)：从缓存区中修改数据，即为修改M
   - 其它内核读取其他缓存中的值(remote read)：数据被写入内存，其他内存读取到数据，即为共享S
   - 其它内核更改其他缓存中的值(remote write)：数据被写入内存，其他内存读取到数据，并修改提交，即为无效I
- 当前状态是Share
   - 内核读取本地缓存中的值(local read)：从缓存区中读取数据，状态不变，还是共享S
   - 本地内核写本地缓存中的值(local write)：在缓存区中修改数据，即为修改M
   - 其它内核读取其他缓存中的值(remote read)：数据被写入内存，其他内存读取数据，即为共享S
   - 其它内核更改其他缓存中的值(remote write)：数据被写入内存，其他内存读取数据，并修改提交，即为无效I
- 当前状态是Invalid
   -  内核读取本地缓存中的值(local read)：如果其他缓存里面没有这个值，状态即为独享E；如果其他缓存里有这个值，状态即为共享S
   - 本地内核写本地缓存中的值(local write)：在缓存区中修改数据，即为修改M
   - 其它内核读取其他缓存中的值(remote read)：其他核的操作与他无关，即为无效I
   - 其它内核更改其他缓存中的值(remote write)：其他核的操作与他无关，即为无效I
### 有序性
> 指令的执行顺序按照代码的先后顺序执行。
> 多线程并发下，指令的执行顺序可能被打乱，导致程序结构错误。

- synchronized
   - 原理是，**一个线程lock之后，必须unlock后**，其他线程才可以重新lock，使得被synchronized包住的代码块在多线程之间是**串行**执行的。
- volatile
   - 使用**内存屏障**达到**禁止指令重排序**，以保证有序性。
#### 指令重排序
##### 指定重排序发生在什么阶段？

1. **编译**阶段，字节码编译成机器指令码阶段。
2. CPU**运行**时，执行指令
##### as-if-serial语义
> 不管怎么重排序(编译器和处理器为了提高并行度),(单线程)。 即在单线程情况下，不能改变程序运行的结果。
> **程序的执行结果不能被改变**。编译器、runtime和处理器都必须遵守as-if-serial语义。

##### happens-before原则

1. 程序顺序原则：即在一个线程内必须保证语义串行性，也就是说按照代码顺序执行
2. 锁规则： **解锁(unlock)操作必然发生在后续的同一个锁的加锁(lock)之前**，也就是说，如果对于一个锁解锁后，再加锁，那么加锁的动作必须在解锁动作之后(同一个锁)
3. volatile规则： **volatile变量的写，先发生于读**，这保证了volatile变量的**可见性**，简单的理解就是，volatile变量在每次被线程访问时，都强迫从主内存中读该变量的值，而当该变量发生变化时，又会强迫将最新的值刷新到主内存，任何时刻，不同的线程总是能够看到该变量的最新值。
4. 线程启动原则： **线程的start()方法先于他的每一个动作**，即如果线程A在执行线程B的start方法之前修改了共享变量的值，那么当线程B执行了start方法之时，线程A对共享变量的修改对线程B可见。
5. 传递性：  A先于B，B先于C，那么A必然先于C
6. 线程终止原则：  **线程的所有操作先于线程的终结**。Thread.join()方法的作用就是等待当前执行的线程的终止。假设在线程B终止之前，修改了共享变量，线程A从线程B的join方法成功返回后，线程B对共享变量的修改将对线程A可见。
7. 线程中断规则：对线程 interrupt()方法的调用先行发生于被中断线程的代码检测到中断事件的发生
8. 对象终结规则：对象的构造函数执行，结束先于finalize()方法
#### 内存屏障
> CPU指令, 用于控制特定条件下的**重排序和内存可见性问题**, Java编译器会根据内存屏障的规则**禁止**重排序

##### 指令类型

1. LoadLoad 读读
2. StoreStore 写写
3. LoadStore 读写
4. StoreLocal 写读

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1678799615593-7dfdeb87-f13b-4738-938e-abd7b0b9324d.png#averageHue=%23f3f3f3&clientId=u82d9b188-acde-4&from=paste&height=195&id=uee830965&originHeight=260&originWidth=1230&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59379&status=done&style=none&taskId=uae47a073-b0ec-492e-a458-16b0ca4354d&title=&width=923)
# 八种内存交互操作
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612960439291-c85760e2-e6d9-4c96-806c-33feb5bfaf8d.png#averageHue=%23f4eee6&height=507&id=Mi4Cz&originHeight=507&originWidth=867&originalType=binary&ratio=1&rotation=0&showTitle=false&size=111416&status=done&style=none&title=&width=867)

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612960470439-a15bf392-5577-4e94-92c9-aa96358af9b8.png#averageHue=%23fefcfa&height=474&id=pAqtR&originHeight=474&originWidth=945&originalType=binary&ratio=1&rotation=0&showTitle=false&size=75620&status=done&style=none&title=&width=945)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612960635346-a6cf3f45-7b6c-431a-a4ab-f20bca81ca07.png#averageHue=%23fdfcf9&height=379&id=DSAkS&originHeight=379&originWidth=931&originalType=binary&ratio=1&rotation=0&showTitle=false&size=61620&status=done&style=none&title=&width=931)

# 
