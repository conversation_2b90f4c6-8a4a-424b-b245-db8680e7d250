# 作用范围
- 作用在类上
   - 类不能被继承

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679928283039-d338bf67-3e5e-44c4-bee6-557510d71a7b.png#averageHue=%232c2b2b&clientId=ua1d6a156-0c7d-4&from=paste&height=489&id=u099ca8e5&originHeight=978&originWidth=1362&originalType=binary&ratio=2&rotation=0&showTitle=false&size=148565&status=done&style=none&taskId=ub0534d8c-1452-45f2-8f85-bd399bd4318&title=&width=681)

- 作用在方法上
   - 方法不能被子类覆盖。
- 作用在变量上
   - 只能初始化一次，就不能再赋值，
   - 如基本变量不能被修改值；引用变量、集合等不能再赋值，但可以修改其内的元素。
- 作用在方法参数上
# 多线程情况
> 多线程情况下，final可以保证变量的可见性。

# 匿名内部类和final
> 匿名内部类使用了外部方法的局部变量，需要使用final修饰这个变量。
> 为什么？
> - **生命周期不同**：局部变量作为线程的私有数据，被存储在虚拟机栈的栈帧中，如或execute执行完，doWork未执行，局部变量可能被销毁了。加final修饰保证变量存在。
> - **数据不同步**：局部变量传递到内部类时，是拷贝一份数据到自己的构造函数中，若不加final修饰，内外部修改互不同步，造成脏数据。所以加final修饰保证可见性。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679927602975-3f69d7c9-7d7b-421b-862a-1fb1eca6bd8b.png#averageHue=%23ac803a&clientId=ua1d6a156-0c7d-4&from=paste&height=344&id=u4bbf3caa&originHeight=688&originWidth=1586&originalType=binary&ratio=2&rotation=0&showTitle=false&size=95120&status=done&style=none&taskId=u4b78fdb8-63b7-4387-aae1-74d9f0cb342&title=&width=793)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679927639500-ee030a24-cac3-4c34-a421-65aa9d259f21.png#averageHue=%232c2c2b&clientId=ua1d6a156-0c7d-4&from=paste&height=344&id=u7815fbec&originHeight=688&originWidth=1306&originalType=binary&ratio=2&rotation=0&showTitle=false&size=72634&status=done&style=none&taskId=u95a99e5d-12c7-493b-a966-7002cff21b7&title=&width=653)

