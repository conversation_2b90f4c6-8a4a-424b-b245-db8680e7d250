# 使用和同步原理
## 代码块
> 锁对象可自定义，可重入。

```java
private final Byte[] bytes = new Byte[0];

public void method() {
    synchronized (bytes) {
        log.info("进入synchronized代码块锁。");
        synchronized (bytes) {
            log.info("再次进入synchronized代码块锁。");
            synchronized (bytes) {
                log.info("再次再次进入synchronized代码块锁。");
            }
        }
    }
}
```
```java
21:59:20.860 [main] INFO com.coding.lock.SynchronizedDemo - 进入synchronized代码块锁。
21:59:20.862 [main] INFO com.coding.lock.SynchronizedDemo - 再次进入synchronized代码块锁。
21:59:20.862 [main] INFO com.coding.lock.SynchronizedDemo - 再次再次进入synchronized代码块锁。
```
### 字节码实现
```java
javac SynchronizedDemo2.java 
javap -c -s -v -l  SynchronizedDemo2.class
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679842503564-d9749bec-1a77-4ff7-8353-d82609e69f6b.png#averageHue=%232e2e2e&clientId=ue237287c-2ea9-4&from=paste&height=490&id=iT6SF&originHeight=980&originWidth=1732&originalType=binary&ratio=2&rotation=0&showTitle=false&size=98887&status=done&style=none&taskId=ud01158a1-6925-4673-b83d-6e0cec68b64&title=&width=866)
> synchronized使用monitorenter和monitorexit来控制代码块同步。
> 执行monitorenter指令代码线程获取到锁，即监视器Monitor的所有权。
> 在Java虚拟机HotSpot中，Monitor是基于C++实现，由ObjectMonitor实现，每个对象都内置一个ObjectMonitor对象。
> wait、notify等方法也依赖monitor对象。

> 执行monitorenter时，会尝试获取对象的锁，若锁的计数器为0则表示锁可以被获取，获取后将锁计数器设置为1.
> 执行monitorexit后，将锁计数器设为0，表示锁被释放，若获取对象锁失败，则当前线程阻塞等待，直到锁被另外一个线程释放为止。

#### 为什么要有两个monitorexit
> 一个是正常解锁，一个是异常解锁。

## 实例方法
> 锁当前实例对象。
> 一个类可以有很多个实例对象，就是new。

```java
    public synchronized void instanceMethod() {
        log.info("进入synchronized实例方法锁。");
    }
```
### 字节码实现
> 修饰实例方法和静态方法，没有monitorenter和monitorexit，
> 而是通过flags，ACC_SYNCHRONIZED，JVM通过ACC_SYNCHRONIZED判断是不是一个同步方法。

## ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679843217198-59a8aafc-85ec-43dc-aea1-6e6fbfceff56.png#averageHue=%232f2f2f&clientId=ue237287c-2ea9-4&from=paste&height=273&id=uf4a7f081&originHeight=546&originWidth=1750&originalType=binary&ratio=2&rotation=0&showTitle=false&size=81176&status=done&style=none&taskId=u61f61404-adaf-45eb-87a1-6ae99685e7b&title=&width=875)
## 静态方法
> 锁当前class对象。
> 一个类只有一个class对象。

```java
    public synchronized static void staticMethod() {
        log.info("进入synchronized静态方法锁。");
    }
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679843239934-23c9cc50-609d-4b7e-b6b5-68cbe8f3ac8f.png#averageHue=%232f2f2f&clientId=ue237287c-2ea9-4&from=paste&height=282&id=uecfd6039&originHeight=564&originWidth=1790&originalType=binary&ratio=2&rotation=0&showTitle=false&size=80695&status=done&style=none&taskId=u5a0a0d9a-d952-42bb-9886-f5e9baf0e75&title=&width=895)
# 同步概念
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679843466646-d8c3f464-099a-4fbd-acd6-9970322b7120.png#averageHue=%23fafcfa&clientId=ue237287c-2ea9-4&from=paste&height=421&id=Jk7Ud&originHeight=842&originWidth=1646&originalType=binary&ratio=2&rotation=0&showTitle=false&size=348638&status=done&style=none&taskId=u2d910995-f161-4a9f-a56f-d28b16e09c3&title=&width=823)
## 1. Java对象头
> 两种对象头
> - 数组类型
> - 非数组类型

### Mark Word (标记字段)
> 存储对象的HashCode,  分代年龄,  锁状态，锁标志位信息。

### Klass Pointer (类型指针)
> 类型指针指向对象的Class对象。

### 数组长度
> 数组对象才有。

## 2. 监视器Monitor
> 每个对象都有一个Monitor，当一个Monitor被持有后，它将处于锁定状态。

### 指令

- monitorenter
- monitorexit
# 锁升级过程
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679843736939-c5b9649b-2f3f-42a6-bbe7-79c38615bf93.png#averageHue=%23f9f8f7&clientId=ue237287c-2ea9-4&from=paste&height=216&id=u3b880910&originHeight=432&originWidth=1526&originalType=binary&ratio=2&rotation=0&showTitle=false&size=166302&status=done&style=none&taskId=u7e0572a3-4a5c-4686-b01b-76a4e434f5d&title=&width=763)
## ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679843993191-84045d2a-5bf6-4a11-b3aa-dd95a806a5aa.png#averageHue=%23f8e8d7&clientId=ue237287c-2ea9-4&from=paste&height=73&id=u9b74699a&originHeight=146&originWidth=1408&originalType=binary&ratio=2&rotation=0&showTitle=false&size=26049&status=done&style=none&taskId=u74ec1d2c-2ee2-414c-9d7c-cc9a3b9bc25&title=&width=704)
## 1. 无锁

- Mark Work内容
   - 对象的hashCode, 对象分代年龄,  是否是偏向锁(0)
   - 标志位-01
## 2. 偏向锁

- 一段代码块一直被一个线程所访问, 不存在多线程竞争, 偏向一个线程。
- 偏向锁只有遇到其他线程尝试竞争偏向锁时, 持有偏向锁的线程才会释放锁,  没有线程竞争, 不会主动释放偏向锁。
- Mark Work内容
   - 偏向线程ID, 偏向时间戳,  对象分代年龄,  是否是偏向锁(1)
   - 标志位-01
## 3. 轻量级锁

- 锁在偏向锁时候, 被第二个线程访问, 偏向锁升级为轻量级锁, 其他线程会通过自旋的方式(CAS)尝试获取锁, 不会阻塞。
- Mark Work内容
   - 指向栈中锁记录的指针
   - 标志位-00
## 4. 重量级锁

- 若当前只有一个等待线程, 则该线程通过自旋等待, 当自旋超过一定的次数, 
或者一个线程在持有锁, 一个在自旋, 又有第三个线程来, 轻量级锁升级为重量级锁。
- 等待锁的线程都进入阻塞状态
- 操作系统 用户态和内核态切换比较重, 所以是重量级
- Mark Work内容
   - 指向互斥量(重量级锁)的指针
   - 标志位-10
# synchronized作用
## 实现原子性
> 加锁。

## 实现可见性

- 线程解锁前，必须把共享变量的最新值刷新到主内存中。
- 线程加锁时，将清空工作内存中共享变量的值，从而使用共享变量时，需要从主内存中重新读取最新的值(注意：加锁与解锁需要是同一把锁)
### demo1会死锁，demo2不会，为什么？
```java
public class SynchronizedDeadLockDemo {

    private static boolean flag = false;
    // private static volatile boolean flag = false;

    public static void main(String[] args) throws InterruptedException {
        new Thread(() -> {
            while (!flag) {
//                System.out.println(flag);
            }
            System.out.println(Thread.currentThread().getName() + " stop");
        }, "t1").start();

        Thread.sleep(1000);

        new Thread(() -> {
            flag = true;
            System.out.println(Thread.currentThread().getName() + " flag changed");
        }, "t2").start();
    }
}
```
```java
public class SynchronizedDeadLockDemo {

    private static boolean flag = false;

    public static void main(String[] args) throws InterruptedException {
        new Thread(() -> {
            while (!flag) {
                System.out.println(flag);
            }
            System.out.println(Thread.currentThread().getName() + " stop");
        }, "t1").start();

        Thread.sleep(1000);

        new Thread(() -> {
            flag = true;
            System.out.println(Thread.currentThread().getName() + " flag changed");
        }, "t2").start();
    }
}
```
#### 答
> System.out.println 输出，使用了synchronized，会刷新共享变量的值，保证可见性。
> demo1 flag使用volatile修饰就不会死锁了，volatile也能保证可见性

```java
    public void println(boolean x) {
        synchronized (this) {
            print(x);
            newLine();
        }
    }
```
# synchronized**自适应的CAS自旋**

# 锁消除
> 锁削除是指虚拟机即时编译器在运行时，对一些代码上要求同步，但是被检测到**不可能存在共享数据竞争**的锁进行削除。

> JVM逃逸分析

# 锁粗化
> 就是将多个连续的加锁、解锁操作连接在一起，扩展成一个范围更大的锁。

> 目的是减少连续的加锁，解锁操作，减少不必要的性能损耗。

