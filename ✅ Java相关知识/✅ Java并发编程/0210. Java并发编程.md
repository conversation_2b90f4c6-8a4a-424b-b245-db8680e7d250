
# 1. 讲一下volatile关键字吧
## 1. 保证线程变量的可见性

- **volatile修饰的变量，当一个线程修改了该变量的值，其他线程是立即可见的。**
   - volatile不能一定能保证线程安全。**无法完全保证原子性**
      - i++;   不能保证原子性，需加锁
         - 使用synchronized关键字或者lock锁 
   - **volatile修饰long和double可以保证其操作原子性。64位**
      - 在操作的时候，可以分成两步，每次对32位操作
   - 线程不会阻塞， 轻量级

## 2.  禁止CPU的指令重排序

   - 重排序种类：![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612961091637-865802f3-9c33-4822-8087-abb0ca9ccbee.png#averageHue=%23f0eabb&height=114&id=S3t8t&originHeight=114&originWidth=865&originalType=binary&ratio=1&rotation=0&showTitle=false&size=26010&status=done&style=none&title=&width=865)
   - 什么是指令重排序？
> 为了使指令更加符合CPU的执行特性，最大限度的发挥机器的性能，提高程序的执行效率，**只要程序的最终结果与它顺序化情况的结果相等，那么指令的执行顺序可以与代码逻辑顺序不一致，**

   - 原理： 内存屏障
      - ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612961316325-f0ae8a78-7102-453c-922d-c719e4aa6cbf.png#averageHue=%23fdfcf9&height=275&id=Y7tyg&originHeight=275&originWidth=860&originalType=binary&ratio=1&rotation=0&showTitle=false&size=40241&status=done&style=none&title=&width=860)
      - ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612961415745-8b04a6fc-c54a-46e8-b2d5-5402ed229916.png#averageHue=%23f7efee&height=498&id=o98qt&originHeight=498&originWidth=644&originalType=binary&ratio=1&rotation=0&showTitle=false&size=63897&status=done&style=none&title=&width=644)
      - ![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612961439684-fc69c06a-6e36-4d08-8cad-e3f4a3128be7.png#averageHue=%23f7f1f0&height=592&id=LjxRv&originHeight=592&originWidth=703&originalType=binary&ratio=1&rotation=0&showTitle=false&size=74629&status=done&style=none&title=&width=703)
# 2. 什么是死锁
## 1. 概念
> 两个及以上的线程竞争资源造成阻塞

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1612963926005-58c00299-53b1-47cf-93a0-8c94dd7899ab.png#averageHue=%23f07d5b&height=224&id=qskPp&originHeight=447&originWidth=588&originalType=binary&ratio=1&rotation=0&showTitle=false&size=159520&status=done&style=none&title=&width=294)
## 2. 形成死锁的四个必要条件是什么？

- **互斥条件**
   - 某资源只能被一个线程占用，其他线程要使用只能等待。
- **占用且等待条件**
   - 一个线程已经有了一个资源，又需要其他资源（已被其他线程占用），请求阻塞等待，自己的资源也不释放。
- **不可抢占条件**
   - 需要其他资源（已被其他线程占用），不能去抢。
- **循环等待条件**
   - A等B， B等A， 互相等对方释放资源。
## 3. 如何避免死锁

- 避免**一个线程同时获得多个锁**
- 避免一个线程在锁内同时占用多个资源，尽量**保证每个锁只占用一个资源**
- 尝试使用定时锁，使用**lock.tryLock(timeout)**来替代使用内部锁机制
# 3. 创建线程的方法 4种

- **extends** **Thread        run**() 
- **implements** **Runnable    run**() 
- **implements** **Callable<T>   T call**()
   - 有返回值， 泛型
- 匿名内部类 **new** Thread(**new** Runnable() {});

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1612964905890-a041047c-0b6c-4331-96f1-77167a45e399.png#averageHue=%23f0eded&height=462&id=Svh7n&originHeight=462&originWidth=880&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=880)
# 4. as-if-serial规则和happens-before规则
### as-if-serial
> 指令重排序遵守的规则

1. 不管怎么排序，结果不能改变
2. 不存在数据依赖的可以被编译器和处理器重排序
3. 一个操作依赖两个操作，这两个操作如果不存在依赖可以重排序
4. 单线程根据此规则不会有问题，但是重排序后多线程会有问题

### happens-before


# 5. 并发关键字 synchronized ？
> synchronized 关键字是用来控制线程同步的，就是在多线程的环境下，控制** synchronized 代码段不被多个线程同时执行**。**阻塞（同时只有一个线程， 其他阻塞）**
> synchronized 可以**修饰类、方法、代码块**。

## 1. 使用方式

- 实例方法、
   - **给当前对象实例加锁**， 进入同步代码之前要先获取当前对象实例的锁
- 静态方法 static
   - **给当前类加锁，作用于所有对象实例（new）**。
      - 不管new了多少份对象实例， static静态方法在JVM只有一份
- 代码块
   - **指定对加锁对象（类）， 对给定的对象加锁。**synchronized (Singleton.class) {}
## 2. 双重检验锁方式实现单例模式

```java
public class Singleton {
   // volatile修饰 线程可见， 禁止指令重排序
   private volatile static Singleton uniqueInstance;
   private Singleton() {}

  public static Singleton getUniqueInstance() {
   		// 先判断对象是否已经实例过，没有实例化过才进入加锁代码
      	if (uniqueInstance == null) {
          	// 类对象加锁
          	synchronized (Singleton.class) {
              	if (uniqueInstance == null) {
                  	uniqueInstance = new Singleton();
              	}
          	}
      	}
      return uniqueInstance;
  }
}
```

## 3. synchronized的底层实现原理

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1613118638105-3051f329-e499-48aa-a95a-ffa4ab397016.png#averageHue=%23f8f6f4&height=445&id=ohPlT&originHeight=445&originWidth=854&originalType=binary&ratio=1&rotation=0&showTitle=false&size=53032&status=done&style=none&title=&width=854)

## 4. synchronized可重入的原理

- 重入锁是指一个线程获取到该锁之后，该线程可以继续获得该锁。**底层原理维护一个计数器**，当**线程获取该锁时，计数器加1**，再次获得该锁时继续加一，释放锁时，计数器减一，当计数器值为0时，表明该锁未被任何线程所持有，其它线程可以竞争获取锁。
## 5. 什么是自旋？
[https://juejin.cn/post/6844904125755293710#heading-69](https://juejin.cn/post/6844904125755293710#heading-69)
## 6. synchronized的锁升级原理

## 7. synchronized、volatile、CAS 比较

- synchronized 是**悲观锁，属于抢占式**，会引起其他线程**阻塞**。
- volatile 提供多线程**共享变量可见性**和**禁止指令重排序**优化。
- CAS 是基于冲突检测的**乐观锁**（**非阻塞**）
## 8. synchronized、Lock、ReentrantLock 一些区别
### synchronized：

   - 是java的一个关键字
   - 给**类、方法、代码块**加锁
   - 不需要手动获取锁、释放锁， 发生异常自动释放锁
   - **可重入锁， 非公平锁**
   - 锁机制：synchronized 操作的应该是对象头中 mark word
### Lock

   - 是Java的一个接口
   - 只能给代码块加锁
   - 手动 lock()、unLock()
### ReentrantLock 
[https://juejin.cn/post/6844904125755293710#heading-75](https://juejin.cn/post/6844904125755293710#heading-75)

   - 是Java的一个类
   - 只能给代码块加锁
   - 手动 lock()、unLock()
   - 可重入锁, **公平、非公平**



```java
Lock lock = new ReentrantLock(true);

public ReentrantLock(boolean fair) {
    sync = fair ? new FairSync() : new NonfairSync();
}
```

- 锁机制：ReentrantLock 底层调用的是 Unsafe 的park 方法加锁

# 6. 多线程中的一些锁？
## 1. 乐观锁
> 很乐观，每次取数据时认为其他线程不会修改，**不加锁，更新时先判断其他线程有没有更新这个数据，版本号机制。**
> **适用多读的场景**

- j.u.c.atomic包下的原子类， CAS
### CAS
> compare and swap 比较且交换， 无锁

- 内存地址V
- 旧的预期值A
- 即将要更新的目标值B

```java
if(V == A) {		//内存值和预想的值一样
    V == B;    // 新值B赋值给内存V
} else {
    // 啥也不做
}
```
#### 问题

- CAS**自旋**操作（**循环时间长开销大**）：当内存地址V与预期值B不相等时会**一直循环比较直到相等。**
- 只能保证一个共享变量的原子操作。
   - 对于多个共享变量，需要加锁
- 出现**ABA问题**：当读取内存值V的时候为A，有一个线程将A改为B，后又改为A，CAS会误认为内存值V没有改变
   - 版本号
   - 从 Java1.5 开始 JDK 的 atomic包里提供了一个类 **AtomicStampedReference **来解决 ABA 问题？
## 2. 悲观锁
> 很悲观，考虑最坏的情况，** 每次读数据认为会被其他线程修改， 所以每次读数据时都加锁**，阻塞其他线程。

- 关系型数据库 行锁、表锁、读锁、写锁， 每次操作前先加锁
- synchronized关键字

# 7. 线程池
> 看java基础，5.10

# 8. 并发容器
## 1. ConcurrentHashMap

## 2. Collections.synchronized * 

```java
 public static <K,V> Map<K,V> synchronizedMap(Map<K,V> m) {
     return new SynchronizedMap<>(m);
 }


private static class SynchronizedMap<K,V>
        implements Map<K,V>, Serializable {
        private static final long serialVersionUID = 1978198479659022715L;

        private final Map<K,V> m;     // Backing Map
        final Object      mutex;        // Object on which to synchronize

        SynchronizedMap(Map<K,V> m) {
            this.m = Objects.requireNonNull(m);
            mutex = this;
        }

        SynchronizedMap(Map<K,V> m, Object mutex) {
            this.m = m;
            this.mutex = mutex;
        }

        public int size() {
            synchronized (mutex) {return m.size();}
        }
 }       
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1613462329464-3ba0398d-ad99-4e2a-8d9e-7c6d31ccd601.png#averageHue=%233b444c&height=253&id=WbqDh&originHeight=253&originWidth=840&originalType=binary&ratio=1&rotation=0&showTitle=false&size=52769&status=done&style=none&title=&width=840)

## 3. CopyOnWriteArrayList 

- 适合**读多写少**场景， 不适合实时读
- 读写分离， 最终一致性。


# 9. 并发工具类
## CountDownLatch

- 计数器， 等待几个线程结束
## CyclicBarrier 

- 回环栅栏
## Semaphore 

- 信号量









