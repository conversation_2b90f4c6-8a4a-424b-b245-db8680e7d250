# 底层实现

> 为每个线程都 new 一个 ThreadLocalMap
> key 为当前线程
> 只要当前线程未执行完, 再 set 之后任意时间都能获取到 ThreadLocalMap
> 再线程执行结束要 remove, 防止 OOM
> 线程隔离

```java
public class ThreadLocal<T> {

    // 设置值
    public void set(T value) {
        // 当前线程
        Thread t = Thread.currentThread();
        // 获取内部map
        ThreadLocalMap map = getMap(t);
        // 存在就将值存入map
        if (map != null) {
            map.set(this, value);
        } else {
            // 不存在先创建再把值存入map
            createMap(t, value);
        }
    }

    // 获取内部map
    ThreadLocalMap getMap(Thread t) {
        return t.threadLocals;
    }

    // 创建内部map,  每个线程都new一个ThreadLocalMap
    void createMap(Thread t, T firstValue) {
        t.threadLocals = new ThreadLocalMap(this, firstValue);
    }

    // 获取当前线程存入的值
    public T get() {
        // 当前线程
        Thread t = Thread.currentThread();
        // 获取内部map
        ThreadLocalMap map = getMap(t);
        // map不为空, 元素不为空则返回
        if (map != null) {
            ThreadLocalMap.Entry e = map.getEntry(this);
            if (e != null) {
                @SuppressWarnings("unchecked")
                T result = (T)e.value;
                return result;
            }
        }
        return setInitialValue();
    }

    // 设置初始值, 内部map不存在就创建再存入, 返回null
    private T setInitialValue() {
        T value = initialValue();
        Thread t = Thread.currentThread();
        ThreadLocalMap map = getMap(t);
        if (map != null) {
            map.set(this, value);
        } else {
            createMap(t, value);
        }
        if (this instanceof TerminatingThreadLocal) {
            TerminatingThreadLocal.register((TerminatingThreadLocal<?>) this);
        }
        return value;
    }

    // 初始值为null
    protected T initialValue() {
        return null;
    }

    // 将内部map值清理, 一定要执行remove, 再finally里!!!
    public void remove() {
         ThreadLocalMap m = getMap(Thread.currentThread());
         if (m != null) {
             m.remove(this);
         }
     }

    // 内部维护的map, key为当前线程, value为传入的值
    // 每个线程都new一个ThreadLocalMap
    static class ThreadLocalMap {
        // 内部主要是对Entry对象进行操作, 每个线程维护一个ThreadLocalMap
    	// 每个ThreadLocalMap维护一个Entry对象
        // Entry是个弱引用, 作用是??
        // 弱引用对象, 在内存不足, GC可以被回收
    	 static class Entry extends WeakReference<ThreadLocal<?>> {
            /** The value associated with this ThreadLocal. */
            Object value;

            Entry(ThreadLocal<?> k, Object v) {
                super(k);
                value = v;
            }
        }

        // 长度2的次幂
        private Entry[] table;
    }
}
```

## 弱引用

![image.png](https://cdn.nlark.com/yuque/0/2022/png/684952/1667026767671-3c8288ce-ee41-431b-a79d-f1040c1abaa4.png#averageHue=%23cbd7bc&clientId=u786da1c1-6eb1-4&from=paste&height=599&id=ub832edbc&originHeight=1198&originWidth=1462&originalType=binary&ratio=1&rotation=0&showTitle=false&size=978134&status=done&style=none&taskId=u6712fdaf-4ab3-4b8e-bafa-46ae4bf3186&title=&width=731)

# 使用场景

> -   线程隔离
> -

# 使用场景怎么用

> -   MDC, 日志打印一个 uuid, 方便查看同一请求打印的日志
> -   mybatis plus 动态表名, 如按月, 传入 202210, 表名自动拼接 202210,
> -   dynamic 动态数据源, 切换数据源操作
> -   分页插件
> -   Spring 事务管理器 TransactionSynchronizationManager

> 注意: 用完要 remove

# 使用不当有哪些问题

## 内存泄露

> -   再使用完 ThreadLocal, 要手动 remove 方法 才可能避免

# ThreadLocal、InheritableThreadLocal、TransmittableThreadLocal

## ThreadLocal

线程本地变量拷贝，线程之间数据隔离

## InheritableThreadLocal

线程本地变量，可以完成**父线程到子线程的值传递**。
只适用于手动创建线程，由线程池创建的线程不适用，因为线程池创建的线程是复用的，无法知道这个变量是哪个父线程设置的。

### 源码

```java
public class InheritableThreadLocal<T> extends ThreadLocal<T> {

    protected T childValue(T parentValue) {
        return parentValue;
    }

    ThreadLocalMap getMap(Thread t) {
       return t.inheritableThreadLocals;
    }

    void createMap(Thread t, T firstValue) {
        t.inheritableThreadLocals = new ThreadLocalMap(this, firstValue);
    }
}
```

### 测试 demo

```java
public class MyInheritableThreadLocal {

    private static final InheritableThreadLocal<String> INHERITABLE_THREAD_LOCAL = new InheritableThreadLocal<>();

    public static String get() {
        return INHERITABLE_THREAD_LOCAL.get();
    }

    public static void set(String data) {
        INHERITABLE_THREAD_LOCAL.set(data);
    }

    public static void remove() {
        INHERITABLE_THREAD_LOCAL.remove();
    }
}
```

```java
public class ThreadLocalThreadPoolTest {
    private static final ExecutorService executor = ThreadUtil.newExecutor(3);

    public static void main(String[] args) {
        new Thread(() -> {
            MyInheritableThreadLocal.set("线程1-test-in");
            for (int i = 0; i < 5; i++) {
                executor.execute(() -> {
                    System.out.println("线程1 threadName: " + Thread.currentThread().getName() + ", data: " + MyInheritableThreadLocal.get());
                });
            }
        }, "线程1").start();

        new Thread(() -> {
            MyInheritableThreadLocal.set("线程2-test-in");
            for (int i = 0; i < 5; i++) {
                executor.execute(() -> {
                    System.out.println("线程2 threadName: " + Thread.currentThread().getName() + ", data: " + MyInheritableThreadLocal.get());
                });
            }
        }, "线程2").start();

        /**
         - 线程2 threadName: pool-1-thread-3, data: 线程2-test-in
         - 线程2 threadName: pool-1-thread-1, data: 线程2-test-in
         - 线程1 threadName: pool-1-thread-2, data: 线程1-test-in
         - 线程1 threadName: pool-1-thread-1, data: 线程2-test-in       // 线程1设置的值, 线程池创建的线程缺得到现成2设置的值
         - 线程1 threadName: pool-1-thread-3, data: 线程2-test-in
         - 线程1 threadName: pool-1-thread-1, data: 线程2-test-in
         - 线程1 threadName: pool-1-thread-2, data: 线程1-test-in
         - 线程2 threadName: pool-1-thread-1, data: 线程2-test-in
         - 线程2 threadName: pool-1-thread-3, data: 线程2-test-in
         - 线程2 threadName: pool-1-thread-2, data: 线程1-test-in       // 线程2设置的值, 线程池创建的线程缺得到现成1设置的值
         */
        /*
         - 正常是要主线程设置什么值, 子线程就要得到什么值, 但是由线程池创建的子线程不行, 由于线程复用, 可能得到其他主线程设置的值.
         */
    }
}
```

![](images/Pasted%20image%2020230520120353.png)

## TransmittableThreadLocal

在使用线程池等会池化复用线程的执行组件情况下，提供 ThreadLocal 值的传递功能，解决异步执行时上下文传递的问题。
一个 Java 标准库本应为框架/中间件设施开发提供的标配能力，本库功能聚焦 & 0 依赖，支持 Java 6~20。
https://kgithub.com/alibaba/transmittable-thread-local/tree/2.x

### maven 依赖

```xml
       <dependency>
           <groupId>com.alibaba</groupId>
           <artifactId>transmittable-thread-local</artifactId>
           <version>2.14.2</version>
       </dependency>
```

### 使用方式

1. 使用 TtlRunnable 和 TtlCallable 来修饰传入线程池的 Runnable 和 Callable

```java
TtlRunnable ttlRunnable = TtlRunnable.get(() -> {
                    System.out.println("线程1 threadName: " + Thread.currentThread().getName() + ", data: " + MyTransmittableThreadLocal.get());
                });
                executor.execute(ttlRunnable);
```

2. 修饰线程池 TtlExecutors

```java
private static final ExecutorService ttlExecutor = TtlExecutors.getTtlExecutorService(ThreadUtil.newExecutor(3));
```

### 测试 demo

#### 定义

```java
public class MyTransmittableThreadLocal {

    private static final TransmittableThreadLocal<String> context = new TransmittableThreadLocal<>();

    public static void set(String data) {
        context.set(data);
    }

    public static String get() {
        return context.get();
    }

    public static void remove() {
        context.remove();
    }
}
```

#### test 类

```java
public class ThreadLocalThreadPoolTest3 {
    private static final ExecutorService executor = ThreadUtil.newExecutor(3);

    /**
     - 2. 修饰线程池 TtlExecutors
     - <a href="https://kgithub.com/alibaba/transmittable-thread-local/tree/2.x#dummy">...</a>
     */
    private static final ExecutorService ttlExecutor = TtlExecutors.getTtlExecutorService(ThreadUtil.newExecutor(3));


    public static void main(String[] args) {
        new Thread(() -> {
            MyTransmittableThreadLocal.set("线程1-test-in");
            for (int i = 0; i < 10; i++) {
                // 1. 使用TtlRunnable和TtlCallable来修饰传入线程池的Runnable和Callable
                TtlRunnable ttlRunnable = TtlRunnable.get(() -> {
                    System.out.println("线程1 threadName: " + Thread.currentThread().getName() + ", data: " + MyTransmittableThreadLocal.get());
                });
                executor.execute(ttlRunnable);
                MyTransmittableThreadLocal.set("线程1-test-in-修改了");
                executor.execute(ttlRunnable);
            }

        }, "线程1").start();

        new Thread(() -> {
            MyTransmittableThreadLocal.set("线程2-test-in");
            for (int i = 0; i < 10; i++) {
                Runnable runnable = () -> {
                    System.out.println("线程2 threadName: " + Thread.currentThread().getName() + ", data: " + MyTransmittableThreadLocal.get());
                };
                assert ttlExecutor != null;
                ttlExecutor.execute(runnable);
            }
        }, "线程2").start();

    }
}
```
