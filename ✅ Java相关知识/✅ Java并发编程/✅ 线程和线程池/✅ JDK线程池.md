# Java线程池使用
## 定义线程池
```java
    @Bean
    public ThreadPoolExecutor myThreadPoolExecutor() {
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(
                5,
                10,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadFactory() {
                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, "my-pool-");
                    }
                },
                new CustomPolicy());

        poolExecutor.allowCoreThreadTimeOut(true);
        // 提前创建核心线程
        poolExecutor.prestartAllCoreThreads();
        return poolExecutor;
    }
```
## 使用
```java
    @Resource
    private ThreadPoolExecutor myThreadPoolExecutor;

    myThreadPoolExecutor.execute(() -> {
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    });
```
# ThreadPoolExecutor 
> java.util.concurrent.ThreadPoolExecutor
> 通过构造方法创建线程池后，默认是只是创建对象，线程实际上还未创建。
> poolExecutor.prestartAllCoreThreads();可以提前创建核心线程

## 类关系图
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679719691506-57922070-43e6-4cc7-a1c9-1fedaf0ac078.png#averageHue=%232e2e2e&clientId=uf9856403-c965-4&from=paste&height=372&id=u5e553d79&originHeight=744&originWidth=752&originalType=binary&ratio=2&rotation=0&showTitle=false&size=37219&status=done&style=none&taskId=u5e976d0f-b93f-48c4-927d-069746f3d24&title=&width=376)
## 构造方法
```java
 public ThreadPoolExecutor(int corePoolSize,
                              int maximumPoolSize,
                              long keepAliveTime,
                              TimeUnit unit,
                              BlockingQueue<Runnable> workQueue,
                              ThreadFactory threadFactory,
                              RejectedExecutionHandler handler) {
        if (corePoolSize < 0 ||
            maximumPoolSize <= 0 ||
            maximumPoolSize < corePoolSize ||
            keepAliveTime < 0)
            throw new IllegalArgumentException();
        if (workQueue == null || threadFactory == null || handler == null)
            throw new NullPointerException();
        this.acc = System.getSecurityManager() == null ?
                null :
                AccessController.getContext();
        this.corePoolSize = corePoolSize;
        this.maximumPoolSize = maximumPoolSize;
        this.workQueue = workQueue;
        this.keepAliveTime = unit.toNanos(keepAliveTime);
        this.threadFactory = threadFactory;
        this.handler = handler;
    }
```
### int corePoolSize
> 核心线程数

### int maximumPoolSize
> 最大线程数

### long keepAliveTime
> 线程空闲时间
> 非核心线程空闲时间超过keepAliveTime，会销毁非核心线程。
> allowCoreThreadTimeOut=true时，核心线程空闲时间超过keepAliveTime，会销毁核心线程。

### TimeUnit unit
> 空闲超时时间

### BlockingQueue<Runnable> workQueue
> 阻塞队列

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679674076483-67f959e5-69be-451a-a24b-0b8b9a69bf20.png#averageHue=%23ececec&clientId=u697009b7-7f3e-4&from=paste&height=398&id=uaf97d791&originHeight=796&originWidth=1816&originalType=binary&ratio=2&rotation=0&showTitle=false&size=633579&status=done&style=none&taskId=u5813a44b-e33e-4e73-b940-4f00fdeb76f&title=&width=908)
### ThreadFactory threadFactory
> 线程工厂，定义线程名称，创建线程

### RejectedExecutionHandler handler
> 拒绝策略，当阻塞队列满了，活跃线程数达到最大线程数，线程池无法执行新任务，则触发拒绝策略。

#### AbortPolicy
> 直接丢弃新任务，抛出RejectedExecutionException异常。线程池不会终止。
> 默认策略

```java
public static class AbortPolicy implements RejectedExecutionHandler {
    /**
     * Creates an {@code AbortPolicy}.
     */
    public AbortPolicy() { }

    /**
     * Always throws RejectedExecutionException.
     *
     * @param r the runnable task requested to be executed
     * @param e the executor attempting to execute this task
     * @throws RejectedExecutionException always
     */
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        throw new RejectedExecutionException("Task " + r.toString() +
                                             " rejected from " +
                                             e.toString());
    }
}
```
#### CallerRunsPolicy
> 线程池无法执行任务，由 使用线程池的那个线程执行任务。

```java
public static class CallerRunsPolicy implements RejectedExecutionHandler {
    /**
     * Creates a {@code CallerRunsPolicy}.
     */
    public CallerRunsPolicy() { }

    /**
     * Executes task r in the caller's thread, unless the executor
     * has been shut down, in which case the task is discarded.
     *
     * @param r the runnable task requested to be executed
     * @param e the executor attempting to execute this task
     */
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        if (!e.isShutdown()) {
            r.run();
        }
    }
}
```
#### DiscardPolicy
> 直接丢弃任务，什么也不做，这样无法感知到任务是否成功还是失败。

```java
public static class DiscardPolicy implements RejectedExecutionHandler {
    /**
     * Creates a {@code DiscardPolicy}.
     */
    public DiscardPolicy() { }

    /**
     * Does nothing, which has the effect of discarding task r.
     *
     * @param r the runnable task requested to be executed
     * @param e the executor attempting to execute this task
     */
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
    }
}
```
#### DiscardOldestPolicy
> 丢弃最老未处理的任务。

```java
public static class DiscardOldestPolicy implements RejectedExecutionHandler {
    /**
     * Creates a {@code DiscardOldestPolicy} for the given executor.
     */
    public DiscardOldestPolicy() { }

    /**
     * Obtains and ignores the next task that the executor
     * would otherwise execute, if one is immediately available,
     * and then retries execution of task r, unless the executor
     * is shut down, in which case task r is instead discarded.
     *
     * @param r the runnable task requested to be executed
     * @param e the executor attempting to execute this task
     */
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        if (!e.isShutdown()) {
            // 检索并删除此队列的头部，如果此队列为空则返回null。
            e.getQueue().poll();
            // 立即执行新任务。
            e.execute(r);
        }
    }
}
```
## 任务执行源码
### execute(Runnable command)
> 线程任务执行入口

```java
public void execute(Runnable command) {
        if (command == null)
            throw new NullPointerException();
        /*
         * Proceed in 3 steps:
         *
         * 1. If fewer than corePoolSize threads are running, try to
         * start a new thread with the given command as its first
         * task.  The call to addWorker atomically checks runState and
         * workerCount, and so prevents false alarms that would add
         * threads when it shouldn't, by returning false.
         *
         * 2. If a task can be successfully queued, then we still need
         * to double-check whether we should have added a thread
         * (because existing ones died since last checking) or that
         * the pool shut down since entry into this method. So we
         * recheck state and if necessary roll back the enqueuing if
         * stopped, or start a new thread if there are none.
         *
         * 3. If we cannot queue task, then we try to add a new
         * thread.  If it fails, we know we are shut down or saturated
         * and so reject the task.
         */
        int c = ctl.get();
        // 1. 检查工作线程数是否小于corePoolSize
        if (workerCountOf(c) < corePoolSize) {
            // 创建核心线程
            if (addWorker(command, true))
                return;
            c = ctl.get();
        }
    	// 2. 线程池时运行状态，工作线程数大于corePoolSize，将任务添加到阻塞队列。
    	// offer失败，创建非核心线程
        if (isRunning(c) && workQueue.offer(command)) {
            int recheck = ctl.get();
            // 再次检查线程池状态，不是运行状态且任务已存在（remove成功），触发拒绝策略。
            if (! isRunning(recheck) && remove(command))
                reject(command);
            // wc为0时，创建一个worker
            else if (workerCountOf(recheck) == 0)
                addWorker(null, false);
        }
        // 3. 阻塞队列满了，判断工作线程数是否大于最大线程数，小于最大线程数则创建非核心线程。
        else if (!addWorker(command, false))
            // 4. 大于最大线程数，触发拒绝策略
            reject(command);
    }
```
### addWorker(Runnable firstTask, boolean core)
> 1. 先死循环验证是否可以创建线程，不行返回false。
> 2. 可以创建，new一个Worker，从线程工厂创建线程，并启动线程执行任务。
> 3. 如果任务执行失败，这回滚以上操作。
> 4. 任务执行成功，返回true。

```java
private boolean addWorker(Runnable firstTask, boolean core) {
        retry:
        for (;;) {
            int c = ctl.get();
            int rs = runStateOf(c);

            // Check if queue empty only if necessary.
            // 判断是否有必要创建线程执行任务
            if (rs >= SHUTDOWN &&
                ! (rs == SHUTDOWN && firstTask == null && ! workQueue.isEmpty())
                // (rs != SHUTDOWN && firstTask != null && workQueue.isEmpty())
               )
                return false;

            // cas增加工作线程个数
            for (;;) {
                // 计算工作线程数
                int wc = workerCountOf(c);
                if (wc >= CAPACITY ||
                    // core为true比较核心线程数；false比较最大线程数，
                    // wc超过这两个数，不创建创建新线程
                    wc >= (core ? corePoolSize : maximumPoolSize))
                    return false;
                if (compareAndIncrementWorkerCount(c))
                    break retry;
                c = ctl.get();  // Re-read ctl
                if (runStateOf(c) != rs)
                    continue retry;
                // else CAS failed due to workerCount change; retry inner loop
            }
        }

        // 工作线程数增加完成，开始创建线程
        boolean workerStarted = false;
        boolean workerAdded = false;
        Worker w = null;
        try {
            // 从ThreadFactory中创建给定的任务和线程
            w = new Worker(firstTask);
            final Thread t = w.thread;
            if (t != null) {
                final ReentrantLock mainLock = this.mainLock;
                mainLock.lock();
                try {
                    // Recheck while holding lock.
                    // Back out on ThreadFactory failure or if
                    // shut down before lock acquired.
                    int rs = runStateOf(ctl.get());

                    if (rs < SHUTDOWN ||
                        (rs == SHUTDOWN && firstTask == null)) {
                        if (t.isAlive()) // precheck that t is startable
                            throw new IllegalThreadStateException();
                        // 添加线程到到hashset，重复利用
                        workers.add(w);
                        int s = workers.size();
                        if (s > largestPoolSize)
                            largestPoolSize = s;
                        workerAdded = true;
                    }
                } finally {
                    mainLock.unlock();
                }
                if (workerAdded) {
                    // 启动线程
                    t.start();
                    workerStarted = true;
                }
            }
        } finally {
            if (! workerStarted)
                // 线程启动失败，回滚工作线程的创建，
                // 加锁 从workers移除worker，减少wc数，让线程池进入TERMINATED状态。
                addWorkerFailed(w);
        }
        // 任务执行成功返回true
        return workerStarted;
    }
```
### 成员变量
> 

```java
// 工作线程set 
private final HashSet<Worker> workers = new HashSet<Worker>();

// 使用原子操作类AtomicInteger的ctl变量，前3位记录线程池的状态，后29位记录线程数
private final AtomicInteger ctl = new AtomicInteger(ctlOf(RUNNING, 0));
// 32-3=29
private static final int COUNT_BITS = Integer.SIZE - 3;
// 高三位用来存储线程池运行状态，其余位数表示线程池的容量
private static final int CAPACITY   = (1 << COUNT_BITS) - 1;

// runState is stored in the high-order bits
private static final int RUNNING    = -1 << COUNT_BITS;
private static final int SHUTDOWN   =  0 << COUNT_BITS;
private static final int STOP       =  1 << COUNT_BITS;
private static final int TIDYING    =  2 << COUNT_BITS;
private static final int TERMINATED =  3 << COUNT_BITS;

// Packing and unpacking ctl
// 获取线程池运行状态
private static int runStateOf(int c)     { return c & ~CAPACITY; }
// 获取线程池运行线程数
private static int workerCountOf(int c)  { return c & CAPACITY; }
// 获取ctl对象
private static int ctlOf(int rs, int wc) { return rs | wc; }
```
### new Worker(firstTask) - 创建线程
> 创建一个工作线程worker，通过线程工厂创建线程

```java
private final class Worker
	extends AbstractQueuedSynchronizer
    implements Runnable {

    /** Thread this worker is running in.  Null if factory fails. */
    final Thread thread;
    /** Initial task to run.  Possibly null. */
    Runnable firstTask;
    /** Per-thread task counter */
    volatile long completedTasks;

    /**
     * Creates with given first task and thread from ThreadFactory.
     * @param firstTask the first task (null if none)
     */
    Worker(Runnable firstTask) {
        setState(-1); // inhibit interrupts until runWorker
        // 任务
        this.firstTask = firstTask;
        // 调用线程工厂，创建新线程，线程池this指向thread，不会被gc回收
        this.thread = getThreadFactory().newThread(this);
    }

    // 启动执行任务
    /** Delegates main run loop to outer runWorker  */
    public void run() {
        runWorker(this);
    }

    ...
}
```
### runWorker(this) - 启动线程
> 真正执行任务的地方，就是我们应用程序要处理的代码
> 死循环从阻塞队列取任务执行，直到任务执行异常中断线程。

```java
final void runWorker(Worker w) {
    Thread wt = Thread.currentThread();
    Runnable task = w.firstTask;
	// 清除worker里的runable，让gc回收？
    w.firstTask = null;
    w.unlock(); // allow interrupts
    boolean completedAbruptly = true;
    try {
        // task != null 有传进来worker直接执行task（创建线程）
        // getTask() 从阻塞队列取任务执行
        while (task != null || (task = getTask()) != null) {
            w.lock();
            // If pool is stopping, ensure thread is interrupted;
            // if not, ensure thread is not interrupted.  This
            // requires a recheck in second case to deal with
            // shutdownNow race while clearing interrupt
            if ((runStateAtLeast(ctl.get(), STOP) ||
                 (Thread.interrupted() &&
                  runStateAtLeast(ctl.get(), STOP))) &&
                !wt.isInterrupted())
                wt.interrupt();
            try {
                beforeExecute(wt, task);
                Throwable thrown = null;
                try {
                    // 执行业务代码
                    task.run();
                } catch (RuntimeException x) {
                    thrown = x; throw x;
                } catch (Error x) {
                    thrown = x; throw x;
                } catch (Throwable x) {
                    thrown = x; throw new Error(x);
                } finally {
                    
                    afterExecute(task, thrown);
                }
            } finally {
                task = null;
                w.completedTasks++;
                w.unlock();
            }
        }
        completedAbruptly = false;
    } finally {
        // 工作线程退出
        processWorkerExit(w, completedAbruptly);
    }
}
```
### getTask() - 取任务
> 从阻塞队列workQueue获取工作线程，会判断核心线程是否空闲。
> 通过poll取出任务等待keepAliveTime时间
> take会阻塞

```java
private Runnable getTask() {
    boolean timedOut = false; // Did the last poll() time out?

    for (;;) {
        int c = ctl.get();
        int rs = runStateOf(c);

        // Check if queue empty only if necessary.
        if (rs >= SHUTDOWN && (rs >= STOP || workQueue.isEmpty())) {
            decrementWorkerCount();
            return null;
        }

        int wc = workerCountOf(c);

        // 达到空闲时间，回收核心线程，或空闲工作线程大于核心线程数，则可以回收核心线程。
        // Are workers subject to culling?
        boolean timed = allowCoreThreadTimeOut || wc > corePoolSize;

        if ((wc > maximumPoolSize || (timed && timedOut))
            && (wc > 1 || workQueue.isEmpty())) {
            if (compareAndDecrementWorkerCount(c))
                return null;
            continue;
        }

        try {
            // timed为true，超过空闲时间可以回收核心线程。
            Runnable r = timed ?
                // 从阻塞队列取出一个任务, 等待keepAliveTime时间没有获取到任务，返回null。
                workQueue.poll(keepAliveTime, TimeUnit.NANOSECONDS) :
                workQueue.take();
            if (r != null)
                return r;
            timedOut = true;
        } catch (InterruptedException retry) {
            timedOut = false;
        }
    }
}
```
### processWorkerExit
> 工作线程退出

```java
private void processWorkerExit(Worker w, boolean completedAbruptly) {
    if (completedAbruptly) // If abrupt, then workerCount wasn't adjusted
        decrementWorkerCount();

    final ReentrantLock mainLock = this.mainLock;
    mainLock.lock();
    try {
        completedTaskCount += w.completedTasks;
        // 移除异常的worker
        workers.remove(w);
    } finally {
        mainLock.unlock();
    }

    tryTerminate();

    int c = ctl.get();
    if (runStateLessThan(c, STOP)) {
        // 线程被中断
        if (!completedAbruptly) {
            int min = allowCoreThreadTimeOut ? 0 : corePoolSize;
            if (min == 0 && ! workQueue.isEmpty())
                min = 1;
            if (workerCountOf(c) >= min)
                return; // replacement not needed
        }
        // 添加一个worker
        addWorker(null, false);
    }
}
```
# 线程池的状态
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679671513184-2e2ee97d-e97b-49ab-89a1-7da09fa8bf81.png#averageHue=%23fbfbfb&clientId=u697009b7-7f3e-4&from=paste&height=465&id=uc1339d56&originHeight=930&originWidth=1562&originalType=binary&ratio=2&rotation=0&showTitle=false&size=154963&status=done&style=none&taskId=u0b6b2faa-a576-4363-bf32-a6f206b4b15&title=&width=781)
```java
	private static final int COUNT_BITS = Integer.SIZE - 3;
	private static final int RUNNING    = -1 << COUNT_BITS;
    private static final int SHUTDOWN   =  0 << COUNT_BITS;
    private static final int STOP       =  1 << COUNT_BITS;
    private static final int TIDYING    =  2 << COUNT_BITS;
    private static final int TERMINATED =  3 << COUNT_BITS;
```
# 线程的状态
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679671565099-d266248c-cb78-4d51-b0bf-22eeb52d0294.png#averageHue=%23f7f7f7&clientId=u697009b7-7f3e-4&from=paste&height=714&id=ub00fe14a&originHeight=1428&originWidth=2586&originalType=binary&ratio=2&rotation=0&showTitle=false&size=517175&status=done&style=none&taskId=u06bc4d74-0641-4c2b-9afd-f6f954f0d7a&title=&width=1293)
# 线程池流程
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679673943737-f3c29fb3-56dd-4d1a-afdb-c19ded4e1e84.png#averageHue=%23fbfbfb&clientId=u697009b7-7f3e-4&from=paste&height=802&id=u99bd5a61&originHeight=1604&originWidth=1690&originalType=binary&ratio=2&rotation=0&showTitle=false&size=173039&status=done&style=none&taskId=uc273e7dd-3971-45c0-b447-f8ec4a74d1f&title=&width=845)
# 线程池的一些问题
## 线程池的核心线程是什么时候创建的？
> 用户程序使用线程池，定义Bean时只是定义了一个线程池对象，还没创建线程，当有任务来时在创建一个Worker。
> 配置poolExecutor.prestartAllCoreThreads();  定义线程池时会提前创建核心线程

## 线程池怎么超时销毁空闲线程？
> 配置了allowCoreThreadTimeOut=true或wc > corePoolSize，
> 通过workQueue.poll(keepAliveTime, TimeUnit.NANOSECONDS)
> 会等待keepAliveTime时间，若没获取到任务，则说明这个线程空闲了，可以销毁。

## 线程池的线程是怎么重复利用的？
> 创建线程后，启动线程runWorker，while循环，不断从从阻塞队列取任务执行。
> while (task != null || (task = getTask()) != null) {
> }

