# 

JDK8 语法使用

> https://gitee.com/wellq/coding-coding.git

## stream

```java
    private List<StreamData> list() {
        List<StreamData> list = new ArrayList<>();
        list.add(new StreamData("10001", "百度", 1));
        list.add(new StreamData("10021", "阿里", 21));
        list.add(new StreamData("10301", "腾讯", 231));
        list.add(new StreamData("10401", "字节", 2));
        return list;
    }
```

### 排序sorted

```java
    @Test
    public void sort() {
        List<StreamData> list = list();
        // 根据某个字段排序-顺序
        List<StreamData> asc = list.stream()
                .sorted(Comparator.comparing(StreamData::getCompanySize))
                .collect(Collectors.toList());
        System.out.println(asc);
        // // 根据某个字段排序-逆序
        List<StreamData> desc = list.stream()
                .sorted(Comparator.comparing(StreamData::getCompanySize).reversed())
                .collect(Collectors.toList());
        System.out.println(desc);
    }
```

### 分组groupingBy

```java
    @Test
    public void groupBy() {
        List<StreamData> list = list();
        // 根据某个字段分组
        Map<Integer, List<StreamData>> collect = list.stream()
                .collect(Collectors.groupingBy(StreamData::getCompanySize));
        System.out.println(JSONUtil.toJsonPrettyStr(collect));
    }
```

### 取最大值max 最小值min

```java
    @Test
    public void max() {
        List<StreamData> list = list();
        // 根据某个字段, 取最大的
        Optional<StreamData> max = list.stream()
                .max(Comparator.comparing(StreamData::getCompanySize));
        System.out.println(max);

        // 根据某个字段, 取最小的
        Optional<StreamData> min = list.stream()
                .min(Comparator.comparing(StreamData::getCompanySize));
        System.out.println(min);
    }
```

### 取出某个字段 map

```java
    @Test
    public void map() {
        List<StreamData> list = list();
        // 将companyCode字段转成list
        List<String> collect = list.stream()
                .map(StreamData::getCompanyCode)
                .collect(Collectors.toList());
        // [10001, 10021, 10301, 10401]
        System.out.println(collect);
    }
```

### 过滤 filter

```java
    @Test
    public void filter() {
        List<StreamData> list = list();
        // 根据条件过滤
        List<StreamData> collect = list.stream()
                .filter(item -> item.getCompanySize() > 100)
                .collect(Collectors.toList());
        System.out.println(collect);
    }
```

## Option



## 函数式 lambada

