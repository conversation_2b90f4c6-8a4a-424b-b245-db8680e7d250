# 注解
## 属性多个别名 _@JsonAlias_
> 只在反序列化时(json字符串转实体)有效
> 在接口请求体, 可以接受不同别名 同一个属性的参数
> area_code, city_code =>areaCode

## 属性别名 _@JsonProperty_("xxxx")
> 序列化和反序列化都有效

```java
@Data
public class AliasBean {
    @JsonAlias({ "fName", "f_name" })
    @JsonProperty("xxxx")
    private String firstName;
    private String lastName;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static void main(String[] args) throws JsonProcessingException {
        String json = "{\"f_name\": \"John\", \"lastName\": \"Green\"}";
        AliasBean aliasBean = OBJECT_MAPPER.readerFor(AliasBean.class).readValue(json);
        System.out.println(aliasBean);
        // {"lastName":"Green","xxxx":"<PERSON>"}
        // AliasBean(firstName=John, lastName=Green)

        String s = OBJECT_MAPPER.writeValueAsString(aliasBean);
        System.out.println(s);
    }
}
```
## 二级实体展开为一级实体 _@JsonUnwrapped_
```java
@Data
@AllArgsConstructor
public class UnwrappedUser {

    private int id;

    @JsonUnwrapped
    private UserName userName;

    @Data
    @AllArgsConstructor
    public static class UserName {
        public String firstName;
        public String lastName;
    }

    public static void main(String[] args) throws JsonProcessingException {
        UnwrappedUser unwrappedUser = new UnwrappedUser(1111, new UserName("22222", "333333"));
        System.out.println(new ObjectMapper().writeValueAsString(unwrappedUser));
        // {"id":1111,"firstName":"22222","lastName":"333333"}
    }

}
```
