# 头插法
> jdk1.7以前版本

> 在使用哈希表时，每个哈希桶中都存储了一条链表。
> 头插法则是在链表中新插入元素时将它插入到链表头部的一种方法。

## 存在问题

- **导致链表过长**。由于头插法将新节点插入链表头部，因此它会导致链表长期增长到固定长度，从而使整个哈希表的性能下降。查询时间复杂度 O(n) （其中 n 为该键值对所在的链表的长度）
- **容易形成死循环**。
   - 多个线程同时并发地修改同一个哈希桶
   - 假设线程 A 和线程 B 现在都尝试将一个新的键值对插入同一个哈希桶的链表中。由于使用头插法，线程 A 首先插入一个节点，指针指向老的链表头部；然后线程 B 尝试再次进行插入，它也将新元素插入到前面的节点上。那么该节点现在成为了链表的新头部，并且其 next 指针指向线程 A 插入的节点。接下来，线程 A 又尝试更新链表头部，它不知道链表已经发生了变化，仍然将其新节点插入到旧链表的头部。如此反复迭代，线程 A、B 不断地抢夺头部位置，导致**链表成环**（cycle）。后续的操作在遍历链表时可能会导致无限循环，从而陷入死循环的情况。
# 尾插法
> jdk1.8之后
> 尾插法是指在处理hash冲突时，将应该放到同一个桶中的元素放到该桶链表的尾部。


## 红黑树

1. 根节点为黑色。
2. 所有叶子结点（NULL节点）均为黑色。
3. 任何相邻的节点都不能同时为红色，即红色节点必须被黑色节点隔开。
4. 每个节点（包括根节点和叶子节点）都必须保证从根到该节点的路径上，黑色节点数量相同。
## 链表和红黑树变换

- 链表长度大于8且数组大小大于等于64时， 链表转化为红黑树。
- 红黑树节点小于6时，有会退化成链表。
```java
    // 默认负载因子
    static final float DEFAULT_LOAD_FACTOR = 0.75f;

    // 当链表长度小于该值时，将不再采取树化优化措施
    static final int TREEIFY_THRESHOLD = 8;

    // 当红黑树节点数小于等于该值时，将红黑树还原成单向链表
    static final int UNTREEIFY_THRESHOLD = 6;

    // 不树话阈值，即当哈希表中的总节点数小于该值时，哈希表扩容采用的不是扩大 2 倍，而是翻倍
    // 此处为了避免数组均衡振荡的高位填充带来的性能损失，默认情况下不占用高位
    static final int MIN_TREEIFY_CAPACITY = 64;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681959182966-a21085e1-47cf-4cf3-9cfd-9815868a9f76.png#averageHue=%23373635&clientId=u46c960ec-5ff2-4&from=paste&height=831&id=udbb947b1&originHeight=831&originWidth=844&originalType=binary&ratio=1&rotation=0&showTitle=false&size=87024&status=done&style=none&taskId=u03d425c0-773f-428d-8d60-21380ae3de2&title=&width=844)
## 源码分析
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681959851198-c5e6a604-985b-47d1-891b-71f231d21d10.png#averageHue=%236481a2&clientId=u46c960ec-5ff2-4&from=paste&height=254&id=u1402ae01&originHeight=254&originWidth=654&originalType=binary&ratio=1&rotation=0&showTitle=false&size=19145&status=done&style=none&taskId=u6bf75032-eb1e-4b08-9679-e74abba77a1&title=&width=654)
### hash
```java
    static final int hash(Object key) {
        int h;
        return (key == null) ? 0 : (h = key.hashCode()) ^ (h >>> 16);
    }
```

1. 先判断key是否为null，若为null则直接返回0。
2. 如果key不为null，则调用key的hashCode()方法，获得一个32位整数h。
3. 接着，对h进行如下操作：
   - h ^ (h >>> 16)
   - 即将h右移16位，并将此结果与h异或。因为java的位运算符优先级比较低，所以先执行无符号右移操作，使得高位被低位覆盖。然后通过异或操作将高16位与低16位混合起来。
### put
```java
    public V put(K key, V value) {
        return putVal(hash(key), key, value, false, true);
    }
```
### putVal
```java
final V putVal(int hash, K key, V value, boolean onlyIfAbsent,
                   boolean evict) {
    Node<K,V>[] tab; Node<K,V> p; int n, i;
    // 数组未初始化，进行初始化。
    if ((tab = table) == null || (n = tab.length) == 0)
        n = (tab = resize()).length;
    // key的哈希&数组长度-1得到的值（数组下标），
    // 在数组这个数组下标没有值，直接把key-value放进去。
    if ((p = tab[i = (n - 1) & hash]) == null)
        tab[i] = newNode(hash, key, value, null);
    else {
        // 下标已有元素，开始判断是链表还是红黑树。
        Node<K,V> e; K k;
        // 若已有元素的key值和新增元素的key是同一个（hash一致，key值一样）
        if (p.hash == hash &&
            ((k = p.key) == key || (key != null && key.equals(k))))
            // 直接覆盖value
            e = p;
        // 已有元素是树节点
        else if (p instanceof TreeNode)
            e = ((TreeNode<K,V>)p).putTreeVal(this, tab, hash, key, value);
        else {
            // 节点是链表，遍历链表，变量binCount记录链表长度
            for (int binCount = 0; ; ++binCount) {
                
                if ((e = p.next) == null) {
                    p.next = newNode(hash, key, value, null);
                    // 如果链表长度达到阈值TREEIFY_THRESHOLD（默认为8），则调用treeifyBin将链表转换为红黑树
                    if (binCount >= TREEIFY_THRESHOLD - 1) // -1 for 1st
                        // 将一个桶中的链表转化为红黑树
                        treeifyBin(tab, hash);
                    break;
                }
                // 若已有元素的key值和新增元素的key是同一个（hash一致，key值一样）
                if (e.hash == hash &&
                    ((k = e.key) == key || (key != null && key.equals(k))))
                    break;
                // 直接覆盖value
                p = e;
            }
        }
        if (e != null) { // existing mapping for key
            V oldValue = e.value;
            if (!onlyIfAbsent || oldValue == null)
                e.value = value;
            afterNodeAccess(e);
            return oldValue;
        }
    }
    ++modCount;
    // 总节点数大于阈值，需进行扩容
    if (++size > threshold)
        // 扩容
        resize();
    afterNodeInsertion(evict);
    return null;
}
```
### treeifyBin 链表转为红黑树
```java
    final void treeifyBin(Node<K,V>[] tab, int hash) {
        int n, index; Node<K,V> e;
        // 若数组长度小于64，只会进行数组扩容，不会转化为红黑树，虽然链表长度大于8。
        if (tab == null || (n = tab.length) < MIN_TREEIFY_CAPACITY)
            resize();
        else if ((e = tab[index = (n - 1) & hash]) != null) {
            TreeNode<K,V> hd = null, tl = null;
            do {
                TreeNode<K,V> p = replacementTreeNode(e, null);
                if (tl == null)
                    hd = p;
                else {
                    p.prev = tl;
                    tl.next = p;
                }
                tl = p;
            } while ((e = e.next) != null);
            if ((tab[index] = hd) != null)
                hd.treeify(tab);
        }
    }
```
