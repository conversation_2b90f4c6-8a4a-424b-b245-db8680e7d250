# HashMap扩容机制
## 一些默认值
### 初始容量 16
> 默认初始容量-必须是2的幂。

```java
static final int DEFAULT_INITIAL_CAPACITY = 1 << 4; // aka 16
```
### 最大容量 1 << 30
> 最大容量，如果两个构造函数中的任何一个带参数地隐式指定了更高的值，则使用最大容量。必须是2的幂<= 1<<30。

```java
static final int MAXIMUM_CAPACITY = 1 << 30;
```
### 负载因子 0.75
```java
static final float DEFAULT_LOAD_FACTOR = 0.75f;
```
### 红黑树节点最小值 8
> hash桶节点至少大于8，链表才可能转化为红黑树。

```java
static final int TREEIFY_THRESHOLD = 8;
```
### 转为链表时节点数量 6
> hash桶节点小于6时，红黑树会退化为链表。

```java
static final int UNTREEIFY_THRESHOLD = 6;
```
### 转为红黑树，数组最小容量 64
> 可以对桶进行树化的最小表容量。(否则，如果一个bin中的节点太多，则会调整表的大小。)应该至少为4 * TREEIFY_THRESHOLD，以避免调整大小和树化阈值之间的冲突。

```java
 static final int MIN_TREEIFY_CAPACITY = 64;
```
## 扩容
[https://blog.csdn.net/lkforce/article/details/89521318](https://blog.csdn.net/lkforce/article/details/89521318)
