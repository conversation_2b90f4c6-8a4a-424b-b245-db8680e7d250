```java
public class Test {
    private Long money;

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public static void main(String[] args) {
        Test test = new Test();
//        Long money = Objects.nonNull(test.getMoney()) ? test.getMoney() : Long.valueOf(0L);
        /**
         * test.getMoney() 自动拆箱：Long.valueOf(test.getMoney())  Long.valueOf(null)  就报空指针异常。
         */
//        Long money = Objects.nonNull(test) ? test.getMoney() : 0L;

        // 类型一致就不会。 返回null
        Long money = Objects.nonNull(test) ? test.getMoney() : Long.valueOf(0L);

        System.out.println(money);
    }
}
```
![](https://cdn.nlark.com/yuque/0/2023/webp/684952/1681783571935-ff2dbb2a-2adf-49ad-87fb-e70a034ce534.webp#averageHue=%23f4f4f3&clientId=u31dae7d5-c0fa-4&from=paste&id=ue5163185&originHeight=453&originWidth=1053&originalType=url&ratio=1&rotation=0&showTitle=false&status=done&style=none&taskId=u86e6b65e-ea2d-4a7d-b1aa-051eb3ab548&title=)
