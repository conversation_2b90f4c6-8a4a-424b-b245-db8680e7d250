# 1. 面对对象思想
## 1.1 三大特性
### 1. 封装

### 2. 继承

### 3. 多态
## 1.2 名词

```java
抽象类: 方法可以有抽象的，也可以有非抽象, 有构造器
接口: 方法都是抽象，属性都是常量，默认有public static final修饰, jdk1.8 默认default
```

# 2. 基本语法
## 2.1 基本数据类型
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247791-0c26744b-62f2-49d1-b68f-9573559b1053.png#averageHue=%23f8f8f8&height=402&id=DVWDg&originHeight=402&originWidth=826&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=826)
## 2.2 instanceof

```java
用来测试一个对象是否为一个类的实例
boolean result = obj instanceof Class;
int i = 0;
System.out.println(i instanceof Integer);//编译不通过 i必须是引用类型，不能是基本类型
System.out.println(i instanceof Object);//编译不通过
// 先检测i能否转换成右边的类型， 不能 编译不通过
```

## 2.3 自动装箱和拆箱
### 1. 装箱

```java
int -> Integer
Integer.valueOf(int)
// [-128 127], IntgerCache.cache,
    
```

### 2. 拆箱

```java
Integer -> int 
Integer.intValue(Integer);
```

## 2.4 重载和重写

```java
重写 Override
 - 继承 extend, 发生在父类与子类之间
 - 方法名， 参数列表， 返回类型必须相同
 - public > protected > default > private
 - 重写的方法不能抛出异常
 
重载 Overload
 - 一个类中， 有同名的方法，参数列表不同（参数类型，参数个数，参数顺序）
 - 返回值没要求，可同可不同
 - 一个类中多态性的一种表现
```

## 2.5 equals与==

```java
== 
 - 比较引用地址（内存地址），判断两个对象的地址是否相同， 是不是同一个对象
 - 两边同一类型
 - 比较地址， int a = 10； long b = 10L; double c = 10.0 true, 地址都是指向10
equals
 - 比较两个对象的内容（值）是否相同，Object类下的
```


## 2.6 Hashcode


```java
hashcode
 - 是根据对象的内存地址换算出一个值hash
 - 如集合添加新元素，计算元素的hashcode， 判断该code是否存在， 不存在就直接存入，存在再用equals比较值是否一样， 一样就不存
 - 两个不相等的对象，它们的hashcode可能相同
  - hash冲突处理方法
   -- 拉链法
   -- 开放定址法
   -- 再哈希
```


## 2.7 String、StringBuffer、StringBuilder

```java
String 
 - 不变字符串， 每次对String进行操作都是生成新的String对象
 - 底层字符数组 private final char value[];
StringBuffer
 - 继承了AbstractStringBuilder抽象类
 - 可变， 底层  char[] value;
 - 线程安全 加了锁
StringBuilder
 - 继承了AbstractStringBuilder抽象类
 - 可变， 底层  char[] value;
 - 线程不安全
```

## 2.8 四种引用、强弱软虚

```java
强引用
 - OOM也不会被回收
 - new String("sssss");
弱引用
 - 垃圾回收器发现了，就会回收
 - WeakReference<String> wrf = new WeakReference<String>(str);
 - 场景：java.util.WeakHashMap， 不需要这个引用， JVM自动回收
软引用
 - OOM时会被回收
 - SoftReference<String> wrf = new SoftReference<String>(new String("str"));
 - 场景：创建缓存的时候，创建的对象放进缓存中，当内存不足时，JVM就会回收早先创建的对象。
虚引用
 - 虚引用的回收机制跟弱引用差不多，但是它被回收之前，会被放入ReferenceQueue 中
 - 虚引用大多被用于引用销毁前的处理工作
 - 虚引用创建的时候，必须带有 ReferenceQueue 
 - PhantomReference<String> prf = new PhantomReference<String>(new String("str"), new ReferenceQueue<>());
 - 场景：对象销毁前的一些操作，比如说资源释放等
```

## 2.9 Java创建对象的方法

```java
- new
- 反射机制
- clone机制
- 序列化机制
```


## 2.10 深拷贝和浅拷贝

```java
clone
浅拷贝
  - 对象的所有变量和原来的一样，所有对其他对象的引用还是指向原来的对象, 
深拷贝
  - 对象的所有对象和原来一样，所有对其他对象的引用也复制，
```

## 2.11 final

```basic
类 不能被继承
方法 不能被重写 (可以重载)
变量 不能修改，编译阶段存入常量池, final String str ="aaa"; 值不可变
引用 引用不可改， 值可以改 final Map, 引用不可变, 值可变
```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611909855848-7b104e23-06a0-4409-b52b-04a5573aa995.png#averageHue=%23766044&height=88&id=TaXr2&originHeight=88&originWidth=597&originalType=binary&ratio=1&rotation=0&showTitle=false&size=12065&status=done&style=none&title=&width=597)
变了
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611910007909-64a2762e-7764-4e94-aad3-ef1439fb8788.png#averageHue=%232e2d2c&height=323&id=L5N4V&originHeight=323&originWidth=659&originalType=binary&ratio=1&rotation=0&showTitle=false&size=35879&status=done&style=none&title=&width=659)
## 2.12 Java 序列化中如果有些字段不想进行序列化，怎么办？

```java
- transient 关键字修饰
- 阻止实例中那些用此关键字修饰的的变量序列化；当对象被反序列化时，被 transient 修饰的变量值不会被持久化和恢复。
- transient 只能修饰变量，不能修饰类和方法。
```


# 3. 集合
## 3.1 List、Map、Set区别

```java
Collection
 - 集合的上级接口
 - 子接口：Set、List、LinkedList、ArrayList、Vector、Stack、Set
Collections
 - 集合的util类
List
 - 有序， 重复元素
 - 继承Collection
 - 
 Map
 - （k,v）
 - key不能重复, 
Set
 - 无序，不能重复
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198248205-8bef2310-affb-44d5-b5f1-790bd3410722.png#averageHue=%23f7f7f7&height=476&id=fBBmF&originHeight=514&originWidth=1381&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1280)
## 3.2 List
### 1. ArrayList

```java
- 底层： 数组， 可以自动增长容量
- 查询快get set
```

### 2. LinkedList

```java
- 底层：双向链表
- 添加、删除快
```

## 3.3. Map
### 1. HashMap

```java
- 底层： 数组 + 链表 + 红黑(jdk1.8)
  -- 根据key的hashCode
 - 继承AbstractMap
 - key可以为null， 只能有一个， 保证key唯一性， value为null可以有多个, 通过hash计算key, 相同到链表
 - 线程不安全， Collections.synchronizedMap(map) 可实现线程安全, ConcurrentHashMap
 - 初始容量大小 16
```

#### Java7实现
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247810-6af78d2f-e6eb-48e3-a1f1-5a22bf340de4.png#averageHue=%23fcfbfb&height=376&id=GY1ll&originHeight=376&originWidth=1078&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1078)
```java
1.  capacity：当前数组容量，始终保持 2^n，可以扩容，扩容后数组大小为当前的 2 倍。
2.  loadFactor：负载因子，默认为 0.75。
3.  threshold：扩容的阈值，等于 capacity * loadFactor
```

#### Java8实现
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247889-760e8893-2131-4429-a832-aa51d285b50f.png#averageHue=%23f7f3f2&height=395&id=HFbEE&originHeight=395&originWidth=1072&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1072)
```
- 链表元素超过8个，会自动转换成红黑树
```


### 2. HashTable

```
- 底层： 
 - 继承 Dictionary
 - key和value都不能为null
 - 线程安全， 方法上加了synchronized关键字，同时只能有一个线程
```


### 3. ConcurrentHashMap

```
- 底层：数据 + 链表 + 红黑树
 - 线程安全，Segment分段锁，没有对整个数据加锁，
 - 默认16个 Segment数, 并发数
 - 最多可以同时支持 16 个线程并发写，只要它们的操作分别分布在不同的 Segment 上。
 - Java8引入红黑树
```

### 4. TreeMap


```
- 现 SortedMap 接口
- 按键值排除， 默认升序
```

### 5. LinkedHashMap

```
- 记录插入顺序
- 是 HashMap 的一个子类
```

## 3.4 Set

# 4. IO
## 4.1 分类

```
字节
 - InputStream、OutputStream
字符
 - Writer、Reader
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247791-b27dc124-c38b-4fdb-bbfa-783379b85730.png#averageHue=%23fbfbf8&height=1080&id=xyxiM&originHeight=1080&originWidth=720&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=720)
## 4.2 IO与NIO
# 5. 多线程

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247970-89368327-e9f9-46d1-b035-ddbb9233fec6.png#height=631&id=KGnQO&originHeight=681&originWidth=1381&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1280)
## 5.1 什么是线程安全

- 多线程访问同一代码, 每次执行产生的结果不确定
   -  保证每次执行的结果确定,一样就是线程安全
- 指某个方法在**多线程环境**中被调用时，能够**正确地处理多个线程之间的共享变量**，使程序功能正确完成。

Servlet 不是线程安全， 单例多线程
### 怎么保证线程安全

- 使用安全类 java.util.concurrent 下的类 原子类 AtomicInteger。。。
- 使用自动锁 synchronized
- 使用手动锁 Lock
   - Lock lock = **new** ReentrantLock();
## 5.2 线程的基本状态

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247941-42fe0c16-26a5-4bbf-b927-e85766fdfc3d.png#height=252&id=Yt6a8&originHeight=252&originWidth=976&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=976)

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247877-f192c215-32be-4584-83e4-2bf3fd266549.png#height=708&id=mCFZK&originHeight=708&originWidth=1162&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1162)
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247764-5c04efae-c04c-4ae1-8e4e-e71a057fcd7e.png#height=316&id=CFIlg&originHeight=316&originWidth=966&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=966)
## 5.3 如何实现多线程

```java
- 继承 Thread 类
- 实现 Runable 接口, 无返回值
- 实现 Callable 接口, 有返回值
- 线程池 ExecutorService
```

## 5.4 线程常用方法


```java
stop() 
 - 强行停止(不推荐, 过时)
interrupt() 
 - 中断线程
 - 调用 interrupt()方法并不会中断一个正在运行的线程
 - 给这个线程一个通知信号，会影响这个线程内部的一个中断标识位。
notify()   
 - Object类的方法, 只唤醒一个线程进入运行状态
 - 调用notify()之前, 当前线程必须先获取对象锁, 才能唤醒
notifyAll() 
 - Object类的方法, 可以唤醒所有处于wait状态的线程
 
wait() 
 - Object类的方法, 线程会释放对象锁, 进入等待队列,notify()可唤醒
 - 导致当前线程进入 WATING 状态
 - 是对象锁, notify()也是对象锁, 如果线程需要等待某些锁, 那么调用对象中的wait()方法就有意义了,如果wait()方法定义在Thread类中，线程正在等待的是哪个锁就不明显了。
sleep() 
 - Thread类的方法, 不会释放对象锁, 指定时间到达再次恢复运行状态
 - 导致线程进入 TIMED-WATING 状态
yield()
 - 使当前线程让出 CPU 执行时间片，与其他线程一起重新竞争 CPU 时间片。
 - 一般情况下，优先级高的线程有更大的可能性成功竞争得到 CPU 时间片，
 - 可能让出后又得到时间片
 
join()
 - 等待其他线程终止，在当前线程中调用一个线程的 join() 方法，则当前线程转为阻塞状态，回到另一个线程结束，当前线程再由阻塞状态变为就绪状态，等待 cpu 的宠幸。
start() 
 - 创建新线程, 进入就绪状态, 未运行
run()  
 - 启动线程, 进入运行状态
 
interrupted() 
 - 静态方法, 检查中断状态时，中断状态会被清零
 -- Java多线程的中断机制是用内部标识来实现的，调用Thread.interrupt()来中断一个线程就会设置中断标识为true。
isInterrupted()
 - 非静态方法, 用来查询线程的中断状态且不会改变中断状态标识
 
 守护线程
  -  setDaemon(true)来设置线程为“守护线程”
currentThread() 得到当前线程
activeCount() 程序中活跃的线程数
...
```
## 5.5 线程调度

```basic
1. 抢占式调度
 - 根据时间片, 不同线程执行时间不一样,有长有短, 有的得不到执行的时间片, 看优先级
2. 协同式调度
 - 某一线程执行完后主动通知系统切换到另一线程上执行
```

### 1. JVM 的线程调度实现- 抢占式调度

   - 按照优先级分配CPU时间片,
      - 优先级 int 0-10
### 2. 线程让出 cpu 的情况

   - 主动让出, 如yield()
   - 阻塞,
   - 时间片执行完成, run()执行完成
## 5.6 [volatile ](https://mp.weixin.qq.com/s/MY7moP4SNnJnKaSKy7WuPA)
```java
- volatile修饰的变量, 具有了可见性, 值一旦修改, 将新值写入内存(JMM), 其他线程都马上知道
- 禁止进行指令重排序
- 比 sychronized 更轻量级的同步锁
- 不能保证原子性, 如a++, 
- 单个读或写 可以保证原子性
```

## 5.7 [ThreadLocal](https://mp.weixin.qq.com/s/G9ISms47K6Kwz9_NPE0wXw)

```java
线程本地变量
每个线程都有一个ThreadLocalMap对象
ThreadLocalMap   // key是ThreadLocal对象 this, value是变量值
```

## 5.8 synchronized

- 解决多线程之间访问资源的同步性
- synchronized修改的方法, 代码块同时在任意时刻只能有一个线程在执行
- 保证原子性和可见性
### 1. 使用方法

   - 修饰实例方法
      - 当前对象加锁, 调用实例方法之前要先获取当前对象锁
   - 修饰静态方法
      - 给当前类加锁, 作用于当前类所有方法
   - 修改代码块
      - 指定加锁对象, 可以是当前对象this, 或自定义对象
### 2. 锁升级过程


## 5.9 CyclicBarrier 、CountDownLatch 、Semaphore  的 用法
### 1. CyclicBarrier 回环栅栏

```java
可以实现让一组线程等待至某个状态之后再全部同时执行, 
CyclicBarrier 可以被重用
CyclicBarrier CyclicBarrier = new CyclicBarrier(2);
CyclicBarrier.await(); 挂起线程
```

### 2. CountDownLatch 计数器

```java
线程计数器, 不能重用
线程A等另外5个线程执行完成在执行
CountDownLatch countDownLatch = new CountDownLatch(5);
线程A执行 countDownLatch.await();   等待其他线程执行
5个线程执行 countDownLatch.countDown();  计数到达5, 线程A就会执行, 没有达到线程A就一直等待

for循环执行5个线程, 全部执行完成, 整合返回值
```

### 3. Semaphore 信号量

```java
可以控制同时访问的线程个数, 
一般用于控制对某组资源的访问权限
窗口买东西, 有50个人, 只有5个窗口
Semaphore semaphore = new Semaphore(5);
semaphore.acquire();  // 获取一个许可, 没有获取就一直等待
xxx 业务逻辑处理
semaphore.release();  // 释放许可, 必须先获取许可

semaphore.availablePermits()   // 可用的许可数目
tryAcquire(); // 不阻塞
```

## 5.10 线程池

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247847-cc057d4f-90cd-4796-bc38-8a78057a1b29.png#height=765&id=W21AE&originHeight=765&originWidth=811&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=811)

### 1. 为什么要用线程池

1. 做的工作主要是**控制运行的线程的数量**，处理过程中将任务放入队列，然后在线程创建后启动这些任务，如果线程数量超过了最大数量, 超出数量的线程排队等候，等其它线程执行完毕，再从队列中取出任务来执行。
   - **先放到队列， 再创建线程执行**
2. 主要特点为：
   - **线程复用(降低资源消耗**)；
   -  控制最大并发数；
   - **管理线程(**进行统一的分配，调优和监控)。
   - **提高响应速度**

#### 线程池的组成?

1. 线程池管理器：用于创建并管理线程池
2. 工作线程：线程池中的线程
3. 任务接口：每个任务必须实现的接口，用于工作线程调度其运行
4. 任务队列：用于存放待处理的任务，提供一种缓冲机制
### 2. 有哪些线程池

```java
ExecutorService executorService = Executors.newFixedThreadPool(5);

newCachedThreadPool
 - 线程池大小不做限制
newFixedThreadPool
 - 固定线程数的线程池
newScheduledThreadPool
 -它可安排在给定延迟后运行命令或者定期地执行。
newSingleThreadExecutor
 - 这个线程池只有一个线程
```

### 3. 如何创建线程池 Executors
#### 源代码

```java
ExecutorService executorService = Executors.newFixedThreadPool(5);
源码:
public static ExecutorService newFixedThreadPool(int nThreads) {
    return new ThreadPoolExecutor(nThreads, nThreads,
                                  0L, TimeUnit.MILLISECONDS,
                                  new LinkedBlockingQueue<Runnable>());
}
private static final RejectedExecutionHandler defaultHandler = new AbortPolicy();
public ThreadPoolExecutor(int corePoolSize,	
                          int maximumPoolSize, 
                          long keepAliveTime, 
                          TimeUnit unit,
                          BlockingQueue<Runnable> workQueue
                          ) {
    this(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue,
         Executors.defaultThreadFactory(), defaultHandler);
}
构造方法:
public ThreadPoolExecutor(int corePoolSize,		// 核心线程数
                          int maximumPoolSize,	// 最大线程数
                          long keepAliveTime,	// 线程空闲时间
                          TimeUnit unit,		 // 线程空闲时间单位
                          BlockingQueue<Runnable> workQueue,		// 阻塞队列
                          ThreadFactory threadFactory,		// 线程工厂
                          RejectedExecutionHandler handler	// 拒绝策略(线程池满了,线程数达到最大)
                          ) {
    if (corePoolSize < 0 ||
        maximumPoolSize <= 0 ||
        maximumPoolSize < corePoolSize ||
        keepAliveTime < 0)
        throw new IllegalArgumentException();
    if (workQueue == null || threadFactory == null || handler == null)
        throw new NullPointerException();
    this.corePoolSize = corePoolSize;
    this.maximumPoolSize = maximumPoolSize;
    this.workQueue = workQueue;
    this.keepAliveTime = unit.toNanos(keepAliveTime);
    this.threadFactory = threadFactory;
    this.handler = handler;
}
```

#### 七个参数解释


```java
corePoolSize  核心线程数
maximumPoolSize  最大线程数
keepAliveTime  空闲线程存活时间 
 - 一个线程如果处于空闲状态，并且当前的线程数量大于corePoolSize，那么指定时间后，这个空闲线程会被销毁
unit  时间单位
workQueue  阻塞队列,新提交的任务放到队列,执行任务再取出执行
threadFactory    线程工厂
 - 可以用来设定线程名、是否为daemon线程等等
handler  拒绝策略, 当任务太多来不及处理，如何拒绝任务。
```


#### 阻塞队列

```java
workQueue  阻塞队列,新提交的任务放到队列,执行任务再取出执行
 - ArrayBlockingQueue
   -- 基于数组的有界阻塞队列，按FIFO(先进先出)排序,新任务进来后，会放到该队列的队尾
   -- 如果线程数量已经达到 maxPoolSize，则会执行拒绝策略。
 - LinkedBlockingQuene
   -- 基于链表的无界阻塞队列(Interger.MAX),按照FIFO排序
   -- maximumPoolSize 达到最大, 有新任务进来，会一直存入该队列  
 - SynchronousQuene
   -- 一个不缓存任务的阻塞队列，生产者放入一个任务必须等到消费者取出这个务， 同步
   -- 线程数量达到maxPoolSize，则执行拒绝策略。
 - PriorityBlockingQueue
   -- 具有优先级的无界阻塞队列，优先级通过参数Comparator实现。
```

#### 拒绝策略

```java
handler  拒绝策略
 - CallerRunsPolicy
   -- 在调用者线程中直接执行被拒绝任务的run方法，除非线程池已经shutdown，则直接抛弃任务。
 - AbortPolicy
   -- 直接丢弃任务，并抛出RejectedExecutionException异常。
   -- 默认策略
 - DiscardPolicy
   -- 直接丢弃任务，什么都不做。
 - DiscardOldestPolicy
   -- 抛弃进入队列最早(最老)的那个任务，然后尝试把这次拒绝的任务放入队列
```

### 4. 线程池工作过程

```java
1. 线程池创建完成
2. 执行execute()添加任务
 2.1 活跃线程数 < corePoolSize, 直接创建核心线程执行任务
 2.2 活跃线程数 >= corePoolSize, 任务放到阻塞队列等待
 2.3 阻塞队列满了, 活跃线程数 < maximumPoolSize, 立刻创建非核心线程执行任务
 2.4 阻塞队列满了, 活跃线程数 >= maximumPoolSize, 执行拒绝策略
3. 一个线程完成, 再从队列中取出下一个任务
4. 如果一个线程无事可做, 空闲时间 > keepAliveTime时, 当前活跃线程 > corePoolSize, 这个线程就会被销毁
```

#### 源码:

```
// 提交任务，任务并非立即执行，所以翻译成执行任务似乎不太合适
public void execute(Runnable command) {
    // 任务不能为空
    if (command == null)
        throw new NullPointerException();
    // 控制变量（高3位存储状态，低29位存储工作线程的数量）
    int c = ctl.get();
    // 1. 如果工作线程数量小于核心数量
    if (workerCountOf(c) < corePoolSize) {
        // 就添加一个工作线程（核心）
        if (addWorker(command, true))
            return;
        // 重新获取下控制变量
        c = ctl.get();
    }
    // 2. 如果达到了核心数量且线程池是运行状态，任务入队列
    if (isRunning(c) && workQueue.offer(command)) {
        int recheck = ctl.get();
        // 再次检查线程池状态，如果不是运行状态，就移除任务并执行拒绝策略
        if (! isRunning(recheck) && remove(command))
            reject(command);
            // 容错检查工作线程数量是否为0，如果为0就创建一个
        else if (workerCountOf(recheck) == 0)
            addWorker(null, false);
    }
    // 3. 任务入队列失败，尝试创建非核心工作线程
    else if (!addWorker(command, false))
        // 非核心工作线程创建失败，执行拒绝策略
        reject(command);
}
```

#### 流程图


![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247905-3f01f138-7cfc-43b6-9700-dfdeaf67d840.png#height=635&id=hQwd9&originHeight=1216&originWidth=1200&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=627)
![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247860-84661315-e0e4-4ddd-bc05-624c0bd4f8c2.png#height=596&id=OC2ZO&originHeight=770&originWidth=724&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=560)
#### 如何合理分配线程池大小？

- CPU密集
   - 没有阻塞， 任务需要大量计算
   - 多核CPU场景
   - 少点线程。线程数=1*cpu核数
- IO密集
   - 任务需要大量IO， 阻塞。若单线程运行IO密集的任务会导致浪费大量时间（CPU在等待），所有使用多线程。
   - 线程多在阻塞等待，多点线程。线程数=2*cpu核数
# 6. 锁
## 6.1 乐观锁
### 版本号

```
- 很乐观, 读多写少, 认为并发写的情况不多, 自己在修改数据时, 别人不会同时修改, 所有不上锁.
 - 每次更新数据版本号version+1, 线程A更新数据前查出version=1, 更新数据, where version = 1;
 		线程B也更新数据前查出version = 1, A已经更新完成数据, version变成2, 这是 B更新条件还是 where version = 1; 更新失败.
```

### CAS(compare and swap) 比较并交换

```java
内存地址V
旧的预期值A
即将要更新的目标值B
if(V == A) {		//内存值和预想的值一样
    V == B;    // 新值B赋值给内存V
} else {
    // 啥也不做
}
缺点: 
 - CAS自旋操作：当内存地址V与预期值B不相等时会一直循环比较直到相等，
 - 只能保证一个共享变量的原子操作，
 - 出现ABA问题：当读取内存值V的时候为A，有一个线程将A改为B，后又改为A，CAS会误认为内存值V没有改变
   -- 加版本号解决
案例:
 - java.util.concurrent.atomic
```

## 6.2悲观锁
## 6.3公平锁

```java
多个线程等待同一个锁时, 必须按照申请锁的先后顺序来获取锁.
    等待锁的线程不会饿死, 整体效率偏低,
ReentrantLock reentrantLock = new ReentrantLock(true);   // 公平锁实现
```
## **

```java
通过抢占获取锁, 有点线程等待了很久可能一直抢不到锁
ReentrantLock reentrantLock = new ReentrantLock(false);

public ReentrantLock(boolean fair) {
	sync = fair ? new FairSync() : new NonfairSync();
}
```


## 6.5死锁

## 6.6 synchronized 和 和 ReentrantLock  的区别


## 6.7 AQS
## 6.8 问题?

   1. **有三个线程T1,T2,T3,如何保证顺序执行？**
      - join()
# 7. 反射

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247807-256a4018-a04a-4dc3-a685-aee1d63f7ec5.png#height=469&id=oOROb&originHeight=478&originWidth=1304&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1280)
## 1. 定义
```
- 反射机制是在运行时，对于任意一个类，都能够知道这个类的所有属性和方法；对于任意个对象，都能够调用它的任意一个方法。在java中，只要给定类的名字，就可以通过反射机制来获得类的所有信息。
 - 这种动态获取的信息以及动态调用对象的方法的功能称为Java语言的反射机制。
```

## 2. 实现场景
```
Class.forName('com.mysql.jdbc.Driver.class');//加载MySQL的驱动类
```



# 8. 异常

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247801-c3be9cc9-eb08-4e3a-80d9-a68fac328b5c.png#height=599&id=Zi6O9&originHeight=599&originWidth=1002&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1002)

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1613617984558-000614c2-dde8-4b56-8df4-8d0d0d97f2be.png#height=397&id=eajLm&originHeight=397&originWidth=747&originalType=binary&ratio=1&rotation=0&showTitle=false&size=62995&status=done&style=none&title=&width=747)

## 1. Error
> 系统机错误，表示运行应用程序中出现了严重的错误

```
和运行时异常一样，编译器也不会对错误进行检查。
代码没问题，JVM出现问题，停止线程
NoClassDefFoundError 类定义找不到
OutOfMemoryError 内存不足
StackOverflowError 栈溢出
```


## 2. Exception
> 程序代码上的问题，程序员应该可以避免的。

### 受检查的异常(CheckedException)
> Exception 中除 RuntimeException 及其子类之外的异常。
> **编译器会检查它**，没有throws，try-catch, 编译不会通过



```

ClassNotFoundException（没有找到指定的类异常），
IOException（IO流异常）
...
```

### 运行时异常(RuntimeException)
> RuntimeException 类及其子类，表示** JVM 在运行期间**可能出现的异常。
> **编译器不会检查它**，发生异常时没有throws，try-catch, 编译还是会通过


```java

 NullPointerException 空指针异常、
 ArrayIndexOutBoundException 数组下标越界异常、
 ClassCastException 类型转换异常、
 ArithmeticExecption 算术异常
 ...
```

### throw和throws

```
throws
 - 在方法上， 向上抛出异常
throw
 - 在方法内， 抛给定义的异常类
 
try-with-resource
	资源自动释放， 实现AutoCloseable 接口
```


# 9. 泛型

# 10. 注解

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611198247835-8d1ef308-6884-4979-a151-a6fd2f3633dc.png#height=1208&id=ouRE0&originHeight=1248&originWidth=1322&originalType=binary&ratio=1&rotation=0&showTitle=false&size=0&status=done&style=none&title=&width=1280)
# **11. API接口安全性**
```
1. appId, secretKey 注册
2. token
3. 签名sign, 防止参数被篡改
   - 请求参数按字典顺序拼接(包括appId), （即key1=value1&key2=value2…）
   - 再+secreKey
   - Md5, 转大写
4. timestamp  60s超时, 防御DOS攻击
5. nonce,  随机字符串, 每次请求服务端记录(60s过期), 防止二次请求, 防重放
6. IP白名单
7. Https
8. 接口回调
以上所有参数, 构造签名后, 服务端通过验证, 生成token和rsa公钥,
请求接口
1. 请求头带上token, 请求体用rsa公钥加密请求参数
2. 验证token通过, 服务端用rsa私钥解密参数, 执行下一步逻辑.
3. 返回结果.
```



