# ipset

```shell
ipset create linkcircle hash:net
ipset add linkcircle *******
ipset add linkcircle *******
```

# iptables

```shell
# 新增自定义链
iptables -t mangle -N URL_FILTER 2>/dev/null

iptables -t mangle -A URL_FILTER \
-m mac --mac-source XX:XX:XX:XX:XX:XX \
-m string --string ${keyword} --algo kmp \
-m set --match-set linkcircle dst \
-m time --timestart 08:00 --timestop 18:00 \
--kerneltz \
-j DROP

iptables -t mangle -A FORWARD -j URL_FILTER

# 清空并删除自定义链
iptables -t mangle -D FORWARD -j URL_FILTER
iptables -t mangle -F URL_FILTER
iptables -t mangle -X URL_FILTER

# 查询自定义链
iptables -t mangle -L URL_FILTER -v -n

# 删除自定义链
iptables -t mangle -L URL_FILTER -v -n --line-numbers
iptables -t mangle -D URL_FILTER 1
iptables -t mangle -D URL_FILTER -m string --string 'qq.com' --algo kmp -j DROP


```

## 关键词

```shell
iptables -t mangle -A URL_FILTER \
-m string --string qq.com --algo kmp \
-m time --timestart 08:00 --timestop 18:00 --kerneltz \
-j DROP
```