## 宿主机

```shell
# 查看物理网卡名称(一般带有路由器分配的ip)
ifconfig

# 设置网卡混杂模式PROMISC
ifconfig eth0 promisc

# 查看网卡标识是否设置成功
ifconfig |grep eth0

docker network create -d macvlan --subnet=************/24 --gateway=************* -o parent=eth0 openwrt_net

```

## 安装

```shell
# 拉取镜像
docker pull crazygit/lean-openwrt-x86-64:latest

# 创建数据卷
docker volume create openwrt_volume

# 启动容器
docker run -d --name openwrt -p 80:80 --privileged  -v openwrt_volume:/etc --network openwrt_net crazygit/openwrt-x86-64 /sbin/init

# 进入容器
docker exec -it openwrt /bin/sh

# 配置Lan口网络
vim /etc/config/network

config interface 'loopback'
        option ifname 'lo'
        option proto 'static'
        option ipaddr '127.0.0.1'
        option netmask '*********'

config globals 'globals'
        option ula_prefix 'fd47:d2ef:0ca0::/48'

config interface 'lan'
        option type 'bridge'
        option ifname 'eth0'
        option proto 'static'
        option ipaddr '************'
        option gateway '*************'
        option netmask '*************'
        option ip6assign '60'
        option dns '***************'
        
        

```

## 国内源

> 清华源 https://mirrors.tuna.tsinghua.edu.cn/help/openwrt/

```shell
 vi /etc/opkg/customfeeds.conf
src/gz openwrt_core http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/targets/rockchip/armv8/packages
src/gz openwrt_base http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/packages/aarch64_generic/base
src/gz openwrt_luci http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/packages/aarch64_generic/luci
src/gz openwrt_packages http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/packages/aarch64_generic/packages
src/gz openwrt_routing http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/packages/aarch64_generic/routing
src/gz openwrt_telephony http://mirrors4.tuna.tsinghua.edu.cn/openwrt/releases/21.02.0/packages/aarch64_generic/telephon

```

