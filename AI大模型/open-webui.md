## open-webui



```shell
docker run -d \
-p 3000:8080 \
-e OPENAI_API_KEY=sk-7wMSpaoIlZA7zE2J210228387eC54988A2815b177fA94525 \
-v open-webui:/app/backend/data \
--name open-webui \
--restart always \
ghcr.io/open-webui/open-webui:main
```

### 找回密码

```shell
htpasswd -bnBC 10 "" admin | tr -d ':\n'
$2y$10$qvvoHul9TQMfxXCqfwI7euv5wBKZVBFWEo.Mdgb.O3IWln25x5ML2%
docker run --rm -v open-webui:/data alpine/socat EXEC:"bash -c 'apk add sqlite && echo UPDATE auth SET password='\''$2y$10$qvvoHul9TQMfxXCqfwI7euv5wBKZVBFWEo.Mdgb.O3IWln25x5ML2%'\'' WHERE email='\''<EMAIL>'\''; | sqlite3 /data/webui.db'", STDIO



```


```
https://api.jimsblog.eu.org/chat   
http://127.0.0.1:3000/
```



## new-api

> https://github.com/Calcium-Ion/new-api
>
> 默认账号root 密码123456

```shell
docker run --name new-api -d --restart always -p 3001:3000 -e TZ=Asia/Shanghai -v new-api:/data calciumion/new-api:latest

```





## fastgpt

> https://github.com/labring/FastGPT
