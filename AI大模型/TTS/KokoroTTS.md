# 文档

> https://huggingface.co/hexgrad/Kokoro-82M
> https://pypi.org/project/kokoro/0.8.4/#history

# 安装conda

```shell
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh
conda --version
```

# conda environment.yml

```yaml
name: kokoro
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r
  - defaults
dependencies:
  - python==3.9
  - pip
  - pip:
      - --index-url https://pypi.tuna.tsinghua.edu.cn/simple
      - kokoro>=0.7.16
      - soundfile
      - misaki[en]>=0.8.2
      - misaki[zh]>=0.8.2

```

> 代理

```shell
export http_proxy="http://***********:7897"
export https_proxy="http://***********:7897"
```

```shell
conda activate kokoro
conda env create --prefix /home/<USER>/data -f environment.yml
conda env update --prefix /home/<USER>/data -f environment.yml
conda activate /home/<USER>/data
conda info --envs
```

# 测试

```python
from flask import Flask, request, jsonify, send_file
from kokoro import KPipeline
import soundfile as sf
import io
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 创建 TTS 生成音频的函数
def generate_audio(text, lang_code='a', voice='af_heart', speed=1):
    try:
        logger.info(f"开始生成音频，参数：text={text}, lang_code={lang_code}, voice={voice}, speed={speed}")
        pipeline = KPipeline(lang_code=lang_code)  # 设置语言代码

        # 生成音频
        generator = pipeline(
            text, voice=voice, speed=speed, split_pattern=r'\n+'
        )

        audio_data = None
        for i, (gs, ps, audio) in enumerate(generator):
            audio_data = audio
            break  # 仅获取第一个音频片段

        if audio_data is None:
            logger.error("音频生成失败：未获取到音频数据")
            return None

        # 将音频写入内存中的字节流（WAV 格式）
        byte_io = io.BytesIO()
        sf.write(byte_io, audio_data, 24000, format='WAV')
        byte_io.seek(0)
        logger.info("音频生成成功")
        return byte_io
    except Exception as e:
        logger.error(f"音频生成出错：{str(e)}")
        return None

# 定义 TTS 接口
@app.route('/generate_audio', methods=['POST'])
def tts():
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            logger.warning("未收到有效的 JSON 数据")
            return jsonify({"error": "No JSON data provided"}), 400

        text = data.get('text', '')
        lang_code = data.get('lang_code', 'a')  # 默认为美式英语
        voice = data.get('voice', 'af_heart')   # 语音模型，默认 'af_heart'
        speed = float(data.get('speed', 1))  # 语速，默认 1.0

        logger.info(f"收到 TTS 请求：{data}")

        if not text:
            logger.warning("未提供文本内容")
            return jsonify({"error": "No text provided"}), 400

        # 验证参数
        if not isinstance(speed, (int, float)) or speed <= 0:
            logger.warning(f"无效的速度参数：{speed}")
            return jsonify({"error": "Invalid speed value"}), 400

        # 生成音频
        audio_file = generate_audio(text, lang_code, voice, speed)

        if audio_file is None:
            logger.error("音频生成失败")
            return jsonify({"error": "Failed to generate audio"}), 500

        # 返回音频文件
        return send_file(
            audio_file,
            mimetype='audio/wav',
            as_attachment=True,
            download_name='output.wav'  # 使用 download_name 替代 attachment_filename
        )

    except Exception as e:
        logger.error(f"处理请求时出错：{str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

```

## 接口

```shell
POST http://*************:5000/generate_audio
{
    "text": "Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?Hello, how are you?",
    "lang_code": "a",
    "voice": "af_heart",
    "speed": 1.2
}
```