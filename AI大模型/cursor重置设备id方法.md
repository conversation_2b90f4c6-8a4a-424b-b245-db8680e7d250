# cursor-reset

> https://linux.do/t/topic/404221
> https://github.com/hamflx/cursor-reset

## windows

- 先退出账号
- 确保已关闭 Cursor IDE
- 以管理员身份打开 PowerShell
- 复制粘贴执行以下命令：

```shell
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass; iwr -Uri "https://raw.githubusercontent.com/hamflx/cursor-reset/refs/heads/main/reset.ps1" -UseBasicParsing | iex
```

- 如果脚本卡在"正在等待 Cursor 进程退出…"，可以在管理员权限的命令行中执行以下命令强制结束所有 Cursor 进程：

```shell
taskkill /f /im cursor.exe
```

# Cursor PRO 体验工具 GUI版

> https://linux.do/t/topic/433091
> https://cursor.ccopilot.org/