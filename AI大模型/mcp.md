# Cursor 对话次数翻倍！增强版

> https://github.com/Minidoracat/mcp-feedback-enhanced
> https://linux.do/t/topic/691622

```json
{
    "mcpServers": {
        "mcp-feedback-enhanced": {
            "command": "/Users/<USER>/.local/bin/uvx",
            "args": [
                "mcp-feedback-enhanced@latest"
            ],
            "timeout": 600,
            "env": {
                "FORCE_WEB": "false",
                "MCP_DEBUG": "false"
            },
            "autoApprove": [
                "interactive_feedback"
            ]
        }
    }
}
```

# time

```json
{
    "mcpServers": {
        "time": {
            "command": "/Users/<USER>/.local/bin/uvx",
            "args": [
                "mcp-server-time",
                "--local-timezone=Asia/Shanghai"
            ]
        }
    }
}
```

# Context7 MCP - Up-to-date Code Docs For Any Prompt

```json
{
    "mcpServers": {
        "context7": {
            "command": "npx",
            "args": [
                "-y",
                "@upstash/context7-mcp@latest"
            ]
        }
    }
}
```

# Sequential Thinking

```json
{
    "mcpServers": {
        "sequential-thinking": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-sequential-thinking"
            ]
        }
    }
}
```