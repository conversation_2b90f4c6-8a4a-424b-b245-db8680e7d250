# maven
```java
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.16</version>
        </dependency>
```
# 自定义连接池
```java
public class SftpPool {

    private static final Logger LOGGER = LoggerFactory.getLogger(SftpPool.class);

    private String host;

    private Integer port;

    private String username;

    private String password;

    private Integer maxSession;

    private Integer maxChannel;

    private Integer timeout;

    private final BlockingQueue<Session> sessionPool;

    private final BlockingQueue<ChannelSftp> channelPool;

    private final AtomicInteger sessionActiveCount = new AtomicInteger();

    private final AtomicInteger channelActiveCount = new AtomicInteger();

    /**
     * 锁对象，用于对池中对象的访问进行同步
     */
    private final Lock lock = new ReentrantLock();

    public SftpPool(String host,
                    Integer port,
                    String username,
                    String password,
                    Integer maxSession,
                    Integer maxChannel,
                    Integer timeout) {
        paramsCheck(host, port, username, password, maxSession, maxChannel, timeout);
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.maxSession = maxSession;
        this.maxChannel = maxChannel;
        this.timeout = timeout;
        sessionPool = new ArrayBlockingQueue<>(this.maxSession);
        channelPool = new ArrayBlockingQueue<>(this.maxChannel * this.maxSession);
    }

    private static void paramsCheck(String host, Integer port, String username, String password,
                                    Integer maxSession, Integer maxChannel, Integer timeout) {
        if (host == null || port == null || username == null || password == null || timeout == null
                || maxSession == null || maxChannel == null) {
            throw new NullPointerException("sftp pool params is null.");
        }

    }

    /**
     * 初始化连接池
     */
    public void initialization() {
        paramsCheck(host, port, username, password, maxSession, maxChannel, timeout);
        lock.lock();
        try {
            for (int i = 0; i < maxSession; i++) {
                Session session = createSession();
                boolean offer = sessionPool.offer(session);
                LOGGER.info("init session: {}, {}", session.getHost(), offer);

                for (int j = 0; j < maxChannel; j++) {
                    ChannelSftp channelSftp = JschUtil.openSftp(session);
                    boolean offerChannel = channelPool.offer(channelSftp);
                    LOGGER.info("init channel: {}, {}", channelSftp.isConnected(), offerChannel);
                }
            }
            LOGGER.info("init finish, channal count: {}", channelPool.size());
        } finally {
            lock.unlock();
        }
    }

    private Session createSession() {
        return JschUtil.createSession(host, port, username, password);
    }

    /**
     * 获取一个Session对象
     *
     * @return Session
     * @throws Exception e
     */
    public Session getSession() throws Exception {
        Session session;
        lock.lock();
        try {
            session = sessionPool.take();
            sessionActiveCount.incrementAndGet();
            LOGGER.info("current active session count: {}", sessionActiveCount.get());
        } finally {
            lock.unlock();
        }
        return session;
    }

    /**
     * 获取一个SFTP通道对象
     *
     * @return ChannelSftp
     * @throws Exception e
     */
    public ChannelSftp getChannelSftp() {
        ChannelSftp sftp = null;
        lock.lock();
        try {
            sftp = channelPool.take();
            if (!sftp.isConnected()) {
                // com.jcraft.jsch.SftpException: java.io.IOException: Pipe closed
                LOGGER.error("channel is not connected, creating new session and channel.");
                // 重新创建channel
                Session session = sftp.getSession();
                if (!session.isConnected()) {
                    sessionPool.remove(session);
                    Session newSession = createSession();
                    sessionPool.offer(newSession);
                    ChannelSftp channelSftp = JschUtil.openSftp(newSession);
                    if (channelSftp.isConnected()) {
                        sftp = channelSftp;
                    }
                } else {
                    sftp = JschUtil.openSftp(session);
                }
            }
            channelActiveCount.incrementAndGet();
            LOGGER.info("current channel active: {}, available: {}", channelActiveCount.get(), channelPool.size());
        } catch (Exception e) {
            LOGGER.error("channelPool.take() error: ", e);
        } finally {
            lock.unlock();
        }
        return sftp;
    }

    /**
     * 归还一个Session对象
     *
     * @param session session
     */
    public void returnSession(Session session) {
        lock.lock();
        try {
            sessionPool.offer(session);
            sessionActiveCount.decrementAndGet();
            LOGGER.info("current active session count: {}", sessionActiveCount.get());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 归还一个SFTP通道对象
     *
     * @param channelSftp channelSftp
     */
    public void returnChannelSftp(ChannelSftp channelSftp) {
        lock.lock();
        try {
            channelPool.offer(channelSftp);
            channelActiveCount.decrementAndGet();
            LOGGER.info("return current channel active: {}, available: {}", channelActiveCount.get(), channelPool.size());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 关闭连接池中的所有Session和ChannelSftp对象
     */
    public void close() {
        lock.lock();
        try {
            for (ChannelSftp sftp : channelPool) {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                }
            }
            for (Session session : sessionPool) {
                if (session.isConnected()) {
                    session.disconnect();
                }
            }
            channelPool.clear();
            sessionPool.clear();
        } finally {
            lock.unlock();
        }
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public BlockingQueue<Session> getSessionPool() {
        return sessionPool;
    }

    public BlockingQueue<ChannelSftp> getChannelPool() {
        return channelPool;
    }

    public Integer getMaxSession() {
        return maxSession;
    }

    public void setMaxSession(Integer maxSession) {
        this.maxSession = maxSession;
    }

    public Integer getMaxChannel() {
        return maxChannel;
    }

    public void setMaxChannel(Integer maxChannel) {
        this.maxChannel = maxChannel;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

}

```
# 定义Bean
```java
@Slf4j
@Configuration
@RequiredArgsConstructor
public class SftpPoolConfig {

    private final YdfkSftpInfoProperties ydfkSftpInfoProperties;

    @Bean
    public SftpPool sftpPool() {
        SftpPool sftpPool = new SftpPool(ydfkSftpInfoProperties.getHost(),
                ydfkSftpInfoProperties.getPort(),
                ydfkSftpInfoProperties.getUsername(),
                ydfkSftpInfoProperties.getPassword(),
                ydfkSftpInfoProperties.getMaxSession(),
                ydfkSftpInfoProperties.getMaxChannel(),
                ydfkSftpInfoProperties.getConnectTimeout());
        sftpPool.initialization();
        log.info("sftp pool init finish");
        return sftpPool;
    }
}
```
# commons-pool2实现
## JschFactory
```java
Slf4j
public class JschFactory implements PooledObjectFactory<ChannelSftp> {

    private final String host;

    private final int port;

    private final String username;

    private final String password;

    public JschFactory(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    /**
     * @param pooledObject
     * @throws Exception
     */
    @Override
    public void activateObject(PooledObject<ChannelSftp> pooledObject) throws Exception {
        // 当连接从池中获取时，需要激活一下连接，
        // 保证它是处于打开状态的
        ChannelSftp channelSftp = pooledObject.getObject();
        if (!channelSftp.isConnected()) {
            channelSftp.connect();
        }
    }

    /**
     * 销毁连接对象
     *
     * @param pooledObject
     * @throws Exception
     */
    @Override
    public void destroyObject(PooledObject<ChannelSftp> pooledObject) throws Exception {
        pooledObject.getObject().disconnect();
    }

    /**
     * 销毁连接对象
     *
     * @param pooledObject
     * @param destroyMode
     * @throws Exception
     */
    @Override
    public void destroyObject(PooledObject<ChannelSftp> pooledObject, DestroyMode destroyMode) throws Exception {
        pooledObject.getObject().disconnect();
    }

    /**
     * 创建连接对象
     *
     * @return
     * @throws Exception
     */
    @Override
    public PooledObject<ChannelSftp> makeObject() throws Exception {
        Session session = JschUtil.createSession(host, port, username, password);
        return new DefaultPooledObject<>(JschUtil.openSftp(session));
    }

    /**
     * 钝化
     *
     * @param pooledObject
     * @throws Exception
     */
    @Override
    public void passivateObject(PooledObject<ChannelSftp> pooledObject) throws Exception {
        log.info("passivateObject");
    }

    /**
     * 验证连接对象
     *
     * @param pooledObject
     * @return
     */
    @Override
    public boolean validateObject(PooledObject<ChannelSftp> pooledObject) {
        log.info("validateObject");
        return pooledObject.getObject().isConnected();
    }

}
```
## JschConnectionPool
```java
@Slf4j
public class JschConnectionPool {

    private final GenericObjectPool<ChannelSftp> pool;

    public JschConnectionPool(String host, int port, String username, String password,
                              Integer maxTotal, Integer minIdle, Integer maxIdle,
                              Duration timout) {
        JschFactory jschFactory = new JschFactory(host, port, username, password);
        GenericObjectPoolConfig<ChannelSftp> config = new GenericObjectPoolConfig<>();

        // 设置最大连接数
        config.setMaxTotal(maxTotal);

        // 设置空闲连接数
        config.setMinIdle(minIdle);

        // 设置最大空闲连接数
        config.setMaxIdle(maxIdle);

        // 设置连接等待时间，单位毫秒
        config.setMaxWait(timout);
        config.setTestOnReturn(true);
        config.setTestOnBorrow(true);
        config.setTestOnCreate(true);
        config.setBlockWhenExhausted(true);
        config.setTestWhileIdle(true);
        config.setMinEvictableIdleTime(Duration.ofSeconds(2));
        pool = new GenericObjectPool<>(jschFactory, config);
    }

    public ChannelSftp borrowObject() throws Exception {
        ChannelSftp channelSftp = pool.borrowObject();
        log.info("borrow pool active: {}, idle: {}", pool.getNumActive(), pool.getNumIdle());
        return channelSftp;
    }

    public void returnObject(ChannelSftp channelSftp) throws Exception {
        if (channelSftp.isConnected()) {
            pool.returnObject(channelSftp);
        }
        log.info("return pool active: {}, idle: {}", pool.getNumActive(), pool.getNumIdle());
    }

    public void close() {
        pool.close();
    }

}
```
## ConnectPoolConfig
```java
@Configuration
public class ConnectPoolConfig {

    @Bean
    public JschConnectionPool jschConnectionPool() {

        JschConnectionPool pool = new JschConnectionPool("172.16.251.81",
                22,
                "root",
                "cqt@2020",
                2,
                1,
                2,
                Duration.ofSeconds(3));
        return pool;
    }
}
```
## 使用
```java
    @PostMapping("upload")
    public void upload() {
        ChannelSftp channelSftp = null;
        try {
            channelSftp = jschConnectionPool.borrowObject();

            channelSftp.put("/home/<USER>",
                    "/home/<USER>");
            log.info("upload success");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (channelSftp != null && channelSftp.isConnected()) {
                try {
                    jschConnectionPool.returnObject(channelSftp);
                } catch (Exception e) {
                    log.error("error: ", e);
                }
            }
        }

    }
```
