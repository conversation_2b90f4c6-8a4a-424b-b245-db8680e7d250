> https://jangrui.github.io/macos/brew%20%E6%9B%B4%E6%8D%A2%E6%B8%85%E5%8D%8E%E5%A4%A7%E5%AD%A6%E9%95%9C%E5%83%8F%E6%BA%90/

## 使用清华源、替换现有上游

```
cd "$(brew --repo)"
git remote set-url origin https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/brew.git
cd "$(brew --repo)/Library/Taps/homebrew/homebrew-core"
git remote set-url origin https://mirrors.tuna.tsinghua.edu.cn/git/homebrew/homebrew-core.git
```

## 更新源
```
cd
drew update
```

## 清华源异常，切回homebrew源
+ 重置brew.git
```
cd "$(brew --repo)"
git remote set-url origin https://github.com/Homebrew/brew.git

```

+ 重置homebrew-core.git
```
cd "$(brew --repo)/Library/Taps/homebrew/homebrew-core"
git remote set-url origin https://github.com/Homebrew/homebrew-core.git

```
+ 更新
```
cd
brew update
```
+ 注释掉bash配置文件里的有关Homebrew Bottles
```
vim ~/.bash_profile
#export HOMEBREW_BOTTLE_DOMAIN=https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles
```
+ 重启bash或让bash重读配置文件。
```
source ~/.bash_profile
```
