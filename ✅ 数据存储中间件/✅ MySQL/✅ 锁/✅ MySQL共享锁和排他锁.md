> > - [Shared and Exclusive Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-shared-exclusive-locks)

# 共享锁 S锁
> 不同事务之间不会排斥，可以同时获取锁并执行。

```sql
-- 同时开启多个事务，都能立即查询到数据。
START TRANSACTION;

-- 开启共享锁
select * from student where id = 1 LOCK IN SHARE MODE;
-- mysql8.0写法
-- SELECT * from student FOR SHARE;

-- select * from student FOR UPDATE;

COMMIT;
```

- AB两个事务都开启事务， 执行查询语句，都能成功，
```sql
START TRANSACTION;

-- 开启共享锁
select * from student where id = 1 LOCK IN SHARE MODE;
```

- 当B事务，修改这条id=1的数据，没成功，在等待A事务提交事务
- 当另一个事务尝试对具备共享锁的数据进行写操作时，会被共享锁排斥。
   - B事务被共享锁排斥。
   - 共享锁也具备排他性，会排斥其他尝试写的线程，当有线程尝试修改同一数据时会陷入阻塞，直至持有共享锁的事务结束才能继续执行。
```sql
START TRANSACTION;
select * from student where id = 1 LOCK IN SHARE MODE;

update student set address = '北京北京' where id = 1；
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680872760617-2eebe598-8558-408f-b457-c5e3e4986cfd.png#averageHue=%23faf9f7&clientId=udd34330a-fa0b-4&from=paste&height=329&id=uc72da260&originHeight=658&originWidth=1320&originalType=binary&ratio=2&rotation=0&showTitle=false&size=154250&status=done&style=none&taskId=u7941f28a-2b07-48d8-9cd0-4e394b4849d&title=&width=660)

- A事务提交事务后， B事务 修改操作通过，B事务阻塞了48秒

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680872828274-f49e587b-225b-4d29-bf37-78a916450bbc.png#averageHue=%23fafafa&clientId=udd34330a-fa0b-4&from=paste&height=147&id=ue6b10227&originHeight=294&originWidth=1508&originalType=binary&ratio=2&rotation=0&showTitle=false&size=31987&status=done&style=none&taskId=u5104d45d-2111-449f-8df7-06037e5f29a&title=&width=754)

- 再次开启A事务，查询id=1，A事务阻塞，

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680872897950-2a4c4259-9c0a-4be2-8b87-c6ba318fe807.png#averageHue=%23fbfbfa&clientId=udd34330a-fa0b-4&from=paste&height=389&id=u9fc1de38&originHeight=778&originWidth=1438&originalType=binary&ratio=2&rotation=0&showTitle=false&size=148453&status=done&style=none&taskId=u5bac9763-8900-4ec4-ae38-198d7037012&title=&width=719)

- 过一段时间，A事务锁等待超时
- 锁超时时间？哪里配置？默认50秒

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680872958195-8b796216-9e73-4257-bd9f-85892e16d5b1.png#averageHue=%23f8f8f8&clientId=udd34330a-fa0b-4&from=paste&height=139&id=u7b3d5285&originHeight=278&originWidth=1974&originalType=binary&ratio=2&rotation=0&showTitle=false&size=45126&status=done&style=none&taskId=ue109a3c3-697c-481e-8038-e7db134ea4c&title=&width=987)
## 锁等待超时时间
```sql
SHOW VARIABLES LIKE 'innodb_lock_wait_timeout';

[mysqld] 
innodb_lock_wait_timeout=120
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680873436991-74802fcd-180d-46e3-b6cc-bd4bffcbf2f2.png#averageHue=%23f8f8f8&clientId=udd34330a-fa0b-4&from=paste&height=83&id=u73e19d21&originHeight=166&originWidth=540&originalType=binary&ratio=2&rotation=0&showTitle=false&size=16928&status=done&style=none&taskId=u963eb08d-f859-4b0a-a500-cf9cfc59fb3&title=&width=270)

- 提交B事务的update操作后，A事务select成功

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680873046472-3d76cb16-52e2-4d10-82c4-cfb5ee82706b.png#averageHue=%23fbfbfb&clientId=udd34330a-fa0b-4&from=paste&height=389&id=ubde3b34e&originHeight=778&originWidth=1496&originalType=binary&ratio=2&rotation=0&showTitle=false&size=164417&status=done&style=none&taskId=u93c6cbc8-3426-4c83-a3ef-9089b766959&title=&width=748)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680873059342-f4c55cb1-3552-4c8e-b094-7be134b88127.png#averageHue=%23fafafa&clientId=udd34330a-fa0b-4&from=paste&height=386&id=ud5b62c60&originHeight=772&originWidth=1346&originalType=binary&ratio=2&rotation=0&showTitle=false&size=168521&status=done&style=none&taskId=u0677ee02-e11f-4ba9-9baa-2faad0509f4&title=&width=673)
# 排他锁 X锁

- 也称为独占锁。
- 当一个线程获取到独占锁后，会排斥其他线程，如若其他线程也想对共享资源/同一数据进行操作，必须等到当前线程释放锁并竞争到锁资源才行。
## 读操作显式获取独占锁
```sql
START TRANSACTION;

select * from student FOR UPDATE;

COMMIT;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680873674334-2758a520-4549-4ce8-a49b-6eeb157e0dd2.png#averageHue=%23fbfaf9&clientId=udd34330a-fa0b-4&from=paste&height=295&id=u4e9028df&originHeight=590&originWidth=1338&originalType=binary&ratio=2&rotation=0&showTitle=false&size=151974&status=done&style=none&taskId=u8c4a495c-e208-4e5a-9b82-b3f18abe062&title=&width=669)
# 共享排他锁 SX锁
> 在SQL执行期间一旦更新操作触发B+Tree叶子节点分裂，那么就会对整棵B+Tree加排它锁，这不但阻塞了后续这张表上的所有的更新操作，同时也阻止了所有试图在B+Tree上的读操作，也就是会导致所有的读写操作都被阻塞，其影响巨大。因此，这种大粒度的排它锁成为了InnoDB支持高并发访问的主要瓶颈，而这也是MySQL 5.7版本中引入SX锁要解决的问题。

> 最简单的方式就是减小SMO问题发生时，锁定的B+Tree粒度，当发生SMO问题时，就只锁定B+Tree的某个分支，而并不是锁定整颗B+树，从而做到不影响其他分支上的读写操作。

