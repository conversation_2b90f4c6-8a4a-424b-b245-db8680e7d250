# demo
A事务开启事务， select ... LOCK IN SHARE MODE; A获取S锁
```sql
START TRANSACTION;
SELECT * FROM deadlock WHERE i = 1 LOCK IN SHARE MODE;
```
B事务开启事务删除这条i=1的记录，B获取X锁，这时再阻塞等待A事务释放共享锁（S锁）
```sql
START TRANSACTION;
DELETE FROM deadlock WHERE i = 1;
```

A事务也删除i=1的记录， A请求X锁，获取不到锁
```sql
START TRANSACTION;
SELECT * FROM deadlock WHERE i = 1 LOCK IN SHARE MODE;
DELETE FROM deadlock WHERE i = 1;

```
B事务报错了
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681041521078-b03170aa-cd4f-4a9d-8589-d77f30f296e9.png#averageHue=%23fafafa&clientId=ub2f537b7-41bd-4&from=paste&height=144&id=ue5481f80&originHeight=288&originWidth=2098&originalType=binary&ratio=2&rotation=0&showTitle=false&size=28952&status=done&style=none&taskId=ud20b15dd-a761-4b6f-8950-fd5629aca65&title=&width=1049)
# 分析
> 此处发生死锁是因为客户端 A 需要 X 锁才能删除该行。但是，无法授予该锁定请求，因为客户端 B 已具有 X 锁请求，并且正在等待客户端 A 释放其 S 锁。A持有的S锁也不能因为B事先请求X锁而升级到X锁。结果，InnoDB 为其中一个客户端生成错误并释放其锁。客户端返回此错误：


# MySQL死锁
> MySQL死锁是指两个或多个事务互相持有对方需要的锁，导致相互等待的状态，从而无法继续执行，也无法撤消，最终导致数据库系统无响应，这种情况被称为死锁。

### MySQL的锁超时机制
> 默认50秒。

```sql
show variables like 'innodb_lock_wait_timeout';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681132682411-dafcc485-7ea0-420d-86e6-e0c4363c8ed1.png#averageHue=%23f9f9f8&clientId=ub230dccf-061b-4&from=paste&height=113&id=uef1cb234&originHeight=226&originWidth=664&originalType=binary&ratio=2&rotation=0&showTitle=false&size=28055&status=done&style=none&taskId=u9a42b9c6-d3d6-465e-8dfc-48c0f109a55&title=&width=332)
## 死锁检测 Deadlock Detection
> 启用死锁检测（默认）后，InnoDB会自动检测事务死锁并回滚一个或多个事务以打破死锁。InnoDB 尝试选择要回滚的小型事务，其中事务的大小由插入、更新或删除的行数决定。

> 在高并发系统上，当多个线程等待同一锁时，死锁检测可能会导致速度变慢。有时，禁用死锁检测并在发生死锁时依赖innodb_lock_wait_timeout设置进行事务回滚可能更有效。可以使用 innodb_deadlock_detect 变量禁用死锁检测。

## 如何最小化和处理死锁

-  [SHOW ENGINE INNODB STATUS](https://dev.mysql.com/doc/refman/5.7/en/show-engine.html)  查看是否有死锁发生。
- 如果频繁的死锁警告引起关注，请通过启用 innodb_print_all_deadlocks 变量来收集更广泛的调试信息。
- 死锁发生，请重试。
- 缩小事务范围和时间。
- 立即提交事务。
- If you use [locking reads](https://dev.mysql.com/doc/refman/5.7/en/glossary.html#glos_locking_read) ([SELECT ... FOR UPDATE](https://dev.mysql.com/doc/refman/5.7/en/select.html) or [SELECT ... LOCK IN SHARE MODE](https://dev.mysql.com/doc/refman/5.7/en/select.html)), try using a lower isolation level such as [READ COMMITTED](https://dev.mysql.com/doc/refman/5.7/en/innodb-transaction-isolation-levels.html#isolevel_read-committed).
- 一个事务修改多个表或同一表中的不同行集时，每次都按一致的顺序执行这些操作。
- 选择合适的索引。
- 少用 FOR UPDATE or LOCK IN SHARE MODE。Using the [READ COMMITTED](https://dev.mysql.com/doc/refman/5.7/en/innodb-transaction-isolation-levels.html#isolevel_read-committed) isolation level is good here, because each consistent read within the same transaction reads from its own fresh snapshot.



