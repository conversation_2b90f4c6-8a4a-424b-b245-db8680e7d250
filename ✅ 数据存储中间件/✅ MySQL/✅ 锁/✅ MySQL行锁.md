> > - [Record Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-record-locks)

# 记录锁 [Record Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-record-locks)
> 记录锁是索引记录上的锁。加在聚簇索引上，若没定义主键索引或唯一索引，InnoDB 会创建一个隐藏的聚集索引，并使用此索引进行记录锁定。
> A record lock is a lock on an index record.
>  See [Section 14.6.2.1, “Clustered and Secondary Indexes”](https://dev.mysql.com/doc/refman/5.7/en/innodb-index-types.html).

SELECT c1 FROM t WHERE c1 = 10 FOR UPDATE;
lock in share mode
```sql
ECORD LOCKS space id 58 page no 3 n bits 72 index `PRIMARY` of table `test`.`t`
trx id 10078 lock_mode X locks rec but not gap
Record lock, heap no 2 PHYSICAL RECORD: n_fields 3; compact format; info bits 0
 0: len 4; hex 8000000a; asc     ;;
 1: len 6; hex 00000000274f; asc     'O;;
 2: len 7; hex b60000019d0110; asc        ;;
```
# 间隙锁 [Gap Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-gap-locks)
> A gap lock is a lock on a gap between index records, or a lock on the gap before the first or after the last index record.
> 间隙锁定是对索引记录之间间隙的锁定，或者是对第一个索引记录之前或之后的间隙的锁定。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681036791086-b9c52646-45f3-4a4d-b0ce-cceb5128773e.png#averageHue=%23fbfbfa&clientId=u81c00499-f5cb-4&from=paste&height=182&id=u4b417d0f&originHeight=364&originWidth=1304&originalType=binary&ratio=2&rotation=0&showTitle=false&size=37296&status=done&style=none&taskId=u3519f511-8d01-4e48-bbc8-f892a620ace&title=&width=652)
```sql
(-∞，5)
(5，10)
(10，15)
(15，20)
(20，2147483647)
(2147483647, +∞)
```
## 间隙锁分析
T1开启一个事务，范围查询for update，事务未提交
```sql
START TRANSACTION;
SELECT c1 FROM t1 WHERE c1 BETWEEN 10 and 20 FOR UPDATE;
```
T2开启一个事务，插入c1=12，插入阻塞了
```sql
START TRANSACTION;
insert into t1 (c1, c2) VALUES (12, '12');
```
> X锁在记录插入之前意图锁定间隙。
> RECORD LOCKS space id 66 page no 3 n bits 72 index PRIMARY of table `demo`.`t1` trx id 8056 **lock_mode X locks gap before rec insert intention waiting**

```sql
mysql tables in use 1, locked 1
LOCK WAIT 2 lock struct(s), heap size 1136, 1 row lock(s)
MySQL thread id 17, OS thread handle 6147829760, query id 7278 localhost 127.0.0.1 root update
insert into t1 (c1, c2) VALUES (12, '12')
------- TRX HAS BEEN WAITING 17 SEC FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 66 page no 3 n bits 72 index PRIMARY of table `demo`.`t1` trx id 8056 lock_mode X locks gap before rec insert intention waiting
Record lock, heap no 5 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
```
# 临键锁 [Next-Key Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-next-key-locks)
> A next-key lock is a combination of a record lock on the index record and a gap lock on the gap before the index record.
临键锁是**索引记录上的记录锁**和_索引记录之前的间隙上的间隙锁_的组合。

> That is, a next-key lock is an index-record lock plus a gap lock on the gap preceding the index record. 
> 也就是说，临键锁是**索引记录锁**加上**索引记录前面的间隙上的间隙锁**

> By default, InnoDB operates in [REPEATABLE READ](https://dev.mysql.com/doc/refman/5.7/en/innodb-transaction-isolation-levels.html#isolevel_repeatable-read) transaction isolation level.
> 默认情况下，**InnoDB 在 REPEATABLE READ 事务隔离级别运行。**

> In this case, InnoDB uses next-key locks for searches and index scans, which prevents phantom rows (see [Section 14.7.4, “Phantom Rows”](https://dev.mysql.com/doc/refman/5.7/en/innodb-next-key-locking.html)).
> 在这种情况下，InnoDB 使用临键锁进行搜索和索引扫描，这可以防止幻读（请参见第 14.7.4 节 “幻像行”）。
> - 当同一查询在不同时间生成不同的行集时，所谓的幻像问题发生在事务中。例如，如果 SELECT 执行了两次，但第二次返回了第一次未返回的行，则该行是“幻像”行。

## 临键锁分析
```sql
(-∞，5]
(5，10]
(10，15]
(15，20]
(20，2147483647]
(2147483647, +∞]
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681040191103-ea182eec-bf45-46db-8760-01782166edfd.png#averageHue=%23fdfdfc&clientId=u47b128f6-c6ec-4&from=paste&height=348&id=u769b8f79&originHeight=696&originWidth=1640&originalType=binary&ratio=2&rotation=0&showTitle=false&size=207188&status=done&style=none&taskId=u3e34388e-1b16-496a-a6a6-d52e676ca5b&title=&width=820)
# 插入意向锁 [Insert Intention Locks](https://dev.mysql.com/doc/refman/5.7/en/innodb-locking.html#innodb-insert-intention-locks)
> An insert intention lock is a type of gap lock set by [INSERT](https://dev.mysql.com/doc/refman/5.7/en/insert.html) operations prior to row insertion. 
> 插入意向锁定是一种在行插入之前由 INSERT 操作设置的间隙锁类型。

> This lock signals the intent to insert in such a way that multiple transactions inserting into the same index gap need not wait for each other if they are not inserting at the same position within the gap. 
> 此锁表示插入意图，多个事务同时插入相同的索引间隙，且不是插入相同的位置，这不需要互相等待。
> Suppose that there are index records with values of 4 and 7. Separate transactions that attempt to insert values of 5 and 6, respectively, each lock the gap between 4 and 7 with **insert intention locks** prior to obtaining the exclusive lock on the inserted row, but do not block each other because the rows are nonconflicting.
> 假设存在值为 4 和 7 的索引记录。尝试分别插入值 5 和 6 的单独事务，在获得插入行的独占锁之前，每个事务都使用**插入意向锁**锁定 4 和 7 之间的间隙，但不会相互阻止，因为这些行不冲突。

# InnoDB行锁优化机制？
next-key lock的规则
原则 1：加锁的基本单位是 next-key lock。next-key lock 是前开后闭区间。
原则 2：只有访问到的对象才会加锁。
优化 1：索引上的等值查询，
命中唯一索，退化为行锁。
命中普通索引，左右两边的GAP Lock + Record Lock。
优化 2：
索引上的等值查询，未命中，所在的Net-Key Lock，退化为GAP Lock 。
索引在范围查询：
1.等值和范围分开判断。
2.索引在范围查询的时候 都会访问到所在区间不满足条件的第一个值为止。
3.如果使用了倒叙排序，按照倒叙排序后，
检索范围的右边多加一个GAP。
哪个方向还有命中的等值判断，再向同方向拓展外开里闭的区间。
