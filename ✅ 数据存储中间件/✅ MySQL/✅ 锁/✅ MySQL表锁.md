> 以表作为锁的基础，将锁加在表上，一张表只能存在一个同一类型的表锁。

# MySQL基本的表锁
## innoDB
> innoDB锁机制是基于聚簇索引实现的，
> sql执行命中索引数据，则加的是行锁。
> 没有命中，加的是表锁。表级别的排他锁。

```sql
select * from student  FOR UPDATE;
```
> show OPEN TABLES where In_use > 0;
> 查询存在表锁

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680935554242-f1eeb69a-88aa-4627-a9a6-2b0fdaa94fc1.png#averageHue=%23f9f9f9&clientId=ueac8bad9-e075-4&from=paste&height=154&id=u067ed180&originHeight=308&originWidth=2358&originalType=binary&ratio=2&rotation=0&showTitle=false&size=36581&status=done&style=none&taskId=u4d7a5092-3a33-4d85-a030-b516978f497&title=&width=1179)
## MyISAM
> MyISAM引擎中，获取了锁还需要自己手动释放锁，否则会造成死锁现象出现。

```sql
-- MyISAM引擎中获取读锁（具备读-读可共享特性）
LOCK TABLES student_myisam READ;

-- MyISAM引擎中获取写锁（具备写-读、写-写排他特性）
LOCK TABLES `student_myisam` WRITE;

-- 释放已获取到的锁
UNLOCK TABLES;

```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680935852970-4b1b1626-8589-44eb-ba02-c983b41da806.png#averageHue=%23f8f8f8&clientId=ueac8bad9-e075-4&from=paste&height=147&id=u75319d31&originHeight=294&originWidth=1764&originalType=binary&ratio=2&rotation=0&showTitle=false&size=30942&status=done&style=none&taskId=u7fc4b21f-7a42-448c-9d3c-6d241a05d06&title=&width=882)

# 元数据锁（Meta Data Lock）
> 简称为MDL锁，这是基于表的元数据加锁。
> **所有存储引擎的表都会存在一个.frm文件，这个文件中主要存储表的结构（DDL语句）**，而MDL锁就是基于.frm文件中的元数据加锁的。
> 是自动加解锁的。

# 意向锁（Intention Lock）
> InnoDB supports _**multiple granularity locking**_** **which permits coexistence of row locks and table locks. For example, a statement such as [LOCK TABLES ... WRITE](https://dev.mysql.com/doc/refman/5.7/en/lock-tables.html) takes an exclusive lock (an X lock) on the specified table. To make locking at multiple granularity levels practical, InnoDB uses [intention locks](https://dev.mysql.com/doc/refman/5.7/en/glossary.html#glos_intention_lock). Intention locks are table-level locks that indicate which type of lock (shared or exclusive) a transaction requires later for a row in a table. 

> InnoDB支持多粒度锁定，允许行锁和表锁共存。
> 例如，诸如[LOCK TABLES ... WRITE](https://dev.mysql.com/doc/refman/5.7/en/lock-tables.html)  对指定的表采用独占锁（X 锁）。为了使多个粒度级别的锁定切实可行，InnoDB使用意向锁。
> 意向锁是表级锁，指示事务稍后对表中的行需要哪种类型的锁（共享或独占）。

## 意向锁类型

- 意向共享锁 （IS） 表示事务打算对表中的各个行设置共享锁。
- 意向独占锁 （IX） 表示事务打算对表中的各个行设置独占锁。
> - An [intention shared lock](https://dev.mysql.com/doc/refman/5.7/en/glossary.html#glos_intention_shared_lock) (IS) indicates that a transaction intends to set a _shared_ lock on individual rows in a table.
> - An [intention exclusive lock](https://dev.mysql.com/doc/refman/5.7/en/glossary.html#glos_intention_exclusive_lock) (IX) indicates that a transaction intends to set an exclusive lock on individual rows in a table.

> For example, [SELECT ... LOCK IN SHARE MODE](https://dev.mysql.com/doc/refman/5.7/en/select.html) sets an IS lock, 
> and [SELECT ... FOR UPDATE](https://dev.mysql.com/doc/refman/5.7/en/select.html) sets an IX lock

## 意向锁协议
The intention locking protocol is as follows:

- Before a transaction can acquire a shared lock on a row in a table, it must first acquire an IS lock or stronger on the table.
   - 在事务获取表中某一行的共享锁（S锁）之前，它必须先获取意向共享锁（IS锁）或更强的IS锁。
- Before a transaction can acquire an exclusive lock on a row in a table, it must first acquire an IX lock on the table.
   - 在事务获取表中某一行的独占锁（X锁）之前，它必须先获取意向独占锁（IX锁）。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680966168845-a72f984b-cf31-492b-862d-9102b0faf355.png#averageHue=%23f2f2f2&clientId=u657f137c-5fe6-4&from=paste&height=177&id=uc5094611&originHeight=354&originWidth=866&originalType=binary&ratio=2&rotation=0&showTitle=false&size=43049&status=done&style=none&taskId=uaf4897fb-277d-4b83-9fca-1982357fd9c&title=&width=433)
> 比如当事务T1打算对ID=8888888这条数据加一个行锁之前，就会先加一个表级别的意向锁，比如目前T1要加一个行级别的读锁，就会先添加一个表级别的意向共享锁，如果T1要加行级别的写锁，亦是同理。
> 此时当事务T2尝试获取一个表级锁时，就会先看一下表上是否有意向锁，如果有的话再判断一下与自身是否冲突，比如表上存在一个意向共享锁，目前T2要获取的是表级别的读锁，那自然不冲突可以获取。但反之，如果T2要获取一个表记的写锁时，就会出现冲突，T2事务则会陷入阻塞，直至T1释放了锁（事务结束）为止。

> A lock is granted to a requesting transaction if it is compatible with existing locks, but not if it conflicts with existing locks. 
> A transaction waits until the conflicting existing lock is released. 
> If a lock request conflicts with an existing lock and cannot be granted because it would cause [deadlock](https://dev.mysql.com/doc/refman/5.7/en/glossary.html#glos_deadlock), an error occurs.
> 如果请求事务与现有锁兼容，则向请求事务授予锁，但如果它与现有锁冲突，则不授予该锁。
> 事务将等待，直到释放冲突的现有锁。
> 如果锁定请求与现有锁定冲突，并且由于会导致死锁而无法授予，则会发生错误。

> Intention locks do not block anything except full table requests (for example, [LOCK TABLES ... WRITE](https://dev.mysql.com/doc/refman/5.7/en/lock-tables.html)). 
> The main purpose of intention locks is to show that someone is locking a row, or going to lock a row in the table.
> 意图锁不会阻止除全表请求之外的任何内容（例如，[LOCK TABLES ... WRITE](https://dev.mysql.com/doc/refman/5.7/en/lock-tables.html)）。
> 意图锁的主要目的是显示某人正在锁定一行，或者要锁定表中的一行。

> Transaction data for an intention lock appears similar to the following in [SHOW ENGINE INNODB STATUS](https://dev.mysql.com/doc/refman/5.7/en/show-engine.html) and [InnoDB monitor](https://dev.mysql.com/doc/refman/5.7/en/innodb-standard-monitor.html) output:

```sql
TABLE LOCK table `test`.`t` trx id 10080 lock mode IX
```

# 自增锁（AUTO-INC Lock）
> An AUTO-INC lock is a special table-level lock taken by transactions inserting into tables with AUTO_INCREMENT columns. 
> In the simplest case, if one transaction is inserting values into the table, any other transactions must wait to do their own inserts into that table, so that rows inserted by the first transaction receive consecutive primary key values.
> 
AUTO-INC 锁是一种特殊的**表级锁**，由插入到具有AUTO_INCREMENT列的表中的事务所采用。
> 在最简单的情况下，如果一个事务正在向表中插入值，则任何其他事务都必须等待自己插入该表，以便第一个事务插入的行接收连续的主键值。

[https://dev.mysql.com/doc/refman/5.7/en/innodb-auto-increment-handling.html](https://dev.mysql.com/doc/refman/5.7/en/innodb-auto-increment-handling.html)
## 自增锁定模式
> **自增锁主要负责维护并发事务下自增列的顺序。**

> innodb_autoinc_lock_mode变量控制用于自动增量锁定的算法。它允许您选择如何在可预测的自动递增值序列和插入操作的最大并发性之间进行权衡。

- innodb_autoinc_lock_mode = 0：传统模式 traditional。
- innodb_autoinc_lock_mode = 1：连续模式 consecutive（MySQL8.0以前的默认模式）。
- innodb_autoinc_lock_mode = 2：交错模式 interleaved（MySQL8.0之后的默认模式）。
### 0 传统模式 traditional
> In this lock mode, all “INSERT-like” statements obtain a special table-level AUTO-INC lock for inserts into tables with AUTO_INCREMENT columns.
> 这个模式，所有插入语句都会获取表级的 自增锁（AUTO-INC lock）

### 1 连续模式 consecutive
> 默认模式

- “简单插入”（要插入的行数是预先知道的）通过在互斥锁（轻量级锁）a mutex (a light-weight lock) 的控制下获取所需数量的自动增量值来避免表级 AUTO-INC 锁定，互斥锁仅在分配过程期间保持，直到语句完成。不使用表级锁（AUTO-INC lock），除非另一个事务持有自增锁。如果另一个事务持有 AUTO-INC 锁，则“简单插入”会等待 AUTO-INC 锁，就好像它是“批量插入”一样。
- “批量插入”使用特殊的 AUTO-INC 表级锁，并一直保持到语句末尾。这适用于[INSERT ... SELECT](https://dev.mysql.com/doc/refman/5.7/en/insert-select.html), [REPLACE ... SELECT](https://dev.mysql.com/doc/refman/5.7/en/replace.html), and [LOAD DATA](https://dev.mysql.com/doc/refman/5.7/en/load-data.html) statements。一次只能执行一个持有 AUTO-INC 锁的语句。如果大容量插入操作的源表与目标表不同，则在对从源表中选择的第一行执行共享锁定后，将获取目标表上的 AUTO-INC 锁定。如果大容量插入操作的源和目标是同一个表，则在所有选定行上采用共享锁后，将采用 AUTO-INC 锁定。
- 例外情况是“混合模式插入”，其中用户为多行“简单插入”中的某些（但不是全部）行提供AUTO_INCREMENT列的显式值。对于此类插入，InnoDB 分配的自动增量值多于要插入的行数。但是，所有自动分配的值都是连续生成的（因此高于）最近执行的上一条语句生成的自动递增值。“超额”数字丢失。
### 2 交错模式 interleaved
> 在交错插入模式中，对于INSERT、REPLACE、INSERT…SELECT、REPLACE…SELECT、LOAD DATA等一系列插入语句，都不会再使用表级别的自增锁，而是全都使用Mutex-Lock来确保安全性。’

若最近的自增值为100，以下语句会报错：a duplicate-key error 23000 (Can't write; duplicate key in table)
```sql
INSERT INTO t1 (c1,c2) VALUES (1,'a'), (NULL,'b'), (101,'c'), (NULL,'d');
```
## insert插入类型
### 简单插入 Simple inserts
> 可以预先确定要插入的行数的语句，
> This includes single-row and multiple-row [INSERT](https://dev.mysql.com/doc/refman/5.7/en/insert.html) and [REPLACE](https://dev.mysql.com/doc/refman/5.7/en/replace.html) statements 

### 批量插入 Bulk inserts
> 事先不知道要插入的行数（以及所需的自动增量值数）的语句。
> This includes [INSERT ... SELECT](https://dev.mysql.com/doc/refman/5.7/en/insert-select.html), [REPLACE ... SELECT](https://dev.mysql.com/doc/refman/5.7/en/replace.html), and [LOAD DATA](https://dev.mysql.com/doc/refman/5.7/en/load-data.html) statements, but not plain INSERT. 

### 混合模式插入 Mixed-mode inserts
> 这些是“简单插入”语句，用于指定某些（但不是全部）新行的自动增量值。
> 有些指定了自增主键的值，有些没有指定。

```sql
INSERT INTO t1 (c1,c2) VALUES (1,'a'), (NULL,'b'), (5,'c'), (NULL,'d');
```
## InnoDB如何初始化AUTO_INCREMENT计数器
> 这个计数器存于内存中。
> This counter is stored only in main memory, not on disk.

当服务器重启后，会查询以下sql查询最大自增id，初始化计数器。
InnoDB 递增语句检索的值，并将其分配给列和表的自动增量计数器。默认情况下，该值递增 1。此默认值可由auto_increment_increment配置设置覆盖。
```sql
SELECT MAX(ai_col) FROM table_name FOR UPDATE;
```
## 自增相关配置
```sql
innodb_autoinc_lock_mode
auto_increment_increment
auto_increment_offset
```
## 注意点

- When an AUTO_INCREMENT integer column runs out of values, a subsequent INSERT operation returns a duplicate-key error. This is general MySQL behavior.
> integer字段 自增达到最大时，会报duplicate-key error

- When you restart the MySQL server, InnoDB may reuse an old value that was generated for an AUTO_INCREMENT column but never stored (that is, a value that was generated during an old transaction that was rolled back).
> mysql重启后，自增值可能会被重复使用，应为一些预分配的自增值是存储在内存中的。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680971159631-51ae9a29-26fb-489c-afaf-ba626fc788ed.png#averageHue=%23fcfcfc&clientId=u657f137c-5fe6-4&from=paste&height=334&id=uf95d39db&originHeight=668&originWidth=1910&originalType=binary&ratio=2&rotation=0&showTitle=false&size=142001&status=done&style=none&taskId=uaa1629e0-9e0a-4b03-8ea1-57067b2384f&title=&width=955)

# 全局锁
> 基于整个数据库来加锁的，加上全局锁之后，整个数据库只能允许读，不允许做任何写操作，一般全局锁是在对整库做数据备份时使用。

```sql
-- 获取全局锁的命令
FLUSH TABLES WITH READ LOCK;

-- 释放全局锁的命令
UNLOCK TABLES;
```
