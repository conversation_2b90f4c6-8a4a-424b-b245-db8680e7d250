# SQL语句类型
## DCL-数据库控制（Data Control Language）
"DCL" 是数据库控制（Data Control Language）的缩写，用于定义和控制数据库访问权限、事务管理等操作。在 SQL 中，DCL 语句包括以下几种：

1. GRANT：授予用户访问数据库对象的权限。
   - GRANT SELECT ON table_name TO user_name;：赋予用户 user_name 对 table_name 表的 SELECT 权限。
2. REVOKE：从用户身上撤销对数据库对象的访问权限。
   - REVOKE INSERT ON table_name FROM user_name;：从用户 user_name 身上撤销对 table_name 表的 INSERT 权限。
3. COMMIT：执行一组数据修改的操作进行确认提交。
   - COMMIT;：将之前执行的 INSERT、UPDATE 或 DELETE 等操作提交到数据库中。
4. ROLLBACK：撤销自上次 COMMIT 或 ROLLBACK 命令以来的所有修改操作。
   - ROLLBACK;：撤销之前执行到现在为止的 INSERT、UPDATE 或 DELETE 等操作。
5. SAVEPOINT：将一个保存点（Savepoint）放在当前事务中，以备后续回滚到指定的位置。
   - SAVEPOINT savepoint_name;：建立一个名为 savepoint_name 的保存点。
6. SET TRANSACTION：设置当前事务的属性。
   - SET TRANSACTION ISOLATION LEVEL READ COMMITTED;：将当前事务隔离级别设置为 READ COMMITTED。

DCL 语句可以协助管理和维护数据库中的安全性、完整性和可靠性，让数据的存取和修改更加有条理、方便和有效。
## DDL-数据库定义语言（Data Definition Language）
DDL 是数据库定义语言（Data Definition Language）的缩写，用于定义数据库对象，例如表、架构和视图。DDL 语句可以创建、修改或删除数据库的结构，但不能直接对数据进行操作。在 SQL 中，常见的 DDL 语句包括以下几种：

1. CREATE：创建数据库对象。
   - CREATE TABLE table_name (column1 datatype1, column2 datatype2, ...);：创建一个名为 table_name 的表。
2. ALTER：修改数据库对象的结构。
   - ALTER TABLE table_name ADD column_name datatype;：在 table_name 表中添加一个名为 column_name 的列。
3. DROP：删除数据库对象。
   - DROP TABLE table_name;：删除名为 table_name 的表。
4. TRUNCATE：清空数据表，但保留数据库对象。
   - TRUNCATE TABLE table_name;：清空名为 table_name 的表。
5. RENAME：重命名指定的数据库对象。
   - RENAME TABLE table_name TO new_table_name;：将名为 table_name 的表重命名为 new_table_name。

DDL 语句是管理和维护关系型数据库中的基本语言之一，可以实现对数据库 Schema 和结构的增删改查操作，对于数据库的设计和维护都有着至关重要的作用。
## DML-数据库操作语言（Data Manipulation Language）
DML 是数据库操作语言（Data Manipulation Language）的缩写，用于对数据库中的数据进行操作，例如新增、查询、修改和删除。在 SQL 中，常见的 DML 语句包括以下几种：

1. SELECT：从数据库表中检索数据。
   - SELECT column1, column2, ... FROM table_name WHERE condition;：从 table_name 表中选择特定的列，并筛选满足 condition 条件的行。
2. INSERT：向数据库表中插入新的行。
   - INSERT INTO table_name (column1, column2, ...) VALUES (value1, value2, ...);：往名为 table_name 的表中插入一些列及其值。
3. UPDATE：更新数据库表中已有的行。
   - UPDATE table_name SET column1 = value1, column2 = value2, ... WHERE condition;：将满足 condition 条件的行中的值赋为具体的更新值。
4. DELETE：从数据库表中删除一条或多条行。
   - DELETE FROM table_name WHERE condition;：删除满足 condition 条件的行。

DML 语句是管理和维护关系型数据库中的基本语言之一，能够实现对数据库数据的任意操作，对于数据的增删改查功能有着至关重要的作用。
