# 百万数据，分页查询 SQL 优化

## 测试表结构

```sql
CREATE TABLE `my_table` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `col1` varchar(255) DEFAULT NULL,
  `col2` varchar(255) DEFAULT NULL,
  `col3` varchar(10) DEFAULT NULL,
  `col4` varchar(20) DEFAULT NULL,
  `col5` varchar(10) DEFAULT NULL,
  `col6` varchar(19) DEFAULT NULL,
  `col7` varchar(1) DEFAULT NULL,
  `col8` varchar(255) DEFAULT NULL,
  `col9` varchar(1) DEFAULT NULL,
  `col10` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18701305 DEFAULT CHARSET=utf8mb4;

```

## 生成测试数据

`call insert_data4();`

```sql
CREATE DEFINER=`root`@`%` PROCEDURE `insert_data4`()
BEGIN
  DECLARE i INT DEFAULT 1;
  DECLARE max_rows INT DEFAULT 9000000;
  DECLARE commit_size INT DEFAULT 10000;
  DECLARE counter INT DEFAULT 0;

  START TRANSACTION;  -- 开启事务

  WHILE (i <= max_rows) DO
    INSERT INTO my_table (col1, col2, col3, col4, col5, col6, col7, col8, col9, col10)
    VALUES (
      CONCAT('col1_', i),
      CONCAT('col2_', i),
      CONCAT('col3_', i),
      CONCAT('col4_', i),
      CONCAT('col5_', i),
      CONCAT('col6_', i),
      '1',
      CONCAT('col8_', i),
      'A',
      CONCAT(i, '.00')
    );

    SET counter = counter + 1;  -- 计数器加1

    IF counter >= commit_size THEN  -- 达到提交数量，提交事务并重置计数器
      COMMIT;
      START TRANSACTION;
      SET counter = 0;
    END IF;

    SET i = i + 1;
  END WHILE;

  COMMIT;  -- 最后执行一次提交以结束事务
END
```

## 优化方式

### 普通 limit

```sql
select SQL_NO_CACHE * from my_table limit 7000000, 100;
```

![](images/Pasted%20image%2020230522224745.png)

### 主键非自增类型

> 耗时少了一半
> 子查询走主键索引，也是覆盖索引，再根据查询到的主键 回表查询。
> 子查询需要扫描主键索引 7000000 条记录，在取后面 100 条记录

```sql
SELECT SQL_NO_CACHE
	t0.*
FROM
	my_table t0
	INNER JOIN ( SELECT id FROM my_table LIMIT 7000000, 100 ) t1 ON t0.id = t1.id;
```

![](images/Pasted%20image%2020230522224836.png)

![](images/Pasted%20image%2020230522224939.png)

### 主键自增类型

> 主键是自增的，子查询只要从起始位置后开始扫描索引，再取后面 10 条数据，速度超快。
> 查询到主键，再回表 10 次即可。

```sql
SELECT SQL_NO_CACHE
	t0.*
FROM
	my_table t0
	INNER JOIN ( SELECT id FROM my_table WHERE id > 7000000 LIMIT 10 ) t1 ON t0.id = t1.id;
```

![](images/Pasted%20image%2020230522225425.png)

![](images/Pasted%20image%2020230522225459.png)
