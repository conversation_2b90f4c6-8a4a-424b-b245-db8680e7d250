# MySQL5.7部署

## 安装MySQL5.7

```shell
wget http://58.220.49.186:9999/mysql/mysql-5.7.31-linux-glibc2.12-x86_64.tar.gz
tar zvxf mysql-5.7.31-linux-glibc2.12-x86_64.tar.gz
mv mysql-5.7.31-linux-glibc2.12-x86_64 /home/<USER>
cd /home/<USER>
mkdir data
mkdir logs
mkdir /var/lib/mysql
touch /etc/my.cnf
```

## 配置文件

```shell
sudo tee /etc/my.cnf <<-'EOF'
[client]
port= 3300
socket = /home/<USER>/data/mysql.sock
default-character-set = utf8
 
[mysqld]
basedir=/home/<USER>
datadir=/home/<USER>/data
socket=/home/<USER>/data/mysql.sock
user=root
port= 3300
server-id = 152
log-error = /home/<USER>/logs/error.log
slow_query_log_file = /home/<USER>/logs/slow.log
log-bin=/home/<USER>/logs/mysql-bin
relay_log=/home/<USER>/logs/mysql-relay-bin
auto-increment-offset=1
auto-increment-increment=2
gtid_mode = on
sync_binlog=0
enforce_gtid_consistency = 1
innodb_undo_tablespaces = 5
innodb_undo_log_truncate = 1
innodb_max_undo_log_size = 1G
innodb_log_file_size = 2G
innodb_log_files_in_group = 10
binlog_cache_size = 4M
max_binlog_cache_size = 2G
max_binlog_size = 1G
expire-logs-days = 7
innodb_buffer_pool_size = 4G
innodb_buffer_pool_instances = 4
innodb_sort_buffer_size=128M
innodb_data_file_path = ibdata1:10M:autoextend
innodb_write_io_threads = 12
innodb_read_io_threads = 6
innodb_thread_concurrency = 24
innodb_flush_log_at_trx_commit = 2
innodb_use_native_aio=1
slave_parallel_workers=4
slave_parallel_type=LOGICAL_CLOCK
max_connections = 5000
max_user_connections=5000
max_connect_errors = 100
event_scheduler=OFF
default-time-zone='+08:00'
log_timestamps=SYSTEM
skip_name_resolve = 1
thread_cache_size = 1000
log_queries_not_using_indexes = 0
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
character-set-server = utf8
init_connect=SET NAMES utf8
lower_case_table_names = 1
explicit_defaults_for_timestamp=true
wait_timeout= 86400
interactive_timeout = 86400
log_bin_trust_function_creators=1
back_log = 50
table_open_cache = 2048
max_allowed_packet = 1024M
max_heap_table_size = 1024M
read_buffer_size = 2M
read_rnd_buffer_size = 2M
join_buffer_size = 2M
query_cache_size = 64M
query_cache_limit = 2M
ft_min_word_len = 4
default-storage-engine = INNODB
innodb_file_per_table = 1
thread_stack = 192K
transaction_isolation = REPEATABLE-READ
tmp_table_size = 64M
binlog_format=row
slow_query_log=1
long_query_time =2
read-only=0
binlog-ignore-db=mysql
binlog-ignore-db=information_schema
binlog-ignore-db=performance_schema
replicate-ignore-db=mysql
replicate-ignore-db=information_schema
replicate-ignore-db=performance_schema
log_slave_updates=1
slave_skip_errors = 1062,1050,1051,1146,1145,1061,1032,1304,1396,1537,1539
show_compatibility_56=on
innodb_autoinc_lock_mode = 2
key_buffer_size = 32M
bulk_insert_buffer_size = 64M
myisam_sort_buffer_size = 128M
myisam_max_sort_file_size = 10G
myisam_repair_threads = 1
innodb_log_buffer_size = 100M
innodb_max_dirty_pages_pct = 90
innodb_lock_wait_timeout = 120
innodb_purge_threads = 4
innodb_large_prefix = 1
innodb_print_all_deadlocks=1
innodb_flush_neighbors=1
innodb_doublewrite=0
innodb_old_blocks_pct=20
skip_ssl
 
[mysqldump]
quick
max_allowed_packet = 1024M
 
[mysql]
no-auto-rehash
 
[myisamchk]
key_buffer_size = 512M
sort_buffer_size = 512M
read_buffer = 8M
write_buffer = 8M
 
[mysqlhotcopy]
interactive-timeout
 
[mysqld_safe]
prompt = '(product)\u@\h [\d]> '
open-files-limit = 8192
EOF
```

## 配置

```shell
安装①：bin/mysqld --initialize --user=root --basedir=/home/<USER>/home/<USER>/data
######执行完后生存的root@localhost: t#=sUjf1ohNf这串密码需要记住
安装②：bin/mysql_ssl_rsa_setup --datadir=/home/<USER>/data
修改系统配置文件:
cd /home/<USER>/support-files
cp mysql.server /etc/init.d/mysql
vi /etc/init.d/mysql
basedir=/home/<USER>
datadir=/home/<USER>/data



启动
启动：service mysql start
# /etc/init.d/mysql start
登陆：mysql -uroot -p
　　--如果出现：-bash: mysql: command not found
　　--就执行：ln -s /home/<USER>/bin/mysql /usr/bin --没有出现就不用执行
--输入上面生成的临时密码
修改密码：mysql> set password=password('cqt@1234');
修改root的远程权限：mysql> grant all privileges on *.* to 'root'@'%' identified by 'cqt@1234';
刷新：mysql> flush privileges;
添加系统路径：
# vi /etc/profile
export PATH=/home/<USER>/ibin:$PATH
加载系统路径：source /etc/profile
配置mysql自动启动：
chmod 755 /etc/init.d/mysql
chkconfig --add mysql
chkconfig --level 345 mysql on
```

```shell
--退出mysql命令窗口
#exit（或者quit）
--查看mysql状态
#service mysql status
--停止mysql
#service mysql stop
--启动mysql
#service mysql start

##跳过密码校验启动
bin/mysqld_safe --skip-grant-tables
##修改root密码
update mysql.user set authentication_string = password('cqt@1234') where user='root' and Host = 'localhost';
##创建用户
CREATE USER 'water'@'%' IDENTIFIED BY 'cqt@1234';
grant all privileges on *.* to 'water'@'%' identified by 'cqt@1234';
```

## 1. 找回密码

> skip-grant-tables

```shell
service mysql start
 mysql -uroot -p
update mysql.user set authentication_string = password('cqt@1234') where user='root' and Host = 'localhost';
service mysql stop
service mysql start
# 去掉skip-grant-tablesvim 
```

## 2. 第一次重置密码

```shell
 mysql -uroot -p
 set password=password('cqt@1234');
grant all privileges on *.* to 'root'@'%' identified by 'cqt@1234';
flush privileges;
```

## 3. 双主配置

```shell
#A机器执行
grant all privileges on *.* to 'root'@'************' identified by 'cqt@fj889977';
change master to master_host='************', master_user='root', master_password='cqt@fj889977', master_port=3300, master_auto_position=1;

##B机器执行
grant all privileges on *.* to 'root'@'************' identified by 'cqt@fj889977';
change master to master_host='************', master_user='root', master_password='cqt@fj889977', master_port=3300, master_auto_position=1;

##两台都执行
start slave;
show slave status\G;
```
