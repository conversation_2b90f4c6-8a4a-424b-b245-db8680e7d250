# Canal监听表数据变化
## mysql开启binlog
```properties
log-bin=mysql-bin
binlog_format=row

#binlog-do-db=test
binlog-ignore-db=mysql
binlog-ignore-db=information_schema
binlog-ignore-db=performance_schema
auto-increment-offset=1
auto-increment-increment=2
#replicate-do-db=test
replicate-ignore-db=mysql
replicate-ignore-db=information_schema
replicate-ignore-db=performance_schema
relay_log=mysql-relay-bin
log-slave-updates=on
binlog-do-db = smp
binlog-do-db = private_number
replicate-do-db = smp
replicate-do-db = private_number
slave_skip_errors = 1062,1050,1051,1146,1145,1061,1032,1304,1396,1537,1539
```
### 查看binlog内容
```properties
mysqlbinlog --no-defaults  mysql-bin.000033
```
## canal部署
### 下载安装
```basic
wget https://kgithub.com/alibaba/canal/releases/download/canal-1.1.6/canal.deployer-1.1.6.tar.gz
mkdir canal.deployer-1.1.6
tar -zxvf canal.deployer-1.1.6.tar.gz -C canal.deployer-1.1.6
```
### 配置文件
#### canal.properties
```properties
#################################################
######### 		common argument		#############
#################################################
# tcp bind ip
canal.ip =
# register ip to zookeeper
canal.register.ip =
canal.port = 11111
canal.metrics.pull.port = 11112
# canal instance user/passwd
# canal.user = canal
# canal.passwd = E3619321C1A937C46A0D8BD1DAC39F93B27D4458

# canal admin config
#canal.admin.manager = 127.0.0.1:8089
canal.admin.port = 11110
canal.admin.user = admin
canal.admin.passwd = 4ACFE3202A5FF5CF467898FC58AAB1D615029441
# admin auto register
#canal.admin.register.auto = true
#canal.admin.register.cluster =
#canal.admin.register.name =

canal.zkServers =
# flush data to zk
canal.zookeeper.flush.period = 1000
canal.withoutNetty = false
# tcp, kafka, rocketMQ, rabbitMQ, pulsarMQ
canal.serverMode = kafka
# flush meta cursor/parse position to file
canal.file.data.dir = ${canal.conf.dir}
canal.file.flush.period = 1000
## memory store RingBuffer size, should be Math.pow(2,n)
canal.instance.memory.buffer.size = 16384
## memory store RingBuffer used memory unit size , default 1kb
canal.instance.memory.buffer.memunit = 1024 
## meory store gets mode used MEMSIZE or ITEMSIZE
canal.instance.memory.batch.mode = MEMSIZE
canal.instance.memory.rawEntry = true

## detecing config
canal.instance.detecting.enable = false
#canal.instance.detecting.sql = insert into retl.xdual values(1,now()) on duplicate key update x=now()
canal.instance.detecting.sql = select 1
canal.instance.detecting.interval.time = 3
canal.instance.detecting.retry.threshold = 3
canal.instance.detecting.heartbeatHaEnable = false

# support maximum transaction size, more than the size of the transaction will be cut into multiple transactions delivery
canal.instance.transaction.size =  1024
# mysql fallback connected to new master should fallback times
canal.instance.fallbackIntervalInSeconds = 60

# network config
canal.instance.network.receiveBufferSize = 16384
canal.instance.network.sendBufferSize = 16384
canal.instance.network.soTimeout = 30

# binlog filter config
# 是否使用druid处理所有的ddl解析来获取库和表名
canal.instance.filter.druid.ddl = true
# 是否忽略dcl语句
canal.instance.filter.query.dcl = true
# 是否忽略dml语句 (mysql5.6之后，在row模式下每条DML语句也会记录SQL到binlog中,可参考MySQL文档)
canal.instance.filter.query.dml = false
# 是否忽略ddl语句
canal.instance.filter.query.ddl = true
# 是否忽略binlog表结构获取失败的异常 (主要解决回溯binlog时,对应表已被删除或者表结构和binlog不一致的情况)
canal.instance.filter.table.error = true
# 是否dml的数据变更事件 (主要针对用户只订阅ddl/dcl的操作)
canal.instance.filter.rows = false
# 是否忽略事务头和尾,比如针对写入kakfa的消息时，不需要写入TransactionBegin/Transactionend事件
canal.instance.filter.transaction.entry = false
canal.instance.filter.dml.insert = false
canal.instance.filter.dml.update = false
canal.instance.filter.dml.delete = false

# binlog format/image check
# 支持的binlog format格式列表 (otter会有支持format格式限制)
canal.instance.binlog.format = ROW,STATEMENT,MIXED 
# 支持的binlog image格式列表 (otter会有支持format格式限制)
canal.instance.binlog.image = FULL,MINIMAL,NOBLOB

# binlog ddl isolation
# ddl语句是否单独一个batch返回 (比如下游dml/ddl如果做batch内无序并发处理,会导致结构不一致)
canal.instance.get.ddl.isolation = false

# parallel parser config
# 是否开启binlog并行解析模式 (串行解析资源占用少,但性能有瓶颈, 并行解析可以提升近2.5倍+)
canal.instance.parser.parallel = true
## concurrent thread number, default 60% available processors, suggest not to exceed Runtime.getRuntime().availableProcessors()
#canal.instance.parser.parallelThreadSize = 16
## disruptor ringbuffer size, must be power of 2
# binlog并行解析的异步ringbuffer队列 (必须为2的指数)
canal.instance.parser.parallelBufferSize = 256

# table meta tsdb info
canal.instance.tsdb.enable = true
canal.instance.tsdb.dir = ${canal.file.data.dir:../conf}/${canal.instance.destination:}
canal.instance.tsdb.url = jdbc:h2:${canal.instance.tsdb.dir}/h2;CACHE_SIZE=1000;MODE=MYSQL;
canal.instance.tsdb.dbUsername = canal
canal.instance.tsdb.dbPassword = canal
# dump snapshot interval, default 24 hour
canal.instance.tsdb.snapshot.interval = 24
# purge snapshot expire , default 360 hour(15 days)
canal.instance.tsdb.snapshot.expire = 360

#################################################
######### 		destinations		#############
#################################################
# 实例目录名称，多个逗号隔开
canal.destinations = example
# conf root dir
canal.conf.dir = ../conf
# auto scan instance dir add/remove and start/stop instance
canal.auto.scan = true
canal.auto.scan.interval = 5
# set this value to 'true' means that when binlog pos not found, skip to latest.
# WARN: pls keep 'false' in production env, or if you know what you want.
canal.auto.reset.latest.pos.mode = false

canal.instance.tsdb.spring.xml = classpath:spring/tsdb/h2-tsdb.xml
#canal.instance.tsdb.spring.xml = classpath:spring/tsdb/mysql-tsdb.xml

canal.instance.global.mode = spring
canal.instance.global.lazy = false
canal.instance.global.manager.address = ${canal.admin.manager}
#canal.instance.global.spring.xml = classpath:spring/memory-instance.xml
canal.instance.global.spring.xml = classpath:spring/file-instance.xml
#canal.instance.global.spring.xml = classpath:spring/default-instance.xml

##################################################
######### 	      MQ Properties      #############
##################################################
# aliyun ak/sk , support rds/mq
canal.aliyun.accessKey =
canal.aliyun.secretKey =
canal.aliyun.uid=

# 是否为json格式，如果设置为false,对应MQ收到的消息为protobuf格式，需要通过CanalMessageDeserializer进行解码
canal.mq.flatMessage = true
# 获取canal数据的批次大小
canal.mq.canalBatchSize = 50
# 获取canal数据的超时时间
canal.mq.canalGetTimeout = 100
# Set this value to "cloud", if you want open message trace feature in aliyun.
canal.mq.accessChannel = local

# 是否开启database混淆hash，确保不同库的数据可以均匀分散，如果关闭可以确保只按照业务字段做MQ分区计算
canal.mq.database.hash = true
# MQ消息发送并行度
canal.mq.send.thread.size = 30
# MQ消息构建并行度
canal.mq.build.thread.size = 8

##################################################
######### 		     Kafka 		     #############
##################################################
# kafka服务端地址
kafka.bootstrap.servers = 172.16.251.53:9192

# kafka为ProducerConfig.ACKS_CONFIG
kafka.acks = 1

# 压缩类型
kafka.compression.type = none

# kafka为ProducerConfig.BATCH_SIZE_CONFIG
kafka.batch.size = 16384

# kafka为ProducerConfig.LINGER_MS_CONFIG , 如果是flatMessage格式建议将该值调大, 如: 200
kafka.linger.ms = 10

# kafka为ProducerConfig.MAX_REQUEST_SIZE_CONFIG
kafka.max.request.size = 1048576

# kafka为ProducerConfig.BUFFER_MEMORY_CONFIG
kafka.buffer.memory = 33554432

# kafka为ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION
kafka.max.in.flight.requests.per.connection = 1

# 发送失败重试次数
kafka.retries = 3

kafka.kerberos.enable = false
kafka.kerberos.krb5.file = "../conf/kerberos/krb5.conf"
kafka.kerberos.jaas.file = "../conf/kerberos/jaas.conf"

##################################################
######### 		    RocketMQ	     #############
##################################################
# rocketMQ为ProducerGroup名
rocketmq.producer.group = test

# 是否开启message trace
rocketmq.enable.message.trace = false

# message trace的topic
rocketmq.customized.trace.topic =

# rocketmq的namespace
rocketmq.namespace =

# rocketmq的namesrv地址
rocketmq.namesrv.addr = *************:9876

# 重试次数
rocketmq.retry.times.when.send.failed = 0

# rocketmq是否开启vip channel
rocketmq.vip.channel.enabled = false

# rocketmq的tag配置
rocketmq.tag = 

##################################################
######### 		    RabbitMQ	     #############
##################################################
rabbitmq.host =
rabbitmq.virtual.host =
rabbitmq.exchange =
rabbitmq.username =
rabbitmq.password =
rabbitmq.deliveryMode =


##################################################
######### 		      Pulsar         #############
##################################################
pulsarmq.serverUrl =
pulsarmq.roleToken =
pulsarmq.topicTenantPrefix =
```
#### example/instance.properties
> mysql连接信息、配置监听哪些库表，库表对应MQ主题配置

```properties
#################################################
## mysql serverId , v1.0.26+ will autoGen
# canal.instance.mysql.slaveId=0

# enable gtid use true/false
canal.instance.gtidon=false

# position info
# mysql连接信息
canal.instance.master.address=*************:3300
canal.instance.master.journal.name=
canal.instance.master.position=
canal.instance.master.timestamp=
canal.instance.master.gtid=

# rds oss binlog
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# table meta tsdb info
canal.instance.tsdb.enable=true
#canal.instance.tsdb.url=**************************************
#canal.instance.tsdb.dbUsername=canal
#canal.instance.tsdb.dbPassword=canal

#canal.instance.standby.address =
#canal.instance.standby.journal.name =
#canal.instance.standby.position =
#canal.instance.standby.timestamp =
#canal.instance.standby.gtid=

# username/password 用户名密码
canal.instance.dbUsername=root
canal.instance.dbPassword=cqt@1234
# 代表数据库的编码方式对应到 java 中的编码类型，比如 UTF-8，GBK , ISO-8859-1
canal.instance.connectionCharset = UTF-8
# enable druid Decrypt database password
canal.instance.enableDruid=false
#canal.instance.pwdPublicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALK4BUxdDltRRE5/zXpVEVPUgunvscYFtEip3pmLlhrWpacX7y7GCMo2/JM6LeHmiiNdH1FWgGCpUfircSwlWKUCAwEAAQ==

# table regex
# 要订阅的库表正则
canal.instance.filter.regex=private_number\\..*
# table black regex
# 过滤掉，不进行数据同步
canal.instance.filter.black.regex=mysql\\.slave_.*
# table field filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.field=test1.t_product:id/subject/keywords,test2.t_company:id/name/contact/ch
# table field black filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.black.field=test1.t_product:subject/product_image,test2.t_company:id/name/contact/ch

# mq config
# mq里的topic名
# canal.mq.topic=example
# dynamic topic route by schema or table regex
#canal.mq.dynamicTopic=mytest1.user,topic2:mytest2\\..*,.*\\..*
# 针对库名或者表名发送动态topic。主题名:库名.表名，多个表之间用逗号分隔
canal.mq.dynamicTopic=AxbBindInfo:private_number\\.private_bind_info_axb_.*,AxeBindInfo:private_number\\.private_bind_info_axe_.*
#canal.mq.dynamicTopic=.*\\..*
# 单队列模式的分区下标，从0开始。
canal.mq.partition=2
# hash partition config
# 动态获取MQ服务端的分区数,如果设置为true之后会自动根据topic获取分区数替换canal.mq.partitionsNum的定义,目前主要适用于RocketMQ
#canal.mq.enableDynamicQueuePartition=false
# 散列模式的分区数
#canal.mq.partitionsNum=5
# mq里的动态队列分区数,比如针对不同topic配置不同partitionsNum
#canal.mq.dynamicTopicPartitionNum=test.*:4,mycanal:6
#分片hash 库名.表名: 唯一主键，多个表之间用逗号分隔
#canal.mq.partitionHash=test.table:id^name,.*\\..*
#################################################

```
#### canal.mq.dynamicTopic 表达式说明
canal 1.1.3版本之后, 支持配置格式：schema 或 schema.table，多个配置之间使用逗号或分号分隔

- 例子1：test\\.test 指定匹配的单表，发送到以test_test为名字的topic上
- 例子2：.*\\..* 匹配所有表，则每个表都会发送到各自表名的topic上
- 例子3：test 指定匹配对应的库，一个库的所有表都会发送到库名的topic上
- 例子4：test\\..* 指定匹配的表达式，针对匹配的表会发送到各自表名的topic上
- 例子5：test,test1\\.test1，指定多个表达式，会将test库的表都发送到test的topic上，test1\\.test1的表发送到对应的test1_test1 topic上，其余的表发送到默认的canal.mq.topic值

为满足更大的灵活性，允许对匹配条件的规则指定发送的topic名字，配置格式：topicName:schema 或 topicName:schema.table

- 例子1: test:test\\.test 指定匹配的单表，发送到以test为名字的topic上
- 例子2: test:.*\\..* 匹配所有表，因为有指定topic，则每个表都会发送到test的topic下
- 例子3: test:test 指定匹配对应的库，一个库的所有表都会发送到test的topic下
- 例子4：testA:test\\..* 指定匹配的表达式，针对匹配的表会发送到testA的topic下
- 例子5：test0:test,test1:test1\\.test1，指定多个表达式，会将test库的表都发送到test0的topic下，test1\\.test1的表发送到对应的test1的topic下，其余的表发送到默认的canal.mq.topic值

大家可以结合自己的业务需求，设置匹配规则，建议MQ开启自动创建topic的能力
#### canal.mq.partitionHash 表达式说明
canal 1.1.3版本之后, 支持配置格式：schema.table:pk1^pk2，多个配置之间使用逗号分隔

- 例子1：test\\.test:pk1^pk2 指定匹配的单表，对应的hash字段为pk1 + pk2
- 例子2：.*\\..*:id 正则匹配，指定所有正则匹配的表对应的hash字段为id
- 例子3：.*\\..*:$pk$ 正则匹配，指定所有正则匹配的表对应的hash字段为表主键(自动查找)
- 例子4: 匹配规则啥都不写，则默认发到0这个partition上
- 例子5：.*\\..* ，不指定pk信息的正则匹配，将所有正则匹配的表,对应的hash字段为表名
   - 按表hash: 一张表的所有数据可以发到同一个分区，不同表之间会做散列 (会有热点表分区过大问题)
- 例子6: test\\.test:id,.\\..* , 针对test的表按照id散列,其余的表按照table散列

注意：大家可以结合自己的业务需求，设置匹配规则，多条匹配规则之间是按照顺序进行匹配(命中一条规则就返回)
