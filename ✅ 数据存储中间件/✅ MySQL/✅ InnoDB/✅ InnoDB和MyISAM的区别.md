| 区别点 | InnoDB | MyISAM |
| --- | --- | --- |
| 磁盘文件 | 2个 .frm、.ibd | 3个 .frm、.MYD、.MYI |
| 支持聚簇索引 | 支持（表数据和索引数据都在idb文件中） | 不支持（表数据和索引数据分开两个文件） |
| 索引数据 | 聚簇索引存储表数据和索引，
非聚簇索引存储主键 | 都是非聚簇索引，存储数据行的地址，无需回表，查询很快（单线程情况下快）。 |
| 事务机制 | 支持事务（undo log） | 不支持 |
| 故障恢复 | 支持（undo log、redo log、bin log） | 不支持 |
| 锁粒度 | 支持表锁、行锁 | 只支持表锁，不支持行锁（因为不支持聚簇索引） |
| 并发性能 | 高（undo log + MVCC） | 低（表锁，同时只能有一个线程读、写） |
| 内存利用度 | 缓冲池 |  |
| 统计行数count() | 会触发全表扫描 | 直接返回，有记录行数（仅只适用于统计全表数据量，有where条件也会全表扫描） |
| delete删除表数据 | 一行一行删除，速度慢 | 直接重新创建表数据文件 |

