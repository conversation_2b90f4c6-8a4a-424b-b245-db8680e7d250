# InnoDB In-Memory Structures
## [Buffer Pool](https://dev.mysql.com/doc/refman/5.7/en/innodb-buffer-pool.html) 缓冲池
> **缓冲池是主存中的一个区域，InnoDB在该表和索引数据被访问时将其缓存。**
> **为了提高缓存管理的效率，缓冲池被实现为一个页链表;很少使用的数据将使用最近最少使用(LRU)算法的变体从缓存中老化。**

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681375292499-be7ed5e1-a2a4-40b4-b446-31021acffdc1.png#averageHue=%23fafaf9&clientId=u27e320d9-aacc-4&from=paste&height=641&id=u764f88ff&originHeight=641&originWidth=469&originalType=binary&ratio=1&rotation=0&showTitle=false&size=33629&status=done&style=none&taskId=u972c5497-17c0-448f-8d17-c74aecae8bc&title=&width=469)
### LRU
> **使用一种变种的LRU算法将缓冲池作为列表进行管理。当需要空间向缓冲池添加新页时，最近使用最少的页将被清除，并将新页添加到列表中间。这个中点插入策略将列表分为两个子列表:**
> - At the **head**, a sublist of new (“young”) pages that were accessed recently（**新的子列表-最近访问**）
> - At the **tail**, a sublist of old pages that were accessed less recently（**旧的子列表-最近访问较少**）
> 
**该算法将频繁使用的页面保留在新的子列表中。旧的子列表包含较少使用的页面;这些页面可能会被删除。**

#### **LRU缺省情况**

- **3/8的缓冲池用于旧的子列表。**
- **列表的中点是新子列表的尾部与旧子列表的头部相遇的边界。**
- **当InnoDB将一个页面读入缓冲池时，它首先将它插入到中点(旧子列表的头部)。一个页面可以被读取，因为它是由用户发起的操作(如SQL查询)所必需的，或者是由InnoDB自动执行的预读操作的一部分。**
- **访问旧子列表中的页面使其“年轻”，将其移动到新子列表的头部。如果读取该页是因为用户发起的操作需要它，则立即进行第一次访问，并将该页设为年轻页。如果由于预读操作而读取该页，则不会立即进行第一次访问，并且可能在该页被逐出之前根本不会进行第一次访问。**
- **当数据库运行时，缓冲池中未被访问的页面将向列表的尾部移动，从而“老化”。新子列表和旧子列表中的页面都会随着其他页面的更新而老化。旧子列表中的页面也会随着页面在中点插入而老化。最终，一个未使用的页面到达旧子列表的尾部并被驱逐。**
> **默认情况下，查询读取的页面立即移动到新的子列表中，这意味着它们在缓冲池中停留的时间更长。**
> **例如，为mysqldump操作或不带WHERE子句的SELECT语句执行的表扫描可以将大量数据带入缓冲池，并清除等量的旧数据，即使新数据永远不会再次使用。**
> **类似地，预读后台线程加载并只访问过一次的页面被移到新列表的头部。这些情况可以将经常使用的页面推到旧的子列表中，在那里它们将成为驱逐的对象。**

### Buffer Pool 配置
> [https://dev.mysql.com/doc/refman/5.7/en/innodb-buffer-pool-resize.html](https://dev.mysql.com/doc/refman/5.7/en/innodb-buffer-pool-resize.html)

> 8G is a valid [innodb_buffer_pool_size](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_size) value because 8G is a multiple of [innodb_buffer_pool_instances=16](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_instances) * [innodb_buffer_pool_chunk_size=128M](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_chunk_size), which is 2G.
> so [innodb_buffer_pool_size](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_size) is adjusted to 10G, which is a multiple of [innodb_buffer_pool_chunk_size](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_chunk_size) * [innodb_buffer_pool_instances](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_instances).
> 不是倍数，会自动调整为倍数

```properties
# 缓存池大小 innodb_buffer_pool_size = innodb_buffer_pool_chunk_size * innodb_buffer_pool_instances的倍数
innodb_buffer_pool_size
innodb_buffer_pool_instances=16
# 块大小 默认128M
innodb_buffer_pool_chunk_size=128M
```
> To avoid potential performance issues, the number of chunks ([innodb_buffer_pool_size](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_size) / [innodb_buffer_pool_chunk_size](https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_buffer_pool_chunk_size)) should not exceed 1000.
> **为了避免潜在的性能问题，块的数量(innodb_buffer_pool_size / innodb_buffer_pool_chunk_size)不应该超过1000。**

### **InnoDB缓冲池指标**
```basic
BUFFER POOL AND MEMORY
----------------------
Total large memory allocated 10994319360
Dictionary memory allocated 6590526
Buffer pool size   655320
Free buffers       335610
Database pages     308144
Old database pages 113586
Modified db pages  207
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 10254321163, not young 1375045
0.00 youngs/s, 0.00 non-youngs/s
Pages read 260195, created 48346, written 27813797
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 98 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 308144, unzip_LRU len: 0
I/O sum[4336]:cur[0], unzip sum[0]:cur[0]
```
| **Name** | **Description** |
| --- | --- |
| Total memory allocated | The total memory allocated for the buffer pool in bytes. |
| Dictionary memory allocated | The total memory allocated for the InnoDB data dictionary in bytes. |
| Buffer pool size | The total size in pages allocated to the buffer pool. |
| Free buffers | The total size in pages of the buffer pool free list. |
| Database pages | The total size in pages of the buffer pool LRU list. |
| Old database pages | The total size in pages of the buffer pool old LRU sublist. |
| Modified db pages | The current number of pages modified in the buffer pool. |
| Pending reads | The number of buffer pool pages waiting to be read into the buffer pool. |
| Pending writes LRU | The number of old dirty pages within the buffer pool to be written from the bottom of the LRU list. |
| Pending writes flush list | The number of buffer pool pages to be flushed during checkpointing. |
| Pending writes single page | The number of pending independent page writes within the buffer pool. |
| Pages made young | The total number of pages made young in the buffer pool LRU list (moved to the head of sublist of “new” pages). |
| Pages made not young | The total number of pages not made young in the buffer pool LRU list (pages that have remained in the “old” sublist without being made young). |
| youngs/s | The per second average of accesses to old pages in the buffer pool LRU list that have resulted in making pages young. See the notes that follow this table for more information. |
| non-youngs/s | The per second average of accesses to old pages in the buffer pool LRU list that have resulted in not making pages young. See the notes that follow this table for more information. |
| Pages read | The total number of pages read from the buffer pool. |
| Pages created | The total number of pages created within the buffer pool. |
| Pages written | The total number of pages written from the buffer pool. |
| reads/s | The per second average number of buffer pool page reads per second. |
| creates/s | The average number of buffer pool pages created per second. |
| writes/s | The average number of buffer pool page writes per second. |
| Buffer pool hit rate | The buffer pool page hit rate for pages read from the buffer pool vs from disk storage. |
| young-making rate | The average hit rate at which page accesses have resulted in making pages young. See the notes that follow this table for more information. |
| not (young-making rate) | The average hit rate at which page accesses have not resulted in making pages young. See the notes that follow this table for more information. |
| Pages read ahead | The per second average of read ahead operations. |
| Pages evicted without access | The per second average of the pages evicted without being accessed from the buffer pool. |
| Random read ahead | The per second average of random read ahead operations. |
| LRU len | The total size in pages of the buffer pool LRU list. |
| unzip_LRU len | The length (in pages) of the buffer pool unzip_LRU list. |
| I/O sum | The total number of buffer pool LRU list pages accessed. |
| I/O cur | The total number of buffer pool LRU list pages accessed in the current interval. |
| I/O unzip sum | The total number of buffer pool unzip_LRU list pages decompressed. |
| I/O unzip cur | The total number of buffer pool unzip_LRU list pages decompressed in the current interval. |



## [Change Buffer](https://dev.mysql.com/doc/refman/5.7/en/innodb-change-buffer.html)

"mysql change buffer" 是 MySQL 数据库中的一种缓存机制，用于优化写入性能。当表被更新时，MySQL会将修改操作暂时缓存在 change buffer 中，而不是立即写入磁盘。这些修改会先被写入到内存中的 change buffer 中，然后在后台异步地将其刷新到磁盘。

使用 change buffer 有以下几个好处：

1.  减少磁盘I/O: 当表被更新时，不需要立即写入磁盘，这样可以减少磁盘I/O操作，提高写入性能。 
2.  提高事务处理性能: 事务处理通常会涉及多次写操作，使用 change buffer 可以将这些操作缓存在内存中，在事务提交时一次性写入磁盘，提高事务处理性能。 
3.  减少锁等待时间: 在写入磁盘时，MySQL通常需要获取表级锁，使用 change buffer 可以减少锁等待时间，提高并发性能。 

需要注意的是，change buffer 只适用于 MyISAM 存储引擎和 InnoDB 存储引擎的非聚集索引。对于 InnoDB 存储引擎的聚集索引，MySQL使用了类似的机制，称为 insert buffer。
## [Adaptive Hash Index](https://dev.mysql.com/doc/refman/5.7/en/innodb-adaptive-hash.html)
## [Log Buffer](https://dev.mysql.com/doc/refman/5.7/en/innodb-redo-log-buffer.html)
