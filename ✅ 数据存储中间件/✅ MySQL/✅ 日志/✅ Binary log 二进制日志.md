> 记录所有对数据库表结构变更和表数据修改的操作。
> 对于[SELECT](https://dev.mysql.com/doc/refman/5.7/en/select.html) or [SHOW](https://dev.mysql.com/doc/refman/5.7/en/show.html) 的语句不会记录。

# Binary log
## bin log配置
```
log_bin=ON
log-bin=base_name.extension
binlog-format=ROW
```
## 什么时候创建新的binlog文件

- mysql服务启动或重启时
- mysql手动flushes
- 当前日志文件的大小达到 max_binlog_size。
## binlog日志记录格式
### STATEMENT

- 基于 SQL 语句。
- 缺点：
   - 主从同步时，数据可能不一致。
### ROW

- 基于数据行。
- InnoDB表，READ COMMITTED or READ UNCOMMITTED，这两种隔离级别只能用ROW格式。
- CREATE TABLE, ALTER TABLE, or DROP TABLE，这些语句还是基于STATEMENT格式。
### MIXED

- 混合日志记录。默认情况下使用基于语句的日志记录，但在某些情况下，日志记录模式会自动切换到基于行的日志记录。
- 以下情况会自动从基于STATEMENT的日志记录切换到基于ROW的日志记录：
   - When a DML statement updates an [NDBCLUSTER](https://dev.mysql.com/doc/refman/5.7/en/mysql-cluster.html) table.
   - 使用[UUID()](https://dev.mysql.com/doc/refman/5.7/en/miscellaneous-functions.html#function_uuid)函数的sql
   - When [FOUND_ROWS()](https://dev.mysql.com/doc/refman/5.7/en/information-functions.html#function_found-rows) or [ROW_COUNT()](https://dev.mysql.com/doc/refman/5.7/en/information-functions.html#function_row-count) is used.
   - When [USER()](https://dev.mysql.com/doc/refman/5.7/en/information-functions.html#function_user), [CURRENT_USER()](https://dev.mysql.com/doc/refman/5.7/en/information-functions.html#function_current-user), or [CURRENT_USER](https://dev.mysql.com/doc/refman/5.7/en/information-functions.html#function_current-user) is used.
   - When a statement refers to one or more system variables. 
   - When one of the tables involved is a log table in the mysql database.
   - When the [LOAD_FILE()](https://dev.mysql.com/doc/refman/5.7/en/string-functions.html#function_load-file) function is used. 
## 相关命令
```sql
-- 查看binlog
show binary logs;
```
## bin log缓存区
> 每个工作线程都有一个bin_log_buffer，线程之间的缓存区互相隔离。
> 可以兼容各种引擎。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681221544213-2abbfe74-0f23-476e-8df1-57166278c970.png#averageHue=%23e5d5bb&clientId=u9056b7ba-4383-4&from=paste&height=452&id=uc3e2016b&originHeight=904&originWidth=1350&originalType=binary&ratio=2&rotation=0&showTitle=false&size=457805&status=done&style=none&taskId=uc2f38f7a-0ccb-43b5-aab0-10799dc75de&title=&width=675)

