# 慢查询日志
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681224063781-bad491f0-a507-4fd1-94f4-97218e97b285.png#averageHue=%23e9f0f1&clientId=u6fcffec5-1e4d-4&from=paste&height=62&id=u29a19ac7&originHeight=124&originWidth=1066&originalType=binary&ratio=2&rotation=0&showTitle=false&size=42847&status=done&style=none&taskId=u3d134e08-2c9f-408f-a909-4808a68ce61&title=&width=533)
```sql
long_query_time 慢查询时间 单位秒，默认为10s, 0.01表示10ms
```
# 错误日志

# General-log查询日志
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681224185764-ba0058b4-e7e3-4303-b20f-a94c16f579e9.png#averageHue=%23f3f3f3&clientId=u6fcffec5-1e4d-4&from=paste&height=70&id=u848286d8&originHeight=140&originWidth=1518&originalType=binary&ratio=2&rotation=0&showTitle=false&size=47940&status=done&style=none&taskId=u2d36fff2-da1b-474a-8294-037610b9ffb&title=&width=759)

# relay-log中继日志
> 主从同步使用，存放通过过来的bin log日志，中转。
> 当主机的增量数据被复制到中继日志后，从机的线程会不断从relay-log日志中读取数据并更新自身的数据，relay-log的结构和bin-log一模一样，同样存在一个xx-relaybin.index索引文件，以及多个xx-relaybin.00001、xx-relaybin.00002....数据文件。
> 
