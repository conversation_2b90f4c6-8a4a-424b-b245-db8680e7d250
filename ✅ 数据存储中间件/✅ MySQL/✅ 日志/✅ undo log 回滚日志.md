> 当一条写入类型的SQL执行时，都会记录Undo-log日志，会生成相应的反SQL放入到Undo-log中。
> 用于实现事务回滚和MVCC机制。

# undo log
## curd操作

- insert插入操作，则生成一个对应的delete操作。
- delete删除操作，InnoDB中会修改隐藏字段deleted_bit=1，则生成改为0的语句。
- update修改操作，生成相反的sql。
## undo log存在哪
> InnoDB默认是将Undo-log存储在xx.ibdata共享表数据文件当中，默认采用段的形式存储。
> 在共享表数据文件中，有一块区域名为Rollback Segment回滚段，每个回滚段中有1024个Undo-log Segment，每个Undo段可存储一条旧数据，而执行写SQL时，Undo-log就是写入到这些段中。

### 基于Undo版本链实现MVCC
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681222587787-bcc05f25-e9aa-4247-8a7c-db9ebb9a6228.png#averageHue=%23ebebeb&clientId=u04cb252c-097e-4&from=paste&height=294&id=uaffa184f&originHeight=588&originWidth=1426&originalType=binary&ratio=2&rotation=0&showTitle=false&size=306280&status=done&style=none&taskId=u6f2e60d4-f314-4c59-b3b2-80f3b2fbd32&title=&width=713)
## undo log 内存缓存池
> InnoDB启动后，会在内存创建一个缓存区BufferPool。
> 存储数据相关的缓冲，如索引、锁、表数据等；各种日志的缓存：undo log，redo log。

> 日志先写入BufferPool，后台线程慢慢写入磁盘。

## 日志刷盘机制
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681223609726-5211197f-e884-4629-9b48-11cfff50e753.png#averageHue=%23ebefec&clientId=u1f42ec32-7642-4&from=paste&height=186&id=u2bf9bf68&originHeight=372&originWidth=1320&originalType=binary&ratio=2&rotation=0&showTitle=false&size=391284&status=done&style=none&taskId=uaf24686b-a317-4f6e-a703-a9fcfbeed95&title=&width=660)
## undo log配置
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681222867696-a9008e08-b962-4d8b-ac3f-98739883e5d4.png#averageHue=%23f0f6f6&clientId=u04cb252c-097e-4&from=paste&height=444&id=u901b6332&originHeight=888&originWidth=1558&originalType=binary&ratio=2&rotation=0&showTitle=false&size=295251&status=done&style=none&taskId=ue8b94fdd-fd93-4d17-a7e9-446b270d970&title=&width=779)

