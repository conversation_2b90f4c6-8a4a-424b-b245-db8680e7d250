# redo log
## 为什么要有redo log
> Redo-log则用来实现数据的恢复。

> 使用BufferPool提升了MySQL性能，缓冲池是存储在内存中。若mysql宕机，写入缓冲池的数据，还未刷到磁盘，那数据就丢失了。
> redo log就是解决这问题的。

## redo log如何生成
> redo log是将记录追加到日志文件的尾部。
> redo log 是一种预写式日志，即在向内存写入数据前，**会先写日志**。
> - 日志先写在内存的redo_log_buffer缓冲区。
> 
当后续数据未被刷写到磁盘、MySQL崩溃时，就可以通过日志来恢复数据，确保所有提交的事务都会被持久化。

### Redo-log的刷盘策略
> 由innodb_flush_log_at_trx_commit参数控制

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681223549988-bedcd086-65b8-417f-a78d-8c6feea87206.png#averageHue=%23e8ece9&clientId=u2f0be2e3-1045-4&from=paste&height=99&id=u737f19f3&originHeight=198&originWidth=1140&originalType=binary&ratio=2&rotation=0&showTitle=false&size=216858&status=done&style=none&taskId=u1baad497-a503-4f3e-9d9b-02a7a66f064&title=&width=570)
## redo log参数
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681223756610-a73481bd-8331-4982-86ba-55473cfe3faa.png#averageHue=%23ebf4f5&clientId=ubd59a9d5-45be-4&from=paste&height=169&id=ub5bb2b43&originHeight=338&originWidth=1554&originalType=binary&ratio=2&rotation=0&showTitle=false&size=144200&status=done&style=none&taskId=u075e9715-37f9-4d31-8b66-8cbec6c37c1&title=&width=777)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681223945666-f1f5d14a-5442-4658-a770-e1e348168964.png#averageHue=%23eaeff0&clientId=ubd59a9d5-45be-4&from=paste&height=163&id=u18c35be6&originHeight=326&originWidth=1426&originalType=binary&ratio=2&rotation=0&showTitle=false&size=130120&status=done&style=none&taskId=u2a5860b0-a175-44ed-a7d5-dfde31d9f07&title=&width=713)

