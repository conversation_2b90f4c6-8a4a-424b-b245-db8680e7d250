> **MySQL的事务机制是基于日志实现的。**

# 事务机制

- undo-log：主要记录SQL的撤销日志，比如目前是insert语句，就记录一条delete日志。
- redo-log：记录当前SQL归属事务的状态，以及记录修改内容和修改页的位置。
   - redo-log是一种**WAL(Write-ahead logging)预写式日志**，在数据发生更改之前会先记录日志，也就是在SQL执行前会先记录一条prepare状态的日志，然后再执行数据的写操作。
- bin-log：记录每条SQL操作日志，只要是用于数据的主从复制与数据恢复/备份。

> MySQL InnoDB引擎不会直接将数据写入磁盘，而是先写到BufferPool缓冲区中，当sql被成功写入缓存区后，再将redo-log日志相应的记录改为commit状态，再有MySQL刷盘机制去具体操作。

> rollback时，将undo-log日志找到对应的撤销sql执行，缓存数据还原。

# 事务恢复机制
