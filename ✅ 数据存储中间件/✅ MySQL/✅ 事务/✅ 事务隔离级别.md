# Read uncommitted/RU：读未提交
- 一个事务可以读取到另一个事务未提交的数据。- 脏读问题
- 通过互斥锁实现，两个事务不能同时写同一数据。所以没有脏写问题。
- 但是读不互斥，就可以读到其他事务未提交的数据。
# Read committed/RC：读已提交

- 当前事务只能读取到其他事务已经提交的数据。
- 写操作通过互斥锁实现。
- 读操作通过MVCC实现。不会读到其他事务未提交的事务，而是读旧版本数据。
- 存在不可重复读问题。
   - RC的MVCC机制一个事务每次读操作都会生成一个ReadView，判断读哪个版本的数据。
   - 如A事务更新某条数据未完成，B事务查询这条数据也未提交，A事务事务提交，B事务再次查询，两次查询的数据不一致。
# Repeatable read/RR：可重复读

- MySQL默认级别。
- 在同一事务中，不管读取多少次，读到的数据都是相同的。
- 写操作通过互斥锁实现。
- 读操作通过MVCC实现。
- 存在幻读问题。
   - RR的MVCC机制一个事务只生成一个ReadView，不管几次读操作。所以每次读的数据都是一致的。
   - A事务修改多行数据，B事务插入一条新数据，A事务提交后，再次查询发现多出一条数据。
- RR级别幻读也可解决
   - **使用临键锁（间隙锁+行锁）这种方式来加锁。**
# Serializable：序列化/串行化

- 所有的事务按序排队后串行化处理。
# 存在问题
| 级别/问题 | 脏写 | 脏读 | 不可重复读 | 幻读 |
| --- | --- | --- | --- | --- |
| 读未提交 |  | x | x | x |
| 读已提交 |  |  | x | x |
| 可重复读 |  |  |  | x |
| 串行化 |  |  |  |  |

- 脏读
   - 一个事务读到了其他事务还未提交的数据，也就是当前事务读到的数据，由于还未提交。
- 不可重复读
   - 在一个事务中，多次读取同一数据，先后读取到的数据不一致。
- 幻读
   - 另外一个事务在第一个事务要处理的目标数据范围之内新增了数据，然后先于第一个事务提交，第一个事务再次查询，发现多了本该不存在的新数据。
   - 其他事务可以进行插入数据，会有幻读问题，这个事务第二次读取数据，可能读到其他事务插入的数据。
- 脏写/更新丢失
   - 多个事务一起操作同一条数据，例如两个事务同时向表中添加一条ID=88的数据，此时就会造成数据覆盖，或者主键冲突的问题，这个问题也被称之为更新丢失问题。

# 相关sql
## 查询隔离级别
```sql
SELECT @@tx_isolation;
show variables like '%tx_isolation%';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680783132662-f83570ff-2e29-42eb-9747-145e9cb91d6e.png#averageHue=%23f8f8f8&clientId=u6c98e255-2c0f-4&from=paste&height=92&id=u7290311e&originHeight=184&originWidth=616&originalType=binary&ratio=2&rotation=0&showTitle=false&size=19122&status=done&style=none&taskId=u18fc061d-843f-43a6-99d1-99d27b84965&title=&width=308)
## 修改隔离级别
```sql
-- 设置隔离级别为RU级别（当前连接生效）
set transaction isolation level read uncommitted;

-- 设置隔离级别为RC级别（全局生效）
set global transaction isolation level read committed;

-- 设置隔离级别为RR级别（当前连接生效）
-- 这里和上述的那条命令作用相同，是第二种设置的方式
set tx_isolation = 'repeatable-read';

-- 设置隔离级别为最高的serializable级别（全局生效）
set global.tx_isolation = 'serializable'; 

```
## 事务开启关闭
```sql
-- 开启一个事务
start transaction;

-- 第一条SQL语句
-- 第二条SQL语句
-- 第三条SQL语句

-- 提交或回滚事务
commit || rollback;
```
