# [概念](https://juejin.cn/post/7155359629050904584)
> MVCC是多版本并发控制（Multi-Version Concurrency Control）的缩写，是一种数据库管理系统中使用的并发控制技术。

- 仅有InnoDB引擎支持
- MySQL中仅在RC读已提交级别、RR可重复读级别才会使用MVCC机制。
- MVCC机制主要通过隐藏字段、Undo-log日志、ReadView这三个东西实现的。
# 隐藏字段
### 隐藏主键 - ROW_ID（6Bytes）
> 聚簇索引使用主键或唯一索引来构建聚簇索引，
> 当表未定义主键或唯一索引时，会自动生成一个递增的row_id作为索引字段。

### 删除标识 - Deleted_Bit（1Bytes）
> delete语句不会立刻把数据物理删除，而是修改**Deleted_Bit=1（true）**。
> 后续查询若记录**Deleted_Bit=1则先过滤**

##### 这个字段好处

- 当一个事务删除数据，又回滚了，若物理删除表数据
   - 那删除数据，会破坏索引树结构
   - 事务回滚，又重新插入数据，又破坏了索引树结构。
##### 什么时候删除

- 后台会有一个purger线程，会自动清理Deleted_Bit=1/true的行数据。
   - 什么时候触发线程？？？
### 最近更新的事务ID - TRX_ID（6Bytes）
> transaction_id，每创建一个事务，都会生成一个顺序递增的ID。
> TRX_ID记录的就是最近一次改动当前这条数据的事务ID。
> **单纯由一条select语句组成的事务并不会分配事务ID，因此默认为0**

### 回滚指针 - ROLL_PTR（7Bytes）
> **rollback_pointer，**
> 当一个事务对一条数据做了改动后，都会将**旧版本的数据放到Undo-log日志中**，
> 而rollback_pointer就是一个地址指针，**指向Undo-log日志中旧版本的数据**，
> 当需要回滚事务时，就可以通过这个隐藏列，来找到改动之前的旧版本数据，
> 而MVCC机制也利用这点，实现了行数据的多版本。


# Undo log 回滚日志
> undo log 存储修改记录的各个版本链

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680529103869-4f2abd0d-7784-40f9-aab8-48588f5b4616.png#averageHue=%23ebebeb&clientId=u436013e0-05de-4&from=paste&height=288&id=u59153807&originHeight=576&originWidth=1434&originalType=binary&ratio=2&rotation=0&showTitle=false&size=305884&status=done&style=none&taskId=u5fe9041a-f7b7-46c6-9d56-8d6a90be608&title=&width=717)
###  版本链的好处

- 实现事务回滚
- 实现MVCC
# ReadView
> 一个事务在尝试读取一条数据时，MVCC基于当前MySQL的运行状态生成的快照。也被称为读视图。
> 这个快照中记录当前所有活跃的事务ID（正在执行的事务（未提交、回滚））


- 一个事务与一个ReadView属于一对一的关系  隔离级别下
### RC和RR级别的差别

- RC级别 每次select语句执行前，都会生成一个ReadView
- RR级别 一个事务只会生成一个ReadView，只有第一个查询生成ReadView，后续查询沿用这个ReadView。
### ReadView内容

- creator_trx_id：代表创建当前这个ReadView的事务ID。
- trx_ids：表示在生成当前ReadView时，系统内活跃的事务ID列表。
- up_limit_id：活跃的事务列表中，最小的事务ID。
- low_limit_id：表示在生成当前ReadView时，系统中要给下一个事务分配的ID值。
> 有T1-T5事务， T1、T2、T4正在执行，T3回滚，T5提交

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680529832736-5c8df928-3cc7-4bf1-893f-c2fd25f59cdd.png#averageHue=%23f9f9f8&clientId=u436013e0-05de-4&from=paste&height=154&id=ubed21093&originHeight=308&originWidth=1476&originalType=binary&ratio=2&rotation=0&showTitle=false&size=146434&status=done&style=none&taskId=ueac70dbd-795f-457d-ad22-850235a1aef&title=&width=738)
```json
{
    "creator_trx_id" : "0",
    "trx_ids" : "[1,2,4]",
    "up_limit_id" : "1",
    "low_limit_id" : "6"
}
```
# MVCC机制实现原理
> 要判断查询数据的事务能不能查询最新版本的数据，若不能则要查询undo log。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680530769301-e63c931d-d4a9-49af-883e-842a919385bd.png#averageHue=%23efefef&clientId=ua74d1059-478d-4&from=paste&height=452&id=u66c1734a&originHeight=904&originWidth=1574&originalType=binary&ratio=2&rotation=0&showTitle=false&size=388207&status=done&style=none&taskId=u1e96948b-71a9-4d08-83e6-987d8df3f1a&title=&width=787)
### undo log存在版本链，该取哪条数据

- 旧版本的数据，其隐藏列trx_id不能在ReadView.trx_ids活跃事务列表中。
# 快照读

- 读取的是快照版本，
- 不加锁，普通select都是快照读
# 当前读

- 读取最新版本。
- select ... for uodate； 是当前读


