
# 1. 语句
[sql语句导图](https://www.processon.com/view/5f620a521e08531edf3354cd#map)
脑图 [https://www.processon.com/view/5e788607e4b027d999c51fc1#outline](https://www.processon.com/view/5e788607e4b027d999c51fc1#outline)
# 2. 存储引擎
## 1.innoDb

```sql
1. 支持事务
2. 支持外键
3. 聚集索引, B+树, 数据文件和索引放在一起, 主键索引的叶子节点是数据文件, 辅助索引的叶子节点是主键的值
4. 不保存表的具体行数, select count(*) from tab; 因为事务, 不同事务,行数不一样
  - SHOW TABLE STATUS 可以查询大概行数Rows
5. 5.7 之后支持全文索引
6. 支持表, 行(默认)级索
7. 存储文件格式: frm是表定义文件，ibd是数据文件
8. 必须要有唯一索引
```

## 2. MyIsam


```sql
1. 不支持事务
2. 不支持外键
3. 非聚集索引, B+树, 数据文件和索引分离, 索引保存数据文件的指针, 主键索引和辅助索引的叶子节点都是数据文件的指针
4. 有一个变量保存行数, select count(*) from tab;
5. 支持全文索引
6. 支持表级锁
7. 存储文件格式: frm是表定义文件，myd是数据文件，myi是索引文件
8. 可以没有唯一索引
```

# 3. log
## 1. bin log - 逻辑日志

```sql
- 记录对数据库表的变更, 新增 , 删除, 修改
- 主从复制
- 三种模式:    show variables like "%binlog_format%";
  - row  记录每一行的修改情况。日志量很大。   5.7.7之后默认
  - statement  记录执行的修改语句。日志量小。   5.7.7之前默认
  - mixed  以上两个混合
- 如何开启binlog
  - binlog-format=ROW
- binlog是Mysql底层实现的, 所有引擎都有
- 日志内容是追加文件写, 写满, 再建一个文件
- 重点：通过二阶段提交来保证binlog和redolog的一致性。如果不一致，就乱套了。
```

## 2. redo log - 物理日志

```
- 记录xx页做了xx修改
- 功能：数据库崩溃恢复，即crash-safe的能力。
- redolog是innoDB特有的,
- 日志内容循环写, 覆盖之前的
```

## 3. undo log - 重做日志

```
- 内容：记录着记录修改前的数据，并且通过记录中的roll pointer隐藏列指向备份数据的undo log，事务失败时，好通过undo日志回滚。
- 功能：回滚和MVCC中的版本链
```


# 4. 事务
## 4.1. 四大特性 ACID

```sql
1. 原子性 atomicity    undolog来保证
  - 要么全部成功, 要么全部失败
2. 一致性 consistency
  - 多个事务对同一数据读取, 结果是一样的
3. 隔离性 isolation    隔离级别和gap锁(间隙锁- RR级别生效)来保证
  - 并发访问, 每个事务互不影响
4. 持久性 durability   redolog来保证
  - 对数据库的操作, 是永久性的, 会保存到磁盘
```

## 4.2. 隔离级别

```sql
1. 读未提交（Read Uncommited）
  - 一个事务还未提交时，他做的更改就能被其他事务读取到。
  - 会有脏读
2. 读已提交（Read Commited）
  - 一个事务所做的更改，只有在提交后才会被其他事务所看到。
  - 解决脏读, 会有不可重复读和幻读
3. 可重复读（Repeatable Read） - 默认
  - 一个事务执行过程中看到的数据总是和该事务启动时看到的数据是一致的。
  - 解决脏读, 不可重复读, 存在幻读(锁)
4. 串行化（Serializable）
  - 对于同一行记录，写会加写锁，读会加读锁，当出现读写所冲突时，后访问的事务必须等待前一个事务执行完成才能继续执行。
  - 没任何问题
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611192652594-37a95d2e-c860-4a92-ba28-51172f2ccd0f.png#align=left&display=inline&height=244&originHeight=244&originWidth=778&size=0&status=done&style=none&width=778)
## 4.3. 并发事务带来的问题
### 1. 脏写

- 一个事务修改了另一个事务已修改但未提交的记录，那就意味着发生了脏写。

- 当为RU级别时，如果事务A先修改记录，未提交，此时事务B在执行修改同一条记录的语句时，会被阻塞。



### 2. 脏读

- 一个事务读取了另一个事务已修改但未提交的记录，那就意味着发生了脏读。



### 3. 不可重复读

- 如果一个事务只能读取另一个已经提交的事务修改后的数据，并且另一个事务每对该数据进行一次修改并提交后，该事务都能查询到最新的值，那就意味着发生了不可重复读。
- 每次读取, 数据都不一样



### 4. 幻读

- 事务A根据一些条件查询一些数据, 事务B查询符合这些条件的数据, 事务A再次按相同条件查询, 发现多了一些数据, 就发生幻读, 突然多出数据

- 解决: 
   - GAP间隙锁, RR隔离级别生效, 锁未出现的行, **范围查询有效**



### 5. 丢失更新

- 一个事务覆盖了另一个事务对同一条数据的修改

- 解决: 
   - 乐观锁 版本号version

   - for update 悲观锁



# 5. 事务实现原理

# 6. 索引
## 6.1 索引是什么

- 助MySQL高效获取数据的数据结构, B+树, hash



## 6.2 为何使用索引, 优缺点
### 1, 优点

   - 提高查询速度
### 2. 缺点

   - 




## 6.3 底层原理
### 1. B+树

```
叶子节点存数据, 
可以减少IO次数
深度
```


## 6.4 索引类型
### 1. 聚簇索引

```sql
- 以主键创建索引
- 叶子节点存储主键和对应的行数据
- 是通过真实数据行的唯一途径, 不需要回表
- 必须有聚簇索引且只有一个
  - 如果表定义了主键，则PK就是聚集索引；
  - 如果表没有定义主键，则第一个非空唯一索引（not NULL unique）列是聚集索引；
  - 否则，InnoDB会创建一个隐藏的row-id作为聚集索引；
```

### 2. 非聚簇索引

```c
- 以普通字段(非主键)创建索引
- 叶子节点存储索引和主键
- 会回表: 先查询到索引列，获取对应的主键，再根据主键回表查询到对应数据行。
```

### 3. 覆盖索引

```sql
- 在一棵索引树上就能获取sql所需的所有字段, 不需要回表.
- Extra字段为Using index
- 把查询的字段作为索引, 单个或联合
- 索引为name, 主键id, select id,name
	
```

## 6.5 索引失效的情况

```sql
1、索引列出现在表达式或者函数中
2、索引列是字符型但值没加引号
3、索引列上使用IS NULL或者IS NOT NULL作为判断条件
4、索引列条件是不等于!=或<>
5、索引列上模糊匹配的值前面加了%,  xxx%索引会生效, %xxx不行
6、不满足最左前缀原则
7、条件是or。如果想让or条件生效，给or每个字段都加上索引。
8、索引检索结果集过多，优化器选择全表扫描

 一般 数据能够进行某种规则排序, 范围查询 可以使用索引, 
  true或false判断 索引一般会失效 如 is null, is not null, !=, <>, %xxx
```

## 6.6 索引如何加? 最左匹配原则
**
```bash
1、经常作为where查询条件且索引选择性高。
2、排序（order by）、分组（group by）、distinct中的字段。
3、多表join时的关联字段。
4、平均长度较小的字段
```

## 6.7 回表

- 非主键索引:  先查询到索引列，获取对应的主键，再根据主键回表查询到对应数据行。



## 6.8 索引下推

- 非主键索引, 减少回表次数

- MySQL5.6之后的新特性，先过滤完索引后找到所有符合索引条件的数据行，随后用where条件对数据行进行过滤。这个是索引下推功能。

- Using index Condition



## 6.9 如何排查 explain

```basic
• type的类型
  • all: 全表遍历
  • index: 全索引遍历。和all的区别在于all扫描全表，而index扫描全部的索引。
  • range: 一定范围的索引扫描
  • ref: 非唯一性索引扫描。常见于用唯一的索引，匹配到的行不唯一。
  • eq_ref: 唯一性索引。使用唯一索引匹配到了唯一的行。
  • const/system: 存储引擎对查询进行了优化，并将查询转化为常量时会出现。常见于将主键置于where条件。

• Extra的类型
  • Using Temporary: 表示MySQL需要使用临时表来存储结果集，常见于排序和分组查询。
  • Using filesort: 文件排序。表示当无法使用索引排序时，使用文件排序方式。
  • Using where: 表示MySQL在存储引擎收到记录后根据where条件过滤结果集。
  • Using index: 表示覆盖索引。只使用索引即可获取结果，不需要回表。
  • Using index Condition: MySQL5.6之后的新特性，先过滤完索引后找到所有符合索引条件的数据行，随后用where条件对数据行进行过滤。这个是索引下推功能。
```

# 6. 锁
# 7. MVCC原理
# 8. 优化
## 8.1 主从
## 8.2 分库分表
## 8.3 读写分离
## 8.4 垂直划分

- 一个表字段, 分成两部分

## 8.5 水平划分
## 8.6 explain

# 问题

```java
mysqldump -uroot -p sjyw_8081 --skip-lock-tables > sjyw_8081.sql

dump 可能超时
set global max_allowed_packet=1000000000;
set global net_buffer_length=1000000;
SET GLOBAL  interactive_timeout=2880000000;
SET GLOBAL  wait_timeout=2880000000;

/usr/local/bin/mysqlfrm --basedir=/usr --port=3306 --user=root /data/mysql/test/ > test_frm.sql

```
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611653891242-1edc2ab8-767c-4b76-aa1e-a0c859a5e57a.png#align=left&display=inline&height=76&originHeight=76&originWidth=527&size=3997&status=done&style=none&width=527)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611653801116-66ce013e-c925-4bb5-bc4d-517ddcba3efd.png#align=left&display=inline&height=173&originHeight=173&originWidth=712&size=26213&status=done&style=none&width=712)
## mysql 误删除ibdata1之后如何恢复

```sql
FLUSH TABLES WITH READ LOCK;

SHOW engine innodb STATUS;

 SET global innodb_max_dirty_pages_pct=0;
```

### 恢复单个表

```sql
错误
Tablespace for table '`mysql`.`innodb_index_stats`' exists. Please DISCARD the tablespace before IMPORT.

删除表 
drop table innodb_index_stats;
报错： Unknown table 'mysql.innodb_index_stats'
0. 先重启服务
1. .ibd和ifrm文件先备份， 再删除，
2. 重新执行建表语句
3. 	alter table innodb_index_stats discard tablespace;
4. 重新导入.ibd 数据文件
5. 	alter table innodb_index_stats import tablespace;
```
### ibadata1被删， .ibd和.frm文件还在

```sql
mysqlfrm 来读取frm文件
# 安装 1.3.6 版本 yum方式
yum install https://mirrors.tuna.tsinghua.edu.cn/epel/7/x86_64/Packages/m/mysql-connector-python-1.1.6-1.el7.noarch.rpm
yum install https://mirrors.tuna.tsinghua.edu.cn/epel/7/x86_64/Packages/m/mysql-utilities-1.3.6-1.el7.noarch.rpm

读取某个库的表结构
 mysqlfrm --diagnostic /data/mysqldb/sjyw8081 > /home/<USER>

批量获取 discard tablespace
mysql -plinewell-123 -N -e " select concat('alter table ',table_name,' discard tablespace ;') from information_schema.tables where table_schema='sjyw8081'; " >/homr/1.sql

批量获取 import tablespace 
mysql  -plinewell-123 -N -e " select concat('alter table ',table_name,' import tablespace ;') from information_schema.tables where table_schema='sjyw8081'; " >import.sql

CREATE TABLE IF NOT EXISTS
```
[https://www.cnblogs.com/gered/p/12533900.html](https://www.cnblogs.com/gered/p/12533900.html)
[https://pdf.us/2019/01/10/2620.html](https://pdf.us/2019/01/10/2620.html)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611669891441-baea6cb8-83d6-45bf-8ac5-85754db7942f.png#align=left&display=inline&height=377&originHeight=377&originWidth=398&size=14722&status=done&style=none&width=398)
