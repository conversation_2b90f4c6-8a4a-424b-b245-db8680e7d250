# sql
```sql
CREATE TABLE `sys_user_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(255)  DEFAULT NULL,
  `password` varchar(255)  DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `password` (`password`),
  KEY `idx_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=7;
```
# explain使用
```sql
EXPLAIN select * from sys_user_info where username = 'admin';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679720776267-536d35c9-329c-4ad0-8b38-f65cca2a8a8f.png#averageHue=%23f8f8f8&clientId=u207e2b31-ca11-4&from=paste&height=96&id=u0d38a110&originHeight=192&originWidth=2038&originalType=binary&ratio=2&rotation=0&showTitle=false&size=49539&status=done&style=none&taskId=uf41479f1-9f28-4ce9-b8e8-c45e3d73a91&title=&width=1019)
# 参数解释
## id
> 执行计划的id，值越大，优先级越高。

## select_type
> 查询语句的类型

- simple：简单语句
```sql
select * from sys_user_info where username = 'admin';
```

- primary：复杂查询的外层查询
```sql
EXPLAIN select * from sys_user_info where id = (select id from sys_user_info where username = 'admin')
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679721546744-97ec8a3d-4c86-4560-8267-73dd20ce195c.png#averageHue=%23f6f6f6&clientId=u207e2b31-ca11-4&from=paste&height=87&id=u97799c1f&originHeight=174&originWidth=2022&originalType=binary&ratio=2&rotation=0&showTitle=false&size=73652&status=done&style=none&taskId=u8dc25db3-51e2-4303-a8e3-4214618a858&title=&width=1011)

- subquery：查询语句中的子查询
- derived：包含在FROM中的子查询
## table
> 当前执行计划在哪张表执行。

## partitions
## type
> 当前执行计划查询的类型。

- all：全表，未命中索引或索引失效。
- system：要查询的表中仅有一条数据。
- const：查询条件，命中索引查询
   - 命中UNIQUE唯一索引
- range：范围查询。
- eq_ref：多表关联查询
- ref：使用了普通索引。
- index：使用辅助索引查询。
## possible_keys
> 可能命中的索引

## key
> 命中的索引名字

## key_len
> 索引字段使用的字节数。

## ref
> 显示使用了哪种查询的类型。

## rows
> 可能扫描多少行数据才能检索出结果。

## filtered
## Extra
> 额外记录一些索引使用信息

- using index：使用覆盖索引查询。
```sql
explain select id from sys_user_info where username = 'admin'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679722829443-2b69e99e-32e8-4a8f-a466-19c376fe52f2.png#averageHue=%23f7f7f7&clientId=u207e2b31-ca11-4&from=paste&height=72&id=uc68a0363&originHeight=144&originWidth=1934&originalType=binary&ratio=2&rotation=0&showTitle=false&size=46318&status=done&style=none&taskId=u34385674-6808-48a0-9c87-e1d2ad28498&title=&width=967)

- using where：where查询，没有使用索引。
```sql
explain select id from sys_user_info where status = 'admin'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679722865357-4bcfa961-0a69-4767-8484-b793e3f91821.png#averageHue=%23f8f8f8&clientId=u207e2b31-ca11-4&from=paste&height=73&id=u41c150a6&originHeight=146&originWidth=2004&originalType=binary&ratio=2&rotation=0&showTitle=false&size=42593&status=done&style=none&taskId=uab0bcf8d-8a52-4daf-972e-c4a38527768&title=&width=1002)

- using index condition：使用到联合索引的前几个字段。
- using temporary：使用了临时表处理查询结果。
- using filesort：以索引字段之外的方式进行排序，效率较低。
```sql
EXPLAIN select id from sys_user_info where username = 'admin' ORDER BY STATUS
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679722758332-6b85b973-7522-43a7-afa6-a485de061b15.png#averageHue=%23f8f8f8&clientId=u207e2b31-ca11-4&from=paste&height=73&id=u7ab39c50&originHeight=146&originWidth=1972&originalType=binary&ratio=2&rotation=0&showTitle=false&size=44712&status=done&style=none&taskId=ud7b2c4ae-6aea-448d-ae9b-4d994d0f6b5&title=&width=986)

- select tables optimized away：在索引字段使用了聚合函数。
```sql
EXPLAIN select MAX(id) from sys_user_info where username = 'admin'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679722693265-91840d39-a955-4ce5-93ad-fe561a1f6449.png#averageHue=%23f9f9f9&clientId=u207e2b31-ca11-4&from=paste&height=82&id=u80a557ec&originHeight=164&originWidth=2040&originalType=binary&ratio=2&rotation=0&showTitle=false&size=79745&status=done&style=none&taskId=udf8e94f1-067b-445a-b416-c5c46ed1f71&title=&width=1020)



