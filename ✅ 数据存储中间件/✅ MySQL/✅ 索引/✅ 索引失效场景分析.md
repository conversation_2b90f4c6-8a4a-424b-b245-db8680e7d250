# sql
```sql
CREATE TABLE `t_student`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sname` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `s_code` int(100) NULL DEFAULT NULL,
  `address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `height` double NULL DEFAULT NULL,
  `classid` int(11) NULL DEFAULT NULL,
  `create_time` datetime(0) NOT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `普通索引`(`height`) USING BTREE,
  INDEX `联合索引`(`sname`, `s_code`, `address`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680621361691-fa124308-6c0c-4042-97cb-e8f1cf39ec5f.png#averageHue=%23f6f6f5&clientId=udd986c82-deef-4&from=paste&height=103&id=u8ab594ec&originHeight=312&originWidth=2592&originalType=binary&ratio=2&rotation=0&showTitle=false&size=135431&status=done&style=none&taskId=u0f94f3d9-854c-401b-be23-8d38b8f87bb&title=&width=852)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680622356872-f844339c-0a77-4dd4-b412-3fbfc0faa33b.png#averageHue=%23f2f2f1&clientId=u887397b6-20d8-4&from=paste&height=117&id=uc9e7353a&originHeight=234&originWidth=1178&originalType=binary&ratio=2&rotation=0&showTitle=false&size=73141&status=done&style=none&taskId=ub4a5fe68-d5b3-4a0e-a0a2-f0c5387cc79&title=&width=589)
# 最左前缀匹配原则
> 联合索引 `sname`, `s_code`, `address`
> 只有查询条件有sname才会命中索引，另外两个字段可有无。
> **第一个索引字段在查询条件中必须存在才会命中索引，且索引字段顺序可随意，优化器会处理。**
> 查询条件为以下索引不生效
> - s_code
> - address
> - `s_code`, `address`

# 索引失效场景
## 1. 查询条件带OR
> OR条件字段都是索引字段，索引才生效，否则所有就失效了。

```sql
-- height是普通索引，s_code不是索引，所以OR查询 索引失效了
explain select sname, s_code from student where height = 170 or s_code = 1;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680621443093-171b14f1-c666-4f02-a188-8291b2445298.png#averageHue=%23f8f8f8&clientId=udd986c82-deef-4&from=paste&height=92&id=uc2627ef5&originHeight=184&originWidth=1972&originalType=binary&ratio=2&rotation=0&showTitle=false&size=55409&status=done&style=none&taskId=ud936fdda-be98-43af-a016-90dc0e7f9da&title=&width=986)
```sql
--  height是普通索引，id是主键索引，OR查询条件都是索引，所以命中索引了
explain select sname, s_code from student where height = 170 or id = 1;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680621494120-4df02bf0-2452-4cd7-8712-7196b34430b1.png#averageHue=%23f9f9f9&clientId=udd986c82-deef-4&from=paste&height=130&id=u7285d56c&originHeight=260&originWidth=2516&originalType=binary&ratio=2&rotation=0&showTitle=false&size=86963&status=done&style=none&taskId=u2a87464c-a9c8-4d00-85bd-a3018f6d35b&title=&width=1258)
## 2. 模糊查询 like %xxx
> %xxx，查询字段也不是索引字段，不走索引。

```sql
explain select id,sname, s_code ,address, height, classid, create_time  from student where sname like '%生';
explain select * from student where sname like '%生';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680622622770-2a1f4025-68e6-40ec-b0ad-52cda1b4110f.png#averageHue=%23f9f9f9&clientId=u887397b6-20d8-4&from=paste&height=102&id=u1cc9cb91&originHeight=204&originWidth=2136&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57967&status=done&style=none&taskId=u308cb3e6-89b1-44ba-ae5c-6ca9731f023&title=&width=1068)

> %xxx，查询字段全是索引字段，type=index 说明使用了辅助索引。这样是能走索引。

```sql
explain select sname, s_code from student where sname like '%生';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623069431-691241f5-ccd1-4ae6-8d40-bde8435cf23d.png#averageHue=%23f8f8f8&clientId=u887397b6-20d8-4&from=paste&height=102&id=u5d5d884a&originHeight=204&originWidth=1922&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59492&status=done&style=none&taskId=u5393d832-67bf-43a6-9fa3-677dc3bbb5b&title=&width=961)

> xxx% 
> 应该会走索引。

```sql
-- 只有一条数据，走索引
explain select id,sname, s_code ,address, height, classid, create_time from student  where sname like '变%';
-- 模糊数据多 没走索引
explain select id,sname, s_code ,address, height, classid, create_time from student  where sname like '学生%';
-- force index强制走索引
explain select id,sname, s_code ,address, height, classid, create_time from student force index(联合索引) where sname like '学生%';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623002502-f0e8f8d2-1e0a-462a-a756-84a2493a63be.png#averageHue=%23f9f9f9&clientId=u887397b6-20d8-4&from=paste&height=137&id=u803353c4&originHeight=274&originWidth=1982&originalType=binary&ratio=2&rotation=0&showTitle=false&size=67852&status=done&style=none&taskId=u595a0661-2098-4a5d-ab79-baa62df8821&title=&width=991)
## 3. 字符串类不带引号''作为查询条件
```sql
explain select create_time from student where sname = 222;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623451343-62635048-931d-4fce-8cb7-4b5922c01341.png#averageHue=%23fafaf9&clientId=u887397b6-20d8-4&from=paste&height=132&id=u3931df09&originHeight=264&originWidth=1934&originalType=binary&ratio=2&rotation=0&showTitle=false&size=64233&status=done&style=none&taskId=ud9f893d3-64c8-4467-bdf8-de77824442a&title=&width=967)

```sql
explain select create_time from student where sname = '222'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623474123-9d3da9d2-f1cb-478b-90f0-d10c19365727.png#averageHue=%23f9f9f8&clientId=u887397b6-20d8-4&from=paste&height=106&id=ue5f1f245&originHeight=212&originWidth=1882&originalType=binary&ratio=2&rotation=0&showTitle=false&size=53352&status=done&style=none&taskId=u9dfb3359-00b3-4cea-b15a-ee5b2b6a088&title=&width=941)

## 4. 索引字段参与计算
> height是普通索引。索引字段height计算导致索引失效。height是普通索引。
> +、-、*、/、!

```sql
explain select create_time from student where height = height+10;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623605951-c94895c9-d48e-447d-abac-5375a66ee1ca.png#averageHue=%23f9f9f9&clientId=u887397b6-20d8-4&from=paste&height=112&id=u7c3b7df6&originHeight=224&originWidth=1952&originalType=binary&ratio=2&rotation=0&showTitle=false&size=48948&status=done&style=none&taskId=u47b0c3db-1c4a-44bf-b0b6-a51015a609d&title=&width=976)
```sql
explain select create_time from student where height = 170;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623627713-27b5ad44-6831-4635-8ba9-9769448f055c.png#averageHue=%23f8f8f8&clientId=u887397b6-20d8-4&from=paste&height=81&id=ue0552981&originHeight=162&originWidth=1908&originalType=binary&ratio=2&rotation=0&showTitle=false&size=43962&status=done&style=none&taskId=u2d47c246-98f3-44d5-8041-26e8e1a42fe&title=&width=954)
## 5. 字段使用函数计算导致索引失效
```sql
explain select create_time from student where SUBSTRING(sname,0,1) = "学";
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680623888592-ad9280c7-c208-4234-aaf4-ea4bceb4c7c7.png#averageHue=%23f9f9f9&clientId=u887397b6-20d8-4&from=paste&height=100&id=ua72d098e&originHeight=200&originWidth=1942&originalType=binary&ratio=2&rotation=0&showTitle=false&size=46454&status=done&style=none&taskId=u16a46675-9202-4f14-bd00-870131b6117&title=&width=971)
## 6. 违背最左前缀匹配
## 7. 不同字段值对比导致失效
```sql
explain select create_time from student where height = s_code;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680624002053-b49c96a0-22cf-429d-98b3-451be7460fad.png#averageHue=%23f9f9f9&clientId=u887397b6-20d8-4&from=paste&height=80&id=u127df977&originHeight=160&originWidth=1928&originalType=binary&ratio=2&rotation=0&showTitle=false&size=41105&status=done&style=none&taskId=u74bb7900-28db-4de0-acbc-373bbb9b727&title=&width=964)
## 8. 反向范围查询导致失效
> **正向范围查询：>、<、between、like、in**
> **反向范围查询：NOT IN、NOT LIKE、IS NOT NULL、!=、<>**
> **IS NULL 和 IS NOT NULL 也不会走索引。**

```sql
explain select create_time from student where height NOT in (170);
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680624125412-a3c319fa-2e57-4b2f-bf1d-a2c147f4ac2c.png#averageHue=%23f8f8f8&clientId=u887397b6-20d8-4&from=paste&height=74&id=u4c8db1a9&originHeight=148&originWidth=1884&originalType=binary&ratio=2&rotation=0&showTitle=false&size=42340&status=done&style=none&taskId=u37fffe0e-f332-4503-8fd2-b6f1dc3a485&title=&width=942)

