# 创建索引注意点
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680620797379-dab0d7b0-6faf-442d-beb9-981014eb5c56.png#averageHue=%23e5e5e5&clientId=u0b0585c8-a78f-4&from=paste&height=220&id=u8a6e5151&originHeight=440&originWidth=1228&originalType=binary&ratio=2&rotation=0&showTitle=false&size=207467&status=done&style=none&taskId=uf2cde4c2-156d-4bda-b42f-ec83c13d6a7&title=&width=614)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680620808070-0241b90e-b386-43c6-b463-02ecfe58cbf5.png#averageHue=%23e4e4e4&clientId=u0b0585c8-a78f-4&from=paste&height=193&id=ue7ede5a6&originHeight=386&originWidth=1178&originalType=binary&ratio=2&rotation=0&showTitle=false&size=175159&status=done&style=none&taskId=u4b7b3795-a592-402e-aba4-c0945bfe843&title=&width=589)

# 索引覆盖
> 查询指定字段都是是索引字段。覆盖索引。不会出现回表。
> Extra： Using Index

# 索引下推
> 查询条件有多个， 先根据条件进行过滤，再进行回表操作。

```sql
set optimizer_switch='index_condition_pushdown=off|on’;
```

#### MRR(Multi-Range Read)机制
> 查询的数据可能在不同的页，有可能在不同页来回切换，产生离散IO。
> MRR机制将随机IO转为顺序IO，将在相同也得数据整合到一起查询，减少离散IO。

## 如何实现
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680625029232-c67a63ea-4f17-47ae-ac2d-bc6d3a73d64f.png#averageHue=%23e8eced&clientId=u7f7fcb66-4ba3-4&from=paste&height=114&id=u91aad9a5&originHeight=228&originWidth=1576&originalType=binary&ratio=2&rotation=0&showTitle=false&size=117032&status=done&style=none&taskId=u8a1d280b-1827-4ea9-835a-55a69d6c0c1&title=&width=788)
```sql
SET @@optimizer_switch='mrr=on|off,mrr_cost_based=on|off';
```

#### Index Skip Scan索引跳跃式扫描
> 8.0引入。
> 使查询条件中，没有使用联合索引的第一个字段，也依旧可以使用联合索引，看起来就像跳过了联合索引中的第一个字段一样，这也是跳跃扫描的名称由来。

> 实际上是优化器为你重构了SQL，索引字段没用，自动加上这个条件

```sql
-- (A、B、C)联合索引
-- 原sql
SELECT * FROM `tb_xx` WHERE B = `xxx` AND C = `xxx`;


SELECT * FROM `tb_xx` WHERE B = `xxx` AND C = `xxx`
UNION ALL
SELECT * FROM `tb_xx` WHERE B = `xxx` AND C = `xxx` AND A = "yyy"
......
SELECT * FROM `tb_xx` WHERE B = `xxx` AND C = `xxx` AND A = "zzz";
```
```sql
set @@optimizer_switch = 'skip_scan=off|on';
```
# 索引相关查询
```sql
-- 查看当前会话的索引使用情况。
show status like '%Handler_read%';
-- 查询全局索引使用情况。
show global status like 'Handler_read%';
```
