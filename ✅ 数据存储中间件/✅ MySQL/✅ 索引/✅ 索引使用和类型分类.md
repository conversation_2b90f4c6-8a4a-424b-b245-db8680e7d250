# sql

```sql
create table student (
	student_id bigint(20) NOT NULL AUTO_INCREMENT,
	name VARCHAR(8) DEFAULT NULL,
	sex INT(1) DEFAULT NULL,
	height INT(1) DEFAULT NULL,
	PRIMARY KEY(`student_id`) USING BTREE
) ENGINE=InnoDB;

CREATE TABLE tableName(
  columnName1 INT(8) NOT NULL,
  columnName2 ....,
  .....,
  INDEX [indexName] (columnName(length)) ,
  UNIQUE INDEX [indexName] (columnName(length))
);

```

# 索引使用

## 索引新增删除

```sql
-- 创建普通索引
ALTER TABLE tableName ADD INDEX indexName(columnName(length) [ASC|DESC]) USING BTREE;
ALTER TABLE student ADD INDEX indx_height(height);

-- 创建联合索引
CREATE INDEX indexName ON tableName (column1(length),column2...);
ALTER TABLE tableName ADD INDEX indexName(column1(length),column2...);

-- 创建唯一索引
ALTER TABLE tableName ADD UNIQUE INDEX indexName(columnName);
CREATE UNIQUE INDEX indexName ON tableName (columnName(length));

-- 创建主键索引
ALTER TABLE tableName ADD PRIMARY KEY indexName(columnName);

-- 创建全文索引，字段需要CHAR、VARCHAR、TEXT类型
ALTER TABLE tableName ADD FULLTEXT INDEX indexName(columnName);
CREATE FULLTEXT INDEX indexName ON tableName(columnName);

-- 删除索引
DROP INDEX indexName ON tableName;

-- 查询强制使用索引
SELECT * FROM table_name FORCE INDEX(index_name) WHERE .....;
explain select * from student force INDEX(indx_height) where height = 2;
```

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680617667897-b7d9f3a6-780f-40db-923a-5b95d5596dfe.png#averageHue=%23ececec&clientId=u8b4ce2e6-bea3-4&from=paste&height=147&id=ue4965b5e&originHeight=294&originWidth=1146&originalType=binary&ratio=2&rotation=0&showTitle=false&size=105829&status=done&style=none&taskId=ub6da97e6-49f1-497f-bd2b-a09dcb4649c&title=&width=573)

## 查看表索引

```sql
show index from student;
```

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680617551129-e083a48f-770f-4ea3-afbe-9b5f3afbc6c2.png#averageHue=%23f8f8f8&clientId=u8b4ce2e6-bea3-4&from=paste&height=112&id=ub4310f7f&originHeight=224&originWidth=2216&originalType=binary&ratio=2&rotation=0&showTitle=false&size=64200&status=done&style=none&taskId=u95bd3e42-ceb0-4075-bbed-2b62e5a601c&title=&width=1108)![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680617602529-63a7e1b4-7e07-49a4-8328-ca785c797422.png#averageHue=%23eaeff0&clientId=u8b4ce2e6-bea3-4&from=paste&height=332&id=u65addc2f&originHeight=664&originWidth=1220&originalType=binary&ratio=2&rotation=0&showTitle=false&size=245752&status=done&style=none&taskId=u6a5ba0e6-481d-4555-8c8f-b5a2aa3ccff&title=&width=610)

# 索引分类

## 根据数据结构

- B+TREE：默认
- HASH
- R-Tree
- T-Tree

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680619164134-88306dd2-efa8-4d18-a4a8-b1888d6f69bb.png#averageHue=%23eaeff0&clientId=u8b4ce2e6-bea3-4&from=paste&height=130&id=udf570d70&originHeight=260&originWidth=1202&originalType=binary&ratio=2&rotation=0&showTitle=false&size=100142&status=done&style=none&taskId=u296379dd-3098-4d20-bb7a-d9f76ca39ea&title=&width=601)

## 根据存储方式

### 聚簇索引

- 一张表只有一个聚簇索引。索引字段必须是非空唯一，一般是主键，没有主键，用唯一索引，没有唯一索引，会创建一个隐藏列 row_id，是递增的值。
- 索引数据和表数据存储在一起，都存储在叶子节点上。

### 非聚簇索引

- 一般是主键作为聚簇索引，那么其他字段建立的索引就是非聚簇索引。
- 索引数据和表数据分开存储，叶子节点存储主键，查询先根据索引查询到主键，再到聚簇索引查询对应的记录。

## 根据字段数量

### 单列索引

- 主键索引
- 唯一索引
- 普通索引

### 多列索引

- 联合索引（组合索引。。。）
- 覆盖索引（也可能是单列）

## 根据功能分类

- 主键索引
- 唯一索引
- 普通索引
- 全文索引（5,7 MyISAM 支持）
- 空间索引（5,7 MyISAM 支持）

# 唯一索引和普通索引，插入，查询的效率对比如何

```
都查磁盘
    唯一索引
      - 插入慢，需要全表扫描，判断唯一性（先查询在更新），
      - 查询快，查询到一条记录就终止查询，因为记录唯一，不会重复
    普通索引
      - 插入快，不需要哦啊不到唯一性
      - 查询慢，需要找到所有匹配的记录

查询
   MySQL按页为单位读取数据，每页16kb，加载数据到内存里。
   对于唯一索引，查询时，如果数据在内存中，直接返回。
   对于普通索引，当查询到满足条件的第一个数据后，有两种情况：
    1. 查询到的数据，不是数据页的最后一个数据，那么直接从数据页查询后一个，数据都在内存中，直接返回。
    2. 查询到的数据是数据页的最后一个数据，需要读取下一页数据，若下一页数据在内存中直接读取，否则从磁盘中读取。

更新数据
   要更新的数据所在的数据页在内存中，这直接更新内存。两者更新性能一样。
   不在内存中，InnoDB会先将要更新的数据缓存到change buffer中，普通索引可以直接修改，唯一索引要保证change buffer和磁盘数据一致性，需要先查询一次数据再进行更新、插入。
```
