# 全表扫描
> 查询数据时， mysql默认会触发磁盘IO来读取表数据，但InnoDB引擎读取时，会利用局部性原理，
也就是预读思想，一次IO会读取**16KB**的磁盘数据放入内存，满足条件的数据留在内存，其他丢弃，
然后继续读取，直到整张表数据都读完之后，把符合条件的数据返回。

> 假设表中一条数据大小为512Byte，一次磁盘IO也只能读32条，假设表中有320w条数据，一次全表就有可能会触发10W次磁盘IO。

# 索引结构选择
> 

## 二叉树
> 它由根节点、左子树和右子树组成。
> 每个节点最多只有两个子节点，
> 其中**左子节点的值小于它的父节点，**
> **右子节点的值大于它的父节点。**

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680695931937-022bd07a-3a71-4a84-bd4a-92264f598bf6.png#averageHue=%23ffffff&clientId=u6c5f872e-21bc-4&from=paste&height=341&id=u5e716e03&originHeight=682&originWidth=818&originalType=binary&ratio=2&rotation=0&showTitle=false&size=44795&status=done&style=none&taskId=uf17272cf-6653-44a0-93dd-2a1418042cc&title=&width=409)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680695894865-470405a4-b2ea-4270-a26d-612aaeb617fc.png#averageHue=%23ffffff&clientId=u6c5f872e-21bc-4&from=paste&height=287&id=ud40a3c41&originHeight=574&originWidth=610&originalType=binary&ratio=2&rotation=0&showTitle=false&size=32641&status=done&style=none&taskId=u92bd603b-8dec-4f68-ab29-9685f35cb16&title=&width=305)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680695820328-8ed6c74f-5962-4d77-b032-b123a3a9afc7.png#averageHue=%23ededed&clientId=u6c5f872e-21bc-4&from=paste&height=126&id=u1e64e7d9&originHeight=252&originWidth=1096&originalType=binary&ratio=2&rotation=0&showTitle=false&size=81706&status=done&style=none&taskId=u9d876814-7481-4aea-99e3-4f1a03b9348&title=&width=548)
## 红黑树
> 红黑树是一种自平衡的二叉查找树。通过颜色标记节点、旋转等操作来确保树的高度始终保持在O(log n)级别。

### 特性

1. 每个节点不是红色就是黑色。
2. 根节点是黑色的。
3. 每个叶子节点（NIL节点）是黑色的。
4. 如果一个节点是红色的，则它的两个子节点都是黑色的。
5. 对于每个节点，从该节点到其所有后代孙节点的简单路径上，均包含相同数目的黑色节点。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680696260184-f806a7d4-3696-4c0f-bf73-12917537c25d.png#averageHue=%23f1efef&clientId=u6c5f872e-21bc-4&from=paste&height=438&id=u6f24b459&originHeight=584&originWidth=1054&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59582&status=done&style=none&taskId=uca4994a3-3691-4f9c-8ad6-79c705f797a&title=&width=791)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680696487966-200b1852-b4ec-475e-9e0e-f2e9e93b4e03.png#averageHue=%23f0f0f0&clientId=u6c5f872e-21bc-4&from=paste&height=103&id=u934ec776&originHeight=206&originWidth=1204&originalType=binary&ratio=2&rotation=0&showTitle=false&size=68603&status=done&style=none&taskId=u47080d62-da43-4574-8bfc-8c75d8e58ab&title=&width=602)
## BTree
> B树可以理解成红黑树的一个变种，B树中一个节点可容纳多个数据。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680696791783-b544382b-a903-43d9-844e-ebba8b82d2df.png#averageHue=%23f4f4f4&clientId=u6c5f872e-21bc-4&from=paste&height=108&id=u5ac03612&originHeight=216&originWidth=1526&originalType=binary&ratio=2&rotation=0&showTitle=false&size=76271&status=done&style=none&taskId=ue9e2798d-422d-4e62-b87c-82fc0ac2232&title=&width=763)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680696622223-4e59c664-6d72-4ae9-892b-bd32f449f06d.png#averageHue=%23ffffff&clientId=u6c5f872e-21bc-4&from=paste&height=248&id=F8zgZ&originHeight=330&originWidth=864&originalType=binary&ratio=2&rotation=0&showTitle=false&size=23852&status=done&style=none&taskId=u184c236c-f2d2-42de-8428-10c7f9cf18b&title=&width=648)
## B+Tree
> 节点分非叶子节点和叶子节点。
> 叶子节点存储数据，非叶子节点存储索引值。
> 叶子节点形成一个有序的链表，方便区间查询。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680696841150-65d27c45-d04a-4626-b522-3107adfc6834.png#averageHue=%23ffffff&clientId=u6c5f872e-21bc-4&from=paste&height=350&id=u8420afbf&originHeight=466&originWidth=1236&originalType=binary&ratio=2&rotation=0&showTitle=false&size=33663&status=done&style=none&taskId=uae023882-3419-4feb-976a-234eefa02d0&title=&width=927)
### 千万级数据的表树有多高?
#### 数据准备
#### 1. 索引字段值的大小。
> int类型占4Bytes，
> bigint 占8Bytes

#### 2. MySQL中B+Tree单个节点的大小。

- 查询页大小：SHOWGLOBAL STATUS LIKE "Innodb_page_size"; 

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680697851743-76f29711-5e6c-4088-b0b9-a10c0b5af8c9.png#averageHue=%23f9f9f9&clientId=u6c5f872e-21bc-4&from=paste&height=107&id=u61598ad9&originHeight=214&originWidth=546&originalType=binary&ratio=2&rotation=0&showTitle=false&size=19576&status=done&style=none&taskId=u02787fc7-cb03-4831-860a-6c52e8955e4&title=&width=273)

- InnoDB引擎一页大小为 16384Bytes=16KB
#### 3. MySQL中单个指针的大小。

- 单个指针6Bytes
#### 计算
> 一条记录大小为 1KB = 1024Bytes
> 单个索引大小为 4Bytes + 6Bytes = 10Bytes
> 单个节点存储索引数量：16384Bytes / 10Bytes = 1638个

2层高的树：根节点可存储1638个叶子节点指针，第二层有1638个叶子节点。
可存储数据：16KB / 1KB * 1638 = 26208条数据

3层高的数据： 16KB / 1KB * 1638 * 1638 = 42928704条数据。
> 只要经过三次IO就能查询到数据。很快。
>  索引键大小=索引字段类型所占的空间、一行表数据大小=所有表字段的类型+隐藏字段（20Bytes）所占大小总和

## Mysql B+Tree
> B+Tree升级版， 链表是双向的

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680698679253-fb118483-555f-44d5-a761-2bee78305b13.png#averageHue=%23e9b9b2&clientId=u6c5f872e-21bc-4&from=paste&height=501&id=u860dda8e&originHeight=1002&originWidth=2534&originalType=binary&ratio=2&rotation=0&showTitle=false&size=841950&status=done&style=none&taskId=ube2a6576-4e55-495e-a787-8d5bcf496df&title=&width=1267)
# 表数据如何存储？
## 查询mysql数据存储位置
```java
SHOW VARIABLES LIKE 'datadir';
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680698913174-9c456f38-9d5a-4e6d-ba3a-0a2ed443cef6.png#averageHue=%23fafafa&clientId=u6c5f872e-21bc-4&from=paste&height=102&id=u81726f21&originHeight=204&originWidth=808&originalType=binary&ratio=2&rotation=0&showTitle=false&size=21844&status=done&style=none&taskId=ue9fa64c7-5512-4258-9873-2efc3341955&title=&width=404)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680699065693-960a96cb-df82-47d5-b005-decf3fb0d180.png#averageHue=%230a3643&clientId=u6c5f872e-21bc-4&from=paste&height=185&id=u4882f6d3&originHeight=370&originWidth=1098&originalType=binary&ratio=2&rotation=0&showTitle=false&size=73469&status=done&style=none&taskId=uefd94d62-1c90-46d6-8204-7e537772719&title=&width=549)
## InnoDB

- .frm  表的结构信息
- .ibd   表的行数据和索引数据
## MyISAM
> MyISAM不支持聚簇索引，应为聚簇索引需要表数据和索引数据存储在一起。MyISAM是分开存储。

- .MYD    表的行数据
- .MYI     表的索引数据
- .frm     表的结构信息
## 总结

- MyISAM引擎的非聚簇索引，关联的是行数据的指针，而InnoDB引擎关联的是聚簇索引的索引键。
- InnoDB需要回表，MyISAM不需要回表。





