# 查询事务
```sql
SELECT * FROM information_schema.INNODB_TRX;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933476722-939bc79a-ce42-44b0-8a34-082fb1d54829.png#averageHue=%23f7f7f7&clientId=u7266fa82-94b1-4&from=paste&height=216&id=u87aae74a&originHeight=432&originWidth=3352&originalType=binary&ratio=2&rotation=0&showTitle=false&size=103704&status=done&style=none&taskId=uc0952ea0-ced7-4051-9b16-86ad6c42dcd&title=&width=1676)
# 查询正在锁的事务

- lock_mode
   - S 共享锁
      - LOCK IN SHARE MODE;
   - X 排他锁
      - 执行 for update  
```sql
SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCKS;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933629107-4865c179-501d-4db6-8c49-0f20c0ce0398.png#averageHue=%23fafaf9&clientId=u7266fa82-94b1-4&from=paste&height=186&id=uec377eb8&originHeight=372&originWidth=3344&originalType=binary&ratio=2&rotation=0&showTitle=false&size=112440&status=done&style=none&taskId=uf8eb3146-108b-4683-a62f-57a97052886&title=&width=1672)
# 查询等待锁的事务
> 在等待锁，事务状态trx_state LOCK_WAIT

```sql
SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCK_WAITS;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933737919-0f3adfd3-ad03-45c3-9115-6ec3a7104b0e.png#averageHue=%23f9f9f9&clientId=u7266fa82-94b1-4&from=paste&height=157&id=u6309ca23&originHeight=314&originWidth=3352&originalType=binary&ratio=2&rotation=0&showTitle=false&size=50351&status=done&style=none&taskId=u29851a31-f5f2-4d32-a14d-2fee0b6fab8&title=&width=1676)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933787926-7ff62048-706d-4e50-be9d-009df79c358a.png#averageHue=%23f5f5f5&clientId=u7266fa82-94b1-4&from=paste&height=172&id=u3cf32087&originHeight=344&originWidth=3356&originalType=binary&ratio=2&rotation=0&showTitle=false&size=123810&status=done&style=none&taskId=u3f345b92-e98d-40e1-8b30-6ea896433eb&title=&width=1678)
# 查询进程
```sql
show PROCESSLIST;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933882864-addea7f0-d242-4155-8e94-d86dab9a7014.png#averageHue=%23f8f8f7&clientId=u7266fa82-94b1-4&from=paste&height=289&id=u13431f69&originHeight=578&originWidth=3356&originalType=binary&ratio=2&rotation=0&showTitle=false&size=234457&status=done&style=none&taskId=u9d929922-ff21-4b92-88f1-5fa98ed2fd4&title=&width=1678)
# 查询是否锁表
```sql
show OPEN TABLES where In_use > 0;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680933938798-85012c1a-b94e-4743-8543-37b124b31107.png#averageHue=%23fafafa&clientId=u7266fa82-94b1-4&from=paste&height=167&id=u7523d2a7&originHeight=334&originWidth=3356&originalType=binary&ratio=2&rotation=0&showTitle=false&size=49506&status=done&style=none&taskId=u2ea6c92f-adb0-4f1c-a632-daef451f880&title=&width=1678)
# 查看session的表
```sql
select * from sys.session;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934091631-edc34626-4929-4865-9e3a-65f489ab9b01.png#averageHue=%23f6f6f5&clientId=u7266fa82-94b1-4&from=paste&height=314&id=ufe0e72fa&originHeight=628&originWidth=3348&originalType=binary&ratio=2&rotation=0&showTitle=false&size=351909&status=done&style=none&taskId=u5c5e1537-332e-44a9-a589-df23ff6e760&title=&width=1674)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934068701-33e081e8-0fd4-4cf2-b137-0840568e3f27.png#averageHue=%23f1f1f1&clientId=u7266fa82-94b1-4&from=paste&height=582&id=ucc00a477&originHeight=1164&originWidth=3318&originalType=binary&ratio=2&rotation=0&showTitle=false&size=343715&status=done&style=none&taskId=u3338e74d-79ef-494c-912e-d6f84d397dd&title=&width=1659)
#查看当前执行的线程
```sql
select * from performance_schema.threads;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934155612-db7e4c4b-af59-49da-bf14-957341fec8a3.png#averageHue=%23f5f5f4&clientId=u7266fa82-94b1-4&from=paste&height=738&id=u91706596&originHeight=1476&originWidth=3348&originalType=binary&ratio=2&rotation=0&showTitle=false&size=1129037&status=done&style=none&taskId=u96516b2e-45e4-4a05-befb-17ce15ed094&title=&width=1674)
# 查看执行完成，但未commit的sql语句
```sql
select * from performance_schema.events_statements_current;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934256637-69e6e33d-c9bc-4114-9ade-7212bc5b30b5.png#averageHue=%23eeeeed&clientId=u7266fa82-94b1-4&from=paste&height=338&id=u7358313d&originHeight=676&originWidth=3352&originalType=binary&ratio=2&rotation=0&showTitle=false&size=477025&status=done&style=none&taskId=uc666a830-c517-49f5-acf9-9f744b97c8d&title=&width=1676)
# 查看慢查询sql语句
```sql
select * from mysql.slow_log;
```
   ![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934286664-719a29c9-54e7-405d-ac0a-11c767176248.png#averageHue=%23f9f9f9&clientId=u7266fa82-94b1-4&from=paste&height=116&id=u2cb61aef&originHeight=232&originWidth=3352&originalType=binary&ratio=2&rotation=0&showTitle=false&size=30827&status=done&style=none&taskId=u613fa4fa-3d37-4ba2-8945-2ef0d6203e0&title=&width=1676)

# 查看慢查询的设置
  show variable like 'slow%'

# 查看加锁的状态
```sql
show engine innodb status;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934353159-3e8e0997-7f97-4ad5-9fa9-1d92a47818a4.png#averageHue=%23fcfcfc&clientId=u7266fa82-94b1-4&from=paste&height=74&id=ud4b3f5d9&originHeight=148&originWidth=1896&originalType=binary&ratio=2&rotation=0&showTitle=false&size=16068&status=done&style=none&taskId=u72a1c56d-5a2a-4a94-be0b-6b1d5f2c1eb&title=&width=948)
```sql
select * from student FOR UPDATE
------- TRX HAS BEEN WAITING 4 SEC FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 65 page no 3 n bits 72 index PRIMARY of table `demo`.`student` trx id 8025 lock_mode X waiting
Record lock, heap no 6 PHYSICAL RECORD: n_fields 9; compact format; info bits 0
```
```sql
select * from student where id > 2 FOR UPDATE
------- TRX HAS BEEN WAITING 3 SEC FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 65 page no 3 n bits 72 index PRIMARY of table `demo`.`student` trx id 8025 lock_mode X waiting
Record lock, heap no 4 PHYSICAL RECORD: n_fields 9; compact format; info bits 0
```
```sql
select * from student where id = 2 FOR UPDATE
------- TRX HAS BEEN WAITING 5 SEC FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 65 page no 3 n bits 72 index PRIMARY of table `demo`.`student` trx id 8016 lock_mode X locks rec but not gap waiting
Record lock, heap no 3 PHYSICAL RECORD: n_fields 9; compact format; info bits 0
```
```sql

=====================================
2023-04-08 14:12:24 0x16e667000 INNODB MONITOR OUTPUT
=====================================
Per second averages calculated from the last 32 seconds
-----------------
BACKGROUND THREAD
-----------------
srv_master_thread loops: 79 srv_active, 0 srv_shutdown, 202579 srv_idle
srv_master_thread log flush and writes: 202658
----------
SEMAPHORES
----------
OS WAIT ARRAY INFO: reservation count 57
OS WAIT ARRAY INFO: signal count 55
RW-shared spins 0, rounds 49, OS waits 24
RW-excl spins 0, rounds 90, OS waits 0
RW-sx spins 0, rounds 0, OS waits 0
Spin rounds per wait: 49.00 RW-shared, 90.00 RW-excl, 0.00 RW-sx
------------
TRANSACTIONS
------------
Trx id counter 8017
Purge done for trx's n:o < 7993 undo n:o < 0 state: running but idle
History list length 0
LIST OF TRANSACTIONS FOR EACH SESSION:
---TRANSACTION 281480221008752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221007848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221006944, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221006040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221005136, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221003328, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 281480221002424, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 8016, ACTIVE 5 sec starting index read
mysql tables in use 1, locked 1
LOCK WAIT 2 lock struct(s), heap size 1136, 1 row lock(s)
MySQL thread id 21, OS thread handle 6146519040, query id 6586 localhost 127.0.0.1 root statistics
select * from student where id = 2 FOR UPDATE
------- TRX HAS BEEN WAITING 5 SEC FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 65 page no 3 n bits 72 index PRIMARY of table `demo`.`student` trx id 8016 lock_mode X locks rec but not gap waiting
Record lock, heap no 3 PHYSICAL RECORD: n_fields 9; compact format; info bits 0
 0: len 4; hex 80000002; asc     ;;
 1: len 6; hex 000000001f2d; asc      -;;
 2: len 7; hex a500000119011c; asc        ;;
 3: len 7; hex e5ada6e7949f32; asc       2;;
 4: len 4; hex 80000002; asc     ;;
 5: len 6; hex e58c97e4baac; asc       ;;
 6: len 8; hex 0000000000806640; asc       f@;;
 7: len 4; hex 80000002; asc     ;;
 8: len 5; hex 99ae454b10; asc   EK ;;

------------------
---TRANSACTION 7999, ACTIVE 60495 sec
2 lock struct(s), heap size 1136, 5 row lock(s)
MySQL thread id 12, OS thread handle 6146191360, query id 1410 localhost 127.0.0.1 root
--------
FILE I/O
--------
I/O thread 0 state: waiting for i/o request (insert buffer thread)
I/O thread 1 state: waiting for i/o request (log thread)
I/O thread 2 state: waiting for i/o request (read thread)
I/O thread 3 state: waiting for i/o request (read thread)
I/O thread 4 state: waiting for i/o request (read thread)
I/O thread 5 state: waiting for i/o request (read thread)
I/O thread 6 state: waiting for i/o request (write thread)
I/O thread 7 state: waiting for i/o request (write thread)
I/O thread 8 state: waiting for i/o request (write thread)
I/O thread 9 state: waiting for i/o request (write thread)
Pending normal aio reads: [0, 0, 0, 0] , aio writes: [0, 0, 0, 0] ,
 ibuf aio reads:, log i/o's:, sync i/o's:
Pending flushes (fsync) log: 0; buffer pool: 0
598 OS file reads, 568 OS file writes, 174 OS fsyncs
0.00 reads/s, 0 avg bytes/read, 0.00 writes/s, 0.00 fsyncs/s
-------------------------------------
INSERT BUFFER AND ADAPTIVE HASH INDEX
-------------------------------------
Ibuf: size 1, free list len 0, seg size 2, 0 merges
merged operations:
 insert 0, delete mark 0, delete 0
discarded operations:
 insert 0, delete mark 0, delete 0
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
Hash table size 34679, node heap has 0 buffer(s)
0.00 hash searches/s, 0.03 non-hash searches/s
---
LOG
---
Log sequence number 4006470
Log flushed up to   4006470
Pages flushed up to 4006470
Last checkpoint at  4006461
0 pending log flushes, 0 pending chkp writes
103 log i/o's done, 0.00 log i/o's/second
----------------------
BUFFER POOL AND MEMORY
----------------------
Total large memory allocated 137428992
Dictionary memory allocated 140843
Buffer pool size   8192
Free buffers       7709
Database pages     483
Old database pages 0
Modified db pages  0
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 0, not young 0
0.00 youngs/s, 0.00 non-youngs/s
Pages read 405, created 78, written 426
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 483, unzip_LRU len: 0
I/O sum[0]:cur[0], unzip sum[0]:cur[0]
--------------
ROW OPERATIONS
--------------
0 <USER> <GROUP> InnoDB, 0 queries in queue
0 read views open inside InnoDB
Process ID=888, Main thread ID=6140293120, state: sleeping
Number of rows inserted 1045, updated 1, deleted 0, read 1342
0.00 inserts/s, 0.00 updates/s, 0.00 deletes/s, 0.00 reads/s
----------------------------
END OF INNODB MONITOR OUTPUT
============================

```
# 查看/修改mysql事务隔离级别
```sql
select @@global.tx_isolation,@@tx_isolation;

-- 设置全局的
set global transaction isolation level read committed; 
-- 当前会话
set session transaction isolation level read committed;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934598566-879c8f72-9900-4daf-b549-a38bf44f6187.png#averageHue=%23f5f6f5&clientId=u7266fa82-94b1-4&from=paste&height=103&id=u69360be0&originHeight=206&originWidth=924&originalType=binary&ratio=2&rotation=0&showTitle=false&size=27452&status=done&style=none&taskId=ub30ebd96-4d46-4db2-b462-d93ba8e7bf5&title=&width=462)
# 查看当前阻塞的事务及对应的线程
```sql
select * from information_schema.innodb_trx i,
              performance_schema.events_statements_current c,
              information_schema.processlist b,
              performance_schema.threads t
        where t.thread_id = c.thread_id
          and i.trx_mysql_thread_id = b.id
          and t.processList_id = b.id;
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934647756-b039329d-fb70-4d82-8a00-9543f3d46f74.png#averageHue=%23f4f4f4&clientId=u7266fa82-94b1-4&from=paste&height=167&id=u2022071e&originHeight=334&originWidth=3354&originalType=binary&ratio=2&rotation=0&showTitle=false&size=126957&status=done&style=none&taskId=u6229e71a-f96c-4111-b76e-d9133ab2be2&title=&width=1677)

# 查询innoDB锁情况
```sql
show status like 'innodb_row_lock_%';

-- Innodb_row_lock_current_waits : 当前等待锁的数量
-- Innodb_row_lock_time : 系统启动到现在，锁定的总时间长度
-- Innodb_row_lock_time_avg : 每次平均锁定的时间
-- Innodb_row_lock_time_max : 最长一次锁定时间
-- Innodb_row_lock_waits : 系统启动到现在总共锁定的次数
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680934858413-9a0fd0a9-baf8-4627-b5bc-b982cf432e59.png#averageHue=%23f1f1f2&clientId=u7266fa82-94b1-4&from=paste&height=155&id=u8f48ebe9&originHeight=310&originWidth=874&originalType=binary&ratio=2&rotation=0&showTitle=false&size=56072&status=done&style=none&taskId=u7d66280c-c406-494a-be20-141d179a00d&title=&width=437)
