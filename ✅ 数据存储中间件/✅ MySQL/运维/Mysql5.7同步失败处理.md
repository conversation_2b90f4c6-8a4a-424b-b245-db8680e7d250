# mysql5.7 gtid主从同步失败处理

> https://bugs.mysql.com/bug.php?id=90823

> Last_SQL_Errno: 1007
> Last_SQL_Error: Coordinator stopped because there were error(s) in the worker(s). The most recent failure being:
> Worker
> 1 failed executing transaction '767de097-1355-11ef-bd31-66ac15b3e02f:709' at master log mysql-bin.000001, end_log_pos
> 21882686. See error log and/or performance_schema.replication_applier_status_by_worker table for more details about
            this
            failure or others, if any.

## 跳过失败的事务

```shell
stop slave;
set gtid_next='8a598d59-1813-11ef-8483-c60d67fc5c58:33033897';
begin; commit;
set gtid_next='AUTOMATIC';
```

## 查看复制工作线程的状态

```shell
SELECT * FROM performance_schema.replication_applier_status_by_worker\G
```

```sql
STOP SLAVE;
RESET MASTER;
SET GLOBAL gtid_purged =
        '8a598d59-1813-11ef-8483-c60d67fc5c58:1-18465884,8b6bb04c-1813-11ef-84b8-ee63d9d51640:5-7605950';
CHANGE MASTER TO MASTER_HOST ='************', MASTER_USER ='root', MASTER_PASSWORD ='cqt@fj889977', MASTER_AUTO_POSITION = 1;
START SLAVE;
```

## 删除binlog

```shell
PURGE BINARY LOGS BEFORE '2024-06-06 12:00:00';

```
