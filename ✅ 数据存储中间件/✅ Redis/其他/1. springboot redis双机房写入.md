# 1. pom依赖
```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
    <version>3.15.0</version>
</dependency>

<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
    <exclusions>
        <exclusion>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
        </exclusion>
    </exclusions>
</dependency>

<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-configuration-processor</artifactId>
</dependency>
```
# 2. 自定义redis配置类
```java
@Data
@Component
@JsonIgnoreProperties(ignoreUnknown = true)
@ConfigurationProperties(prefix = "spring.redis")
public class MultiRedisProperties implements Serializable {

    /**
     * Login password of the redis server.
     */
    private String password;

    /**
     * Connection timeout.
     */
    private Integer timeout;

    /**
     * 本地集群
     */
    private Cluster cluster;

    /**
     * 异地集群
     */
    private Cluster2 cluster2;

    /**
     * redisson master最大连接数
     */
    private Integer masterConnectionPoolSize;

    /**
     * jedis 连接池
     */
    private Jedis jedis;

    @Data
    public static class Cluster implements Serializable {

        /**
         * Comma-separated list of "host:port" pairs to bootstrap from. This represents an
         * "initial" list of cluster nodes and is required to have at least one entry.
         */
        private List<String> nodes;

        /**
         * Maximum number of redirects to follow when executing commands across the
         * cluster.
         */
        private Integer maxRedirects;

        /**
         * 是否激活
         */
        private Boolean active;

        /**
         * 机房位置 A/B
         */
        private String location;

    }

    /**
    * 多个redis数据源, 需要定义多个cluster, 不然 先加载的cluster会被后一个覆盖
    * 几个redis数据源就有几个cluster对象, 不同的对象
    */
    @Data
    public static class Cluster2 implements Serializable {

        /**
         * Comma-separated list of "host:port" pairs to bootstrap from. This represents an
         * "initial" list of cluster nodes and is required to have at least one entry.
         */
        private List<String> nodes;

        /**
         * Maximum number of redirects to follow when executing commands across the
         * cluster.
         */
        private Integer maxRedirects;

        /**
         * 是否激活
         */
        private Boolean active;

        /**
         * 机房位置 A/B
         */
        private String location;

    }


    @Data
    public static class Jedis {

        /**
         * Jedis pool configuration.
         */
        private Pool pool;

    }

    @Data
    public static class Pool {

        private int maxIdle = 50;

        private int minIdle = 10;

        private int maxActive = 200;

        private long maxWait = -1;

        private long timeBetweenEvictionRuns = -1;
    }

}

```
# 3. redisson配置类
> 在Bean上使用@RefreshScope注解, 配合nacos 实现配置自动刷新

```java
@Slf4j
@Configuration
public class MultiRedissonConfig {

    private static final String REDIS_PROTOCOL_PREFIX = "redis://";
    private static final String REDISS_PROTOCOL_PREFIX = "rediss://";

    private final MultiRedisProperties redisProperties;

    public MultiRedissonConfig(MultiRedisProperties redisProperties) {
        this.redisProperties = redisProperties;
    }

    @RefreshScope
    @Bean(name = "redissonClient")
    @Primary
    public RedissonClient redissonClient(MultiRedisProperties redisProperties) {
        Config config = getConfig(redisProperties.getCluster().getNodes());
        config.setCodec(StringCodec.INSTANCE);
//        config.setCodec(new LZ4Codec());
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (!osInfo.getName().contains("Win")) {
            log.info("redisson use epoll...");
            // linux 传输模式使用epoll, 性能貌似更高, win不支持
            config.setTransportMode(TransportMode.EPOLL);
        }
        return Redisson.create(config);
    }

    @RefreshScope
    @Bean(name = "redissonClient2")
    @ConditionalOnProperty(prefix = "spring.redis.cluster2", name = "active", havingValue = "true")
    public RedissonClient redissonClient2(MultiRedisProperties redisProperties) {
        Config config = getConfig(redisProperties.getCluster2().getNodes());
        config.setCodec(StringCodec.INSTANCE);
//        config.setCodec(new LZ4Codec());
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (!osInfo.getName().contains("Win")) {
            config.setTransportMode(TransportMode.EPOLL);
        }
        return Redisson.create(config);
    }

    @SuppressWarnings("all")
    private Config getConfig(List<String> nodesObject) {
        int timeout = redisProperties.getTimeout();

        String[] nodes = convert(nodesObject);

        Config config = new Config();
        config.useClusterServers()
                .addNodeAddress(nodes)
                .setConnectTimeout(timeout)
                .setTimeout(3000)
                .setIdleConnectionTimeout(3000)
                .setRetryAttempts(1)
                .setMasterConnectionPoolSize(redisProperties.getMasterConnectionPoolSize())
                .setRetryInterval(1000)
                .setCheckSlotsCoverage(false)
                .setScanInterval(10000)
                .setReadMode(ReadMode.MASTER)
                .setPassword(redisProperties.getPassword());
        return config;
    }

    @SuppressWarnings("all")
    private String[] convert(List<String> nodesObject) {
        List<String> nodes = new ArrayList<>(nodesObject.size());
        for (String node : nodesObject) {
            if (!node.startsWith(REDIS_PROTOCOL_PREFIX) && !node.startsWith(REDISS_PROTOCOL_PREFIX)) {
                nodes.add(REDIS_PROTOCOL_PREFIX + node);
            } else {
                nodes.add(node);
            }
        }
        return nodes.toArray(new String[nodes.size()]);
    }

}

```
# 4. RedisTemplate配置类
> springboot-data-redis 注册多个RedisTemplate

```java
@Configuration
public class MultiRedisConfig {

    private final MultiRedisProperties multiRedisProperties;

    public MultiRedisConfig(MultiRedisProperties multiRedisProperties) {
        this.multiRedisProperties = multiRedisProperties;
    }

    /**
     * 本地集群
     */
    @RefreshScope
    @Bean("redisTemplate")
    @SuppressWarnings("all")
    public RedisTemplate<String, Object> redisTemplate(MultiRedisProperties multiRedisProperties) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置redis连接工厂
        List<String> nodes = multiRedisProperties.getCluster().getNodes();
        template.setConnectionFactory(redisConnectionFactory(nodes));
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 异地集群
     */
    @RefreshScope
    @Bean("redisTemplate2")
    @SuppressWarnings("all")
    @ConditionalOnProperty(prefix = "spring.redis.cluster2", name = "active", havingValue = "true")
    public RedisTemplate<String, Object> redisTemplate2(MultiRedisProperties multiRedisProperties) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置redis连接工厂
        List<String> nodes = multiRedisProperties.getCluster2().getNodes();
        template.setConnectionFactory(redisConnectionFactory(nodes));
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    public RedisConnectionFactory redisConnectionFactory(List<String> clusterNodes) {
        List<RedisNode> redisNodeList = new ArrayList<>();
        for (String nodes : clusterNodes) {
            String[] node = nodes.split(":");
            RedisNode redisNode = new RedisNode(node[0], Convert.toInt(node[1]));
            redisNodeList.add(redisNode);
        }
        RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration();
        clusterConfiguration.setClusterNodes(redisNodeList);
        clusterConfiguration.setPassword(RedisPassword.of(multiRedisProperties.getPassword()));

        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(clusterConfiguration, poolConfig(multiRedisProperties));
        jedisConnectionFactory.afterPropertiesSet();
        return jedisConnectionFactory;
    }

    /**
     * jedis连接池
     */
    public JedisPoolConfig poolConfig(MultiRedisProperties multiRedisProperties) {
        MultiRedisProperties.Pool pool = multiRedisProperties.getJedis().getPool();
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(pool.getMaxIdle());
        poolConfig.setMaxTotal(pool.getMaxActive());
        poolConfig.setMaxWaitMillis(pool.getMaxWait());
        poolConfig.setMinIdle(pool.getMinIdle());
        poolConfig.setTimeBetweenEvictionRunsMillis(pool.getTimeBetweenEvictionRuns());
        return poolConfig;
    }

    protected final RedisClusterConfiguration getClusterConfiguration() {
        MultiRedisProperties.Cluster cluster = multiRedisProperties.getCluster();
        RedisClusterConfiguration config = new RedisClusterConfiguration(cluster.getNodes());
        if (cluster.getMaxRedirects() != null) {
            config.setMaxRedirects(cluster.getMaxRedirects());
        }
        if (this.multiRedisProperties.getPassword() != null) {
            config.setPassword(RedisPassword.of(this.multiRedisProperties.getPassword()));
        }
        return config;
    }

}
```
