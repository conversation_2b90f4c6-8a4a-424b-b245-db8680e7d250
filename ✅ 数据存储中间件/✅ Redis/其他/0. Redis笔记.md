# 1. Redis是什么

```
一种支持Key-Value等多种数据结构的存储系统
c语言开发
数据存在内存中
可缓存数据, 分布式锁, 发布订阅, 数据结构丰富, 支持事务, 持久化(容灾恢复), 集群高可用, lua脚本...
单线程, 多路IO复用模型
```
## 单线程的redis为什么这么快?

- **纯内存**操作
- 单线程操作，避免了频繁的上下文切换
- 采用了**非阻塞I/O多路复用**机制 
# 2. 缓存存在的问题
## 2.1 缓存穿透

```
请求一个不存在的key, redis中没有, 直接请求数据库, 用户量大的时候, 可能导致数据库崩溃
- 将不存在的key存下, 并设置过期时间, 给用户返回空
 -- 占用存, redis和数据库可能存在数据不一致的问题
- 布隆过滤器bitmap, 判断存不存在
    -- 将全部可能的结果 放到布隆过滤器中, 用户请求命中就返回, 没有命中直接丢弃, 不会直接去请求数据库
- 参数校验, 不合法的请求参数直接返回错误
```


## 2.2 缓存雪崩

```
大量key同时失效, 请求全部达到数据库层, 可能导致数据库崩溃, 后续请求全部挂掉, 像是雪崩
- key的失效时间设置增加一个随机数, 不同时失效
- 增加redis集群, 保证redis高可用
- 缓存预热
- 布隆过滤器
- 限流
```


## 2.3 缓存击穿

```
某个热点key失效, 大量请求直接到达数据库, 可能导致数据库崩溃, 像是直接凿开一个洞
- 热点key不过期
- 增加redis集群, 保证redis高可用
-
```

## 2.4 缓存预热

```java
把热点数据提前从数据库同步到redis, 用户请求时, 不必请求数据库
- 定时刷新
- 留接口, 页面按钮
- 程序启动时 @PostConstruct
```
## 2.5 缓存与数据库的数据一致性问题

# 3. 键key的过期策略
### 如何判断数据是否过期的呢？

```basic
- 有一个过期字典(hash表)
- key为真实数据的key, value为unix时间戳(long long类型)
- 定期删除 + 惰性删除 + 淘汰策略
```
## 3.1 定期删除

```basic
随机抽取一些key, 过期则删除, 没有检测所有key
```

## 3.2 惰性删除

```basic
定期删除 不会删除全部过期的key
所以需要惰性删除, 在使用key的时候, 检测下key是否过期, 过期则删除
```
# 4. 淘汰策略
 - 内存不足时

```java
若key一直未使用, 则需要淘汰策略
 设置最大可使用内存     
    maxmemory  0    #占用物理内存的比例，默认值为0，表示不限制。生产环境中根据需求设定，通常设置在50%以上。
 每次随机选取待删除数据的个数
    maxmemory-samples  5  #选取数据时并不会全库扫描，导致严重的性能消耗，降低读写性能。因此采用随机获取数据的方式作为待检测删除数据选择删除策略
    maxmemory-policy     #达到最大内存后，对被挑选出来的数据进行删除的方式
-- volatile-lru：从已设置过期时间的数据集（server.db[i].expires）中挑选最近最少使用的数据淘汰
-- volatile-ttl：从已设置过期时间的数据集（server.db[i].expires）中挑选将要过期的数据淘汰
-- volatile-random：从已设置过期时间的数据集（server.db[i].expires）中任意选择数据淘汰
-- allkeys-lru：从数据集（server.db[i].dict）中挑选最近最少使用的数据淘汰, 针对全部key
-- allkeys-random：从数据集（server.db[i].dict）中任意选择数据淘汰
-- no-eviction（驱逐）：禁止驱逐数据，可以保证数据不会丢失, 新写入操作会报错 (默认策略)
    
---  volatile-xxx 策略只会针对带过期时间的 key 进行淘汰，allkeys-xxx 策略会对所有的key 进行淘汰。
```

# 5. 数据结构
## string

```basic
string
- 动态字符串, 预分配冗余空间的方式来减少内存的频繁分配, 分配空间大于实际字符串长度
- 预分配的策略是动态调整的, 扩容, 小于1M, 加倍分配空间, 大于1M, 每次只加1M空间
- 字符串最大长度为 512M
- set,get,strlen,exists,dect,incr,setex
场景:
- 计数
- 存字符串, json
```

## list

```
list --- LinkedArrayList
- 双向链表
- rpush,lpop,lpush,rpop,lrange、llen
场景:
- 发布订阅, 消息队列
```

## hash

```
hash --- HashMap
- 数组+链表
- 存结构化数据, kv
- key, item, value  可以单独修改某个value
- hset,hmset,hexists,hget,hgetall,hkeys,hvals
场景: 
- 用户信息..
- 对象数据
```

## set 无序

```
set --- HashSet
- 无序集合, 数据不重复
- sadd,spop,smembers,sismember,scard,sinterstore,sunion
- 可以基于 set 轻易实现交集、并集、差集的操作
场景:
- 共同关注、共同粉丝、共同喜好等功能, 交集, 两个set
```

## zset  排序sorted set

```
zset
- 相比set, 增加了一个权重参数 score
  -- zadd myZset 3.0 value1 # 添加元素到 sorted set 中 3.0 为权重
  -- 可以根据score排序, 也可以根据score范围查找value列表
- zadd,zcard,zscore,zrange,zrevrange,zrem 
场景:
- 排行榜
```
## 布隆过滤器

```java
底层存储为一个 bitmap
当来一个数据时，经过 n 个 hash 函数，得到 n 个数值
将 hash 得到的 n 个数值，映射到 bitmap，标记对应的位置为 1
    
    如果来一个数据，通过 hash 计算之后，若这个 n 个值，对应的 bitmap 都是 1，那么表示这个数据可能存在；如果有一个不为 1，则表示这个数据一定不存在
    
 应用场景: 
缓存穿透，爬虫 url 去重
```

### 代码实现

```java
 	/**
     * 根据给定的布隆过滤器添加值
     */
    public <T> void addByBloomFilter(BloomFilterHelper<T> bloomFilterHelper, String key, T value) {
        Preconditions.checkArgument(bloomFilterHelper != null, "bloomFilterHelper不能为空");
        int[] offset = bloomFilterHelper.murmurHashOffset(value);
        for (int i : offset) {
            redisTemplate.opsForValue().setBit(key, i, true);
        }
    }

    /**
         * 根据给定的布隆过滤器判断值是否存在
         */
    public <T> boolean includeByBloomFilter(BloomFilterHelper<T> bloomFilterHelper, String key, T value) {
        Preconditions.checkArgument(bloomFilterHelper != null, "bloomFilterHelper不能为空");
        int[] offset = bloomFilterHelper.murmurHashOffset(value);
        for (int i : offset) {
            if (!redisTemplate.opsForValue().getBit(key, i)) {
                return false;
            }
        }

        return true;
    }


```

```java
    @Test
    void bloom() {

        for (int i = 0; i < 1000; i++) {
            redisUtil.addByBloomFilter(orderBloomFilterHelper, "order2", String.valueOf(i));

        }

    }

    @Test
    void existBloom() {
        boolean order = redisUtil.includeByBloomFilter(orderBloomFilterHelper, "order2", "1000");
        System.out.println(order);

    }
```

## bitmap位图

```java
基于 Redis 的字符串类型实现的, 一个比特位只能表示 0 和 1 两种状态

SETBIT
GETBIT
BITCOUNT
BITFIELD
BITPOS
BLPOP

使用场景:
签到
实现布隆过滤器
统计用户是否在线, 然后用户 id 为 offset，如果在线就设置为 1，不在线就设置为 0


```

```java
例如u:sign:1000:201902表示ID=1000的用户在2019年2月的签到记录。

# 用户2月17号签到
SETBIT u:sign:1000:201902 16 1 # 偏移量是从0开始，所以要把17减1

# 检查2月17号是否签到
GETBIT u:sign:1000:201902 16 # 偏移量是从0开始，所以要把17减1

# 统计2月份的签到次数
BITCOUNT u:sign:1000:201902 # 返回当月签到次数

# 获取2月份前28天的签到数据
BITFIELD u:sign:1000:201902 get u28 0

# 获取2月份首次签到的日期
BITPOS u:sign:1000:201902 0 # 返回的首次签到的偏移量，加上1即为当月的某一天
```
[https://blog.csdn.net/qq1311256696/article/details/109473669](https://blog.csdn.net/qq1311256696/article/details/109473669)

![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611975270202-67aa54b3-3bd5-499c-8fd3-55e6b45c35b1.png#align=left&display=inline&height=121&originHeight=121&originWidth=846&size=20562&status=done&style=none&width=846)
![image.png](https://cdn.nlark.com/yuque/0/2021/png/684952/1611975303700-3c2f5dfc-1c99-459f-98a6-2f9294896b80.png#align=left&display=inline&height=422&originHeight=422&originWidth=824&size=21697&status=done&style=none&width=824)

- offset从0开始 , offset = 14即 14+1 位为1
- 

# 6. 持久化机制 RDB, AOF

```
RDB
 - 将内存的数据存到本地硬盘, dump.rdb, 快照
 - 配置:
   -- save 900 1  #在900秒(15分钟)之后，如果至少有1个key发生变化，Redis就会自动触发BGSAVE命令创建快照。   
   -- save 300 10  #在300秒(5分钟)之后，如果至少有10个key发生变化，Redis就会自动触发BGSAVE命令创建快照。
   -- save 60 10000  #在60秒(1分钟)之后，如果至少有10000个key发生变化，Redis就会自动触发BGSAVE命令创建快照。
 
AOF
 - 将写命令追加到文件里, redis重启时, 重新执行写命令
 - 配置:
  -- 开启: appendonly yes,  文件名: appendonly.aof
  -- appendfsync always    #每次有数据修改发生时都会写入AOF文件,这样会严重降低Redis的速度
  -- appendfsync everysec  #每秒钟同步一次，显示地将多个写命令同步到硬盘  ***
  -- appendfsync no        #让操作系统决定何时进行同步
 
-- RDB快速恢复数据, AOF补充最近数据
```

# 7. 分布式锁

```
setnx key value
```

# 8. 高可用, 集群
## 8.1 主从
### 主节点配置

```basic
**************
port 6379
daemonize yes    // 后台启动
masterauth linewell-123    // 主从认证密码
requirepass linewell-123     // redis密码
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875800-7a1ba055-010c-40ec-8796-d42b279a9479.png#align=left&display=inline&height=294&originHeight=294&originWidth=574&size=0&status=done&style=none&width=574)
### 从节点配置

```basic
**************/147
port 6379
daemonize yes
requirepass linewell-123
masterauth linewell-123    // 主节点认证密码
slaveof ************** 6379    // 主节点ip 端口
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875844-a84a8888-54ee-454b-a3a7-7ed8a9162611.png#align=left&display=inline&height=390&originHeight=390&originWidth=474&size=0&status=done&style=none&width=474)

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875826-5bc710c8-d082-4a0e-af84-e47366037727.png#align=left&display=inline&height=407&originHeight=407&originWidth=417&size=0&status=done&style=none&width=417)
### 原理

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875893-1303dc8c-a39e-4e52-97f4-4d28d319a29f.png#align=left&display=inline&height=422&originHeight=422&originWidth=781&size=0&status=done&style=none&width=781)
### 总结

```
主节点挂掉之后, 需要手动启动主节点, 
一定时间内, 数据可能不一致
```

## 8.2 哨兵模式
### 好处

```
监控
提醒
故障自动转移
```

### 配置

```basic
三台节点配置一样
port 26379  // 默认端口
daemonize yes    
sentinel monitor mymaster ************** 6379 1    // 主节点地址, 这个主服务器判断为下线失效至少需要1个Sentinel同意
sentinel auth-pass mymaster linewell-123    // 主节点密码
```

### 原理

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875795-d3985800-3eab-4924-b6d4-9b5b45908165.png#align=left&display=inline&height=526&originHeight=526&originWidth=775&size=0&status=done&style=none&width=775)

- 若超过**30秒**内没有收到回复, 就对master主观下线, 并询问其他Sentinel节点, 是否可以主观下线,  数据超过配置数则下线
- sentinel monitor mymaster ************** 6379 1
- 默认30秒可配置 , sentinel down-after-milliseconds mymaster 30000
### springboot使用sentinel

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875800-931494c6-6985-49c8-b097-76145383786a.png#align=left&display=inline&height=375&originHeight=375&originWidth=437&size=0&status=done&style=none&width=437)
## 8.3 cluster集群

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875811-a91478c1-d371-4666-b3f5-6816e1da9eb9.png#align=left&display=inline&height=597&originHeight=597&originWidth=783&size=0&status=done&style=none&width=783)

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611405875801-dfc59a66-556d-4ee6-8b08-4047de971ccd.png#align=left&display=inline&height=504&originHeight=504&originWidth=781&size=0&status=done&style=none&width=781)

# 9. 性能优化
  [redis性能优化](https://www.notion.so/Redis-fb6baebc1c344eb1b62c5cd5d744296e)




