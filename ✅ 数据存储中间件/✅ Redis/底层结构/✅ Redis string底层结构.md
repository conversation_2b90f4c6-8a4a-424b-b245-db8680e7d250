# string命令
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681742472244-fb5def3e-e7b5-4be0-bd7c-2f2df13fa734.png#averageHue=%23eef0f1&clientId=uc099a069-6605-4&from=paste&height=322&id=u875ed018&originHeight=644&originWidth=918&originalType=binary&ratio=2&rotation=0&showTitle=false&size=116603&status=done&style=none&taskId=u0378277b-17dd-4de0-90ca-567f40d3360&title=&width=459)
# 底层
> redis的string类型底层使用简单动态字符串（SDS）作为数据结构。

> **采用预分配冗余空间的方式来减少内存的频繁分配**。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681742630669-1ba70c0d-ac47-4619-91f2-4d0075bcc6e6.png#averageHue=%23cadac6&clientId=uc099a069-6605-4&from=paste&height=109&id=u06cdcf12&originHeight=217&originWidth=803&originalType=binary&ratio=2&rotation=0&showTitle=false&size=20270&status=done&style=none&taskId=u4d9d1196-4bf6-4ea0-be64-f4aff88f4f3&title=&width=401.5)
## 机制

1. capacity一般大于实际长度len
2. 当字符串长度小于1M时，扩容是加倍先有得空间，1+1=2M
3. 超过1M，扩容时一次只会多扩1M的空间。
4. 字符串最大长度为512M
5. 字符串是有多个字节组成，每个字节又是由8个bit组成，如此便可以将一个字符串看成很多bit的组成，这就是位图bitmap。
## c
> SDS Simple Dynamic String

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681742890863-ed2f41aa-3755-4edb-ac85-da06b54e852e.png#averageHue=%23ccdbc9&clientId=uc099a069-6605-4&from=paste&height=459&id=ub9c4d6d8&originHeight=918&originWidth=1464&originalType=binary&ratio=2&rotation=0&showTitle=false&size=121791&status=done&style=none&taskId=u3124b33a-879b-4318-8b2f-05852ac37b4&title=&width=732)
