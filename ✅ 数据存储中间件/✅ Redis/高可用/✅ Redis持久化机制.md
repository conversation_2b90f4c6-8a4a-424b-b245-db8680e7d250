> 默认没有开启持久化。

# RDB
> Redis DataBase 快照，就是把当前进程的数据生成一个快照文件保存在磁盘上。

## 触发方式
### 手动触发

- **save命令**
   - **阻塞redis服务器，直到RDB完成。**
- **bgsave命令**
   - **Redis进程执行fock操作创建子进程，RDB持久化由子进程负责，阻塞只发生在fork阶段。**
### 自动触发

- save m n 自动触发bgsave
- 主从复制，主节点bgsave生成快照，发送到从节点
- 执行debug reload命令重新加载redis时
- 执行shutdown命令时。
### 配置
```properties
#不开启rdb
save ""

#在m秒内有n次修改时，自动触发bgsave生成rdb文件
# save m n
# 900秒有1个key发生改变
save 900 1
save 300 10
save 60 10000

# 文件名称
dbfilename dump.rdb

# 文件保存路径
dir /home/<USER>/app/redis/data/

# 如果持久化出错，主进程是否停止写入
stop-writes-on-bgsave-error no

# 是否压缩，将在字符串类型的数据被快照到磁盘文件时，启用LZF压缩算法
rdbcompression yes

# 导入时是否检查
rdbchecksum yes
```
# AOF
> redis先执行命令，执行成功，再写入日志。文本形式。写后日志。
> 追加文件末尾

## **为什么采用写后日志**？
> - 避免额外的检查开销，可能有错误的命令，恢复数据时，不然还要检查命令正确性。
> 
写后日志，可以保证日志都是正确的命令。
> - 不会阻塞当前的写操作。

## 配置
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679319481339-e57d0d3f-c58f-486d-bd25-50723f5aa443.png#averageHue=%23efdd8f&clientId=u2ccee9b2-beff-4&from=paste&height=209&id=uffff0b10&originHeight=418&originWidth=1534&originalType=binary&ratio=2&rotation=0&showTitle=false&size=395154&status=done&style=none&taskId=ua8ba23ca-5dbf-401c-90bd-0f7acd7ff9b&title=&width=767)
```properties
# 开启AOF
appendonly no

appendfilename "appendonly.aof"

dir ./

# 同步策略 
# appendfsync always
appendfsync everysec
# appendfsync no

# aof重写期间是否同步
no-appendfsync-on-rewrite no

# 重写触发配置
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 加载aof出错如何处理
aof-load-truncated yes

# 文件重写策略
aof-rewrite-incremental-fsync yes
```
## AOF重写
> 重写由bgrewriteaof子线程执行

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1679319648091-291c808d-e858-4250-87b7-f5a15ecd4a6d.png#averageHue=%23f3f3eb&clientId=u2ccee9b2-beff-4&from=paste&height=212&id=u5829174d&originHeight=424&originWidth=1588&originalType=binary&ratio=2&rotation=0&showTitle=false&size=241533&status=done&style=none&taskId=u9fc1335d-b913-4425-a8b0-f904e466ecb&title=&width=794)

# 混合
