# Redis6.2.6部署

## 安装

```shell
mkdir -p /home/<USER>
cd /home/<USER>
yum -y install gcc-c++ ruby ruby-devel rubygems rpm-build
wget http://58.220.49.186:9999/redis/redis-6.2.6.tar.gz
tar -xzf redis-6.2.6.tar.gz 
mv redis-6.2.6 redis
cd redis
mkdir data logs pid 
make && make install
mkdir 9100
 
```

## 配置文件

```shell
sudo tee  /home/<USER>/redis/9100/redis.conf <<-'EOF'
io-threads-do-reads yes
io-threads 5
daemonize yes
tcp-backlog 511
timeout 0
tcp-keepalive 60
loglevel notice
databases 16
dir /home/<USER>/redis/data
stop-writes-on-bgsave-error no
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 64000000
repl-backlog-ttl 7200
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-entries 512
list-max-ziplist-value 64
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
activerehashing yes
client-output-buffer-limit normal 256mb 128mb 60
client-output-buffer-limit replica 512mb 256mb 60
hz 10
save ""
port 9100
maxmemory 0
maxmemory-policy volatile-lfu
appendonly no
appendfsync everysec
appendfilename "appendonly-9100.aof"
dbfilename dump-9100.rdb
aof-rewrite-incremental-fsync yes
no-appendfsync-on-rewrite yes
auto-aof-rewrite-min-size 62500kb
auto-aof-rewrite-percentage 89
maxclients 10000
protected-mode yes
bind 0.0.0.0
list-max-ziplist-size -2
list-compress-depth 0
always-show-logo yes
pidfile "/home/<USER>/redis/pid/redis-9100.pid"
logfile "/home/<USER>/redis/logs/redis-9100.log"
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
aof-use-rdb-preamble no
activedefrag yes
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-ignore-bytes 300mb
active-defrag-cycle-min 10
active-defrag-cycle-max 30
active-defrag-max-scan-fields 1000
replica-serve-stale-data yes
replica-priority 100
replica-read-only yes
replica-lazy-flush yes
client-output-buffer-limit pubsub 32mb 8mb 60
replica-ignore-maxmemory yes
stream-node-max-bytes 4kb
stream-node-max-entries 100
dynamic-hz yes
rdb-save-incremental-fsync yes
repl-ping-replica-period 10
latency-monitor-threshold 30
cluster-enabled yes
cluster-node-timeout 2000
cluster-migration-barrier 1
cluster-config-file nodes-9100.conf
cluster-require-full-coverage no
cluster-replica-validity-factor 10
requirepass Cqt@redis2021
masterauth Cqt@redis2021
cluster-allow-reads-when-down yes
cluster-slave-validity-factor 0
EOF
```

## 启动

```shell
./src/redis-server /home/<USER>/redis/9100/redis.conf
```

## 创建集群

```shell
src/redis-cli -a Cqt@redis2021 --cluster create \
10.255.26.23:9100 \
10.255.26.24:9100 \
10.255.26.25:9100 \
10.255.26.26:9100 \
10.255.26.27:9100 \
10.255.26.28:9100 \
 --cluster-replicas 1
```
