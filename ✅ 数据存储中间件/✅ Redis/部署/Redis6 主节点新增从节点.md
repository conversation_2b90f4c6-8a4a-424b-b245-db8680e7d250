# 安装好单节点并启动

```shell
/home/<USER>/redis/src/redis-server /home/<USER>/redis/9100/redis.conf
```

# 删除从节点

```shell
4a0cc863b4dc5ad3860a1f7e57e596f66c3b54d8 ***********:9100@19100 master,fail - 1737096884988 0 0 connected
889d5e63a7e5bc4f16b409180e908dd964a36a19 10.255.1.20:9100@19100 slave,fail - 1737097028546 0 0 connected
941a8b676349a7f24064851c6beb533b30d354d9 ***********:9100@19100 myself,master - 0 1737425395000 10 connected 5461-10922
77f62fdd85076b1dbf392111511b954cfbd96c93 ***********:9100@19100 master - 0 1737425395393 9 connected 10923-16383
7a24bd498133579e91a020fff7a08883bb19a756 ***********:9100@19100 slave,fail - 1737096891904 0 0 connected
94c51d3234353cb4b93cb40ed0fb06b618f32d42 ***********:9100@19100 master - 0 1737425396001 11 connected 0-5460
```

```shell
./redis-cli --cluster del-node <集群任意节点IP>:<端口> <要移除的从节点ID>
./redis-cli --cluster del-node -a Cqt@redis2021 ***********:9100 889d5e63a7e5bc4f16b409180e908dd964a36a19


./redis-cli -h *********** -p 9100 -a Cqt@redis2021 
CLUSTER FORGET 4a0cc863b4dc5ad3860a1f7e57e596f66c3b54d8
CLUSTER FORGET 889d5e63a7e5bc4f16b409180e908dd964a36a19
CLUSTER FORGET 7a24bd498133579e91a020fff7a08883bb19a756


```


# 添加节点

```shell
./redis-cli --cluster add-node 新节点ip:端口 集群节点ip:端口 -a [password]
./redis-cli --cluster add-node ***********:9100 ***********:9100 -a 123456

```

# 设置为从节点, 登录从节点

```shell
./redis-cli -h *********** -p 9100  -a Cqt@redis2021 cluster replicate [master节点id]

./redis-cli -h *********** -p 9100  -a Cqt@redis2021 cluster replicate 94c51d3234353cb4b93cb40ed0fb06b618f32d42
```

```
 cluster nodes
4a0cc863b4dc5ad3860a1f7e57e596f66c3b54d8 ***********:9100@19100 slave 94c51d3234353cb4b93cb40ed0fb06b618f32d42 0 1730256412377 1 connected
941a8b676349a7f24064851c6beb533b30d354d9 ***********:9100@19100 master - 0 1730256412072 2 connected 5461-10922
94c51d3234353cb4b93cb40ed0fb06b618f32d42 ***********:9100@19100 myself,master - 0 1730256411000 1 connected 0-5460
77f62fdd85076b1dbf392111511b954cfbd96c93 ***********:9100@19100 master - 0 1730256411767 3 connected 10923-16383
```

# 主从切换

杀掉一个master 1.19 , slave 1.11 升为master

![image-20241030105647912](images/image-20241030105647912.png)

# 问题

## redis集群 master节点全部挂断, 从不会升主

![image-20241030134919119](images/image-20241030134919119.png)