# string
> 字符串

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743005728-f21da7b9-c76f-44b1-9289-7eb748c0cf44.png#averageHue=%23eeeff1&clientId=u3d41c7f7-6850-4&from=paste&height=322&id=ufa5adfc0&originHeight=644&originWidth=906&originalType=binary&ratio=2&rotation=0&showTitle=false&size=116235&status=done&style=none&taskId=ud0bc6d3b-d887-49bd-8a50-a5a2d4a1172&title=&width=453)
# set
> 无序集合。元素不能重复。
> Redis 中集合是通过哈希表实现的，所以添加，删除，查找的复杂度都是 O(1)。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743139457-763916b0-414d-44f3-9908-9a2c29067e50.png#averageHue=%23f0f1f2&clientId=u3d41c7f7-6850-4&from=paste&height=236&id=uc6894ea9&originHeight=472&originWidth=1192&originalType=binary&ratio=2&rotation=0&showTitle=false&size=86229&status=done&style=none&taskId=u36e2f9fe-9e26-4f46-8f1e-d2d53a45711&title=&width=596)
# zset
> 有序集合，元素不能重复。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743260025-1491ebf6-53b3-4e6a-8644-7c152c71f30b.png#averageHue=%23f9f9f9&clientId=u3d41c7f7-6850-4&from=paste&height=591&id=u0764ffe3&originHeight=1182&originWidth=1312&originalType=binary&ratio=2&rotation=0&showTitle=false&size=366647&status=done&style=none&taskId=u7264affa-2b95-4aba-bdc6-da03b57800e&title=&width=656)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743270138-229c01b1-0503-43d3-b00c-31a104de5c4a.png#averageHue=%23f0f1f3&clientId=u3d41c7f7-6850-4&from=paste&height=234&id=uad74677c&originHeight=468&originWidth=1198&originalType=binary&ratio=2&rotation=0&showTitle=false&size=105405&status=done&style=none&taskId=u67d23790-fe4e-429a-a9d0-882bd57899b&title=&width=599)
# list
> 双向链表

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743067122-d9873ee6-23cb-40ce-a8f4-56367e2e2e2e.png#averageHue=%23f1f2f3&clientId=u3d41c7f7-6850-4&from=paste&height=534&id=ubd519d59&originHeight=1068&originWidth=1206&originalType=binary&ratio=2&rotation=0&showTitle=false&size=203662&status=done&style=none&taskId=u1680e7b0-219f-49ac-becb-45a762c2713&title=&width=603)
# hash
> Redis hash 是一个 string 类型的 field（字段） 和 value（值） 的映射表，hash 特别适合用于存储对象。

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743200965-5422b361-2314-4962-b287-ff12f3564795.png#averageHue=%23f0f1f3&clientId=u3d41c7f7-6850-4&from=paste&height=236&id=u02427326&originHeight=472&originWidth=1206&originalType=binary&ratio=2&rotation=0&showTitle=false&size=87930&status=done&style=none&taskId=u88250ca0-dfee-47f7-8b96-5c0da347681&title=&width=603)
# HyperLogLogs
> 基数统计。
> IP数统计，UV等

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743413193-25a734c5-b6c4-4d70-bcec-a0fa0887bd90.png#averageHue=%23292d35&clientId=u3d41c7f7-6850-4&from=paste&height=315&id=u58ff2350&originHeight=630&originWidth=1200&originalType=binary&ratio=2&rotation=0&showTitle=false&size=121542&status=done&style=none&taskId=u29a8a6a3-2db0-4d10-8204-23c8072616d&title=&width=600)
# bitmap
> Bitmap 即位图数据结构，都是操作二进制位来进行记录，只有0 和 1 两个状态。
> 打卡信息， 是否签到

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743510176-696b88f3-fdbf-42e4-b6ae-0dd5a4a7f9a3.png#averageHue=%23292d35&clientId=ucf2110e4-acdf-4&from=paste&height=665&id=u6c95b1cf&originHeight=1330&originWidth=1188&originalType=binary&ratio=2&rotation=0&showTitle=false&size=193241&status=done&style=none&taskId=u911ef690-26c3-45c3-98dc-3dd95f663c8&title=&width=594)
# geo
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681743550165-039b517b-dc35-48cc-a2b8-015b5f7637ec.png#averageHue=%23fefefe&clientId=ucf2110e4-acdf-4&from=paste&height=143&id=uab530848&originHeight=286&originWidth=326&originalType=binary&ratio=2&rotation=0&showTitle=false&size=29364&status=done&style=none&taskId=ua48bf698-2b50-497f-b703-845d1edb7ca&title=&width=163)
