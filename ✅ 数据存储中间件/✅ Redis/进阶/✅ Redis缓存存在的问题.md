# 缓存击穿
> 缓存key过期了，数据存在数据库中，所有请求都打到DB上。

解决：

- 加锁更新：查询DB数据再更新到缓存中，这步骤加锁。获取到锁之后再查次缓存，可能上一个线程已经把数据更新到缓存了。
- 热点数据缓存永不失效。
# 缓存穿透
> key缓存中不存在，数据库中也不存在，所有请求直接到DB，就好像缓存不存在一样，直接穿透到DB。

解决：

- 缓存和db都查不到，返回null，并设置过期时间，防止后续这个key可能有值而查不到数据。
- 布隆过滤器
# 缓存雪崩
> 某一时刻，大量key同时失效和服务器宕机重启导致缓存丢失，导致请求都打到DB上。

解决：

- 热点数据缓存永不失效。
- 集群部署，提升缓存可用性。
- 熔断降级。
# 缓存预热
> 提前把数据加载到缓存中
> - 项目启动加载
> - 页面按钮
> - 定时任务

# 内存不足时会怎样
> 当redis内存不足时，会触发内存淘汰策略，具体看配了什么策略。
> - redis.conf   maxmemory参数 最大内存，
> - set maxmemory 234  动态修改内存上限

# 缓存和数据库一致性问题
## cache aside模式
> 先更新数据库，再删除缓存

# 本地缓存和分布式缓存一致性问题

- 发布订阅
- 消息队列
- 过期时间

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681564277865-1781508d-7f0b-45de-8a63-d3f8e8b10626.png#averageHue=%23fdf8f7&clientId=u6ed858a5-4bf2-4&from=paste&height=646&id=u3b292d4d&originHeight=1292&originWidth=1686&originalType=binary&ratio=2&rotation=0&showTitle=false&size=264172&status=done&style=none&taskId=uce3b6abf-11c7-4e6e-80b5-e895bc6ed58&title=&width=843)
# 热点key问题

- 先监控热点key，使用monitor命令统计热点key
- 再把热点key打散到不同服务器。
- 加本地缓存。
# 大key问题
## 多大

- value超过10kb
- list，set等集合超过1万条
## 如何找到大key

- bigkeys命令: 使⽤用bigkeys命令以遍历的⽅方式分析Redis实例例中的所有Key，并返回整体统计信息与每个数据 类型中Top1的⼤大Key
- redis-rdb-tools: redis-rdb-tools是由Python写的⽤用来分析Redis的rdb快照⽂文件⽤用的⼯工具，它可以把rdb快照 ⽂文件⽣生成json⽂文件或者⽣生成报表⽤用来分析Redis的使⽤用详情。
## 如何处理

- 删除 unlink命令

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681564700154-ad0e967e-14c7-4c27-a92f-19b0460d725f.png#averageHue=%23fef7f2&clientId=u6ed858a5-4bf2-4&from=paste&height=554&id=u2e1eccbf&originHeight=1108&originWidth=1676&originalType=binary&ratio=2&rotation=0&showTitle=false&size=255291&status=done&style=none&taskId=u4eba0d50-4149-42aa-997c-11d0d6e43d3&title=&width=838)
# redis阻塞了
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681564828269-693bc1c7-18fc-4c1b-8914-82e62b0c2692.png#averageHue=%23fefaf9&clientId=u6ed858a5-4bf2-4&from=paste&height=518&id=ub13103d1&originHeight=1036&originWidth=1776&originalType=binary&ratio=2&rotation=0&showTitle=false&size=224528&status=done&style=none&taskId=u5b665108-f2a0-4ed7-8930-d25b3b192c4&title=&width=888)
