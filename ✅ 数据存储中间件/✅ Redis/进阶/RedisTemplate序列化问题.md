# RedisTemplate序列化问题

![image-20230723212416945](images/image-20230723212416945.png)

## value序列化方式为string

```java
    @Bean("redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        StringRedisSerializer stringValueSerializer = new StringRedisSerializer();
        template.setStringSerializer(stringValueSerializer);
        template.setValueSerializer(stringValueSerializer);
        template.setKeySerializer(stringValueSerializer);
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
```

### set操作

> set 前对象转json
>
> 写入redis里也是json字符串, 写入的对象必须是String

```java
 redisTemplate.opsForValue().set("stringKey", JSONUtil.toJsonStr(redisData));
```



![image-20230723213923034](images/image-20230723213923034.png)



> 写入为对象时报错

```java
 redisTemplate.opsForValue().set("stringKey", redisData);
```

![image-20230723214132175](images/image-20230723214132175.png)

## value序列化方式为jackson

```java
    @Bean("redisTemplate2")
    public RedisTemplate<String, Object> redisTemplate2(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        StringRedisSerializer stringValueSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringValueSerializer);
        template.setHashKeySerializer(stringValueSerializer);

        Jackson2JsonRedisSerializer<?> valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        valueSerializer.setObjectMapper(objectMapper);
        template.setValueSerializer(valueSerializer);
        template.setHashValueSerializer(valueSerializer);
        return template;
    }
```

### set操作

#### 1. 写入String

```java
        // Jackson序列化 写入是String
        RedisData redisData = new RedisData(IdUtil.fastSimpleUUID(), RandomUtil.randomInt());
        redisTemplate2.opsForValue().set("stringKey", JSONUtil.toJsonStr(redisData));
		// {"name":"9000b40e879d459e9b22ded6fd1ab0bf","age":909171619}
    	Object o = redisTemplate2.opsForValue().get("stringKey");
```

> Jackson序列化 写入是String, redisTemplate内部又进行一次序列化, 写入redis的数据前后加了引号
>
> 读取的也是string, 会把引号去掉

![image-20230723214443067](images/image-20230723214443067.png)

反序列化测试

> 读取到数据, 再使用jackson反序列化就可以获取正常的json字符串

```java
    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // "{\"name\":\"f7b29acedc1444c79a865f026964b56c\",\"age\":-718740930}"
        Object o = objectMapper.readValue("\"{\\\"name\\\":\\\"f7b29acedc1444c79a865f026964b56c\\\",\\\"age\\\":-718740930}\"", Object.class);
        // {"name":"f7b29acedc1444c79a865f026964b56c","age":-718740930}
        System.out.println(o);
    }
```

序列化测试

> 字符串再经过jackson序列化, 就会成这样!

```java
    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // {"name":"f7b29acedc1444c79a865f026964b56c","age":-718740930}
        String s = objectMapper.writeValueAsString("{\"name\":\"f7b29acedc1444c79a865f026964b56c\",\"age\":-718740930}");
        // "{\"name\":\"f7b29acedc1444c79a865f026964b56c\",\"age\":-718740930}"
        System.out.println(s);
    }
```



#### 2. 写入对象

```java
        // Jackson序列化 写入是Object
        RedisData redisData = new RedisData(IdUtil.fastSimpleUUID(), RandomUtil.randomInt());
        redisTemplate2.opsForValue().set("objectKey", redisData);
		// {name=7a3818d476224bde8d0e2ec787177f63, age=1691483443}
		Object o = redisTemplate2.opsForValue().get("objectKey");
```

> 写入redis是json
>
> 读取的是对象 LinkedHashMap

![image-20230723214942986](images/image-20230723214942986.png)

![image-20230723214922334](images/image-20230723214922334.png)

### 正确用法

> jackson序列化 写入数据需要写入对象, 序列化有redisTemplate自己实现, 用户不需要手动序列化!

## 什么时候写入redis的json会带上全限定类名?

> objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);

```Java
    @Bean("redisTemplate2")
    public RedisTemplate<String, Object> redisTemplate2(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        StringRedisSerializer stringValueSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringValueSerializer);
        template.setHashKeySerializer(stringValueSerializer);

        Jackson2JsonRedisSerializer<?> valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 开启这行代码, 写入redis的json会带上对象全限定类名
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        valueSerializer.setObjectMapper(objectMapper);
        template.setValueSerializer(valueSerializer);
        template.setHashValueSerializer(valueSerializer);
        return template;
    }
```

![image-20230723220151790](images/image-20230723220151790.png)

> 读取的也是响应对象

![image-20230723220233142](images/image-20230723220233142.png)

> 开启这个配置之后, 写入的对象必须要有无参构造, 没有则报错

![image-20230723220342055](images/image-20230723220342055.png)

### 为什么?

```
ObjectMapper.DefaultTyping.NON_FINAL
```

#### ObjectMapper测试

```java
    public static void main(String[] args) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 开启这行代码, 写入redis的json会带上对象全限定类名
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        RedisData redisData = new RedisData(IdUtil.fastSimpleUUID(), RandomUtil.randomInt());
        String s = objectMapper.writeValueAsString(redisData);
        // {"name":"dca43af567984687a6bfefd479c6a154","age":1200306482}
        // ["com.kk0.redis.redistemplate.RedisData",{"name":"de5498bd01b7439a8f2242bdaa69c5dc","age":1606027989}]
        System.out.println(s);
    }
```

