# 概念
> Redis布隆过滤器是一个**基于Bit数组和Hash算法**实现的数据结构。在Redis中用于快速判断一个元素是否存在于集合中。它的作用类似于传统的哈希表，但它占用的空间更小，并且可以支持更高的并发量。
> Redis布隆过滤器最大的优点是可以**实现快速的查找和去重。**

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681549726574-33963aa9-f8c3-4cc9-be3d-95328c38681a.png#averageHue=%23fdfcfb&clientId=uc0ef9c0f-86fc-4&from=paste&height=233&id=u2ee589a5&originHeight=466&originWidth=1738&originalType=binary&ratio=2&rotation=0&showTitle=false&size=103739&status=done&style=none&taskId=u837a6c2d-a345-4329-9121-d0ee42a434c&title=&width=869)
## Bit数组
> Bit数组是一种存储二进制位的数据结构，通常使用一个数组来存储二进制位，每个数组元素都只能存储0或1。由于一个字节可以存储八个二进制位，因此Bit数组也可以被称为"位图"。

## 哈希算法
> 使用Redis布隆过滤器时需要先将需要加入判断的元素经过多个Hash函数进行映射到Bit数组上，并将相应位置标记为1。
查询时也同样将要查询的元素通过Hash函数映射到Bit数组上进行查找，如果所有对应位置都为1，则说明这个元素可能存在于集合中，如果有任何一个位置为0，则说明这个元素一定不存在于集合中。

我们判断缓存key是否存在，同样，K个哈希函数，映射到bit列列表上的K个点，判断是不是1: 

- 如果有一个点不是1，那么key一定不存在; 
- 如果都是1，也只是表示key可能存在。 
## 缺点

-  它在判断元素是否在集合中时是有一定错误几率，因为哈希算法有⼀定的碰撞的概率。 
- 不支持删除元素。
# 实现
## redisson实现
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681550261180-45d10171-9d0c-442f-8bea-9a63c0e21d4b.png#averageHue=%234d6172&clientId=uc0ef9c0f-86fc-4&from=paste&height=414&id=u623b64dc&originHeight=828&originWidth=1094&originalType=binary&ratio=2&rotation=0&showTitle=false&size=71795&status=done&style=none&taskId=u363f01dc-c9db-467d-ac79-fa7b9c446ad&title=&width=547)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681549979480-c081b7ca-6779-42da-9b51-f146a53ac691.png#averageHue=%2332363d&clientId=uc0ef9c0f-86fc-4&from=paste&height=240&id=u2f7e372e&originHeight=480&originWidth=822&originalType=binary&ratio=2&rotation=0&showTitle=false&size=57235&status=done&style=none&taskId=u733f320d-89bd-42f2-9714-969d948c1b8&title=&width=411)
```java
@Autowired
private RedissonClient redissonClient;

@PostMapping("addBloomFilter")
public void addBloomFilter() {

    RBloomFilter<Object> rBloomFilter = redissonClient.getBloomFilter("BloomFilter");
    /**
     * 初始化
     * @param expectedInsertions - expected amount of insertions per element
     * @param falseProbability - expected false probability 预期false概率
     */
    rBloomFilter.tryInit(100000, 0.01);
    String objectId = IdUtil.objectId();
    boolean add = rBloomFilter.add(objectId);
    boolean contains = rBloomFilter.contains(objectId);
    long count = rBloomFilter.count();
    log.info("add: {}, contains: {}, count: {}", add, contains, count);
}
```
## RedisTemplate  + Lua
> 没完成。不支持BF.ADD命令。

```java
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @PostMapping("addBloomFilterByLua")
    public void addBloomFilterByLua() {
        String addLua = "return redis.call('BF.ADD',KEYS[1],ARGV[1])";
        DefaultRedisScript<Boolean> addScript = new DefaultRedisScript<>(addLua, Boolean.class);
        String objectId = IdUtil.objectId();
        String key = "BloomFilter-Lua";
        Boolean add = redisTemplate.execute(addScript, Lists.newArrayList(key), objectId);
        log.info("script, add execute: {}", add);

        String existLua = "return redis.call('BF.EXISTS',KEYS[1],ARGV[1])";
        DefaultRedisScript<Boolean> existScript = new DefaultRedisScript<>(existLua, Boolean.class);
        Boolean exist = redisTemplate.execute(existScript, Lists.newArrayList(key), objectId);
        log.info("script, exist execute: {}", exist);
    }
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1681554851214-df79f244-44e2-42ca-8cb8-70bfddcfc7ea.png#averageHue=%232b2c2f&clientId=uc0ef9c0f-86fc-4&from=paste&height=274&id=u313cfd8d&originHeight=548&originWidth=1908&originalType=binary&ratio=2&rotation=0&showTitle=false&size=149344&status=done&style=none&taskId=u492de50c-f133-4ab2-94d8-721749b10cb&title=&width=954)
### 命令
```java
// 创建一个布隆过滤器并指定其容量和误判率
BF.RESERVE my_filter 0.01 10000

// 添加元素
BF.ADD my_filter element1
BF.ADD my_filter element2

// 检查元素是否存在，返回1 可能存在，返回0 一定不存在。
BF.EXISTS my_filter element1
```
### 安装RedisBloom
```java
git clone https://github.com/RedisBloom/RedisBloom.git
cd RedisBloom
make
cp redisbloom.so /path/to/redis/modules/

// redis.conf添加
loadmodule /path/to/redis/modules/redisbloom.so

```
