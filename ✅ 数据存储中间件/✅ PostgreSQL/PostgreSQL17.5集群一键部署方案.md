# PostgreSQL 17.5 高可用集群一键部署方案

## 1. 架构概述

### 1.1 整体架构图

```
                    ┌─────────────────┐
                    │   应用服务器     │
                    │   (Java应用)    │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   HAProxy VIP   │
                    │  *************  │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   ┌────▼────┐          ┌────▼────┐          ┌────▼────┐
   │HAProxy-1│          │HAProxy-2│          │         │
   │主负载均衡│          │备负载均衡│          │         │
   └─────────┘          └─────────┘          └─────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   ┌────▼────┐          ┌────▼────┐          ┌────▼────┐
   │PG-Node1 │          │PG-Node2 │          │PG-Node3 │
   │Primary  │◄────────►│Standby  │◄────────►│Standby  │
   │+<PERSON>roni │   流复制   │+<PERSON><PERSON><PERSON> │   流复制   │+<PERSON>roni │
   │+etcd    │          │+etcd    │          │+etcd    │
   └─────────┘          └─────────┘          └─────────┘
```

### 1.2 服务器规划

```bash
# PostgreSQL + Patroni + etcd 节点 (3台)
************  pg-node1  (Primary + etcd-1)
************  pg-node2  (Standby + etcd-2)
************  pg-node3  (Standby + etcd-3)

# HAProxy 负载均衡节点 (2台)
************  haproxy-1 (Master)
************  haproxy-2 (Backup)

# 虚拟IP
************* haproxy-vip (对外服务地址)

# 注意: 以上IP地址为示例，实际部署时需要在脚本开头修改对应的配置变量:
# PG_NODE1_IP, PG_NODE2_IP, PG_NODE3_IP
# HAPROXY_NODE1_IP, HAPROXY_NODE2_IP, HAPROXY_VIP
# NETWORK_SEGMENT
```

### 1.3 目录规划

```bash
# 所有数据和日志都放在 /home 目录下
/home/<USER>/          # PostgreSQL根目录
├── data/                  # 数据目录
├── logs/                  # 日志目录
├── archive/               # 归档目录
├── backup/                # 备份目录
├── scripts/               # 脚本目录
└── src/                   # 源码目录

/home/<USER>/                # etcd根目录
├── data/                  # etcd数据目录
└── logs/                  # etcd日志目录

/home/<USER>/             # Patroni根目录
├── config/                # 配置目录
└── logs/                  # 日志目录
```

## 2. 一键安装脚本

### 2.1 PostgreSQL节点安装脚本

创建文件：`install_postgresql_node.sh`

```bash
#!/bin/bash

# ============================================================================
# PostgreSQL 17.5 + Patroni + etcd 一键安装脚本
# 适用于: CentOS 8+ / RHEL 8+ / Rocky Linux 8+
# 作者: linshiqiang
# 日期: 2025-01-02
# ============================================================================

# ============================================================================
# 配置参数区域 - 根据实际环境修改
# ============================================================================

# 基础配置
export INSTALL_USER="postgres"                    # PostgreSQL运行用户
export INSTALL_GROUP="postgres"                   # PostgreSQL运行用户组
export BASE_DIR="/home"                           # 基础安装目录

# PostgreSQL配置
export PG_VERSION="17.5"                          # PostgreSQL版本
export PG_PORT="5432"                             # PostgreSQL端口
export PG_DATA_DIR="${BASE_DIR}/postgresql/data"  # 数据目录
export PG_LOG_DIR="${BASE_DIR}/postgresql/logs"   # 日志目录
export PG_ARCHIVE_DIR="${BASE_DIR}/postgresql/archive"  # 归档目录
export PG_BACKUP_DIR="${BASE_DIR}/postgresql/backup"    # 备份目录
export PG_SCRIPTS_DIR="${BASE_DIR}/postgresql/scripts"  # 脚本目录
export PG_SRC_DIR="${BASE_DIR}/postgresql/src"    # 源码目录

# 数据库用户密码配置
export POSTGRES_PASSWORD="PostgresPass123!"       # postgres超级用户密码
export REPLICATOR_PASSWORD="ReplicatorPass123!"   # 复制用户密码
export REWIND_PASSWORD="RewindPass123!"           # rewind用户密码

# 网络配置 - 根据实际环境修改IP地址
export PG_NODE1_IP="************"                 # PostgreSQL节点1 IP
export PG_NODE2_IP="************"                 # PostgreSQL节点2 IP
export PG_NODE3_IP="************"                 # PostgreSQL节点3 IP
export HAPROXY_NODE1_IP="************"            # HAProxy节点1 IP
export HAPROXY_NODE2_IP="************"            # HAProxy节点2 IP
export HAPROXY_VIP="*************"                # HAProxy虚拟IP
export NETWORK_SEGMENT="***********/24"           # 网络段

# 集群配置
export CLUSTER_NAME="postgres-cluster"            # 集群名称
export NODE_NAME=""                                # 节点名称(自动检测)
export NODE_IP=""                                  # 节点IP(自动检测)

# etcd配置
export ETCD_VERSION="v3.5.10"                     # etcd版本
export ETCD_PORT_CLIENT="2379"                    # etcd客户端端口
export ETCD_PORT_PEER="2380"                      # etcd节点间通信端口
export ETCD_DATA_DIR="${BASE_DIR}/etcd/data"      # etcd数据目录
export ETCD_LOG_DIR="${BASE_DIR}/etcd/logs"       # etcd日志目录

# Patroni配置
export PATRONI_PORT="8008"                        # Patroni REST API端口
export PATRONI_CONFIG_DIR="${BASE_DIR}/patroni/config"  # Patroni配置目录
export PATRONI_LOG_DIR="${BASE_DIR}/patroni/logs"       # Patroni日志目录

# 集群节点配置 (基于上面的IP配置自动生成)
export ETCD_CLUSTER_NODES=(
    "etcd-1=http://${PG_NODE1_IP}:${ETCD_PORT_PEER}"
    "etcd-2=http://${PG_NODE2_IP}:${ETCD_PORT_PEER}"
    "etcd-3=http://${PG_NODE3_IP}:${ETCD_PORT_PEER}"
)

export ETCD_ENDPOINTS=(
    "http://${PG_NODE1_IP}:${ETCD_PORT_CLIENT}"
    "http://${PG_NODE2_IP}:${ETCD_PORT_CLIENT}"
    "http://${PG_NODE3_IP}:${ETCD_PORT_CLIENT}"
)

# 网络配置映射
export NODE_MAPPING=(
    "${PG_NODE1_IP}:pg-node1:etcd-1"
    "${PG_NODE2_IP}:pg-node2:etcd-2"
    "${PG_NODE3_IP}:pg-node3:etcd-3"
)

# ============================================================================
# 函数定义区域
# ============================================================================

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
}

# 检查命令执行结果
check_result() {
    if [ $? -eq 0 ]; then
        log_info "$1 成功"
    else
        log_error "$1 失败"
        exit 1
    fi
}

# 检测当前节点信息
detect_node_info() {
    log_info "检测当前节点信息..."
    
    # 获取本机IP地址
    NODE_IP=$(ip route get ******* | awk '{print $7; exit}')
    
    # 根据IP确定节点名称和etcd名称
    for mapping in "${NODE_MAPPING[@]}"; do
        IFS=':' read -r ip node_name etcd_name <<< "$mapping"
        if [ "$ip" = "$NODE_IP" ]; then
            NODE_NAME="$node_name"
            ETCD_NAME="$etcd_name"
            break
        fi
    done
    
    if [ -z "$NODE_NAME" ]; then
        log_error "无法确定节点名称，请检查NODE_MAPPING配置"
        exit 1
    fi
    
    log_info "当前节点: $NODE_NAME, IP: $NODE_IP, etcd名称: $ETCD_NAME"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [ ! -f /etc/redhat-release ]; then
        log_error "此脚本仅支持 CentOS/RHEL/Rocky Linux 系统"
        exit 1
    fi
    
    # 检查系统版本
    OS_VERSION=$(cat /etc/redhat-release | grep -oE '[0-9]+' | head -1)
    if [ "$OS_VERSION" -lt 8 ]; then
        log_error "系统版本过低，需要 CentOS 8+ / RHEL 8+ / Rocky Linux 8+"
        exit 1
    fi
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户执行此脚本"
        exit 1
    fi
    
    log_info "系统环境检查通过"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖包..."
    
    # 更新系统
    dnf update -y
    check_result "系统更新"
    
    # 安装编译依赖
    dnf groupinstall -y "Development Tools"
    check_result "开发工具安装"
    
    # 安装PostgreSQL编译依赖
    dnf install -y \
        gcc gcc-c++ make cmake \
        readline-devel zlib-devel openssl-devel \
        libxml2-devel libxslt-devel \
        python3 python3-pip python3-devel \
        wget curl vim git htop iotop \
        net-tools telnet nc \
        chrony \
        bison flex gettext \
        krb5-devel pam-devel \
        openldap-devel systemd-devel
    check_result "依赖包安装"
    
    # 配置时间同步
    systemctl enable chronyd
    systemctl start chronyd
    check_result "时间同步配置"
}

# 创建用户和目录
create_user_and_dirs() {
    log_info "创建用户和目录结构..."

    # 创建postgres用户
    if ! id "$INSTALL_USER" &>/dev/null; then
        useradd -r -s /bin/bash "$INSTALL_USER"
        check_result "创建postgres用户"
    else
        log_info "用户 $INSTALL_USER 已存在"
    fi

    # 创建目录结构
    mkdir -p "$PG_DATA_DIR" "$PG_LOG_DIR" "$PG_ARCHIVE_DIR" "$PG_BACKUP_DIR" "$PG_SCRIPTS_DIR" "$PG_SRC_DIR"
    mkdir -p "$ETCD_DATA_DIR" "$ETCD_LOG_DIR"
    mkdir -p "$PATRONI_CONFIG_DIR" "$PATRONI_LOG_DIR"

    # 设置目录权限
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/postgresql"
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/etcd"
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "${BASE_DIR}/patroni"

    chmod 700 "$PG_DATA_DIR"
    chmod 755 "$PG_LOG_DIR" "$PG_ARCHIVE_DIR" "$PG_BACKUP_DIR" "$PG_SCRIPTS_DIR"

    check_result "目录创建和权限设置"
}

# 编译安装PostgreSQL 17.5
install_postgresql() {
    log_info "开始编译安装PostgreSQL $PG_VERSION..."

    cd "$PG_SRC_DIR"

    # 下载PostgreSQL源码
    if [ ! -f "postgresql-${PG_VERSION}.tar.gz" ]; then
        log_info "下载PostgreSQL源码..."
        wget "https://ftp.postgresql.org/pub/source/v${PG_VERSION}/postgresql-${PG_VERSION}.tar.gz"
        check_result "PostgreSQL源码下载"
    fi

    # 解压源码
    if [ ! -d "postgresql-${PG_VERSION}" ]; then
        tar -xzf "postgresql-${PG_VERSION}.tar.gz"
        check_result "PostgreSQL源码解压"
    fi

    cd "postgresql-${PG_VERSION}"

    # 配置编译选项
    log_info "配置PostgreSQL编译选项..."
    ./configure \
        --prefix=/usr/local/pgsql \
        --with-openssl \
        --with-libxml \
        --with-libxslt \
        --enable-thread-safety \
        --with-readline \
        --with-zlib \
        --enable-debug
    check_result "PostgreSQL配置"

    # 编译
    log_info "编译PostgreSQL (这可能需要几分钟)..."
    make -j$(nproc)
    check_result "PostgreSQL编译"

    # 安装
    log_info "安装PostgreSQL..."
    make install
    check_result "PostgreSQL安装"

    # 编译安装contrib模块
    log_info "编译安装contrib模块..."
    cd contrib
    make -j$(nproc)
    make install
    check_result "contrib模块安装"

    # 创建软链接
    ln -sf /usr/local/pgsql/bin/* /usr/local/bin/

    # 配置环境变量
    cat > /etc/profile.d/postgresql.sh << 'EOF'
export PGHOME=/usr/local/pgsql
export PATH=$PGHOME/bin:$PATH
export LD_LIBRARY_PATH=$PGHOME/lib:$LD_LIBRARY_PATH
export MANPATH=$PGHOME/share/man:$MANPATH
EOF

    # 配置postgres用户环境变量
    cat >> "/home/<USER>/.bashrc" << EOF
export PGDATA=$PG_DATA_DIR
export PGPORT=$PG_PORT
export PGUSER=$INSTALL_USER
export PGHOME=/usr/local/pgsql
export PATH=\$PGHOME/bin:\$PATH
export LD_LIBRARY_PATH=\$PGHOME/lib:\$LD_LIBRARY_PATH
export MANPATH=\$PGHOME/share/man:\$MANPATH
EOF

    log_info "PostgreSQL $PG_VERSION 安装完成"
}

# 安装etcd
install_etcd() {
    log_info "安装etcd $ETCD_VERSION..."

    cd /tmp

    # 下载etcd
    if [ ! -f "etcd-${ETCD_VERSION}-linux-amd64.tar.gz" ]; then
        log_info "下载etcd..."
        wget "https://github.com/etcd-io/etcd/releases/download/${ETCD_VERSION}/etcd-${ETCD_VERSION}-linux-amd64.tar.gz"
        check_result "etcd下载"
    fi

    # 解压并安装
    tar -xzf "etcd-${ETCD_VERSION}-linux-amd64.tar.gz"
    cp "etcd-${ETCD_VERSION}-linux-amd64/etcd"* /usr/local/bin/
    chmod +x /usr/local/bin/etcd*

    # 清理临时文件
    rm -rf "etcd-${ETCD_VERSION}-linux-amd64"*

    check_result "etcd安装"
}

# 配置Python3清华源
configure_python_mirror() {
    log_info "配置Python3清华源..."

    # 创建pip配置目录
    mkdir -p ~/.pip
    mkdir -p /home/<USER>/.pip

    # 配置pip清华源
    cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

    # 为postgres用户配置pip清华源
    cat > /home/<USER>/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
EOF

    # 设置权限
    chown -R $INSTALL_USER:$INSTALL_GROUP /home/<USER>/.pip

    check_result "Python3清华源配置"
}

# 安装Python依赖和Patroni
install_patroni() {
    log_info "安装Patroni..."

    # 配置Python清华源
    configure_python_mirror

    # 升级pip (使用清华源)
    python3 -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
    check_result "pip升级"

    # 安装Patroni及依赖 (使用清华源)
    pip3 install patroni[etcd]==3.2.0 -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip3 install psycopg2-binary==2.9.9 -i https://pypi.tuna.tsinghua.edu.cn/simple
    pip3 install python-etcd==0.4.5 -i https://pypi.tuna.tsinghua.edu.cn/simple
    check_result "Patroni安装"

    # 创建Patroni软链接
    ln -sf /usr/local/bin/patroni* /usr/bin/
}

# 配置系统参数
configure_system() {
    log_info "配置系统参数..."

    # 配置内核参数
    cat >> /etc/sysctl.conf << EOF

# PostgreSQL优化参数
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.shmmni = 4096
kernel.sem = 50100 64128000 50100 1280
fs.file-max = 7672460
net.ipv4.ip_local_port_range = 9000 65000
net.core.rmem_default = 262144
net.core.rmem_max = 4194304
net.core.wmem_default = 262144
net.core.wmem_max = 1048576
vm.swappiness = 1
vm.dirty_background_ratio = 3
vm.dirty_ratio = 10
EOF

    sysctl -p
    check_result "内核参数配置"

    # 配置用户资源限制
    cat >> /etc/security/limits.conf << EOF

# PostgreSQL用户资源限制
$INSTALL_USER soft nofile 65536
$INSTALL_USER hard nofile 65536
$INSTALL_USER soft nproc 32768
$INSTALL_USER hard nproc 32768
$INSTALL_USER soft memlock unlimited
$INSTALL_USER hard memlock unlimited
EOF

    check_result "用户资源限制配置"

    # 关闭防火墙和SELinux (测试环境)
    systemctl stop firewalld 2>/dev/null || true
    systemctl disable firewalld 2>/dev/null || true
    setenforce 0 2>/dev/null || true
    sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config 2>/dev/null || true

    log_info "系统参数配置完成"
}

# 配置etcd
configure_etcd() {
    log_info "配置etcd..."

    # 构建etcd集群字符串
    ETCD_INITIAL_CLUSTER=""
    for node in "${ETCD_CLUSTER_NODES[@]}"; do
        if [ -z "$ETCD_INITIAL_CLUSTER" ]; then
            ETCD_INITIAL_CLUSTER="$node"
        else
            ETCD_INITIAL_CLUSTER="$ETCD_INITIAL_CLUSTER,$node"
        fi
    done

    # 创建etcd配置文件
    cat > "/etc/etcd.conf" << EOF
# etcd配置文件
name: $ETCD_NAME
data-dir: $ETCD_DATA_DIR
listen-client-urls: http://$NODE_IP:$ETCD_PORT_CLIENT
advertise-client-urls: http://$NODE_IP:$ETCD_PORT_CLIENT
listen-peer-urls: http://$NODE_IP:$ETCD_PORT_PEER
initial-advertise-peer-urls: http://$NODE_IP:$ETCD_PORT_PEER
initial-cluster: $ETCD_INITIAL_CLUSTER
initial-cluster-token: postgres-etcd-cluster
initial-cluster-state: new
log-level: info
log-outputs: [$ETCD_LOG_DIR/etcd.log]
heartbeat-interval: 100
election-timeout: 1000
max-snapshots: 5
max-wals: 5
EOF

    # 创建etcd systemd服务文件
    cat > /etc/systemd/system/etcd.service << EOF
[Unit]
Description=etcd key-value store
Documentation=https://github.com/etcd-io/etcd
After=network.target
Wants=network-online.target

[Service]
Type=notify
User=$INSTALL_USER
Group=$INSTALL_GROUP
ExecStart=/usr/local/bin/etcd --config-file /etc/etcd.conf
Restart=always
RestartSec=10s
LimitNOFILE=40000
NoNewPrivileges=true
ProtectHome=true
ProtectSystem=strict
ReadWritePaths=$ETCD_DATA_DIR $ETCD_LOG_DIR

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    check_result "etcd配置"
}

# 初始化PostgreSQL数据库
init_postgresql() {
    log_info "初始化PostgreSQL数据库..."

    # 切换到postgres用户初始化数据库
    su - "$INSTALL_USER" -c "
        /usr/local/pgsql/bin/initdb -D $PG_DATA_DIR \
            --encoding=UTF8 \
            --locale=C \
            --data-checksums \
            --auth-local=trust \
            --auth-host=md5
    "
    check_result "PostgreSQL数据库初始化"

    # 创建基础配置文件
    cat > "$PG_DATA_DIR/postgresql.conf" << EOF
# PostgreSQL 17.5 配置文件

# 连接和认证
listen_addresses = '*'
port = $PG_PORT
max_connections = 200
superuser_reserved_connections = 3

# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL配置
wal_level = replica
wal_buffers = 16MB
min_wal_size = 1GB
max_wal_size = 4GB
wal_log_hints = on
archive_mode = on
archive_command = 'test ! -f $PG_ARCHIVE_DIR/%f && cp %p $PG_ARCHIVE_DIR/%f'

# 复制配置
max_wal_senders = 10
max_replication_slots = 10
hot_standby = on

# 检查点配置
checkpoint_completion_target = 0.9
checkpoint_timeout = 5min

# 日志配置
logging_collector = on
log_destination = 'csvlog'
log_directory = '$PG_LOG_DIR'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# 性能优化
random_page_cost = 1.1
effective_io_concurrency = 200
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
default_statistics_target = 100

# 其他配置
unix_socket_directories = '/tmp'
shared_preload_libraries = 'pg_stat_statements'
EOF

    # 创建pg_hba.conf
    cat > "$PG_DATA_DIR/pg_hba.conf" << EOF
# PostgreSQL Client Authentication Configuration File

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     trust

# IPv4 local connections:
host    all             all             127.0.0.1/32            md5
host    all             all             $NETWORK_SEGMENT        md5

# IPv6 local connections:
host    all             all             ::1/128                 md5

# Replication connections
host    replication     replicator      127.0.0.1/32            md5
host    replication     replicator      $NETWORK_SEGMENT        md5
EOF

    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PG_DATA_DIR"
    check_result "PostgreSQL配置文件创建"
}

# 配置Patroni
configure_patroni() {
    log_info "配置Patroni..."

    # 构建etcd endpoints字符串
    ETCD_ENDPOINTS_STR=""
    for endpoint in "${ETCD_ENDPOINTS[@]}"; do
        if [ -z "$ETCD_ENDPOINTS_STR" ]; then
            ETCD_ENDPOINTS_STR="$endpoint"
        else
            ETCD_ENDPOINTS_STR="$ETCD_ENDPOINTS_STR,$endpoint"
        fi
    done

    # 创建Patroni配置文件
    cat > "$PATRONI_CONFIG_DIR/patroni.yml" << EOF
# Patroni配置文件
scope: $CLUSTER_NAME
namespace: /db/
name: $NODE_NAME

restapi:
  listen: $NODE_IP:$PATRONI_PORT
  connect_address: $NODE_IP:$PATRONI_PORT

etcd:
  hosts: $ETCD_ENDPOINTS_STR

bootstrap:
  dcs:
    ttl: 30
    loop_wait: 10
    retry_timeout: 30
    maximum_lag_on_failover: 1048576
    master_start_timeout: 300
    synchronous_mode: false

    postgresql:
      use_pg_rewind: true
      use_slots: true

      parameters:
        max_connections: 200
        shared_buffers: 256MB
        effective_cache_size: 1GB
        work_mem: 4MB
        maintenance_work_mem: 64MB
        wal_level: replica
        wal_buffers: 16MB
        min_wal_size: 1GB
        max_wal_size: 4GB
        wal_log_hints: on
        max_wal_senders: 10
        max_replication_slots: 10
        hot_standby: on
        checkpoint_completion_target: 0.9
        archive_mode: on
        archive_command: 'test ! -f $PG_ARCHIVE_DIR/%f && cp %p $PG_ARCHIVE_DIR/%f'
        logging_collector: on
        log_destination: 'csvlog'
        log_directory: '$PG_LOG_DIR'
        log_filename: 'postgresql-%Y-%m-%d_%H%M%S.log'
        log_rotation_age: 1d
        log_rotation_size: 100MB
        log_min_duration_statement: 1000
        log_checkpoints: on
        log_connections: on
        log_disconnections: on
        log_lock_waits: on
        log_temp_files: 0
        random_page_cost: 1.1
        effective_io_concurrency: 200
        max_worker_processes: 8
        max_parallel_workers_per_gather: 4
        max_parallel_workers: 8
        max_parallel_maintenance_workers: 4
        default_statistics_target: 100
        unix_socket_directories: '/tmp'
        shared_preload_libraries: 'pg_stat_statements'

  initdb:
  - encoding: UTF8
  - data-checksums
  - locale: C

  pg_hba:
  - host replication replicator 127.0.0.1/32 md5
  - host replication replicator $NETWORK_SEGMENT md5
  - host all all 0.0.0.0/0 md5

  users:
    admin:
      password: '$POSTGRES_PASSWORD'
      options:
        - createrole
        - createdb

postgresql:
  listen: $NODE_IP:$PG_PORT
  connect_address: $NODE_IP:$PG_PORT
  data_dir: $PG_DATA_DIR
  bin_dir: /usr/local/pgsql/bin
  pgpass: /tmp/pgpass_$NODE_NAME

  authentication:
    replication:
      username: replicator
      password: '$REPLICATOR_PASSWORD'
    superuser:
      username: postgres
      password: '$POSTGRES_PASSWORD'
    rewind:
      username: rewind_user
      password: '$REWIND_PASSWORD'

  parameters:
    unix_socket_directories: '/tmp'

tags:
    nofailover: false
    noloadbalance: false
    clonefrom: false
    nosync: false
EOF

    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PATRONI_CONFIG_DIR"
    check_result "Patroni配置"
}

# 创建systemd服务文件
create_systemd_services() {
    log_info "创建systemd服务文件..."

    # 创建Patroni服务文件
    cat > /etc/systemd/system/patroni.service << EOF
[Unit]
Description=Runners to orchestrate a high-availability PostgreSQL
Documentation=https://patroni.readthedocs.io/
After=syslog.target network.target etcd.service
Wants=network-online.target

[Service]
Type=simple
User=$INSTALL_USER
Group=$INSTALL_GROUP
Environment=PATRONI_CONFIG_FILE=$PATRONI_CONFIG_DIR/patroni.yml
ExecStart=/usr/local/bin/patroni $PATRONI_CONFIG_DIR/patroni.yml
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=process
TimeoutSec=30
Restart=no
StandardOutput=journal
StandardError=journal
SyslogIdentifier=patroni
NoNewPrivileges=true
ProtectHome=true
ReadWritePaths=$PG_DATA_DIR $PG_LOG_DIR $PG_ARCHIVE_DIR $PATRONI_LOG_DIR /tmp

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    check_result "systemd服务文件创建"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."

    # 创建集群状态检查脚本
    cat > "$PG_SCRIPTS_DIR/check_cluster.sh" << 'EOF'
#!/bin/bash
echo "=== PostgreSQL集群状态检查 ==="
echo "检查时间: $(date)"
echo

echo "1. Patroni集群状态:"
patronictl -c /home/<USER>/config/patroni.yml list

echo
echo "2. etcd集群状态:"
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 endpoint health

echo
echo "3. PostgreSQL进程状态:"
ps aux | grep postgres | grep -v grep

echo
echo "4. 端口监听状态:"
ss -tuln | grep -E ":(5432|8008|2379|2380)"

echo "=== 检查完成 ==="
EOF

    # 创建备份脚本
    cat > "$PG_SCRIPTS_DIR/backup.sh" << EOF
#!/bin/bash
BACKUP_DIR="$PG_BACKUP_DIR"
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="postgres_backup_\${DATE}"

mkdir -p \${BACKUP_DIR}

echo "开始备份: \${BACKUP_NAME}"
pg_basebackup -h $NODE_IP -D \${BACKUP_DIR}/\${DATE} -U replicator -v -P -W -X stream

tar -czf \${BACKUP_DIR}/\${BACKUP_NAME}.tar.gz -C \${BACKUP_DIR} \${DATE}
rm -rf \${BACKUP_DIR}/\${DATE}

echo "备份完成: \${BACKUP_DIR}/\${BACKUP_NAME}.tar.gz"
EOF

    # 设置脚本权限
    chmod +x "$PG_SCRIPTS_DIR"/*.sh
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$PG_SCRIPTS_DIR"

    check_result "管理脚本创建"
}

# 主安装函数
main() {
    log_info "开始PostgreSQL 17.5集群节点安装..."

    # 检测节点信息
    detect_node_info

    # 检查系统环境
    check_system

    # 安装依赖
    install_dependencies

    # 创建用户和目录
    create_user_and_dirs

    # 编译安装PostgreSQL
    install_postgresql

    # 安装etcd
    install_etcd

    # 安装Patroni
    install_patroni

    # 配置系统参数
    configure_system

    # 配置etcd
    configure_etcd

    # 初始化PostgreSQL
    init_postgresql

    # 配置Patroni
    configure_patroni

    # 创建systemd服务
    create_systemd_services

    # 创建管理脚本
    create_management_scripts

    log_info "PostgreSQL节点安装完成！"
    log_info "请在所有节点安装完成后，按以下顺序启动服务："
    log_info "1. 启动etcd: systemctl start etcd"
    log_info "2. 启动Patroni: systemctl start patroni"
    log_info "3. 检查集群状态: $PG_SCRIPTS_DIR/check_cluster.sh"
}

# ============================================================================
# 脚本执行入口
# ============================================================================

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "PostgreSQL 17.5集群节点安装脚本"
    echo "用法: $0"
    echo "请在脚本开头修改配置参数后执行"
    exit 0
fi

# 执行主函数
main "$@"
```

### 2.2 HAProxy节点安装脚本

创建文件：`install_haproxy_node.sh`

```bash
#!/bin/bash

# ============================================================================
# HAProxy + Keepalived 一键安装脚本
# 适用于: CentOS 8+ / RHEL 8+ / Rocky Linux 8+
# 作者: linshiqiang
# 日期: 2025-01-02
# ============================================================================

# ============================================================================
# 配置参数区域 - 根据实际环境修改
# ============================================================================

# 基础配置
export BASE_DIR="/home"                           # 基础目录
export HAPROXY_CONFIG_DIR="/etc/haproxy"          # HAProxy配置目录
export KEEPALIVED_CONFIG_DIR="/etc/keepalived"    # Keepalived配置目录

# HAProxy配置
export HAPROXY_STATS_PORT="8404"                  # 统计页面端口
export HAPROXY_STATS_USER="admin"                 # 统计页面用户名
export HAPROXY_STATS_PASS="admin123"              # 统计页面密码

# 网络配置 - 根据实际环境修改IP地址
export PG_NODE1_IP="************"                 # PostgreSQL节点1 IP
export PG_NODE2_IP="************"                 # PostgreSQL节点2 IP
export PG_NODE3_IP="************"                 # PostgreSQL节点3 IP
export HAPROXY_NODE1_IP="************"            # HAProxy节点1 IP
export HAPROXY_NODE2_IP="************"            # HAProxy节点2 IP
export HAPROXY_VIP="*************"                # HAProxy虚拟IP
export NETWORK_SEGMENT="***********/24"           # 网络段
export VIP_INTERFACE="eth0"                       # 网络接口名称

# PostgreSQL集群配置
export PG_WRITE_PORT="5000"                       # 写入端口
export PG_READ_PORT="5001"                        # 读取端口
export PG_POOL_PORT="5002"                        # 连接池端口

# PostgreSQL节点配置 (基于上面的IP配置自动生成)
export PG_NODES=(
    "${PG_NODE1_IP}:5432:8008"  # IP:PG_PORT:PATRONI_PORT
    "${PG_NODE2_IP}:5432:8008"
    "${PG_NODE3_IP}:5432:8008"
)

# Keepalived配置
export VIP="$HAPROXY_VIP"                         # 虚拟IP
export VRRP_ROUTER_ID="51"                        # 虚拟路由器ID
export VRRP_PASSWORD="postgres_ha_2024"           # VRRP认证密码

# 节点配置映射 (基于上面的IP配置自动生成)
export NODE_MAPPING=(
    "${HAPROXY_NODE1_IP}:haproxy-1:MASTER:100"   # IP:主机名:角色:优先级
    "${HAPROXY_NODE2_IP}:haproxy-2:BACKUP:90"
)

# ============================================================================
# 函数定义区域
# ============================================================================

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

# 检查命令执行结果
check_result() {
    if [ $? -eq 0 ]; then
        log_info "$1 成功"
    else
        log_error "$1 失败"
        exit 1
    fi
}

# 检测当前节点信息
detect_node_info() {
    log_info "检测当前节点信息..."

    NODE_IP=$(ip route get ******* | awk '{print $7; exit}')

    for mapping in "${NODE_MAPPING[@]}"; do
        IFS=':' read -r ip hostname role priority <<< "$mapping"
        if [ "$ip" = "$NODE_IP" ]; then
            NODE_HOSTNAME="$hostname"
            NODE_ROLE="$role"
            NODE_PRIORITY="$priority"
            break
        fi
    done

    if [ -z "$NODE_HOSTNAME" ]; then
        log_error "无法确定节点信息，请检查NODE_MAPPING配置"
        exit 1
    fi

    log_info "当前节点: $NODE_HOSTNAME, IP: $NODE_IP, 角色: $NODE_ROLE, 优先级: $NODE_PRIORITY"
}

# 安装HAProxy和Keepalived
install_haproxy_keepalived() {
    log_info "安装HAProxy和Keepalived..."

    # 更新系统
    dnf update -y

    # 安装软件包
    dnf install -y haproxy keepalived curl wget net-tools
    check_result "HAProxy和Keepalived安装"

    # 备份原始配置
    cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.bak 2>/dev/null || true
    cp /etc/keepalived/keepalived.conf /etc/keepalived/keepalived.conf.bak 2>/dev/null || true
}

# 配置HAProxy
configure_haproxy() {
    log_info "配置HAProxy..."

    # 构建PostgreSQL服务器列表
    PG_SERVERS=""
    for i in "${!PG_NODES[@]}"; do
        IFS=':' read -r ip pg_port patroni_port <<< "${PG_NODES[$i]}"
        node_num=$((i + 1))
        PG_SERVERS="${PG_SERVERS}    server pg-node${node_num} ${ip}:${pg_port} maxconn 100 check port ${patroni_port}
"
    done

    # 创建HAProxy配置文件
    cat > /etc/haproxy/haproxy.cfg << EOF
# HAProxy PostgreSQL集群负载均衡配置

global
    maxconn 4000
    log stdout local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

defaults
    mode tcp
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    log global
    option tcplog
    option dontlognull
    retries 3

# PostgreSQL主库写入服务
listen postgres_write
    bind *:$PG_WRITE_PORT
    description "PostgreSQL Master Write Service"
    option httpchk GET /master
    http-check expect status 200
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions
$PG_SERVERS

# PostgreSQL从库读取服务
listen postgres_read
    bind *:$PG_READ_PORT
    description "PostgreSQL Standby Read Service"
    balance roundrobin
    option httpchk GET /replica
    http-check expect status 200
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions
$PG_SERVERS

# PostgreSQL连接池服务
listen postgres_pool
    bind *:$PG_POOL_PORT
    description "PostgreSQL Connection Pool"
    balance leastconn
    option tcp-check
    default-server inter 3s fall 3 rise 2
$PG_SERVERS

# HAProxy统计页面
listen stats
    bind *:$HAPROXY_STATS_PORT
    description "HAProxy Statistics"
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats auth $HAPROXY_STATS_USER:$HAPROXY_STATS_PASS
    stats show-legends
    stats show-node
EOF

    check_result "HAProxy配置"
}

# 配置Keepalived
configure_keepalived() {
    log_info "配置Keepalived..."

    # 创建健康检查脚本
    cat > /etc/keepalived/check_haproxy.sh << 'EOF'
#!/bin/bash
if ! pgrep haproxy > /dev/null; then
    exit 1
fi

if ! curl -s http://localhost:8404/stats > /dev/null; then
    exit 1
fi

if ! nc -z localhost 5000; then
    exit 1
fi

exit 0
EOF

    chmod +x /etc/keepalived/check_haproxy.sh

    # 创建状态变更通知脚本
    cat > /etc/keepalived/notify_master.sh << 'EOF'
#!/bin/bash
echo "$(date): Becoming MASTER" >> /var/log/keepalived/state.log
systemctl restart haproxy
EOF

    cat > /etc/keepalived/notify_backup.sh << 'EOF'
#!/bin/bash
echo "$(date): Becoming BACKUP" >> /var/log/keepalived/state.log
EOF

    cat > /etc/keepalived/notify_fault.sh << 'EOF'
#!/bin/bash
echo "$(date): Entering FAULT state" >> /var/log/keepalived/state.log
EOF

    chmod +x /etc/keepalived/notify_*.sh

    # 创建日志目录
    mkdir -p /var/log/keepalived

    # 创建Keepalived配置文件
    cat > /etc/keepalived/keepalived.conf << EOF
global_defs {
    notification_email {
        <EMAIL>
    }
    notification_email_from keepalived@$NODE_HOSTNAME
    smtp_server 127.0.0.1
    smtp_connect_timeout 30
    router_id $NODE_HOSTNAME
    vrrp_skip_check_adv_addr
    vrrp_strict
    vrrp_garp_interval 0
    vrrp_gna_interval 0
}

vrrp_script chk_haproxy {
    script "/etc/keepalived/check_haproxy.sh"
    interval 2
    weight -2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state $NODE_ROLE
    interface $VIP_INTERFACE
    virtual_router_id $VRRP_ROUTER_ID
    priority $NODE_PRIORITY
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass $VRRP_PASSWORD
    }
    virtual_ipaddress {
        $VIP/24
    }
    track_script {
        chk_haproxy
    }
    notify_master "/etc/keepalived/notify_master.sh"
    notify_backup "/etc/keepalived/notify_backup.sh"
    notify_fault "/etc/keepalived/notify_fault.sh"
}
EOF

    check_result "Keepalived配置"
}

# 配置系统服务
configure_services() {
    log_info "配置系统服务..."

    # 启用服务
    systemctl enable haproxy
    systemctl enable keepalived

    # 配置rsyslog支持HAProxy日志
    cat >> /etc/rsyslog.conf << 'EOF'

# HAProxy日志配置
$ModLoad imudp
$UDPServerRun 514
$UDPServerAddress 127.0.0.1
local0.*    /var/log/haproxy/haproxy.log
& stop
EOF

    # 创建HAProxy日志目录
    mkdir -p /var/log/haproxy
    chown haproxy:haproxy /var/log/haproxy

    # 重启rsyslog
    systemctl restart rsyslog

    check_result "系统服务配置"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."

    mkdir -p "$BASE_DIR/haproxy/scripts"

    # 创建状态检查脚本
    cat > "$BASE_DIR/haproxy/scripts/check_status.sh" << 'EOF'
#!/bin/bash
echo "=== HAProxy集群状态检查 ==="
echo "检查时间: $(date)"
echo

echo "1. HAProxy服务状态:"
systemctl status haproxy --no-pager -l

echo
echo "2. Keepalived服务状态:"
systemctl status keepalived --no-pager -l

echo
echo "3. 虚拟IP状态:"
ip addr show | grep \$HAPROXY_VIP

echo
echo "4. 端口监听状态:"
ss -tuln | grep -E ":(5000|5001|5002|8404)"

echo
echo "5. HAProxy统计信息:"
curl -s http://localhost:8404/stats | grep -E "(pg-node|FRONTEND|BACKEND)" | head -10

echo "=== 检查完成 ==="
EOF

    chmod +x "$BASE_DIR/haproxy/scripts/check_status.sh"

    check_result "管理脚本创建"
}

# 主安装函数
main() {
    log_info "开始HAProxy节点安装..."

    # 检测节点信息
    detect_node_info

    # 安装软件包
    install_haproxy_keepalived

    # 配置HAProxy
    configure_haproxy

    # 配置Keepalived
    configure_keepalived

    # 配置系统服务
    configure_services

    # 创建管理脚本
    create_management_scripts

    log_info "HAProxy节点安装完成！"
    log_info "请在PostgreSQL集群启动后，启动HAProxy服务："
    log_info "1. 启动HAProxy: systemctl start haproxy"
    log_info "2. 启动Keepalived: systemctl start keepalived"
    log_info "3. 检查状态: $BASE_DIR/haproxy/scripts/check_status.sh"
    log_info "4. 访问统计页面: http://$VIP:$HAPROXY_STATS_PORT/stats"
}

# 执行主函数
main "$@"
```

## 3. 部署步骤

### 3.1 环境准备

```bash
# ============================================================================
# 所有节点执行 - 环境准备
# ============================================================================

# 1. 配置主机名解析 (所有节点) - 根据实际IP修改
cat >> /etc/hosts << 'EOF'
# PostgreSQL集群节点
************ pg-node1
************ pg-node2
************ pg-node3

# HAProxy节点
************ haproxy-1
************ haproxy-2
************* haproxy-vip

# 集群服务域名
************* postgres-cluster-write
************* postgres-cluster-read
EOF

# 或者使用变量方式 (推荐)
cat >> /etc/hosts << EOF
# PostgreSQL集群节点
${PG_NODE1_IP} pg-node1
${PG_NODE2_IP} pg-node2
${PG_NODE3_IP} pg-node3

# HAProxy节点
${HAPROXY_NODE1_IP} haproxy-1
${HAPROXY_NODE2_IP} haproxy-2
${HAPROXY_VIP} haproxy-vip

# 集群服务域名
${HAPROXY_VIP} postgres-cluster-write
${HAPROXY_VIP} postgres-cluster-read
EOF

# 2. 关闭防火墙和SELinux (测试环境)
systemctl stop firewalld
systemctl disable firewalld
setenforce 0
sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config

# 3. 配置时间同步
dnf install -y chrony
systemctl enable chronyd
systemctl start chronyd
```

### 3.2 PostgreSQL节点部署

```bash
# ============================================================================
# PostgreSQL节点部署 (在3台PostgreSQL节点上执行)
# ============================================================================

# 1. 下载安装脚本
wget -O install_postgresql_node.sh https://your-domain.com/install_postgresql_node.sh

# 2. 修改脚本配置参数 (根据实际环境调整)
vim install_postgresql_node.sh

# 重点修改脚本开头的网络配置部分:
# ============================================================================
# 网络配置 - 根据实际环境修改IP地址
# ============================================================================
# export PG_NODE1_IP="************"                 # PostgreSQL节点1 IP
# export PG_NODE2_IP="************"                 # PostgreSQL节点2 IP
# export PG_NODE3_IP="************"                 # PostgreSQL节点3 IP
# export HAPROXY_NODE1_IP="************"            # HAProxy节点1 IP
# export HAPROXY_NODE2_IP="************"            # HAProxy节点2 IP
# export HAPROXY_VIP="*************"                # HAProxy虚拟IP
# export NETWORK_SEGMENT="***********/24"           # 网络段

# 其他可选修改:
# - 密码配置: POSTGRES_PASSWORD, REPLICATOR_PASSWORD等
# - 目录配置: 如果不使用/home目录，需要修改BASE_DIR
# - 端口配置: PG_PORT, PATRONI_PORT等

# 3. 执行安装脚本
chmod +x install_postgresql_node.sh
./install_postgresql_node.sh

# 4. 检查安装结果
echo "检查PostgreSQL安装:"
/usr/local/pgsql/bin/postgres --version

echo "检查etcd安装:"
etcd --version

echo "检查Patroni安装:"
patroni --version
```

### 3.3 HAProxy节点部署

```bash
# ============================================================================
# HAProxy节点部署 (在2台HAProxy节点上执行)
# ============================================================================

# 1. 下载安装脚本
wget -O install_haproxy_node.sh https://your-domain.com/install_haproxy_node.sh

# 2. 修改脚本配置参数
vim install_haproxy_node.sh

# 重点修改脚本开头的网络配置部分 (与PostgreSQL脚本保持一致):
# ============================================================================
# 网络配置 - 根据实际环境修改IP地址
# ============================================================================
# export PG_NODE1_IP="************"                 # PostgreSQL节点1 IP
# export PG_NODE2_IP="************"                 # PostgreSQL节点2 IP
# export PG_NODE3_IP="************"                 # PostgreSQL节点3 IP
# export HAPROXY_NODE1_IP="************"            # HAProxy节点1 IP
# export HAPROXY_NODE2_IP="************"            # HAProxy节点2 IP
# export HAPROXY_VIP="*************"                # HAProxy虚拟IP
# export NETWORK_SEGMENT="***********/24"           # 网络段
# export VIP_INTERFACE="eth0"                       # 网络接口名称

# 其他可选修改:
# - HAProxy统计页面: HAPROXY_STATS_USER, HAPROXY_STATS_PASS
# - 端口配置: PG_WRITE_PORT, PG_READ_PORT等

# 3. 执行安装脚本
chmod +x install_haproxy_node.sh
./install_haproxy_node.sh

# 4. 检查安装结果
haproxy -v
keepalived --version
```

### 3.4 启动集群服务

```bash
# ============================================================================
# 启动集群服务 (按顺序执行)
# ============================================================================

# 第一步: 启动etcd集群 (在所有PostgreSQL节点执行)
systemctl start etcd
systemctl enable etcd

# 等待etcd集群启动完成
sleep 10

# 验证etcd集群状态 (使用配置的IP地址)
etcdctl --endpoints=http://${PG_NODE1_IP}:2379,http://${PG_NODE2_IP}:2379,http://${PG_NODE3_IP}:2379 endpoint health

# 第二步: 启动Patroni集群 (先启动主库节点)
# 在pg-node1上执行:
systemctl start patroni
systemctl enable patroni

# 等待主库初始化完成
sleep 60

# 在pg-node2和pg-node3上执行:
systemctl start patroni
systemctl enable patroni

# 等待从库同步完成
sleep 30

# 验证PostgreSQL集群状态
patronictl -c /home/<USER>/config/patroni.yml list

# 第三步: 启动HAProxy服务 (在HAProxy节点执行)
systemctl start haproxy
systemctl enable haproxy

systemctl start keepalived
systemctl enable keepalived

# 验证HAProxy状态 (使用配置的VIP地址)
curl http://${HAPROXY_VIP}:8404/stats
```

## 4. 验证测试

### 4.1 集群状态验证

```bash
# ============================================================================
# 集群状态验证
# ============================================================================

# 1. 检查PostgreSQL集群状态
patronictl -c /home/<USER>/config/patroni.yml list

# 预期输出:
# + Cluster: postgres-cluster (7234567890123456789) -----+----+-----------+
# | Member   | Host         | Role    | State   | TL | Lag in MB |
# +----------+--------------+---------+---------+----+-----------+
# | pg-node1 | ************ | Leader  | running |  1 |           |
# | pg-node2 | ************ | Replica | running |  1 |         0 |
# | pg-node3 | ************ | Replica | running |  1 |         0 |
# +----------+--------------+---------+---------+----+-----------+

# 2. 检查etcd集群状态 (使用配置的IP地址)
etcdctl --endpoints=http://${PG_NODE1_IP}:2379,http://${PG_NODE2_IP}:2379,http://${PG_NODE3_IP}:2379 endpoint health

# 3. 检查HAProxy状态 (使用配置的VIP地址)
curl -s http://${HAPROXY_VIP}:8404/stats | grep -E "(pg-node|FRONTEND|BACKEND)"

# 4. 检查虚拟IP (使用配置的VIP地址)
ip addr show | grep ${HAPROXY_VIP}
```

### 4.2 功能测试

```bash
# ============================================================================
# 功能测试
# ============================================================================

# 1. 测试写入功能 (连接主库) - 使用配置的VIP地址
psql -h ${HAPROXY_VIP} -p 5000 -U postgres -d postgres -c "
CREATE TABLE test_cluster (
    id SERIAL PRIMARY KEY,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO test_cluster (content) VALUES ('Cluster test data');
SELECT * FROM test_cluster;
"

# 2. 测试读取功能 (连接从库) - 使用配置的VIP地址
psql -h ${HAPROXY_VIP} -p 5001 -U postgres -d postgres -c "
SELECT * FROM test_cluster;
SELECT pg_is_in_recovery() as is_standby;
"

# 3. 测试故障转移
# 在主库节点执行:
systemctl stop patroni

# 观察自动切换过程
watch -n 1 "patronictl -c /home/<USER>/config/patroni.yml list"

# 验证新主库可写入 - 使用配置的VIP地址
psql -h ${HAPROXY_VIP} -p 5000 -U postgres -d postgres -c "
INSERT INTO test_cluster (content) VALUES ('After failover test');
SELECT * FROM test_cluster ORDER BY id DESC LIMIT 3;
"

# 恢复原节点
systemctl start patroni
```

## 5. 日常管理

### 5.1 常用管理命令

```bash
# ============================================================================
# PostgreSQL集群管理命令
# ============================================================================

# 查看集群状态
patronictl -c /home/<USER>/config/patroni.yml list

# 手动切换主库
patronictl -c /home/<USER>/config/patroni.yml switchover postgres-cluster

# 重启指定节点
patronictl -c /home/<USER>/config/patroni.yml restart postgres-cluster pg-node2

# 暂停自动故障转移
patronictl -c /home/<USER>/config/patroni.yml pause postgres-cluster

# 恢复自动故障转移
patronictl -c /home/<USER>/config/patroni.yml resume postgres-cluster

# 查看集群配置
patronictl -c /home/<USER>/config/patroni.yml show-config

# 编辑集群配置
patronictl -c /home/<USER>/config/patroni.yml edit-config postgres-cluster
```

### 5.2 监控脚本

```bash
# ============================================================================
# 集群监控脚本
# ============================================================================

# PostgreSQL节点监控
/home/<USER>/scripts/check_cluster.sh

# HAProxy节点监控
/home/<USER>/scripts/check_status.sh

# 设置定时监控 (可选)
cat > /etc/cron.d/postgres-monitor << 'EOF'
# PostgreSQL集群监控
*/5 * * * * postgres /home/<USER>/scripts/check_cluster.sh >> /home/<USER>/logs/monitor.log 2>&1
*/2 * * * * root /home/<USER>/scripts/check_status.sh >> /var/log/haproxy/monitor.log 2>&1
EOF
```

## 6. 故障处理

### 6.1 常见问题

```bash
# ============================================================================
# 常见问题处理
# ============================================================================

# 问题1: etcd启动失败
# 解决方案:
# 1. 检查配置文件
etcd --config-file /etc/etcd.conf --print-config

# 2. 检查数据目录权限
chown -R postgres:postgres /home/<USER>/data

# 3. 清理数据重新初始化 (谨慎操作)
systemctl stop etcd
rm -rf /home/<USER>/data/*
systemctl start etcd

# 问题2: Patroni无法连接etcd
# 解决方案:
# 1. 检查etcd服务状态
systemctl status etcd

# 2. 测试etcd连接 (使用配置的IP地址)
etcdctl --endpoints=http://${PG_NODE1_IP}:2379 endpoint health

# 3. 检查网络连通性 (使用配置的IP地址)
telnet ${PG_NODE1_IP} 2379

# 问题3: HAProxy健康检查失败
# 解决方案:
# 1. 手动测试健康检查 (使用配置的IP地址)
curl -I http://${PG_NODE1_IP}:8008/master
curl -I http://${PG_NODE2_IP}:8008/replica

# 2. 检查Patroni REST API
systemctl status patroni

# 3. 调整健康检查参数
# 在haproxy.cfg中修改: inter 5s fall 5 rise 2
```

## 7. 总结

### 7.1 方案特点

- **一键部署**: 通过脚本自动化安装，减少人工错误
- **源码编译**: 使用PostgreSQL 17.5最新版本，性能更优
- **目录统一**: 所有数据和日志统一放在/home目录，便于管理
- **配置灵活**: 脚本开头集中配置，易于定制
- **功能完整**: 包含监控、备份、故障处理等完整功能

### 7.2 适用场景

- 中小型企业PostgreSQL集群快速部署
- 开发测试环境搭建
- 学习PostgreSQL高可用技术
- 需要定制化配置的生产环境

### 7.3 注意事项

- 生产环境使用前请充分测试
- 根据实际硬件配置调整PostgreSQL参数
- 定期备份数据和配置文件
- 监控集群健康状态
- 及时更新安全补丁

通过本方案，可以快速部署一个基于PostgreSQL 17.5的高可用数据库集群，为应用提供稳定可靠的数据服务。
