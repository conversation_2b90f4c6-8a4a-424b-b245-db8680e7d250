# PostgreSQL高可用集群部署方案

## 1. 架构概述

### 1.1 整体架构图
```
                    ┌─────────────────┐
                    │   应用服务器     │
                    │   (Java应用)    │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   HAProxy       │
                    │  (负载均衡器)    │
                    │  读写分离+故障转移│
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
   ┌────▼────┐          ┌────▼────┐          ┌────▼────┐
   │PostgreSQL│          │PostgreSQL│          │PostgreSQL│
   │+ Patroni │          │+ Patroni │          │+ <PERSON><PERSON><PERSON> │
   │ (Primary)│◄────────►│(Standby) │◄────────►│(Standby) │
   │ 主库-写入 │   流复制   │ 从库-读取 │   流复制   │ 从库-读取 │
   └────┬────┘          └────┬────┘          └────┬────┘
        │                    │                    │
        └────────────────────┼────────────────────┘
                             │
                    ┌────────▼────────┐
                    │   etcd 集群      │
                    │ (分布式配置中心)  │
                    │ 存储集群状态信息  │
                    └─────────────────┘
```

### 1.2 核心组件说明

| 组件 | 作用 | 部署数量 | 说明 |
|------|------|----------|------|
| **PostgreSQL** | 核心数据库服务 | 3个节点 | 1主2从，采用流复制实现数据同步 |
| **Patroni** | 高可用管理工具 | 3个节点 | Python编写，负责自动故障检测和切换 |
| **etcd** | 分布式配置存储 | 3个节点 | 存储集群配置、状态信息和选主信息 |
| **HAProxy** | 负载均衡器 | 2个节点 | 提供读写分离、连接池和健康检查 |

### 1.3 技术特性

- **高可用性**: 主库故障时自动切换到从库，RTO < 30秒
- **读写分离**: 写操作路由到主库，读操作分发到从库
- **数据一致性**: 支持同步/异步复制模式
- **自动故障恢复**: 故障节点恢复后自动加入集群
- **零停机维护**: 支持滚动升级和在线扩容

## 2. 环境准备

### 2.1 硬件配置要求

#### 测试环境配置
```
CPU: 4核心 (Intel/AMD x86_64)
内存: 8GB RAM
存储: 100GB SSD (建议NVMe)
网络: 千兆网卡
操作系统: CentOS 8+ / RHEL 8+ / Ubuntu 20.04+
```

#### 生产环境推荐配置
```
CPU: 16核心以上 (支持超线程)
内存: 64GB RAM以上
存储:
  - 数据盘: 1TB+ NVMe SSD (RAID 10)
  - WAL日志盘: 200GB+ NVMe SSD (独立磁盘)
  - 备份盘: 2TB+ SATA (RAID 1)
网络: 万兆网卡 (双网卡绑定)
```

### 2.2 服务器规划

```bash
# 集群节点规划 (最小化3+3+2架构)
节点类型          主机名           IP地址        角色说明
pg-node1         pg-master       ************  PostgreSQL Primary + Patroni
pg-node2         pg-standby1     ************  PostgreSQL Standby + Patroni
pg-node3         pg-standby2     ************  PostgreSQL Standby + Patroni
etcd-node1       etcd-1          ************  etcd集群节点1 (Leader候选)
etcd-node2       etcd-2          ************  etcd集群节点2 (Follower)
etcd-node3       etcd-3          ************  etcd集群节点3 (Follower)
haproxy-node1    haproxy-master  ************  HAProxy主节点 + Keepalived
haproxy-node2    haproxy-backup  ************  HAProxy备节点 + Keepalived

# 虚拟IP (VIP) 规划
haproxy-vip      haproxy-vip     ************* HAProxy虚拟IP (对外服务)
```

### 2.3 网络端口规划

```bash
# PostgreSQL相关端口
5432    # PostgreSQL数据库服务端口
8008    # Patroni REST API端口 (健康检查)

# etcd相关端口
2379    # etcd客户端通信端口
2380    # etcd节点间通信端口

# HAProxy相关端口
5000    # PostgreSQL写入端口 (主库)
5001    # PostgreSQL读取端口 (从库)
8404    # HAProxy统计页面端口
```

### 2.4 系统环境配置

```bash
# ============================================================================
# 在所有节点上执行以下配置
# ============================================================================

# 1. 设置主机名 (每个节点设置对应的主机名)
hostnamectl set-hostname pg-master    # 在pg-node1上执行
hostnamectl set-hostname pg-standby1  # 在pg-node2上执行
hostnamectl set-hostname pg-standby2  # 在pg-node3上执行
# ... 其他节点类似

# 2. 配置主机名解析
cat >> /etc/hosts << 'EOF'
# PostgreSQL集群节点
************ pg-master pg-node1
************ pg-standby1 pg-node2
************ pg-standby2 pg-node3

# etcd集群节点
************ etcd-1 etcd-node1
************ etcd-2 etcd-node2
************ etcd-3 etcd-node3

# HAProxy节点
************ haproxy-master haproxy-node1
************ haproxy-backup haproxy-node2
************* haproxy-vip

# 集群服务域名 (应用连接使用)
************* postgres-cluster-write
************* postgres-cluster-read
EOF

# 3. 关闭防火墙和SELinux (测试环境)
systemctl stop firewalld
systemctl disable firewalld
setenforce 0
sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config

# 4. 配置时间同步
dnf install -y chrony
systemctl enable chronyd
systemctl start chronyd

# 5. 系统内核参数优化 (PostgreSQL专用)
cat >> /etc/sysctl.conf << 'EOF'
# ============================================================================
# PostgreSQL数据库系统优化参数
# ============================================================================

# 共享内存配置 (根据实际内存大小调整)
kernel.shmmax = 68719476736          # 最大共享内存段大小 (64GB)
kernel.shmall = 4294967296           # 系统共享内存总量 (页数)
kernel.shmmni = 4096                 # 最大共享内存段数量

# 信号量配置
kernel.sem = 50100 64128000 50100 1280  # 信号量参数

# 文件句柄限制
fs.file-max = 7672460                # 系统最大文件句柄数

# 网络参数优化
net.ipv4.ip_local_port_range = 9000 65000    # 本地端口范围
net.core.rmem_default = 262144               # 默认接收缓冲区大小
net.core.rmem_max = 4194304                  # 最大接收缓冲区大小
net.core.wmem_default = 262144               # 默认发送缓冲区大小
net.core.wmem_max = 1048576                  # 最大发送缓冲区大小
net.core.netdev_max_backlog = 5000           # 网络设备队列长度

# 虚拟内存管理
vm.swappiness = 1                    # 降低swap使用倾向
vm.dirty_background_ratio = 3        # 后台写入比例
vm.dirty_ratio = 10                  # 强制写入比例
vm.dirty_expire_centisecs = 500      # 脏页过期时间
vm.dirty_writeback_centisecs = 100   # 脏页写回间隔
EOF

# 应用内核参数
sysctl -p

# 6. 用户资源限制配置
cat >> /etc/security/limits.conf << 'EOF'
# PostgreSQL用户资源限制
postgres soft nofile 65536
postgres hard nofile 65536
postgres soft nproc 32768
postgres hard nproc 32768
postgres soft memlock unlimited
postgres hard memlock unlimited
EOF

# 7. 创建必要的目录结构
mkdir -p /data/postgresql/{15/data,archive,backup,scripts}
mkdir -p /data/patroni
mkdir -p /var/log/postgresql
mkdir -p /var/run/postgresql

# 8. 安装基础软件包
dnf update -y
dnf install -y wget curl vim git htop iotop net-tools telnet
```

## 3. 软件安装

### 3.1 PostgreSQL 15 安装 (所有PostgreSQL节点)

```bash
# ============================================================================
# PostgreSQL 15 数据库安装配置
# ============================================================================

# 1. 添加PostgreSQL官方YUM源
dnf install -y https://download.postgresql.org/pub/repos/yum/reporpms/EL-8-x86_64/pgdg-redhat-repo-latest.noarch.rpm

# 2. 安装PostgreSQL 15
dnf install -y postgresql15-server postgresql15-contrib postgresql15-devel

# 3. 创建postgres用户的SSH密钥 (用于节点间免密登录)
su - postgres -c "ssh-keygen -t rsa -N '' -f ~/.ssh/id_rsa"

# 4. 配置postgres用户环境变量
cat >> /var/lib/pgsql/.bashrc << 'EOF'
# PostgreSQL环境变量配置
export PGDATA=/data/postgresql/15/data
export PGPORT=5432
export PGUSER=postgres
export PGHOME=/usr/pgsql-15
export PATH=$PGHOME/bin:$PATH
export LD_LIBRARY_PATH=$PGHOME/lib:$LD_LIBRARY_PATH
export MANPATH=$PGHOME/share/man:$MANPATH

# Patroni环境变量
export PATRONI_CONFIG_FILE=/etc/patroni/patroni.yml
EOF

# 5. 设置目录权限
chown -R postgres:postgres /data/postgresql
chown -R postgres:postgres /var/log/postgresql
chown -R postgres:postgres /var/run/postgresql
chmod 700 /data/postgresql/15/data
```

### 3.2 Python和Patroni安装 (所有PostgreSQL节点)

```bash
# ============================================================================
# Python 3 和 Patroni 高可用组件安装
# ============================================================================

# 1. 安装Python3开发环境
dnf install -y python3 python3-pip python3-devel gcc gcc-c++ make

# 2. 升级pip到最新版本
python3 -m pip install --upgrade pip

# 3. 安装Patroni及其依赖 (支持etcd后端)
pip3 install patroni[etcd]==3.1.0
pip3 install psycopg2-binary==2.9.7
pip3 install python-etcd==0.4.5

# 4. 创建Patroni配置和数据目录
mkdir -p /etc/patroni
mkdir -p /data/patroni
mkdir -p /var/log/patroni

# 5. 设置目录权限
chown -R postgres:postgres /data/patroni
chown -R postgres:postgres /var/log/patroni
chown -R postgres:postgres /etc/patroni

# 6. 创建Patroni日志轮转配置
cat > /etc/logrotate.d/patroni << 'EOF'
/var/log/patroni/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 postgres postgres
    postrotate
        /bin/kill -HUP `cat /var/run/patroni.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
EOF
```

### 3.3 etcd集群安装 (etcd节点)

```bash
# ============================================================================
# etcd 分布式配置存储安装
# ============================================================================

# 1. 下载并安装etcd (使用最新稳定版本)
ETCD_VER=v3.5.9
DOWNLOAD_URL=https://github.com/etcd-io/etcd/releases/download

# 下载etcd二进制文件
wget ${DOWNLOAD_URL}/${ETCD_VER}/etcd-${ETCD_VER}-linux-amd64.tar.gz
tar xzf etcd-${ETCD_VER}-linux-amd64.tar.gz

# 安装到系统目录
cp etcd-${ETCD_VER}-linux-amd64/etcd* /usr/local/bin/
chmod +x /usr/local/bin/etcd*

# 清理下载文件
rm -rf etcd-${ETCD_VER}-linux-amd64*

# 2. 创建etcd系统用户
useradd -r -s /bin/false etcd

# 3. 创建etcd数据和配置目录
mkdir -p /var/lib/etcd
mkdir -p /etc/etcd
mkdir -p /var/log/etcd

# 4. 设置目录权限
chown -R etcd:etcd /var/lib/etcd
chown -R etcd:etcd /etc/etcd
chown -R etcd:etcd /var/log/etcd

# 5. 验证etcd安装
/usr/local/bin/etcd --version
/usr/local/bin/etcdctl version
```

### 3.4 HAProxy负载均衡器安装 (HAProxy节点)

```bash
# ============================================================================
# HAProxy 负载均衡器和 Keepalived 高可用安装
# ============================================================================

# 1. 安装HAProxy和Keepalived
dnf install -y haproxy keepalived

# 2. 备份原始配置文件
cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.bak
cp /etc/keepalived/keepalived.conf /etc/keepalived/keepalived.conf.bak

# 3. 创建HAProxy日志目录
mkdir -p /var/log/haproxy
chown haproxy:haproxy /var/log/haproxy

# 4. 配置rsyslog支持HAProxy日志
cat >> /etc/rsyslog.conf << 'EOF'
# HAProxy日志配置
$ModLoad imudp
$UDPServerRun 514
$UDPServerAddress 127.0.0.1
local0.*    /var/log/haproxy/haproxy.log
& stop
EOF

# 重启rsyslog服务
systemctl restart rsyslog

# 5. 创建HAProxy统计页面用户认证文件
echo "admin:$(openssl passwd -1 'admin123')" > /etc/haproxy/stats.passwd
chown haproxy:haproxy /etc/haproxy/stats.passwd
chmod 600 /etc/haproxy/stats.passwd
```

## 4. 详细配置

### 4.1 etcd集群配置

#### 4.1.1 etcd-node1配置 (/etc/etcd/etcd.conf)

```yaml
# ============================================================================
# etcd集群节点1配置文件
# ============================================================================

# 节点基本信息
name: etcd-1                                    # 当前节点名称
data-dir: /var/lib/etcd                        # 数据存储目录

# 客户端通信配置
listen-client-urls: http://************:2379   # 监听客户端请求的URL
advertise-client-urls: http://************:2379 # 向客户端通告的URL

# 节点间通信配置
listen-peer-urls: http://************:2380     # 监听节点间通信的URL
initial-advertise-peer-urls: http://************:2380 # 向其他节点通告的URL

# 集群配置
initial-cluster: etcd-1=http://************:2380,etcd-2=http://************:2380,etcd-3=http://************:2380
initial-cluster-token: postgres-etcd-cluster    # 集群令牌，防止不同集群混淆
initial-cluster-state: new                     # 集群初始状态 (new/existing)

# 日志配置
log-level: info                                 # 日志级别
log-outputs: [/var/log/etcd/etcd.log]          # 日志输出文件

# 性能调优
heartbeat-interval: 100                        # 心跳间隔 (毫秒)
election-timeout: 1000                         # 选举超时 (毫秒)
max-snapshots: 5                              # 最大快照保留数量
max-wals: 5                                    # 最大WAL文件保留数量

# 安全配置 (生产环境建议启用TLS)
# client-cert-auth: false
# trusted-ca-file: /etc/etcd/ca.pem
# cert-file: /etc/etcd/etcd.pem
# key-file: /etc/etcd/etcd-key.pem
```

#### 4.1.2 etcd-node2配置 (/etc/etcd/etcd.conf)

```yaml
# ============================================================================
# etcd集群节点2配置文件 (与节点1类似，仅IP地址不同)
# ============================================================================

name: etcd-2
data-dir: /var/lib/etcd
listen-client-urls: http://************:2379
advertise-client-urls: http://************:2379
listen-peer-urls: http://************:2380
initial-advertise-peer-urls: http://************:2380
initial-cluster: etcd-1=http://************:2380,etcd-2=http://************:2380,etcd-3=http://************:2380
initial-cluster-token: postgres-etcd-cluster
initial-cluster-state: new
log-level: info
log-outputs: [/var/log/etcd/etcd.log]
heartbeat-interval: 100
election-timeout: 1000
max-snapshots: 5
max-wals: 5
```

#### 4.1.3 etcd-node3配置 (/etc/etcd/etcd.conf)

```yaml
# ============================================================================
# etcd集群节点3配置文件
# ============================================================================

name: etcd-3
data-dir: /var/lib/etcd
listen-client-urls: http://************:2379
advertise-client-urls: http://************:2379
listen-peer-urls: http://************:2380
initial-advertise-peer-urls: http://************:2380
initial-cluster: etcd-1=http://************:2380,etcd-2=http://************:2380,etcd-3=http://************:2380
initial-cluster-token: postgres-etcd-cluster
initial-cluster-state: new
log-level: info
log-outputs: [/var/log/etcd/etcd.log]
heartbeat-interval: 100
election-timeout: 1000
max-snapshots: 5
max-wals: 5
```

#### 4.1.4 etcd systemd服务文件 (/etc/systemd/system/etcd.service)

```ini
# ============================================================================
# etcd系统服务配置文件
# ============================================================================

[Unit]
Description=etcd key-value store
Documentation=https://github.com/etcd-io/etcd
After=network.target
Wants=network-online.target

[Service]
Type=notify                                     # 服务类型，支持systemd通知
User=etcd                                       # 运行用户
Group=etcd                                      # 运行用户组
ExecStart=/usr/local/bin/etcd --config-file /etc/etcd/etcd.conf  # 启动命令
Restart=always                                  # 自动重启策略
RestartSec=10s                                  # 重启间隔时间
LimitNOFILE=40000                              # 文件句柄限制

# 安全配置
NoNewPrivileges=true                           # 禁止获取新权限
ProtectHome=true                               # 保护家目录
ProtectSystem=strict                           # 保护系统目录
ReadWritePaths=/var/lib/etcd /var/log/etcd     # 可写路径

[Install]
WantedBy=multi-user.target                     # 启动目标
```

### 4.2 Patroni高可用配置

#### 4.2.1 pg-node1 (主库) Patroni配置 (/etc/patroni/patroni.yml)

```yaml
# ============================================================================
# Patroni PostgreSQL高可用配置文件 - 主库节点
# ============================================================================

# 集群基本信息
scope: postgres-cluster                         # 集群名称，所有节点必须相同
namespace: /db/                                # etcd中的命名空间
name: pg-node1                                 # 当前节点名称，集群内唯一

# Patroni REST API配置
restapi:
  listen: ************:8008                    # REST API监听地址
  connect_address: ************:8008           # REST API连接地址

# etcd连接配置
etcd:
  hosts: ************:2379,************:2379,************:2379  # etcd集群地址列表

# 集群引导配置 (仅在首次初始化时使用)
bootstrap:
  # 分布式配置存储参数
  dcs:
    ttl: 30                                     # 领导者TTL时间 (秒)
    loop_wait: 10                              # 主循环等待时间 (秒)
    retry_timeout: 30                          # 重试超时时间 (秒)
    maximum_lag_on_failover: 1048576           # 故障转移时允许的最大延迟 (字节)
    master_start_timeout: 300                  # 主库启动超时时间 (秒)
    synchronous_mode: false                    # 是否启用同步复制模式

    # PostgreSQL配置参数
    postgresql:
      use_pg_rewind: true                      # 启用pg_rewind进行快速恢复
      use_slots: true                          # 启用复制槽

      # PostgreSQL核心参数配置
      parameters:
        # 连接和认证
        max_connections: 200                   # 最大连接数
        superuser_reserved_connections: 3      # 超级用户保留连接数

        # 内存配置 (根据服务器内存调整)
        shared_buffers: 256MB                  # 共享缓冲区大小
        effective_cache_size: 1GB              # 有效缓存大小
        work_mem: 4MB                          # 工作内存
        maintenance_work_mem: 64MB             # 维护工作内存

        # WAL配置
        wal_level: replica                     # WAL级别，支持流复制
        wal_buffers: 16MB                      # WAL缓冲区大小
        min_wal_size: 1GB                      # 最小WAL大小
        max_wal_size: 4GB                      # 最大WAL大小
        wal_log_hints: on                      # 启用WAL日志提示

        # 复制配置
        max_wal_senders: 10                    # 最大WAL发送进程数
        max_replication_slots: 10              # 最大复制槽数量
        hot_standby: on                        # 启用热备模式

        # 检查点配置
        checkpoint_completion_target: 0.9      # 检查点完成目标
        checkpoint_timeout: 5min               # 检查点超时时间

        # 归档配置
        archive_mode: on                       # 启用归档模式
        archive_command: 'test ! -f /data/postgresql/archive/%f && cp %p /data/postgresql/archive/%f'

        # 日志配置
        logging_collector: on                  # 启用日志收集器
        log_destination: 'csvlog'              # 日志格式
        log_directory: 'log'                   # 日志目录
        log_filename: 'postgresql-%Y-%m-%d_%H%M%S.log'  # 日志文件名格式
        log_rotation_age: 1d                   # 日志轮转时间
        log_rotation_size: 100MB               # 日志轮转大小
        log_min_duration_statement: 1000      # 记录慢查询阈值 (毫秒)
        log_checkpoints: on                    # 记录检查点
        log_connections: on                    # 记录连接
        log_disconnections: on                 # 记录断开连接
        log_lock_waits: on                     # 记录锁等待
        log_temp_files: 0                      # 记录临时文件

        # 性能优化
        random_page_cost: 1.1                  # 随机页面成本 (SSD优化)
        effective_io_concurrency: 200          # 有效IO并发数
        max_worker_processes: 8                # 最大工作进程数
        max_parallel_workers_per_gather: 4     # 每个Gather节点的最大并行工作进程数
        max_parallel_workers: 8                # 最大并行工作进程数
        max_parallel_maintenance_workers: 4    # 最大并行维护工作进程数

        # 统计信息
        default_statistics_target: 100         # 默认统计信息目标

        # 其他优化
        unix_socket_directories: '/var/run/postgresql'  # Unix套接字目录

  # 数据库初始化配置
  initdb:
  - encoding: UTF8                             # 数据库编码
  - data-checksums                             # 启用数据校验和
  - locale: C                                  # 数据库区域设置

  # pg_hba.conf配置 (客户端认证)
  pg_hba:
  - host replication replicator 127.0.0.1/32 md5      # 本地复制连接
  - host replication replicator ***********/24 md5    # 集群内复制连接
  - host all all 0.0.0.0/0 md5                        # 所有客户端连接

  # 初始用户配置
  users:
    admin:                                     # 管理员用户
      password: 'AdminPass123!'                # 管理员密码 (生产环境请修改)
      options:
        - createrole                           # 可创建角色
        - createdb                             # 可创建数据库

# PostgreSQL实例配置
postgresql:
  listen: ************:5432                   # PostgreSQL监听地址
  connect_address: ************:5432          # PostgreSQL连接地址
  data_dir: /data/postgresql/15/data           # 数据目录
  bin_dir: /usr/pgsql-15/bin                   # 二进制文件目录
  pgpass: /tmp/pgpass0                         # 密码文件路径

  # 认证配置
  authentication:
    replication:                               # 复制用户认证
      username: replicator                     # 复制用户名
      password: 'ReplicatorPass123!'           # 复制用户密码
    superuser:                                 # 超级用户认证
      username: postgres                       # 超级用户名
      password: 'PostgresPass123!'             # 超级用户密码
    rewind:                                    # pg_rewind用户认证
      username: rewind_user                    # rewind用户名
      password: 'RewindPass123!'               # rewind用户密码

  # PostgreSQL运行时参数
  parameters:
    unix_socket_directories: '/var/run/postgresql'     # Unix套接字目录

# 节点标签配置 (用于控制节点行为)
tags:
    nofailover: false                          # 是否参与故障转移
    noloadbalance: false                       # 是否参与负载均衡
    clonefrom: false                           # 是否作为克隆源
    nosync: false                              # 是否参与同步复制
```

#### 4.2.2 pg-node2 (从库1) Patroni配置 (/etc/patroni/patroni.yml)

```yaml
# ============================================================================
# Patroni PostgreSQL高可用配置文件 - 从库节点1
# 与主库配置基本相同，仅修改节点特定信息
# ============================================================================

scope: postgres-cluster
namespace: /db/
name: pg-node2                                 # 节点名称改为pg-node2

restapi:
  listen: ************:8008                    # 修改为节点2的IP
  connect_address: ************:8008

etcd:
  hosts: ************:2379,************:2379,************:2379

# 从库节点不需要bootstrap配置，会自动从主库同步

postgresql:
  listen: ************:5432                    # 修改为节点2的IP
  connect_address: ************:5432
  data_dir: /data/postgresql/15/data
  bin_dir: /usr/pgsql-15/bin
  pgpass: /tmp/pgpass1                          # 修改pgpass文件名

  authentication:
    replication:
      username: replicator
      password: 'ReplicatorPass123!'
    superuser:
      username: postgres
      password: 'PostgresPass123!'
    rewind:
      username: rewind_user
      password: 'RewindPass123!'

  parameters:
    unix_socket_directories: '/var/run/postgresql'

tags:
    nofailover: false
    noloadbalance: false
    clonefrom: false
    nosync: false
```

#### 4.2.3 pg-node3 (从库2) Patroni配置 (/etc/patroni/patroni.yml)

```yaml
# ============================================================================
# Patroni PostgreSQL高可用配置文件 - 从库节点2
# ============================================================================

scope: postgres-cluster
namespace: /db/
name: pg-node3                                 # 节点名称改为pg-node3

restapi:
  listen: ************:8008                    # 修改为节点3的IP
  connect_address: ************:8008

etcd:
  hosts: ************:2379,************:2379,************:2379

postgresql:
  listen: ************:5432                    # 修改为节点3的IP
  connect_address: ************:5432
  data_dir: /data/postgresql/15/data
  bin_dir: /usr/pgsql-15/bin
  pgpass: /tmp/pgpass2                          # 修改pgpass文件名

  authentication:
    replication:
      username: replicator
      password: 'ReplicatorPass123!'
    superuser:
      username: postgres
      password: 'PostgresPass123!'
    rewind:
      username: rewind_user
      password: 'RewindPass123!'

  parameters:
    unix_socket_directories: '/var/run/postgresql'

tags:
    nofailover: false
    noloadbalance: false
    clonefrom: false
    nosync: false
```

#### 4.2.4 Patroni systemd服务文件 (/etc/systemd/system/patroni.service)

```ini
# ============================================================================
# Patroni系统服务配置文件
# ============================================================================

[Unit]
Description=Runners to orchestrate a high-availability PostgreSQL
Documentation=https://patroni.readthedocs.io/
After=syslog.target network.target etcd.service
Wants=network-online.target

[Service]
Type=simple                                    # 服务类型
User=postgres                                  # 运行用户
Group=postgres                                 # 运行用户组
Environment=PATRONI_CONFIG_FILE=/etc/patroni/patroni.yml  # 配置文件环境变量
ExecStart=/usr/local/bin/patroni /etc/patroni/patroni.yml  # 启动命令
ExecReload=/bin/kill -HUP $MAINPID            # 重载命令
KillMode=process                               # 终止模式
TimeoutSec=30                                  # 超时时间
Restart=no                                     # 重启策略 (由Patroni自己管理)

# 日志配置
StandardOutput=journal                         # 标准输出到journal
StandardError=journal                          # 标准错误到journal
SyslogIdentifier=patroni                       # 系统日志标识符

# 安全配置
NoNewPrivileges=true                           # 禁止获取新权限
ProtectHome=true                               # 保护家目录
ReadWritePaths=/data/postgresql /var/log/patroni /var/run/postgresql  # 可写路径

[Install]
WantedBy=multi-user.target                     # 启动目标
```

### 4.3 HAProxy负载均衡配置

#### 4.3.1 HAProxy主配置文件 (/etc/haproxy/haproxy.cfg)

```
# ============================================================================
# HAProxy PostgreSQL集群负载均衡配置
# ============================================================================

# 全局配置段
global
    # 基本配置
    maxconn 4000                               # 最大并发连接数
    log stdout local0                          # 日志配置
    chroot /var/lib/haproxy                    # chroot目录
    stats socket /run/haproxy/admin.sock mode 660 level admin  # 管理套接字
    stats timeout 30s                          # 统计超时时间
    user haproxy                               # 运行用户
    group haproxy                              # 运行用户组
    daemon                                     # 后台运行

    # SSL配置 (如果需要)
    # ssl-default-bind-ciphers ECDHE+aes128gcm:ECDHE+aes256gcm:ECDHE+aes128sha256:ECDHE+aes256sha384
    # ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

# 默认配置段
defaults
    mode tcp                                   # 工作模式 (TCP透传)
    timeout connect 5000ms                     # 连接超时时间
    timeout client 50000ms                     # 客户端超时时间
    timeout server 50000ms                     # 服务器超时时间
    log global                                 # 使用全局日志配置
    option tcplog                              # 启用TCP日志
    option dontlognull                         # 不记录空连接
    retries 3                                  # 重试次数

# PostgreSQL主库写入服务 (端口5000)
listen postgres_write
    bind *:5000                                # 绑定端口5000
    description "PostgreSQL Master Write Service"

    # 健康检查配置
    option httpchk GET /master                 # HTTP健康检查路径
    http-check expect status 200               # 期望的HTTP状态码

    # 服务器默认配置
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions

    # PostgreSQL服务器列表 (主库优先，从库作为备份)
    server pg-node1 ************:5432 maxconn 100 check port 8008
    server pg-node2 ************:5432 maxconn 100 check port 8008 backup
    server pg-node3 ************:5432 maxconn 100 check port 8008 backup

# PostgreSQL从库读取服务 (端口5001)
listen postgres_read
    bind *:5001                                # 绑定端口5001
    description "PostgreSQL Standby Read Service"
    balance roundrobin                         # 负载均衡算法

    # 健康检查配置 (检查从库状态)
    option httpchk GET /replica                # 检查从库健康状态
    http-check expect status 200

    # 服务器默认配置
    default-server inter 3s fall 3 rise 2 on-marked-down shutdown-sessions

    # PostgreSQL服务器列表 (所有节点都可以提供读服务)
    server pg-node1 ************:5432 maxconn 100 check port 8008
    server pg-node2 ************:5432 maxconn 100 check port 8008
    server pg-node3 ************:5432 maxconn 100 check port 8008

# HAProxy统计页面
listen stats
    bind *:8404                                # 统计页面端口
    description "HAProxy Statistics"
    stats enable                               # 启用统计页面
    stats uri /stats                           # 统计页面URI
    stats refresh 30s                          # 刷新间隔
    stats admin if TRUE                        # 启用管理功能
    stats auth admin:admin123                  # 认证用户名密码
    stats show-legends                         # 显示图例
    stats show-node                            # 显示节点信息

# PostgreSQL连接池服务 (可选，用于连接池)
listen postgres_pool
    bind *:5002                                # 连接池端口
    description "PostgreSQL Connection Pool"
    balance leastconn                          # 最少连接数负载均衡

    # 连接池特定配置
    option tcp-check                           # TCP检查
    tcp-check connect                          # TCP连接检查
    tcp-check send-binary 00000016             # PostgreSQL协议检查
    tcp-check expect binary 4e                 # 期望的响应

    # 服务器配置 (较大的连接数限制)
    server pg-node1 ************:5432 maxconn 200 check
    server pg-node2 ************:5432 maxconn 200 check
    server pg-node3 ************:5432 maxconn 200 check
```

#### 4.3.2 Keepalived高可用配置

**HAProxy主节点Keepalived配置 (/etc/keepalived/keepalived.conf)**:

```bash
# ============================================================================
# Keepalived高可用配置 - HAProxy主节点
# ============================================================================

global_defs {
    notification_email {
        <EMAIL>                      # 通知邮箱
    }
    notification_email_from keepalived@haproxy-master
    smtp_server 127.0.0.1                     # SMTP服务器
    smtp_connect_timeout 30                    # SMTP连接超时
    router_id HAPROXY_MASTER                   # 路由器ID
    vrrp_skip_check_adv_addr                   # 跳过广播地址检查
    vrrp_strict                                # 严格模式
    vrrp_garp_interval 0                       # GARP间隔
    vrrp_gna_interval 0                        # GNA间隔
}

# HAProxy健康检查脚本
vrrp_script chk_haproxy {
    script "/etc/keepalived/check_haproxy.sh"  # 检查脚本路径
    interval 2                                 # 检查间隔 (秒)
    weight -2                                  # 权重调整
    fall 3                                     # 失败次数阈值
    rise 2                                     # 恢复次数阈值
}

# VRRP实例配置
vrrp_instance VI_1 {
    state MASTER                               # 初始状态 (MASTER/BACKUP)
    interface eth0                             # 网络接口名称
    virtual_router_id 51                       # 虚拟路由器ID (0-255)
    priority 100                               # 优先级 (数值越大优先级越高)
    advert_int 1                               # 广播间隔 (秒)
    authentication {
        auth_type PASS                         # 认证类型
        auth_pass postgres_ha_2024             # 认证密码
    }
    virtual_ipaddress {
        *************/24                       # 虚拟IP地址
    }
    track_script {
        chk_haproxy                            # 跟踪脚本
    }
    notify_master "/etc/keepalived/notify_master.sh"    # 成为主节点时执行的脚本
    notify_backup "/etc/keepalived/notify_backup.sh"    # 成为备节点时执行的脚本
    notify_fault "/etc/keepalived/notify_fault.sh"      # 故障时执行的脚本
}
```

**HAProxy备节点Keepalived配置 (/etc/keepalived/keepalived.conf)**:

```bash
# ============================================================================
# Keepalived高可用配置 - HAProxy备节点
# ============================================================================

global_defs {
    notification_email {
        <EMAIL>
    }
    notification_email_from keepalived@haproxy-backup
    smtp_server 127.0.0.1
    smtp_connect_timeout 30
    router_id HAPROXY_BACKUP                   # 路由器ID改为BACKUP
    vrrp_skip_check_adv_addr
    vrrp_strict
    vrrp_garp_interval 0
    vrrp_gna_interval 0
}

vrrp_script chk_haproxy {
    script "/etc/keepalived/check_haproxy.sh"
    interval 2
    weight -2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state BACKUP                               # 初始状态为BACKUP
    interface eth0
    virtual_router_id 51                       # 必须与主节点相同
    priority 90                                # 优先级低于主节点
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass postgres_ha_2024             # 密码必须与主节点相同
    }
    virtual_ipaddress {
        *************/24                       # 虚拟IP必须相同
    }
    track_script {
        chk_haproxy
    }
    notify_master "/etc/keepalived/notify_master.sh"
    notify_backup "/etc/keepalived/notify_backup.sh"
    notify_fault "/etc/keepalived/notify_fault.sh"
}
```

#### 4.3.3 Keepalived检查脚本

**HAProxy健康检查脚本 (/etc/keepalived/check_haproxy.sh)**:

```bash
#!/bin/bash
# ============================================================================
# HAProxy健康检查脚本
# ============================================================================

# 检查HAProxy进程是否运行
if ! pgrep haproxy > /dev/null; then
    echo "$(date): HAProxy process not found" >> /var/log/keepalived/check.log
    exit 1
fi

# 检查HAProxy统计页面是否可访问
if ! curl -s http://localhost:8404/stats > /dev/null; then
    echo "$(date): HAProxy stats page not accessible" >> /var/log/keepalived/check.log
    exit 1
fi

# 检查PostgreSQL写入端口是否可用
if ! nc -z localhost 5000; then
    echo "$(date): PostgreSQL write port 5000 not accessible" >> /var/log/keepalived/check.log
    exit 1
fi

# 检查PostgreSQL读取端口是否可用
if ! nc -z localhost 5001; then
    echo "$(date): PostgreSQL read port 5001 not accessible" >> /var/log/keepalived/check.log
    exit 1
fi

echo "$(date): HAProxy health check passed" >> /var/log/keepalived/check.log
exit 0
```

**状态变更通知脚本**:

```bash
# /etc/keepalived/notify_master.sh
#!/bin/bash
echo "$(date): Becoming MASTER" >> /var/log/keepalived/state.log
systemctl restart haproxy

# /etc/keepalived/notify_backup.sh
#!/bin/bash
echo "$(date): Becoming BACKUP" >> /var/log/keepalived/state.log

# /etc/keepalived/notify_fault.sh
#!/bin/bash
echo "$(date): Entering FAULT state" >> /var/log/keepalived/state.log
```

## 5. 部署步骤

### 5.1 启动etcd集群

```bash
# ============================================================================
# 第一步：启动etcd分布式配置存储集群
# ============================================================================

# 在所有etcd节点上执行以下操作

# 1. 重新加载systemd配置
systemctl daemon-reload

# 2. 启用etcd服务开机自启
systemctl enable etcd

# 3. 启动etcd服务
systemctl start etcd

# 4. 检查etcd服务状态
systemctl status etcd

# 5. 查看etcd日志 (确认启动正常)
journalctl -u etcd -f

# 6. 验证etcd集群状态 (在任意etcd节点执行)
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 endpoint health

# 7. 查看etcd集群成员信息
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 member list

# 8. 测试etcd读写功能
etcdctl --endpoints=http://************:2379 put test_key test_value
etcdctl --endpoints=http://************:2379 get test_key
etcdctl --endpoints=http://************:2379 del test_key

# 预期输出示例:
# endpoint health:
# http://************:2379 is healthy: successfully committed proposal: took = 2.345ms
# http://************:2379 is healthy: successfully committed proposal: took = 1.234ms
# http://************:2379 is healthy: successfully committed proposal: took = 1.567ms
```

### 5.2 启动PostgreSQL集群

```bash
# ============================================================================
# 第二步：启动PostgreSQL + Patroni高可用集群
# ============================================================================

# 在所有PostgreSQL节点上执行以下操作

# 1. 设置脚本执行权限
chmod +x /etc/keepalived/*.sh

# 2. 创建日志目录
mkdir -p /var/log/keepalived
chown keepalived:keepalived /var/log/keepalived

# 3. 重新加载systemd配置
systemctl daemon-reload

# 4. 启用Patroni服务开机自启
systemctl enable patroni

# 5. 启动Patroni服务 (先启动主库节点pg-node1)
# 在pg-node1上执行:
systemctl start patroni

# 等待主库初始化完成 (约1-2分钟)
sleep 120

# 在pg-node2上执行:
systemctl start patroni

# 等待从库同步完成 (约30秒)
sleep 30

# 在pg-node3上执行:
systemctl start patroni

# 6. 检查Patroni服务状态
systemctl status patroni

# 7. 查看Patroni日志
journalctl -u patroni -f

# 8. 检查PostgreSQL集群状态 (在任意PostgreSQL节点执行)
patronictl -c /etc/patroni/patroni.yml list

# 预期输出示例:
# + Cluster: postgres-cluster (7234567890123456789) -----+----+-----------+
# | Member   | Host         | Role    | State   | TL | Lag in MB |
# +----------+--------------+---------+---------+----+-----------+
# | pg-node1 | ************ | Leader  | running |  1 |           |
# | pg-node2 | ************ | Replica | running |  1 |         0 |
# | pg-node3 | ************ | Replica | running |  1 |         0 |
# +----------+--------------+---------+---------+----+-----------+

# 9. 测试PostgreSQL连接
psql -h ************ -p 5432 -U postgres -c "SELECT version();"

# 10. 检查复制状态 (在主库执行)
psql -h ************ -p 5432 -U postgres -c "SELECT * FROM pg_stat_replication;"
```

### 5.3 启动HAProxy负载均衡

```bash
# ============================================================================
# 第三步：启动HAProxy负载均衡和Keepalived高可用
# ============================================================================

# 在所有HAProxy节点上执行以下操作

# 1. 设置脚本执行权限
chmod +x /etc/keepalived/*.sh

# 2. 创建日志目录
mkdir -p /var/log/keepalived
chown root:root /var/log/keepalived

# 3. 启用服务开机自启
systemctl enable haproxy
systemctl enable keepalived

# 4. 启动HAProxy服务
systemctl start haproxy

# 5. 检查HAProxy服务状态
systemctl status haproxy

# 6. 启动Keepalived服务
systemctl start keepalived

# 7. 检查Keepalived服务状态
systemctl status keepalived

# 8. 查看虚拟IP是否绑定成功 (在主节点执行)
ip addr show | grep *************

# 9. 测试HAProxy统计页面
curl http://*************:8404/stats

# 10. 查看HAProxy日志
tail -f /var/log/haproxy/haproxy.log

# 11. 查看Keepalived日志
journalctl -u keepalived -f
```

## 6. 测试验证

### 6.1 连接测试

```bash
# ============================================================================
# 数据库连接功能测试
# ============================================================================

# 1. 测试写连接 (连接到主库)
psql -h ************* -p 5000 -U postgres -d postgres -c "
CREATE TABLE test_write (
    id SERIAL PRIMARY KEY,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO test_write (content) VALUES ('Test write operation');
SELECT * FROM test_write;
"

# 2. 测试读连接 (连接到从库)
psql -h ************* -p 5001 -U postgres -d postgres -c "
SELECT * FROM test_write;
SELECT pg_is_in_recovery() as is_standby;
"

# 3. 测试读写分离
for i in {1..10}; do
    echo "=== 写操作测试 $i ==="
    psql -h ************* -p 5000 -U postgres -d postgres -c "
    INSERT INTO test_write (content) VALUES ('Write test $i');
    SELECT inet_server_addr() as server_ip, inet_server_port() as server_port;
    "

    echo "=== 读操作测试 $i ==="
    psql -h ************* -p 5001 -U postgres -d postgres -c "
    SELECT COUNT(*) FROM test_write;
    SELECT inet_server_addr() as server_ip, inet_server_port() as server_port;
    "
    sleep 1
done

# 4. 测试连接池
echo "=== 连接池测试 ==="
for i in {1..5}; do
    psql -h ************* -p 5002 -U postgres -d postgres -c "
    SELECT inet_server_addr() as server_ip, current_timestamp;
    " &
done
wait
```

### 6.2 故障转移测试

```bash
# ============================================================================
# 高可用故障转移测试
# ============================================================================

# 1. 模拟主库故障 (在pg-node1上执行)
echo "=== 模拟主库故障 ==="
systemctl stop patroni

# 2. 观察自动切换过程 (在其他节点执行)
echo "=== 观察故障转移过程 ==="
watch -n 1 "patronictl -c /etc/patroni/patroni.yml list"

# 3. 验证新主库可写入 (等待切换完成后执行)
sleep 30
psql -h ************* -p 5000 -U postgres -d postgres -c "
INSERT INTO test_write (content) VALUES ('After failover test');
SELECT * FROM test_write ORDER BY id DESC LIMIT 5;
"

# 4. 恢复原主库 (在pg-node1上执行)
echo "=== 恢复原主库 ==="
systemctl start patroni

# 5. 观察节点重新加入集群
watch -n 1 "patronictl -c /etc/patroni/patroni.yml list"

# 6. 验证数据一致性
psql -h ************* -p 5001 -U postgres -d postgres -c "
SELECT COUNT(*) FROM test_write;
SELECT content FROM test_write WHERE content LIKE '%failover%';
"
```

### 6.3 HAProxy故障转移测试

```bash
# ============================================================================
# HAProxy高可用测试
# ============================================================================

# 1. 查看当前虚拟IP位置
echo "=== 当前VIP状态 ==="
ip addr show | grep *************

# 2. 模拟HAProxy主节点故障
echo "=== 模拟HAProxy主节点故障 ==="
# 在当前持有VIP的节点上执行
systemctl stop haproxy
systemctl stop keepalived

# 3. 验证VIP自动切换 (在备节点执行)
sleep 10
ip addr show | grep *************

# 4. 测试服务可用性
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT 'HAProxy failover test' as message, current_timestamp;
"

# 5. 恢复HAProxy主节点
systemctl start haproxy
systemctl start keepalived
```

### 6.4 性能基准测试

```bash
# ============================================================================
# 数据库性能基准测试
# ============================================================================

# 1. 安装pgbench (如果未安装)
dnf install -y postgresql15-contrib

# 2. 初始化测试数据
pgbench -h ************* -p 5000 -U postgres -i -s 100 postgres

# 3. 执行读写混合测试
pgbench -h ************* -p 5000 -U postgres -c 10 -j 2 -T 60 postgres

# 4. 执行只读测试 (连接从库)
pgbench -h ************* -p 5001 -U postgres -c 10 -j 2 -T 60 -S postgres

# 5. 查看测试结果和统计信息
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del
FROM pg_stat_user_tables
WHERE tablename LIKE 'pgbench%';
"
```

## 7. 监控维护

### 7.1 关键监控指标

#### 7.1.1 PostgreSQL数据库监控

```bash
# ============================================================================
# PostgreSQL数据库关键监控指标
# ============================================================================

# 1. 数据库连接数监控
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections,
    count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
FROM pg_stat_activity;
"

# 2. 数据库性能指标监控
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT
    datname,
    numbackends as connections,
    xact_commit as commits,
    xact_rollback as rollbacks,
    blks_read as disk_reads,
    blks_hit as buffer_hits,
    round((blks_hit::float/(blks_read + blks_hit + 1) * 100)::numeric, 2) as cache_hit_ratio
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1');
"

# 3. 复制延迟监控
psql -h ************ -p 5432 -U postgres -d postgres -c "
SELECT
    client_addr,
    client_hostname,
    state,
    sent_lsn,
    write_lsn,
    flush_lsn,
    replay_lsn,
    write_lag,
    flush_lag,
    replay_lag
FROM pg_stat_replication;
"

# 4. 表空间使用情况监控
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;
"

# 5. 慢查询监控
psql -h ************* -p 5000 -U postgres -d postgres -c "
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;
"
```

#### 7.1.2 Patroni集群监控

```bash
# ============================================================================
# Patroni集群状态监控
# ============================================================================

# 1. 集群整体状态监控脚本
cat > /data/postgresql/scripts/monitor_cluster.sh << 'EOF'
#!/bin/bash

echo "=== PostgreSQL集群状态监控 ==="
echo "监控时间: $(date)"
echo

# 检查Patroni集群状态
echo "1. Patroni集群状态:"
patronictl -c /etc/patroni/patroni.yml list

echo
echo "2. etcd集群健康状态:"
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 endpoint health

echo
echo "3. PostgreSQL服务状态:"
systemctl status postgresql-15 --no-pager -l

echo
echo "4. Patroni服务状态:"
systemctl status patroni --no-pager -l

echo
echo "5. 复制延迟检查:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    CASE WHEN pg_is_in_recovery() THEN 'STANDBY' ELSE 'PRIMARY' END as role,
    CASE WHEN pg_is_in_recovery() THEN
        EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()))::int
    ELSE 0 END as lag_seconds;
"

echo
echo "6. 数据库大小统计:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    pg_database.datname,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
ORDER BY pg_database_size(pg_database.datname) DESC;
"

echo "=== 监控完成 ==="
EOF

chmod +x /data/postgresql/scripts/monitor_cluster.sh

# 2. 设置定时监控任务
cat > /etc/cron.d/postgres-monitor << 'EOF'
# PostgreSQL集群监控任务
*/5 * * * * postgres /data/postgresql/scripts/monitor_cluster.sh >> /var/log/postgresql/cluster_monitor.log 2>&1
EOF
```

#### 7.1.3 HAProxy监控

```bash
# ============================================================================
# HAProxy负载均衡监控
# ============================================================================

# 1. HAProxy状态监控脚本
cat > /etc/haproxy/monitor_haproxy.sh << 'EOF'
#!/bin/bash

echo "=== HAProxy负载均衡监控 ==="
echo "监控时间: $(date)"
echo

# 检查HAProxy进程状态
echo "1. HAProxy进程状态:"
systemctl status haproxy --no-pager -l

echo
echo "2. HAProxy统计信息:"
echo "stats show stat" | socat stdio /run/haproxy/admin.sock

echo
echo "3. 后端服务器健康状态:"
curl -s http://localhost:8404/stats | grep -E "(pg-node|FRONTEND|BACKEND)" | head -20

echo
echo "4. 连接数统计:"
ss -tuln | grep -E ":(5000|5001|5002|8404)"

echo
echo "5. Keepalived VIP状态:"
ip addr show | grep *************

echo "=== HAProxy监控完成 ==="
EOF

chmod +x /etc/haproxy/monitor_haproxy.sh

# 2. 设置HAProxy监控任务
cat > /etc/cron.d/haproxy-monitor << 'EOF'
# HAProxy监控任务
*/2 * * * * root /etc/haproxy/monitor_haproxy.sh >> /var/log/haproxy/monitor.log 2>&1
EOF
```

### 7.2 日常维护命令

#### 7.2.1 集群管理命令

```bash
# ============================================================================
# PostgreSQL集群日常管理命令
# ============================================================================

# 1. 查看集群状态
patronictl -c /etc/patroni/patroni.yml list

# 2. 查看集群配置
patronictl -c /etc/patroni/patroni.yml show-config

# 3. 手动切换主库 (计划内维护)
patronictl -c /etc/patroni/patroni.yml switchover postgres-cluster

# 4. 重启指定节点
patronictl -c /etc/patroni/patroni.yml restart postgres-cluster pg-node2

# 5. 重新加载配置
patronictl -c /etc/patroni/patroni.yml reload postgres-cluster

# 6. 暂停自动故障转移 (维护期间)
patronictl -c /etc/patroni/patroni.yml pause postgres-cluster

# 7. 恢复自动故障转移
patronictl -c /etc/patroni/patroni.yml resume postgres-cluster

# 8. 查看节点历史
patronictl -c /etc/patroni/patroni.yml history postgres-cluster

# 9. 编辑集群配置
patronictl -c /etc/patroni/patroni.yml edit-config postgres-cluster

# 10. 强制重新初始化节点 (危险操作)
patronictl -c /etc/patroni/patroni.yml reinit postgres-cluster pg-node2
```

#### 7.2.2 备份恢复管理

```bash
# ============================================================================
# PostgreSQL备份恢复管理
# ============================================================================

# 1. 创建基础备份脚本
cat > /data/postgresql/scripts/backup_database.sh << 'EOF'
#!/bin/bash

# 备份配置
BACKUP_DIR="/data/postgresql/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="postgres_backup_${DATE}"
RETENTION_DAYS=7

# 创建备份目录
mkdir -p ${BACKUP_DIR}/${DATE}

# 执行基础备份
echo "开始执行基础备份: ${BACKUP_NAME}"
pg_basebackup -h ************ -D ${BACKUP_DIR}/${DATE} -U replicator -v -P -W -X stream

# 压缩备份
echo "压缩备份文件..."
tar -czf ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz -C ${BACKUP_DIR} ${DATE}
rm -rf ${BACKUP_DIR}/${DATE}

# 清理过期备份
echo "清理过期备份..."
find ${BACKUP_DIR} -name "postgres_backup_*.tar.gz" -mtime +${RETENTION_DAYS} -delete

echo "备份完成: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
EOF

chmod +x /data/postgresql/scripts/backup_database.sh

# 2. 创建逻辑备份脚本
cat > /data/postgresql/scripts/logical_backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/data/postgresql/backup/logical"
DATE=$(date +%Y%m%d_%H%M%S)
DATABASES=("postgres" "your_app_db")  # 需要备份的数据库列表

mkdir -p ${BACKUP_DIR}

for db in "${DATABASES[@]}"; do
    echo "备份数据库: ${db}"
    pg_dump -h ************* -p 5000 -U postgres -d ${db} | gzip > ${BACKUP_DIR}/${db}_${DATE}.sql.gz
done

echo "逻辑备份完成"
EOF

chmod +x /data/postgresql/scripts/logical_backup.sh

# 3. 设置定时备份任务
cat > /etc/cron.d/postgres-backup << 'EOF'
# PostgreSQL自动备份任务
0 2 * * * postgres /data/postgresql/scripts/backup_database.sh >> /var/log/postgresql/backup.log 2>&1
0 6 * * * postgres /data/postgresql/scripts/logical_backup.sh >> /var/log/postgresql/logical_backup.log 2>&1
EOF

# 4. 恢复操作示例
cat > /data/postgresql/scripts/restore_example.sh << 'EOF'
#!/bin/bash
# 数据库恢复示例脚本 (仅供参考，实际使用需谨慎)

BACKUP_FILE="/data/postgresql/backup/postgres_backup_20241201_020000.tar.gz"
RESTORE_DIR="/data/postgresql/restore"

echo "警告: 此操作将停止PostgreSQL服务并恢复数据"
echo "请确认备份文件路径: ${BACKUP_FILE}"
read -p "是否继续? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "操作已取消"
    exit 1
fi

# 停止Patroni服务
systemctl stop patroni

# 备份当前数据目录
mv /data/postgresql/15/data /data/postgresql/15/data.backup.$(date +%Y%m%d_%H%M%S)

# 解压备份文件
mkdir -p ${RESTORE_DIR}
tar -xzf ${BACKUP_FILE} -C ${RESTORE_DIR}

# 恢复数据目录
cp -r ${RESTORE_DIR}/* /data/postgresql/15/data/
chown -R postgres:postgres /data/postgresql/15/data
chmod 700 /data/postgresql/15/data

# 启动Patroni服务
systemctl start patroni

echo "恢复完成，请检查集群状态"
EOF

chmod +x /data/postgresql/scripts/restore_example.sh
```

#### 7.2.3 性能优化管理

```bash
# ============================================================================
# PostgreSQL性能优化管理
# ============================================================================

# 1. 数据库性能分析脚本
cat > /data/postgresql/scripts/performance_analysis.sh << 'EOF'
#!/bin/bash

echo "=== PostgreSQL性能分析报告 ==="
echo "分析时间: $(date)"
echo

# 1. 数据库大小分析
echo "1. 数据库大小统计:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    datname as database_name,
    pg_size_pretty(pg_database_size(datname)) as size,
    pg_database_size(datname) as size_bytes
FROM pg_database
WHERE datname NOT IN ('template0', 'template1')
ORDER BY pg_database_size(datname) DESC;
"

echo
echo "2. 表大小统计 (TOP 10):"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;
"

echo
echo "3. 索引使用情况分析:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC
LIMIT 10;
"

echo
echo "4. 缓存命中率分析:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    datname,
    round((blks_hit::float/(blks_read + blks_hit + 1) * 100)::numeric, 2) as cache_hit_ratio,
    blks_read,
    blks_hit
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1');
"

echo
echo "5. 连接状态分析:"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    state,
    count(*) as connection_count
FROM pg_stat_activity
GROUP BY state
ORDER BY connection_count DESC;
"

echo "=== 性能分析完成 ==="
EOF

chmod +x /data/postgresql/scripts/performance_analysis.sh

# 2. 慢查询分析脚本
cat > /data/postgresql/scripts/slow_query_analysis.sh << 'EOF'
#!/bin/bash

echo "=== 慢查询分析报告 ==="
echo "分析时间: $(date)"
echo

# 需要先启用pg_stat_statements扩展
psql -h localhost -p 5432 -U postgres -d postgres -c "
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
"

echo "1. 执行时间最长的查询 (TOP 10):"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;
"

echo
echo "2. 平均执行时间最长的查询 (TOP 10):"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
"

echo
echo "3. 执行次数最多的查询 (TOP 10):"
psql -h localhost -p 5432 -U postgres -d postgres -c "
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY calls DESC
LIMIT 10;
"

echo "=== 慢查询分析完成 ==="
EOF

chmod +x /data/postgresql/scripts/slow_query_analysis.sh
```

## 5. 部署步骤

### 5.1 启动etcd集群

```bash
# ============================================================================
# 第一步：启动etcd分布式配置存储集群
# ============================================================================

# 在所有etcd节点上执行以下操作

# 1. 重新加载systemd配置
systemctl daemon-reload

# 2. 启用etcd服务开机自启
systemctl enable etcd

# 3. 启动etcd服务
systemctl start etcd

# 4. 检查etcd服务状态
systemctl status etcd

# 5. 查看etcd日志 (确认启动正常)
journalctl -u etcd -f

# 6. 验证etcd集群状态 (在任意etcd节点执行)
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 endpoint health

# 7. 查看etcd集群成员信息
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 member list

# 8. 测试etcd读写功能
etcdctl --endpoints=http://************:2379 put test_key test_value
etcdctl --endpoints=http://************:2379 get test_key
etcdctl --endpoints=http://************:2379 del test_key

# 预期输出示例:
# endpoint health:
# http://************:2379 is healthy: successfully committed proposal: took = 2.345ms
# http://************:2379 is healthy: successfully committed proposal: took = 1.234ms
# http://************:2379 is healthy: successfully committed proposal: took = 1.567ms
```

### 5.2 启动PostgreSQL集群

```bash
# ============================================================================
# 第二步：启动PostgreSQL + Patroni高可用集群
# ============================================================================

# 在所有PostgreSQL节点上执行以下操作

# 1. 设置脚本执行权限
chmod +x /etc/keepalived/*.sh

# 2. 创建日志目录
mkdir -p /var/log/keepalived
chown keepalived:keepalived /var/log/keepalived

# 3. 重新加载systemd配置
systemctl daemon-reload

# 4. 启用Patroni服务开机自启
systemctl enable patroni

# 5. 启动Patroni服务 (先启动主库节点pg-node1)
# 在pg-node1上执行:
systemctl start patroni

# 等待主库初始化完成 (约1-2分钟)
sleep 120

# 在pg-node2上执行:
systemctl start patroni

# 等待从库同步完成 (约30秒)
sleep 30

# 在pg-node3上执行:
systemctl start patroni

# 6. 检查Patroni服务状态
systemctl status patroni

# 7. 查看Patroni日志
journalctl -u patroni -f

# 8. 检查PostgreSQL集群状态 (在任意PostgreSQL节点执行)
patronictl -c /etc/patroni/patroni.yml list

# 预期输出示例:
# + Cluster: postgres-cluster (7234567890123456789) -----+----+-----------+
# | Member   | Host         | Role    | State   | TL | Lag in MB |
# +----------+--------------+---------+---------+----+-----------+
# | pg-node1 | ************ | Leader  | running |  1 |           |
# | pg-node2 | ************ | Replica | running |  1 |         0 |
# | pg-node3 | ************ | Replica | running |  1 |         0 |
# +----------+--------------+---------+---------+----+-----------+

# 9. 测试PostgreSQL连接
psql -h ************ -p 5432 -U postgres -c "SELECT version();"

# 10. 检查复制状态 (在主库执行)
psql -h ************ -p 5432 -U postgres -c "SELECT * FROM pg_stat_replication;"
```

### 5.3 启动HAProxy负载均衡

```bash
# ============================================================================
# 第三步：启动HAProxy负载均衡和Keepalived高可用
# ============================================================================

# 在所有HAProxy节点上执行以下操作

# 1. 设置脚本执行权限
chmod +x /etc/keepalived/*.sh

# 2. 创建日志目录
mkdir -p /var/log/keepalived
chown root:root /var/log/keepalived

# 3. 启用服务开机自启
systemctl enable haproxy
systemctl enable keepalived

# 4. 启动HAProxy服务
systemctl start haproxy

# 5. 检查HAProxy服务状态
systemctl status haproxy

# 6. 启动Keepalived服务
systemctl start keepalived

# 7. 检查Keepalived服务状态
systemctl status keepalived

# 8. 查看虚拟IP是否绑定成功 (在主节点执行)
ip addr show | grep *************

# 9. 测试HAProxy统计页面
curl http://*************:8404/stats

# 10. 查看HAProxy日志
tail -f /var/log/haproxy/haproxy.log

# 11. 查看Keepalived日志
journalctl -u keepalived -f
```

## 8. 故障处理

### 8.1 常见问题及解决方案

#### 8.1.1 etcd集群问题

```bash
# ============================================================================
# etcd集群常见问题处理
# ============================================================================

# 问题1: etcd节点无法启动
# 解决方案:
# 1. 检查配置文件语法
etcd --config-file /etc/etcd/etcd.conf --print-config

# 2. 检查数据目录权限
ls -la /var/lib/etcd
chown -R etcd:etcd /var/lib/etcd

# 3. 检查端口占用
ss -tuln | grep -E ":(2379|2380)"

# 问题2: etcd集群脑裂
# 解决方案:
# 1. 停止所有etcd节点
systemctl stop etcd

# 2. 清理数据目录 (谨慎操作)
rm -rf /var/lib/etcd/*

# 3. 重新初始化集群
systemctl start etcd

# 问题3: etcd性能问题
# 解决方案:
# 1. 检查磁盘IO性能
iostat -x 1

# 2. 调整etcd参数
# 在etcd.conf中添加:
# heartbeat-interval: 200
# election-timeout: 2000
```

#### 8.1.2 PostgreSQL集群问题

```bash
# ============================================================================
# PostgreSQL集群常见问题处理
# ============================================================================

# 问题1: Patroni无法启动PostgreSQL
# 解决方案:
# 1. 检查数据目录权限
ls -la /data/postgresql/15/data
chown -R postgres:postgres /data/postgresql/15/data
chmod 700 /data/postgresql/15/data

# 2. 检查PostgreSQL日志
tail -f /data/postgresql/15/data/log/postgresql-*.log

# 3. 手动启动PostgreSQL测试
su - postgres -c "/usr/pgsql-15/bin/postgres -D /data/postgresql/15/data"

# 问题2: 主从复制延迟过大
# 解决方案:
# 1. 检查网络延迟
ping ************
ping ************

# 2. 检查复制状态
psql -h ************ -p 5432 -U postgres -c "
SELECT
    client_addr,
    state,
    write_lag,
    flush_lag,
    replay_lag
FROM pg_stat_replication;
"

# 3. 调整复制参数
# 在postgresql.conf中调整:
# wal_sender_timeout = 60s
# wal_receiver_timeout = 60s

# 问题3: 自动故障转移失败
# 解决方案:
# 1. 检查Patroni配置
patronictl -c /etc/patroni/patroni.yml show-config

# 2. 手动触发故障转移
patronictl -c /etc/patroni/patroni.yml failover postgres-cluster

# 3. 检查etcd连接
etcdctl --endpoints=http://************:2379 get /db/postgres-cluster/leader

# 问题4: 数据库连接数过多
# 解决方案:
# 1. 查看当前连接
psql -h localhost -p 5432 -U postgres -c "
SELECT count(*), state FROM pg_stat_activity GROUP BY state;
"

# 2. 终止空闲连接
psql -h localhost -p 5432 -U postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE state = 'idle' AND state_change < now() - interval '1 hour';
"

# 3. 调整连接参数
# max_connections = 200
# shared_buffers = 256MB
```

#### 8.1.3 HAProxy问题

```bash
# ============================================================================
# HAProxy常见问题处理
# ============================================================================

# 问题1: HAProxy无法启动
# 解决方案:
# 1. 检查配置文件语法
haproxy -f /etc/haproxy/haproxy.cfg -c

# 2. 检查端口占用
ss -tuln | grep -E ":(5000|5001|8404)"

# 3. 检查日志
tail -f /var/log/haproxy/haproxy.log

# 问题2: 后端服务器健康检查失败
# 解决方案:
# 1. 手动测试健康检查
curl -I http://************:8008/master
curl -I http://************:8008/replica

# 2. 检查Patroni REST API
systemctl status patroni

# 3. 调整健康检查参数
# inter 5s fall 5 rise 2

# 问题3: Keepalived VIP切换异常
# 解决方案:
# 1. 检查VRRP日志
journalctl -u keepalived -f

# 2. 检查网络接口
ip addr show eth0

# 3. 手动测试脚本
/etc/keepalived/check_haproxy.sh

# 4. 重启Keepalived服务
systemctl restart keepalived
```

### 8.2 应急处理流程

#### 8.2.1 数据库紧急恢复流程

```bash
# ============================================================================
# PostgreSQL紧急恢复流程
# ============================================================================

# 场景1: 主库完全故障，需要紧急恢复
echo "=== 紧急恢复流程 ==="

# 1. 评估故障范围
patronictl -c /etc/patroni/patroni.yml list

# 2. 如果有可用从库，手动提升为主库
patronictl -c /etc/patroni/patroni.yml failover postgres-cluster --candidate pg-node2

# 3. 如果所有节点都故障，从备份恢复
# 停止所有Patroni服务
for node in pg-node1 pg-node2 pg-node3; do
    ssh $node "systemctl stop patroni"
done

# 选择一个节点作为新主库，恢复最新备份
BACKUP_FILE="/data/postgresql/backup/postgres_backup_latest.tar.gz"
tar -xzf $BACKUP_FILE -C /data/postgresql/15/data/
chown -R postgres:postgres /data/postgresql/15/data

# 启动新主库
systemctl start patroni

# 4. 重建其他从库
for node in pg-node2 pg-node3; do
    ssh $node "
        systemctl stop patroni
        rm -rf /data/postgresql/15/data/*
        systemctl start patroni
    "
done

echo "=== 紧急恢复完成，请验证数据完整性 ==="
```

#### 8.2.2 网络分区处理

```bash
# ============================================================================
# 网络分区故障处理
# ============================================================================

# 场景: 网络分区导致集群分裂
echo "=== 网络分区处理流程 ==="

# 1. 识别网络分区
# 检查各节点的连通性
for node in pg-node1 pg-node2 pg-node3; do
    echo "检查节点 $node 连通性:"
    ping -c 3 $node
done

# 2. 确定主分区 (包含多数节点的分区)
# 检查etcd集群状态
etcdctl --endpoints=http://************:2379,http://************:2379,http://************:2379 endpoint health

# 3. 在主分区中选择新的主库
patronictl -c /etc/patroni/patroni.yml list

# 4. 暂停自动故障转移，防止进一步分裂
patronictl -c /etc/patroni/patroni.yml pause postgres-cluster

# 5. 网络恢复后，重新同步数据
# 恢复自动故障转移
patronictl -c /etc/patroni/patroni.yml resume postgres-cluster

# 重启分区中的从库节点
systemctl restart patroni

echo "=== 网络分区处理完成 ==="
```

## 9. Java应用连接示例

### 9.1 Spring Boot配置示例

```yaml
# ============================================================================
# Spring Boot application.yml 配置示例
# ============================================================================

spring:
  datasource:
    # 主数据源配置 (写操作)
    primary:
      jdbc-url: jdbc:postgresql://*************:5000/your_database
      username: your_app_user
      password: your_app_password
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        idle-timeout: 300000
        max-lifetime: 1200000
        connection-timeout: 20000

    # 从数据源配置 (读操作)
    secondary:
      jdbc-url: jdbc:postgresql://*************:5001/your_database
      username: your_app_user
      password: your_app_password
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 15
        minimum-idle: 3
        idle-timeout: 300000
        max-lifetime: 1200000
        connection-timeout: 20000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
```

### 9.2 Java连接池配置

```java
/**
 * <AUTHOR>
 * @date 2025-01-02 15:30:00
 * @description PostgreSQL数据源配置类
 */
@Configuration
@EnableTransactionManagement
public class DatabaseConfig {

    @Primary
    @Bean(name = "primaryDataSource")
    @ConfigurationProperties("spring.datasource.primary")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    @Bean(name = "secondaryDataSource")
    @ConfigurationProperties("spring.datasource.secondary")
    public DataSource secondaryDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    @Primary
    @Bean(name = "primaryJdbcTemplate")
    public JdbcTemplate primaryJdbcTemplate(@Qualifier("primaryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "secondaryJdbcTemplate")
    public JdbcTemplate secondaryJdbcTemplate(@Qualifier("secondaryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
```

### 9.3 读写分离注解实现

```java
/**
 * <AUTHOR>
 * @date 2025-01-02 15:35:00
 * @description 读写分离注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ReadOnly {
    boolean value() default true;
}

/**
 * <AUTHOR>
 * @date 2025-01-02 15:36:00
 * @description 数据源切换AOP
 */
@Aspect
@Component
@Slf4j
public class DataSourceAspect {

    @Around("@annotation(readOnly)")
    public Object around(ProceedingJoinPoint point, ReadOnly readOnly) throws Throwable {
        try {
            if (readOnly.value()) {
                DataSourceContextHolder.setDataSourceType(DataSourceType.SECONDARY);
                log.debug("切换到从库数据源");
            } else {
                DataSourceContextHolder.setDataSourceType(DataSourceType.PRIMARY);
                log.debug("切换到主库数据源");
            }
            return point.proceed();
        } finally {
            DataSourceContextHolder.clearDataSourceType();
        }
    }
}
```

## 10. 总结

本PostgreSQL高可用集群部署方案采用了业界成熟的技术栈组合：

### 10.1 技术优势

- **高可用性**: 通过Patroni实现自动故障检测和切换，RTO < 30秒
- **数据一致性**: 支持同步/异步复制，保证数据安全
- **读写分离**: HAProxy实现智能路由，提升系统性能
- **扩展性**: 支持在线扩容和滚动升级
- **监控完善**: 提供全方位的监控和告警机制

### 10.2 适用场景

- 中大型企业级应用
- 对数据一致性要求较高的业务系统
- 需要7x24小时不间断服务的应用
- 读多写少的业务场景

### 10.3 注意事项

- 生产环境部署前请充分测试
- 定期备份和恢复演练
- 监控集群健康状态
- 及时更新安全补丁
- 根据业务需求调整配置参数

通过本方案，可以构建一个稳定、高效、可扩展的PostgreSQL数据库集群，为企业应用提供可靠的数据服务支撑。