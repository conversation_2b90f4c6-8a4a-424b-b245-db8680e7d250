#!/bin/bash

# ============================================================================
# Python3 清华源配置脚本
# 适用于: CentOS 8+ / RHEL 8+ / Debian 11+ / Ubuntu 20.04+
# 作者: linshiqiang
# 日期: 2025-01-02
# ============================================================================

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

# 检查命令执行结果
check_result() {
    if [ $? -eq 0 ]; then
        log_info "$1 成功"
    else
        log_error "$1 失败"
        exit 1
    fi
}

# 配置Python3清华源
configure_python_mirror() {
    log_info "开始配置Python3清华源..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户执行此脚本"
        exit 1
    fi
    
    # 获取postgres用户信息
    INSTALL_USER="postgres"
    INSTALL_GROUP="postgres"
    
    # 创建pip配置目录
    log_info "创建pip配置目录..."
    mkdir -p ~/.pip
    mkdir -p /home/<USER>/.pip
    
    # 配置root用户的pip清华源
    log_info "配置root用户pip清华源..."
    cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
retries = 5

[install]
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    # 为postgres用户配置pip清华源
    log_info "配置postgres用户pip清华源..."
    cat > /home/<USER>/.pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
retries = 5

[install]
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    # 设置权限
    if id "$INSTALL_USER" &>/dev/null; then
        chown -R $INSTALL_USER:$INSTALL_GROUP /home/<USER>/.pip
        log_info "设置postgres用户pip配置权限"
    else
        log_info "postgres用户不存在，跳过权限设置"
    fi
    
    # 配置全局pip配置 (系统级)
    log_info "配置系统级pip清华源..."
    mkdir -p /etc/pip
    cat > /etc/pip/pip.conf << 'EOF'
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
timeout = 120
retries = 5

[install]
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    check_result "Python3清华源配置"
}

# 处理Debian/Ubuntu的externally-managed-environment问题
fix_debian_python_env() {
    log_info "检查并修复Debian/Ubuntu Python环境限制..."
    
    # 检查是否为Debian/Ubuntu系统
    if [ -f /etc/debian_version ]; then
        log_info "检测到Debian/Ubuntu系统"
        
        # 移除Python外部管理限制文件
        if ls /usr/lib/python*/EXTERNALLY-MANAGED >/dev/null 2>&1; then
            log_info "发现Python外部管理限制文件，正在移除..."
            rm -f /usr/lib/python*/EXTERNALLY-MANAGED
            check_result "移除Python外部管理限制"
        else
            log_info "未发现Python外部管理限制文件"
        fi
        
        # 安装必要的Python包
        if command -v apt >/dev/null 2>&1; then
            log_info "安装Python相关包..."
            apt update
            apt install -y python3-full python3-dev python3-pip
            check_result "Python包安装"
        fi
    else
        log_info "非Debian/Ubuntu系统，跳过相关处理"
    fi
}

# 测试pip源连接
test_pip_mirror() {
    log_info "测试pip清华源连接..."
    
    # 测试连接
    if curl -s --connect-timeout 10 https://pypi.tuna.tsinghua.edu.cn/simple/ >/dev/null; then
        log_info "清华源连接正常"
    else
        log_error "清华源连接失败，请检查网络"
        return 1
    fi
    
    # 测试pip安装
    log_info "测试pip安装功能..."
    python3 -m pip install --upgrade pip --break-system-packages -i https://pypi.tuna.tsinghua.edu.cn/simple 2>/dev/null || \
    python3 -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
    check_result "pip升级测试"
}

# 显示配置信息
show_config_info() {
    log_info "Python3清华源配置完成！"
    echo
    echo "配置文件位置:"
    echo "  - Root用户: ~/.pip/pip.conf"
    echo "  - Postgres用户: /home/<USER>/.pip/pip.conf"
    echo "  - 系统级: /etc/pip/pip.conf"
    echo
    echo "清华源地址: https://pypi.tuna.tsinghua.edu.cn/simple"
    echo
    echo "使用方法:"
    echo "  pip3 install package_name"
    echo "  或者手动指定源:"
    echo "  pip3 install package_name -i https://pypi.tuna.tsinghua.edu.cn/simple"
    echo
    echo "如果遇到SSL证书问题，可以使用:"
    echo "  pip3 install package_name -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn"
}

# 主函数
main() {
    log_info "开始配置Python3清华源..."
    
    # 配置清华源
    configure_python_mirror
    
    # 处理Debian/Ubuntu特殊问题
    fix_debian_python_env
    
    # 测试连接
    test_pip_mirror
    
    # 显示配置信息
    show_config_info
    
    log_info "Python3清华源配置脚本执行完成！"
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Python3清华源配置脚本"
    echo "用法: $0"
    echo "功能: 配置pip使用清华大学镜像源，加速Python包下载"
    echo "适用: CentOS 8+, RHEL 8+, Debian 11+, Ubuntu 20.04+"
    exit 0
fi

# 执行主函数
main "$@"
