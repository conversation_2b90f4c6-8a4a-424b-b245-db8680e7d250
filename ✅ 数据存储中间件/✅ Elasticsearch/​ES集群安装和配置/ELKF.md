# ELK
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682342598752-1bf6b3f4-4452-439d-b492-b1ceeb951e89.png#averageHue=%235fa6af&clientId=u577bb23d-fa41-4&from=paste&height=361&id=u98ba1b6f&originHeight=722&originWidth=698&originalType=binary&ratio=2&rotation=0&showTitle=false&size=59322&status=done&style=none&taskId=uca99002e-0714-4fec-8d90-e73b36f1658&title=&width=349)
## Elasticsearch
> Elasticsearch 处于最核心的位置，它可以帮我们对数据进行存储，并快速地搜索及分析数据。

## Logstash
> **L**ogstash 负责数据的采集，处理（丰富数据，数据转换等）

## Kibana
> **Ki**bana 负责数据展示，分析，管理，监督，警报及方案。

## FileBeat
> Beats

