# 安装文件目录布局
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682344826568-1c395fcd-e09d-494c-8b1a-695533d7dfe5.png#averageHue=%23f5f5f5&clientId=ubab7341e-b470-4&from=paste&height=412&id=u1ae2e28a&originHeight=824&originWidth=2316&originalType=binary&ratio=2&rotation=0&showTitle=false&size=204283&status=done&style=none&taskId=u0afeec6a-8bca-4f53-a8c0-07a35bd5924&title=&width=1158)
## 原生安装
### 配置文件
#### node01
```yaml
http:
  port: "9200"
path:
  data: /bitnami/elasticsearch/data
transport:
  port: "9300"
network:
  host: es-node01
  publish_host: es-node01
  bind_host: 0.0.0.0
cluster:
  name: es-cluster
  initial_master_nodes:
    - es-node01
    - es-node02
    - es-node03
node:
  name: es-node01
discovery:
  seed_hosts:
    - es-node01
    - es-node02
    - es-node03
  initial_state_timeout: 10m
xpack:
  security:
    enabled: "true"
  ml:
    enabled: "false"
```
#### node02
```yaml
http:
  port: "9200"
path:
  data: /bitnami/elasticsearch/data
transport:
  port: "9300"
network:
  host: es-node02
  publish_host: es-node02
  bind_host: 0.0.0.0
cluster:
  name: es-cluster
  initial_master_nodes:
    - es-node01
    - es-node02
    - es-node03
node:
  name: es-node02
discovery:
  seed_hosts:
    - es-node01
    - es-node02
    - es-node03
  initial_state_timeout: 10m
xpack:
  security:
    enabled: "true"
  ml:
    enabled: "false"
```
#### node03
```yaml
http:
  port: "9200"
path:
  data: /bitnami/elasticsearch/data
transport:
  port: "9300"
network:
  host: es-node03
  publish_host: es-node03
  bind_host: 0.0.0.0
cluster:
  name: es-cluster
  initial_master_nodes:
    - es-node01
    - es-node02
    - es-node03
node:
  name: es-node03
discovery:
  seed_hosts:
    - es-node01
    - es-node02
    - es-node03
  initial_state_timeout: 10m
xpack:
  security:
    enabled: "true"
  ml:
    enabled: "false"
```
# Docker-compose安装ES集群
## 创建目录
```
rm -rf /home/<USER>/es/es0*
mkdir -p /home/<USER>/es/es01/config /home/<USER>/es/es02/config /home/<USER>/es/es03/config
mkdir -p /home/<USER>/es/es01/data /home/<USER>/es/es02/data /home/<USER>/es/es03/data
mkdir -p /home/<USER>/es/es01/logs /home/<USER>/es/es02/logs /home/<USER>/es/es03/logs
chmod -R 777 /home/<USER>/es/es0*
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682401856151-87c049d7-80db-481d-b9d7-0c49bb9e70e1.png#averageHue=%23060504&clientId=uca713f53-b629-4&from=paste&height=281&id=uf48e6546&originHeight=281&originWidth=339&originalType=binary&ratio=1&rotation=0&showTitle=false&size=8273&status=done&style=none&taskId=uf597dcad-87d0-4f2e-9e1b-6dd00509812&title=&width=339)
## yml
```yaml
version: '2.2'
services:
  es01:
    image: elasticsearch:7.14.1
    container_name: es01
    hostname: es01
    privileged: true
    environment:
      - node.name=es01
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es02,es03
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - ./es01/data:/usr/share/elasticsearch/data
      - ./es01/logs:/usr/share/elasticsearch/logs
      - ./es01/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./es01/config/elastic-certificates.p12:/usr/share/elasticsearch/config/elastic-certificates.p12
      - ./plugins/ik:/usr/share/elasticsearch/plugins/ik     # IK中文分词插件
    ports:
      - "9210:9200"
      - "9310:9300"
    networks:
      - elastic
  es02:
    image: elasticsearch:7.14.1
    container_name: es02
    hostname: es02
    privileged: true
    environment:
      - node.name=es02
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es01,es03
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - ./es02/data:/usr/share/elasticsearch/data
      - ./es02/logs:/usr/share/elasticsearch/logs
      - ./es02/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./es01/config/elastic-certificates.p12:/usr/share/elasticsearch/config/elastic-certificates.p12
      - ./plugins/ik:/usr/share/elasticsearch/plugins/ik     # IK中文分词插件
    ports:
      - "9220:9200"
      - "9320:9300"
    networks:
      - elastic
  es03:
    image: elasticsearch:7.14.1
    container_name: es03
    hostname: es03
    privileged: true
    environment:
      - node.name=es03
      - cluster.name=es-docker-cluster
      - discovery.seed_hosts=es01,es02
      - cluster.initial_master_nodes=es01,es02,es03
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - ./es03/data:/usr/share/elasticsearch/data
      - ./es03/logs:/usr/share/elasticsearch/logs
      - ./es03/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./es01/config/elastic-certificates.p12:/usr/share/elasticsearch/config/elastic-certificates.p12
      - ./plugins/ik:/usr/share/elasticsearch/plugins/ik     # IK中文分词插件
    ports:
      - "9230:9200"
      - "9330:9300"
    networks:
      - elastic
  kibana:
    image: kibana:7.14.1
    container_name: kibana
    restart: unless-stopped
    volumes:
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
    ports:
      - "5601:5601"
    depends_on:
      - es01
      - es02
      - es03
    links:
      - es01
      - es02
      - es03
    networks:
      - elastic
networks:
  elastic:
    driver: bridge

```
## 启动
```yaml
docker-compose -f bitnami-elasticsearch-docker-compose.yml up -d
```
# ES设置密码
> [https://blog.csdn.net/qq_41435602/article/details/123448532](https://blog.csdn.net/qq_41435602/article/details/123448532)

```properties
cluster.name: "docker-cluster"
network.host: 0.0.0.0

http.port: 9200
# 开启es跨域
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-headers: Authorization
# 开启安全控制
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.keystore.type: PKCS12
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.keystore.path: elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: elastic-certificates.p12
xpack.security.transport.ssl.keystore.password: 123456
xpack.security.transport.ssl.truststore.password: 123456
xpack.security.transport.ssl.truststore.type: PKCS12
xpack.security.audit.enabled: true

```
## 生成证书
```bash
# 进入任意一台容器
docker exec -it es01 bash
./bin/elasticsearch-certutil ca
./bin/elasticsearch-certutil cert --ca elastic-stack-ca.p12

# 复制证书到宿主机
docker cp es01:/usr/share/elasticsearch/elastic-certificates.p12 elastic-certificates.p12
```
## 密码设置
```bash
docker exec -it es-node01 bash

# 设置自定义密码
./bin/elasticsearch-setup-passwords interactive

# 设置随机密码
./bin/elasticsearch-setup-passwords auto

# 验证密码
curl 'http://localhost:9220'
curl -u elastic:password 'http://localhost:9220'
curl -u elastic:123456 'http://localhost:9220'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682394409743-5e79a98e-26f8-4386-aa9b-6eebee3c2641.png#averageHue=%23060403&clientId=uca713f53-b629-4&from=paste&height=381&id=ub1809fd9&originHeight=381&originWidth=1079&originalType=binary&ratio=1&rotation=0&showTitle=false&size=35919&status=done&style=none&taskId=u29c1d13f-1122-42a2-9caf-d1a32ce85d1&title=&width=1079)

# 分词插件
> [https://github.com/medcl/elasticsearch-analysis-ik/releases](https://github.com/medcl/elasticsearch-analysis-ik/releases)

```bash
curl -XPUT http://es01:9210/index -u elastic:123456

curl -XPOST http://localhost:9200/index/_mapping -H 'Content-Type:application/json' -d'
{
        "properties": {
            "content": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            }
        }

}'

curl -XPOST http://localhost:9200/index/_create/1 -H 'Content-Type:application/json' -d'
{"content":"美国留给伊拉克的是个烂摊子吗"}
'

curl -XPOST http://localhost:9200/index/_create/2 -H 'Content-Type:application/json' -d'
{"content":"公安部：各地校车将享最高路权"}
'

curl -XPOST http://localhost:9200/index/_create/3 -H 'Content-Type:application/json' -d'
{"content":"中韩渔警冲突调查：韩警平均每天扣1艘中国渔船"}
'

curl -XPOST http://localhost:9200/index/_create/4 -H 'Content-Type:application/json' -d'
{"content":"中国驻洛杉矶领事馆遭亚裔男子枪击 嫌犯已自首"}
'
```
# kibana安装
```yaml
#
# ** THIS IS AN AUTO-GENERATED FILE **
#

# Default Kibana configuration for docker target

server.name: kibana
server.host: "0.0.0.0"
server.publicBaseUrl: "http://kibana:5601"
elasticsearch.hosts: [ "http://es01:9200","http://es02:9200","http://es03:9200" ]
xpack.monitoring.ui.container.elasticsearch.enabled: true
elasticsearch.username: "elastic"  # es账号
elasticsearch.password: "123456"   # es密码
i18n.locale: zh-CN # 中文

```
[kibana.yml](https://www.yuque.com/attachments/yuque/0/2023/yml/684952/1682421406876-a0205119-3774-425f-92cd-ca86d5508780.yml)
