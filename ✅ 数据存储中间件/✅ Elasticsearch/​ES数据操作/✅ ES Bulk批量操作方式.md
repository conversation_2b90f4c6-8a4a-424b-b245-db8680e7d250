# ES 批量操作方式
## BulkRequest 原生api

```java
    public <T> BulkResponse bulk(List<DocWriteRequest<T>> indexRequestList) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (DocWriteRequest<T> writeRequest : indexRequestList) {
            bulkRequest.add(writeRequest);
        }
        return client.bulk(bulkRequest, RequestOptions.DEFAULT);
    }
```

![image-20230529200258385](images/image-20230529200258385.png)

## BulkProcessor 缓存+自动

> 批量处理器是一个线程安全的批量处理类，允许轻松设置何时“刷新”新的批量请求(基于操作数量、大小或时间)，并轻松控制允许并行执行的并发批量请求的数量。
> 为了创建一个新的批量处理器，请使用BulkProcessor.Builder。

### BulkProcessor定义Bean

> Listener回调

```java
@Slf4j
@Configuration(proxyBeanMethods = false)
public class BulkProcessorConfig {

    private final RestHighLevelClient client;

    public BulkProcessorConfig(RestHighLevelClient client) {
        this.client = client;
    }

    @Bean("bulkProcessor")
    public BulkProcessor bulkProcessor() {

        BiConsumer<BulkRequest, ActionListener<BulkResponse>> bulkConsumer =
                (request, bulkListener) -> client.bulkAsync(request, RequestOptions.DEFAULT, bulkListener);
        return BulkProcessor.builder(bulkConsumer, new BulkProcessor.Listener() {
                    @Override
                    public void beforeBulk(long executionId, BulkRequest request) {
                        int i = request.numberOfActions();
                        log.info("beforeBulk: {}", i);
                    }

                    @Override
                    public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
                        log.info("afterBulk: {}", response.status());
                    }

                    @Override
                    public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
                        log.info("afterBulk failure: ", failure);
                    }
                }, "bulkProcessor")
                .setBulkActions(1000) // 达到刷新的条数
                .setBulkSize(new ByteSizeValue(1, ByteSizeUnit.MB)) // 达到 刷新的大小
                .setFlushInterval(TimeValue.timeValueSeconds(5)) // 固定刷新的时间频率
                .setConcurrentRequests(1) //并发线程数
                .setBackoffPolicy(BackoffPolicy.exponentialBackoff(TimeValue.timeValueMillis(100), 3)) // 重试补偿策略
                .build();
    }
}
```

### 批量操作

```java
    private final BulkProcessor bulkProcessor;

    /**
     * org.elasticsearch.action.bulk.BulkRequestHandler#execute(org.elasticsearch.action.bulk.BulkRequest, long)
     */
    public <T> void bulkProcessor(List<DocWriteRequest<T>> docWriteRequestList) {
        for (DocWriteRequest<T> writeRequest : docWriteRequestList) {
            bulkProcessor.add(writeRequest);
        }
    }
```

### execute

```java
    public void execute(BulkRequest bulkRequest, long executionId) {
        Runnable toRelease = () -> {};
        boolean bulkRequestSetupSuccessful = false;
        try {
            // bulk操作之前回调
            listener.beforeBulk(executionId, bulkRequest);
            // 并发线程数控制 默认1 concurrentRequests
            // 构造方法定义 this.semaphore = new Semaphore(concurrentRequests > 0 ? concurrentRequests : 1);
            semaphore.acquire();
            toRelease = semaphore::release;
            CountDownLatch latch = new CountDownLatch(1);
            // 执行批量操作
            retry.withBackoff(consumer, bulkRequest, ActionListener.runAfter(new ActionListener<BulkResponse>() {
                @Override
                public void onResponse(BulkResponse response) {
                    // bulk执行完成回调
                    listener.afterBulk(executionId, bulkRequest, response);
                }

                @Override
                public void onFailure(Exception e) {
                    // bulk执行异常e回调
                    listener.afterBulk(executionId, bulkRequest, e);
                }
            }, () -> {
                semaphore.release();
                latch.countDown();
            }));
            bulkRequestSetupSuccessful = true;
            if (concurrentRequests == 0) {
                latch.await();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.info(() -> new ParameterizedMessage("Bulk request {} has been cancelled.", executionId), e);
            listener.afterBulk(executionId, bulkRequest, e);
        } catch (Exception e) {
            logger.warn(() -> new ParameterizedMessage("Failed to execute bulk request {}.", executionId), e);
            listener.afterBulk(executionId, bulkRequest, e);
        } finally {
            if (bulkRequestSetupSuccessful == false) {  // if we fail on client.bulk() release the semaphore
                toRelease.run();
            }
        }
    }
```

