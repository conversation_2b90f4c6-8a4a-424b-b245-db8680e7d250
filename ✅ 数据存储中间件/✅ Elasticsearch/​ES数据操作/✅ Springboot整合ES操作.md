# Springboot 整合 ES 操作

## Spring Data Elasticsearch

### 依赖

```xml
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
```

### yml 配置

```yaml
spring:
  elasticsearch:
    uris:
      - 172.16.251.52:9210
      - 172.16.251.52:9220
      - 172.16.251.52:9230
    username: elastic
    password: 123456
```

## Elasticsearch Rest High Level Client

### 依赖

```xml
 <elasticsearch.version>7.15.2</elasticsearch.version>

 <dependency>
    <groupId>org.elasticsearch</groupId>
    <artifactId>elasticsearch</artifactId>
    <version>${elasticsearch.version}</version>
</dependency>

<dependency>
    <groupId>org.elasticsearch.client</groupId>
    <artifactId>elasticsearch-rest-high-level-client</artifactId>
    <version>${elasticsearch.version}</version>
</dependency>

<dependency>
    <groupId>org.elasticsearch.client</groupId>
    <artifactId>elasticsearch-rest-client</artifactId>
    <version>${elasticsearch.version}</version>
</dependency>
```

### ES 客户端 Bean

```java
@Slf4j
@Configuration
public class RestHighLevelClientConfig {

    private final ElasticsearchProperties elasticsearchProperties;

    public RestHighLevelClientConfig(ElasticsearchProperties elasticsearchProperties) {
        this.elasticsearchProperties = elasticsearchProperties;
    }

    @Bean
    public RestHighLevelClient restHighLevelClient() {
        List<String> uris = elasticsearchProperties.getUris();
        List<HttpHost> httpHostList = new ArrayList<>();
        for (String url : uris) {
            String[] strings = url.split(":");
            httpHostList.add(new HttpHost(strings[0], Integer.parseInt(strings[1]), "http"));
        }

        // 认证信息
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(elasticsearchProperties.getUsername(), elasticsearchProperties.getPassword()));
        RestHighLevelClient client = new RestHighLevelClient(
                RestClient.builder(httpHostList.toArray(new HttpHost[]{}))
                        .setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
                            @Override
                            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                                httpClientBuilder.disableAuthCaching();
                                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                            }
                        }));
        log.info("RestHighLevelClient create.");
        return client;
    }
}
```
