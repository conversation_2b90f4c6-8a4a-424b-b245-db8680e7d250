# ES 基本CURD语法入门
## curl使用
### 免密码
> curl -X<VERB> '<PROTOCOL>://<HOST>:<PORT>/<PATH>?<QUERY_STRING>' -d '<BODY>'

- <VERB> ：适当的 HTTP 方法或动词。 例如，GET，POST，PUT，HEAD 或 DELETE
- <PROTOCOL>：http 或 https。 如果你在 Elasticsearch 前面有一个 HTTPS 代理，或者你使用 Elasticsearch 安全功能来加密 HTTP 通信，请使用后者
- <HOST>：Elasticsearch 集群中任何节点的主机名。 或者，将 localhost 用于本地计算机上的节点
- <PORT>：运行 Elasticsearch HTTP 服务的端口，默认为9200
- <PATH>：API 端点，可以包含多个组件，例如 _cluster/stats 或 _nodes/stats/jvm
- <QUERY_STRING>：任何可选的查询字符串参数。 例如，?pretty 将漂亮地打印 JSON 响应以使其更易于阅读
- <BODY>：JSON 编码的请求正文（如有必要）
```basic
curl -X GET http://es01:9210/_cluster/stats
curl -X GET http://es01:9210/_nodes/stats/jvm

```
### 密码认证
> curl -u elastic:password -X<VERB> '<PROTOCOL>://<HOST>:<PORT>/<PATH>?<QUERY_STRING>' -d '<BODY>'

## 检查es是否安装成功
> curl -XGET 'http://es01:9210/' -H 'Content-Type: application/json'
> curl -XGET -u "elastic:changeme"'http://es01:9210/' -H 'Content-Type: application/json'

```json
{
  "name" : "es-node01",
  "cluster_name" : "es-cluster",
  "cluster_uuid" : "i1-LTtguR3aqYyPTZp3MTg",
  "version" : {
    "number" : "7.17.9",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48222227ee6b9e70e502f0f0daa52435ee634d",
    "build_date" : "2023-01-31T05:34:43.305517834Z",
    "build_snapshot" : false,
    "lucene_version" : "8.11.1",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
```
## 集群状态
```bash
curl -X GET http://es02:9210/_cat/nodes\?v
curl -u elastic:123123 -X GET http://localhost:9200/_cat/nodes?v
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682345216434-6b598c04-924b-4084-b45c-36cd698ac059.png#averageHue=%230b3745&clientId=uf7afd127-bb9f-4&from=paste&height=112&id=u31b8874c&originHeight=224&originWidth=1526&originalType=binary&ratio=2&rotation=0&showTitle=false&size=52095&status=done&style=none&taskId=ue8af33cc-06b4-4561-805b-496b2d77b6d&title=&width=763)
## CURD
### 索引 Index
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-********-0567-4749-b837-ef69cc7d3521.png#averageHue=%23fcfcfc&clientId=uc014c44d-0e86-4&from=paste&height=375&id=udf15c555&originHeight=750&originWidth=1862&originalType=binary&ratio=2&rotation=0&showTitle=false&size=177352&status=done&style=none&taskId=u821310bc-3577-4dd7-b343-16b56c0a9e1&title=&width=931)
### 导入json文件
> @/home/<USER>/es/ac.json  要加@符号。

```yaml
curl -H "Content-Type: application/json" -XPOST "localhost:9210/bank/_bulk?pretty&refresh" --data-binary "@/home/<USER>/es/ac.json" -u elastic:123456
```
### DELETE 删除数据
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-f356f114-7af4-4df6-ad90-d047cb8d1ecc.png#averageHue=%23f2f4f7&clientId=ud2d44be7-c4e4-4&from=paste&height=325&id=u18590159&originHeight=650&originWidth=2346&originalType=binary&ratio=2&rotation=0&showTitle=false&size=78576&status=done&style=none&taskId=ucbd6a795-4179-408b-85a8-25f12b8ccda&title=&width=1173)
### PUT 设置数据到es
```basic
curl -XPUT 'http://es01:9210/twitter/_doc/1?pretty' -u elastic:123456 -H 'Content-Type: application/json' -d '
{
    "user": "kimchy",
    "post_date": "2009-11-15T13:12:00",
    "message": "Trying out Elasticsearch, so far so good?"
}'
 
curl -XPUT 'http://es01:9210/twitter/_doc/2?pretty' -u elastic:123456 -H 'Content-Type: application/json' -d '
{
    "user": "kimchy",
    "post_date": "2009-11-15T14:12:12",
    "message": "Another tweet, will it be indexed?"
}'
 
curl -XPUT 'http://es01:9210/twitter/_doc/3?pretty' -u elastic:123456 -H 'Content-Type: application/json' -d '
{
    "user": "elastic",
    "post_date": "2010-01-15T01:46:38",
    "message": "Building the site, should be kewl"
}'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682343773023-ea091aaa-1704-4e52-bf0e-698faf370df0.png#averageHue=%23022c37&clientId=uc014c44d-0e86-4&from=paste&height=449&id=u4f5cf21b&originHeight=898&originWidth=1934&originalType=binary&ratio=2&rotation=0&showTitle=false&size=84281&status=done&style=none&taskId=u9d8d441b-bd91-4f59-be5f-ea74de7ccb0&title=&width=967)
### GET 根据id查询
```basic
curl -XGET 'http://es01:9210/twitter/_doc/1?pretty=true'
curl -XGET 'http://es01:9210/twitter/_doc/2?pretty=true'
curl -XGET 'http://es01:9210/twitter/_doc/3?pretty=true'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682343755130-eb0a1daf-4754-443c-a7a8-58425f5baa63.png#averageHue=%23032e39&clientId=uc014c44d-0e86-4&from=paste&height=320&id=uf916781c&originHeight=640&originWidth=1442&originalType=binary&ratio=2&rotation=0&showTitle=false&size=66394&status=done&style=none&taskId=u30a7372b-c1f5-4985-833b-0f4b4d77cf9&title=&width=721)
### 根据索引索引
```basic
curl -XGET 'http://es01:9210/twitter/_search?q=user:kimchy&pretty=true'
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682343941303-53d55b61-7c6f-4af2-a332-90827f134076.png#averageHue=%23022c38&clientId=uc014c44d-0e86-4&from=paste&height=479&id=ubae2629a&originHeight=958&originWidth=1836&originalType=binary&ratio=2&rotation=0&showTitle=false&size=84158&status=done&style=none&taskId=ueb2c33f2-e8ee-4e45-aba1-ce6e195100e&title=&width=918)
```bash
# 搜索单个索引
curl -XGET 'http://es01:9210/twitter/_search?pretty=true' -H 'Content-Type: application/json' -d '
{
    "query" : {
        "match" : { "user": "kimchy" }
    }
}'

# 搜索查多个索引
curl -XGET 'http://es01:9210/twitter,another_user/_search?pretty=true' -H 'Content-Type: application/json' -d '
{
    "query" : {
        "match_all" : {}
    }
}'

# 搜索所有索引
curl -XGET 'http://es01:9210/_search?pretty=true' -H 'Content-Type: application/json' -d '
{
    "query" : {
        "match_all" : {}
    }
}'
```
### 分词搜索
```bash
curl -XPUT http://es01:9210/index -u elastic:123456

curl -XPOST http://es01:9210/index/_mapping -u elastic:123456 -H 'Content-Type:application/json' -d'
{
        "properties": {
            "content": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            }
        }

}'

curl -XPOST 'http://es01:9210/index/_create/1?pretty' -u elastic:123456 -H 'Content-Type:application/json' -d'
{"content":"美国留给伊拉克的是个烂摊子吗"}
'

curl -XPOST http://es01:9210/index/_create/2?pretty -u elastic:123456 -H 'Content-Type:application/json' -d'
{"content":"公安部：各地校车将享最高路权"}
'

curl -XPOST http://es01:9210/index/_create/3?pretty -u elastic:123456 -H 'Content-Type:application/json' -d'
{"content":"中韩渔警冲突调查：韩警平均每天扣1艘中国渔船"}
'

curl -XPOST http://es01:9210/index/_create/4?pretty -u elastic:123456 -H 'Content-Type:application/json' -d'
{"content":"中国驻洛杉矶领事馆遭亚裔男子枪击 嫌犯已自首"}
'

curl -XPOST http://es01:9210/index/_search?pretty -u elastic:123456 -H 'Content-Type:application/json' -d'
{
    "query" : { "match" : { "content" : "中国" }},
    "highlight" : {
        "pre_tags" : ["<tag1>", "<tag2>"],
        "post_tags" : ["</tag1>", "</tag2>"],
        "fields" : {
            "content" : {}
        }
    }
}
'
```
## 分页
[ES分页查询](https://www.yuque.com/newq/java-study/lciunqtvpdoymddy?view=doc_embed)
# 搜索方法
## 测试索引
```basic
{
    "mappings": {
        "properties": {
            "_class": {
                "doc_values": false,
                "index": false,
                "type": "keyword"
            },
            "id": {
                "type": "long"
            },
            "orderAmount": {
                "type": "long"
            },
            "orderDesc": {
                "type": "text"
            },
            "orderNo": {
                "type": "keyword"
            },
            "orderType": {
                "type": "integer"
            },
            "userPhone": {
                "analyzer": "ik_smart",
                "search_analyzer": "ik_max_word",
                "type": "text"
            },
            "username": {
                "analyzer": "ik_smart",
                "search_analyzer": "ik_max_word",
                "type": "text"
            }
        }
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682422865276-6784aa52-c738-4b1c-9e0d-955459533334.png#averageHue=%231a242d&clientId=u5f10e6f0-bba8-4&from=paste&height=144&id=ucf542b92&originHeight=288&originWidth=1834&originalType=binary&ratio=2&rotation=0&showTitle=false&size=93260&status=done&style=none&taskId=u6b1971a6-1e63-4918-ac3f-f9508f46447&title=&width=917)
## 前缀搜索-prefix
> 查询username字段 以 中国开头的字段
> Can only use prefix queries on keyword, text and wildcard fields 
> 只能在keyword, text and wildcard fields  使用前缀搜索，
> - 性能差，扫描整个倒排索引。

```basic
GET /order/_search
{
  "query": {
    "prefix": {
      "orderNo": {
        "value": "233333"
      }
    }
  }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682423696775-f7e86a99-9236-4960-a7ed-55d030fd9bf2.png#averageHue=%23f0f3f7&clientId=u5f10e6f0-bba8-4&from=paste&height=671&id=u8cc29a8c&originHeight=1342&originWidth=2884&originalType=binary&ratio=2&rotation=0&showTitle=false&size=241421&status=done&style=none&taskId=u0acd7f8f-1f26-4530-a04d-70569c6d1b8&title=&width=1442)
## 通配符搜索-wildcard
> 类似前缀搜索。
> ?：任意字符
> *：0个或任意多个字符
> - 性能差，扫描整个倒排索引。

```basic
GET /order/_search
{
  "query": {
    "wildcard": {
      "orderDesc": {
        "value": "*1*"
      }
    }
  }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1682423641340-3ca87c62-aa93-43a7-a589-81bcc29e1461.png#averageHue=%23f0f3f7&clientId=u5f10e6f0-bba8-4&from=paste&height=672&id=u09cc7101&originHeight=1344&originWidth=2828&originalType=binary&ratio=2&rotation=0&showTitle=false&size=240111&status=done&style=none&taskId=u713ed789-dd73-4629-bf3a-c67800d6558&title=&width=1414)
## 正则搜索-regexp
> 性能差
> [0-9]：指定范围内的数字
[a-z]：指定范围内的字母
.：一个字符
+：前面的正则表达式可以出现一次或多次

```basic
GET /order/_search
{
  "query": {
    "regexp": {
      "orderDesc": "测试+"
    }
  }
}
```
## 推荐搜索-match_phrase_prefix
```basic
GET /order/_search

{
  "query": {
    "match_phrase_prefix": {
      "orderDesc": {
        "query": "i 测",
        "slop":5,
        "max_expansions": 1
      }
    }
  }
}
```
## 误拼写时的模糊搜索-fuzzy
```basic
GET /order/_search

{
  "query": {
    "fuzzy": {
      "text": {
        "orderDesc": "测速",
        "fuzziness": 2
      }
    }
  }
}
```
# 查询数据
## 查询所有 match_all + 排序 sort
```basic
GET /bank/_search
{
  "query": { "match_all": {} },
  "sort": [
    { "account_number": "asc" }
  ]
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-0a753dab-4545-4e84-965f-fdb276b122f3.png#averageHue=%23eaf0f5&clientId=u2853c704-269a-4&from=paste&height=611&id=u9c906180&originHeight=1222&originWidth=2602&originalType=binary&ratio=2&rotation=0&showTitle=false&size=344127&status=done&style=none&taskId=u76bdbc2b-a1fb-474d-92c1-b7fd9de6ebb&title=&width=1301)
## 分页 from + size
```basic
GET /bank/_search
{
  "query": { "match_all": {} },
  "sort": [
    { "account_number": "asc" }
  ],
  "from": 10,
  "size": 1
}
```
## 按字段匹配 match
> 查询address 字段中包含 mill 或者 lane的数据，不区分大小写。

```basic
GET /bank/_search
{
  "query": { "match": { "address": "mill lane" } }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-51ecfd53-2398-4470-a4f5-4a6fcb023bdb.png#averageHue=%23ecf1f6&clientId=u2853c704-269a-4&from=paste&height=597&id=u5cc6b9b6&originHeight=1194&originWidth=2606&originalType=binary&ratio=2&rotation=0&showTitle=false&size=386844&status=done&style=none&taskId=u26c63b3e-7129-44ef-9336-99c6099c8a2&title=&width=1303)
## 按段落匹配 match_phrase
> 匹配段落  词组 “mill lane”

```basic
GET /bank/_search
{
  "query": { "match_phrase": { "address": "mill lane" } }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-adb3825e-cd23-4569-ad3e-a30be0d34637.png#averageHue=%23eef2f6&clientId=u2853c704-269a-4&from=paste&height=688&id=uf495850b&originHeight=1376&originWidth=2660&originalType=binary&ratio=2&rotation=0&showTitle=false&size=383986&status=done&style=none&taskId=ue58fe50d-d8b6-4b7e-96f6-9cd77c8630b&title=&width=1330)
## 多条件查询 bool
> must, should, must_not 和 filter 都是bool查询的子句

```basic
GET /bank/_search
{
  "query": {
    "bool": {
      "must": [
        { "match": { "age": "40" } }
      ],
      "must_not": [
        { "match": { "state": "ID" } }
      ]
    }
  }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-adc40b4f-3270-45ff-a04d-f045ceb784ee.png#averageHue=%23e4edf5&clientId=u2853c704-269a-4&from=paste&height=636&id=u5760ffd6&originHeight=1272&originWidth=2606&originalType=binary&ratio=2&rotation=0&showTitle=false&size=386462&status=done&style=none&taskId=u779673d3-dcc5-4fae-a7be-a528c92ee68&title=&width=1303)
## 查询条件 query or filter
```basic
GET /bank/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "state": "ND"
          }
        }
      ],
      "filter": [
        {
          "term": {
            "age": "40"
          }
        },
        {
          "range": {
            "balance": {
              "gte": 20000,
              "lte": 30000
            }
          }
        }
      ]
    }
  }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/*************-9be95913-e8fc-4d7e-9020-24b6182b2126.png#averageHue=%23d5e6f2&clientId=u2853c704-269a-4&from=paste&height=599&id=u7bcc5184&originHeight=1198&originWidth=2640&originalType=binary&ratio=2&rotation=0&showTitle=false&size=323781&status=done&style=none&taskId=u7bfa4296-3a75-4f4a-b33d-fabf7905b0e&title=&width=1320)
# 聚合查询 Aggregation
> 根据state分组，并计算平均数，且按平均数排序，从大到小

```basic
GET /bank/_search
{
  "size": 0,
  "aggs": {
    "group_by_state": {
      "terms": {
        "field": "state.keyword",
        "order": {
          "average_balance": "desc"
        }
      },
      "aggs": {
        "average_balance": {
          "avg": {
            "field": "balance"
          }
        }
      }
    }
  }
}
```
```basic
{
  "took" : 27,
  "timed_out" : false,
  "_shards" : {
    "total" : 1,
    "successful" : 1,
    "skipped" : 0,
    "failed" : 0
  },
  "hits" : {
    "total" : {
      "value" : 1000,
      "relation" : "eq"
    },
    "max_score" : null,
    "hits" : [ ]
  },
  "aggregations" : {
    "group_by_state" : {
      "doc_count_error_upper_bound" : -1,
      "sum_other_doc_count" : 827,
      "buckets" : [
        {
          "key" : "CO",
          "doc_count" : 14,
          "average_balance" : {
            "value" : 32460.***********
          }
        },
        {
          "key" : "NE",
          "doc_count" : 16,
          "average_balance" : {
            "value" : 32041.5625
          }
        },
        {
          "key" : "AZ",
          "doc_count" : 14,
          "average_balance" : {
            "value" : 31634.785714285714
          }
        },
        {
          "key" : "MT",
          "doc_count" : 17,
          "average_balance" : {
            "value" : 31147.41176470588
          }
        },
        {
          "key" : "VA",
          "doc_count" : 16,
          "average_balance" : {
            "value" : 30600.0625
          }
        },
        {
          "key" : "GA",
          "doc_count" : 19,
          "average_balance" : {
            "value" : 30089.0
          }
        },
        {
          "key" : "MA",
          "doc_count" : 24,
          "average_balance" : {
            "value" : 29600.333333333332
          }
        },
        {
          "key" : "IL",
          "doc_count" : 22,
          "average_balance" : {
            "value" : 29489.727272727272
          }
        },
        {
          "key" : "NM",
          "doc_count" : 14,
          "average_balance" : {
            "value" : 28792.64285714286
          }
        },
        {
          "key" : "LA",
          "doc_count" : 17,
          "average_balance" : {
            "value" : 28791.823529411766
          }
        }
      ]
    }
  }
}

```
