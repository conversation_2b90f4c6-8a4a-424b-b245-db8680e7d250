# ES 三种分页查询方法

## from + size 深度分页

### http 实现

```shell
GET order/_search
{
  "from": 3,
  "size": 9999
}
```

默认 from + size < 10000, 超过会报错
![](images/1684829171252.png)
修改查询上限 max_result_window

```shell

PUT order/_settings
{
  "index":{
    "max_result_window":10001
  }
}
```

### Java 实现

```java
   public SearchResponse page(@NonNull Integer pageSize, Integer pageNum, Map<String, SortOrder> sortOrderMap, @NonNull String... indexName) throws IOException {
        Assert.isTrue(pageSize > 0, "pageSize must > 0");

        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 计算记录偏移量，从0开始
        if (pageNum != null) {
            sourceBuilder.from((pageNum - 1) * pageSize);
        }
        // 设置每页显示的记录数
        sourceBuilder.size(pageSize);
        // 排序
        if (!CollectionUtils.isEmpty(sortOrderMap)) {
            sortOrderMap.forEach(sourceBuilder::sort);
        }
        searchRequest.source(sourceBuilder);
        return client.search(searchRequest, RequestOptions.DEFAULT);
    }
```

### 说明

查询每个分片的分页数据，在客户端再聚合排序，深度分页，查询的数据越多，有可能发生 OOM。
分页 10 条，第一页 1-10，第 100 页，1-110， 第 10000 页，1-10010。。。

## scroll 滚动

### http 实现

```shell
# 第一次查询 scroll=1m 是本次快照的结果缓存的有效时间
GET order/_search?scroll=1m
{

}

# 根据scroll_id查询后续
POST /_search/scroll
{
  "scroll":"1m",
  "scroll_id":"FGluY2x1ZGVfY29udGV4dF91dWlkDnF1ZXJ5VGhlbkZldGNoAxZhTU1GdGItLVRMVzBOeVY0c1pJQThBAAAAAAAKUx4WeXYyRWRmbVJRaEtjT2VyU3QxYW5GURZJMDlJNkh2VlFiaXNVR3M1ejU5b21BAAAAAAAPKOgWSWZxb09ZREpRMDZIR3hldGJuR3RRZxY3dkNKcUlVZlRlT0xPcENXLUdmN2NRAAAAAAAD2L0WdXVibVZxSm1RMFdHZU1xMndvVGdaUQ=="

}

# 清除滚动id
DELETE /_search/scroll
{
    "scroll_id":"FGluY2x1ZGVfY29udGV4dF91dWlkDnF1ZXJ5VGhlbkZldGNoAxZhTU1GdGItLVRMVzBOeVY0c1pJQThBAAAAAAAKUx4WeXYyRWRmbVJRaEtjT2VyU3QxYW5GURZJMDlJNkh2VlFiaXNVR3M1ejU5b21BAAAAAAAPKOgWSWZxb09ZREpRMDZIR3hldGJuR3RRZxY3dkNKcUlVZlRlT0xPcENXLUdmN2NRAAAAAAAD2L0WdXVibVZxSm1RMFdHZU1xMndvVGdaUQ=="
}
```

### Java 实现

```java
    /**
     - 滚动分页查询
     */
    public SearchResponse scrollQuery(String indexName, Integer pageSize, String scrollId) throws IOException {
        // 滚动查询 超时时间
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        if (StrUtil.isNotEmpty(scrollId)) {
            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
            searchScrollRequest.scroll(scroll);
            return client.scroll(searchScrollRequest, RequestOptions.DEFAULT);
        }
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        // 一次查询数量,
        sourceBuilder.size(pageSize);
        searchRequest.source(sourceBuilder);

        searchRequest.scroll(scroll);

        return client.search(searchRequest, RequestOptions.DEFAULT);
    }

    /**
     - 清除滚动id
     *
     - @param scrollId 滚动id
     */
    public ClearScrollResponse clearScroll(String scrollId) throws IOException {
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        return client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
    }
```

### 说明

scroll 先查询第一批数据，获取 scroll_id，带上 scroll_id 再查询下一批数据，后续一直带上。
结束要清除 scroll_id。
scroll 搜索会在第一次搜索的时候，保存一个当时的视图快照，之后只会基于该视图快照搜索数据，期间数据变更，感知不到。
因此，**滚动查询不适合实时性要求高的搜索场景。**

注意：

1. GET order/\_search?scroll=1m 1m 是本次快照的结果缓存的有效时间，不是查询超时时间
2. 滚动查询不适合实时性要求高的搜索场景
3. 查询完毕需要清除 scroll_id

## searchAfter

### http 实现

```shell
GET order/_search
{
    "from":0,
    "size":1,

    "sort":[
        {
            "id":{
                "order":"asc"
            },
             "orderNo":{
                "order":"asc"
            }
        }
    ],
    "search_after":[
          40,
          "53638094"
    ]
}
```

### Java 实现

```java
    /**
     - searchAfter分页查询
     */
    public SearchResponse searchAfter(SearchAfterDTO searchAfterDTO) throws IOException {
        SearchRequest searchRequest = new SearchRequest(searchAfterDTO.getIndexName());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(0);
        sourceBuilder.size(searchAfterDTO.getPageSize());
        Map<String, String> sortOrderMap = searchAfterDTO.getSortOrderMap();
        Assert.notNull(sortOrderMap, "sortOrderMap不能为空!");
        List<SortBuilder<?>> sorts = new ArrayList<>();
        for (Map.Entry<String, String> entry : sortOrderMap.entrySet()) {
            sorts.add(SortBuilders.fieldSort(entry.getKey()).order(SortOrder.fromString(entry.getValue())));
        }
        sourceBuilder.sort(sorts);
        sourceBuilder.searchAfter(searchAfterDTO.getSearchAfter());
        searchRequest.source(sourceBuilder);
        return client.search(searchRequest, RequestOptions.DEFAULT);
    }
```

### 说明

1. 实时查询，在查询过程中，排序顺序可能会随着索引的更新和删除而改变。每次查询带上上一次查询返回的 search_after。
2. 只能往后一页一页查询
3. 使用 search_after 时，参数 from 必须设置为 0（或-1）
4. https://www.elastic.co/guide/en/elasticsearch/reference/6.0/search-request-search-after.html

## 参考

https://zhuanlan.zhihu.com/p/624297206
