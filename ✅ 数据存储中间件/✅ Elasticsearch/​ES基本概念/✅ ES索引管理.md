# ES索引管理
## 索引概念
## 索引手动创建

- **settings**: 用来设置分片,副本等配置信息
   - number_of_replicas 副本数
   - number_of_shards 分片数
- **mappings**: 字段映射，类型等
```basic
PUT /my_index
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text"
      },
      "name": {
        "type": "text",
        "fields": {
          "keyword": {
            "type": "keyword",
            "ignore_above": 256
          }
        }
      },
      "age": {
        "type": "long"
      }
    }
  },
  "settings": {
    "number_of_replicas": 3,
    "number_of_shards": 3
  }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683209133594-ab137718-b269-4d9e-9466-7bbf884d0097.png#averageHue=%23e9eef4&clientId=u8817a324-9aaa-4&from=paste&height=528&id=u0141e630&originHeight=1056&originWidth=2896&originalType=binary&ratio=2&rotation=0&showTitle=false&size=183718&status=done&style=none&taskId=uc468756b-d324-49f7-bf0e-5f95e700aa4&title=&width=1448)
## 修改索引-副本数
```basic
PUT /my_index/_settings
{
  "settings": {
    "number_of_replicas": 1
  }
}
```
## 打开、关闭索引
> 一旦索引被关闭，那么这个索引只能显示元数据信息，**不能够进行读写操作**。

```basic
POST /my_index/_close

POST /my_index/_open
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683210064796-415ea07a-3e3e-41e3-98b9-4fea802ac457.png#averageHue=%23f0f2f6&clientId=u8817a324-9aaa-4&from=paste&height=283&id=ue7c40bc7&originHeight=566&originWidth=3408&originalType=binary&ratio=2&rotation=0&showTitle=false&size=152825&status=done&style=none&taskId=u74c0672c-f07f-4624-a665-072d7b90678&title=&width=1704)
无法插入数据
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683210122074-88e7a2b4-9163-4d9a-a24f-d3f4695a7bfe.png#averageHue=%23eef2f6&clientId=u8817a324-9aaa-4&from=paste&height=475&id=uc729f116&originHeight=950&originWidth=3002&originalType=binary&ratio=2&rotation=0&showTitle=false&size=225894&status=done&style=none&taskId=u4db68cc7-9cba-4d15-aa2d-5295913a2cd&title=&width=1501)
无法读取数据
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683210263181-34a3f9d2-32dd-4552-939b-5c8eb5cebd50.png#averageHue=%23f2f4f7&clientId=u8817a324-9aaa-4&from=paste&height=554&id=u1b45ed79&originHeight=1108&originWidth=3112&originalType=binary&ratio=2&rotation=0&showTitle=false&size=269867&status=done&style=none&taskId=u7e02bab9-6094-4902-be21-5be442e8546&title=&width=1556)
## 删除索引
```basic
DELETE /my_index
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683209041041-619e727c-5792-448b-9a1a-4b3e61bb8d28.png#averageHue=%23ebeff4&clientId=u8817a324-9aaa-4&from=paste&height=96&id=u6b339083&originHeight=192&originWidth=474&originalType=binary&ratio=2&rotation=0&showTitle=false&size=10064&status=done&style=none&taskId=u7708a4c2-a549-4ac8-96c1-4c0b0413921&title=&width=237)
## 插入数据
```basic
POST /my_index/_doc
{
	"title": "测试插入数据",
  "name": "插入",
  "age": 10
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683209275384-b5a33d6b-9f2c-49cf-91f0-c29171b76631.png#averageHue=%23f2f4f7&clientId=u8817a324-9aaa-4&from=paste&height=309&id=u5eaa08a4&originHeight=618&originWidth=2494&originalType=binary&ratio=2&rotation=0&showTitle=false&size=132468&status=done&style=none&taskId=u1e3bf4a1-7a63-4d62-bbfc-94f1ba3757e&title=&width=1247)
### 插入数据格式不对的数据
> 若数据格式是数字类型，value只能是数字

![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683209387334-cea33ddf-914c-43bc-955d-f2aee6462a7e.png#averageHue=%23f1f3f7&clientId=u8817a324-9aaa-4&from=paste&height=413&id=udf8e1d1d&originHeight=826&originWidth=3376&originalType=binary&ratio=2&rotation=0&showTitle=false&size=239182&status=done&style=none&taskId=uf71acc69-9909-4fd1-8abb-ad99a68bd69&title=&width=1688)
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683209417469-1f7ff129-a494-4d9f-b0bb-edd5cde7a87f.png#averageHue=%23eef1f5&clientId=u8817a324-9aaa-4&from=paste&height=288&id=uec94a60d&originHeight=576&originWidth=2564&originalType=binary&ratio=2&rotation=0&showTitle=false&size=124428&status=done&style=none&taskId=u81e6c0d9-ae43-46c2-b4ad-a33543ad9b6&title=&width=1282)
## 查看索引信息
### mapping
```basic
GET /my_index/_mapping
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683210413614-ca33f36f-9f99-4f7d-b47b-88264a2c1804.png#averageHue=%23f2f4f7&clientId=u8817a324-9aaa-4&from=paste&height=483&id=uaeb87c8e&originHeight=966&originWidth=2576&originalType=binary&ratio=2&rotation=0&showTitle=false&size=161223&status=done&style=none&taskId=u9349731b-61f8-4757-b503-c4bcf192936&title=&width=1288)
### setting
```basic
GET /my_index/_setting
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683210535323-275ba232-1332-45e3-8b7c-369edd633f14.png#averageHue=%23f1f3f7&clientId=u8817a324-9aaa-4&from=paste&height=443&id=u0dc8b785&originHeight=886&originWidth=2788&originalType=binary&ratio=2&rotation=0&showTitle=false&size=185961&status=done&style=none&taskId=uee3000e6-11e7-4fe5-bd6b-83228c8793a&title=&width=1394)
