# ShardingSphere Props 配置属性详解

## 📋 概述

`props` 是 ShardingSphere 的全局配置属性，用于控制 ShardingSphere 的运行时行为。本文档详细介绍了 ShardingSphere 5.x 版本中所有可用的 props 配置属性。

## 🔧 核心配置属性

### 1. SQL 相关配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `sql-show` | boolean | false | 是否在日志中打印 SQL |
| `sql-simple` | boolean | false | 是否在日志中打印简单风格的 SQL |
| `sql-comment-parse-enabled` | boolean | false | 是否解析 SQL 注释 |
| `sql-federation-type` | String | NONE | SQL 联邦执行器类型：NONE/ORIGINAL/ADVANCED |

```yaml
props:
  sql-show: true
  sql-simple: false
  sql-comment-parse-enabled: false
  sql-federation-type: NONE
```

### 2. 执行器配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `executor-size` | int | 0 | 任务处理线程池大小，0表示无限制 |
| `max-connections-size-per-query` | int | 1 | 每次查询在每个数据库实例中的最大连接数 |
| `kernel-executor-size` | int | 0 | 内核处理线程池大小，0表示使用CPU核数 |

```yaml
props:
  executor-size: 16
  max-connections-size-per-query: 1
  kernel-executor-size: 16
```

### 3. 检查和验证配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `check-table-metadata-enabled` | boolean | false | 是否检查分片元数据结构一致性 |
| `check-duplicate-table-enabled` | boolean | false | 是否检查重复表 |

```yaml
props:
  check-table-metadata-enabled: false
  check-duplicate-table-enabled: false
```

## 🎯 代理模式配置（Proxy 专用）

### 4. 代理相关配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `proxy-frontend-flush-threshold` | int | 128 | 传输数据条数的 IO 刷新阈值 |
| `proxy-hint-enabled` | boolean | false | 是否允许使用 Hint |
| `proxy-backend-query-fetch-size` | int | -1 | 后端查询的一次获取大小 |
| `proxy-frontend-executor-size` | int | 0 | 前端 Netty 执行器线程大小 |

```yaml
props:
  proxy-frontend-flush-threshold: 128
  proxy-hint-enabled: false
  proxy-backend-query-fetch-size: -1
  proxy-frontend-executor-size: 0
```

## 🔍 功能特性配置

### 5. 分布式事务配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `xa-transaction-manager-type` | String | Atomikos | XA事务管理器类型：Atomikos/Narayana/Bitronix |

```yaml
props:
  xa-transaction-manager-type: Atomikos
```

### 6. 数据脱敏配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `query-with-cipher-column` | boolean | true | 是否使用加密列进行查询 |

```yaml
props:
  query-with-cipher-column: true
```

### 7. 影子库配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `shadow-enabled` | boolean | false | 是否开启影子库功能 |

```yaml
props:
  shadow-enabled: false
```

### 8. 读写分离配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `load-balance-algorithm-type` | String | ROUND_ROBIN | 读库负载均衡算法类型 |

```yaml
props:
  load-balance-algorithm-type: ROUND_ROBIN
```

## 📊 监控和性能配置

### 9. 性能监控配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `show-process-list-enabled` | boolean | false | 是否允许查看处理中的运行任务 |
| `lock-wait-timeout-milliseconds` | long | 50000 | 锁等待超时毫秒数 |
| `cached-connections` | int | 0 | 每个物理数据源维持的空闲连接数 |

```yaml
props:
  show-process-list-enabled: false
  lock-wait-timeout-milliseconds: 50000
  cached-connections: 0
```

### 10. 系统日志配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `system-log-level` | String | INFO | 系统日志级别：ERROR/WARN/INFO/DEBUG/TRACE |

```yaml
props:
  system-log-level: INFO
```

## 📝 完整配置示例

### 开发环境配置
```yaml
props:
  # SQL 显示
  sql-show: true
  sql-simple: false
  
  # 执行器配置
  executor-size: 16
  max-connections-size-per-query: 1
  kernel-executor-size: 16
  
  # 检查配置
  check-table-metadata-enabled: false
  check-duplicate-table-enabled: false
  
  # 日志配置
  system-log-level: DEBUG
  
  # 功能配置
  query-with-cipher-column: true
  shadow-enabled: false
```

### 生产环境配置
```yaml
props:
  # SQL 显示（生产环境建议关闭）
  sql-show: false
  sql-simple: false
  
  # 执行器配置
  executor-size: 32
  max-connections-size-per-query: 1
  kernel-executor-size: 32
  
  # 检查配置
  check-table-metadata-enabled: false
  check-duplicate-table-enabled: false
  
  # 日志配置
  system-log-level: WARN
  
  # 性能配置
  lock-wait-timeout-milliseconds: 30000
  cached-connections: 10
  
  # 功能配置
  query-with-cipher-column: true
  shadow-enabled: false
```

## ⚠️ 注意事项

### 性能影响
- `sql-show: true` 会影响性能，生产环境建议设置为 `false`
- `executor-size` 建议根据 CPU 核数和并发量调整
- `max-connections-size-per-query` 控制连接数，避免连接池耗尽

### 版本兼容性
- 不同版本的 ShardingSphere 支持的属性可能有差异
- 建议查阅对应版本的官方文档确认

### 模式限制
- 以 `proxy-` 开头的配置只在 ShardingSphere-Proxy 模式下生效
- JDBC 模式下这些配置会被忽略

## 🎯 最佳实践

1. **开发环境**：开启 SQL 显示，便于调试
2. **测试环境**：适中的日志级别，开启必要的检查
3. **生产环境**：关闭 SQL 显示，优化性能配置
4. **监控配置**：根据需要开启进程列表查看功能
5. **线程池配置**：根据硬件资源合理配置线程池大小

## 📚 参考资料

- [ShardingSphere 官方文档](https://shardingsphere.apache.org/)
- [配置手册](https://shardingsphere.apache.org/document/current/cn/user-manual/shardingsphere-jdbc/yaml-config/)
