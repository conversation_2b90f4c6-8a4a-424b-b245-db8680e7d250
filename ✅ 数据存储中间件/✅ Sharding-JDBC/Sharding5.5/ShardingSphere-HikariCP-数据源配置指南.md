# ShardingSphere HikariCP 数据源配置指南

## 📋 概述

本文档详细介绍如何在 ShardingSphere 中配置 HikariCP 数据源，包括基础配置、性能优化、生产环境最佳实践等内容。

## 🔧 基础配置

### 1. 基本数据源配置

```yaml
dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: ************************************_name?useSSL=false&serverTimezone=Asia/Shanghai
    username: root
    password: password
    
  ds_0:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: ***********************************************************************************
    username: root
    password: password
```

### 2. 完整的 HikariCP 配置

```yaml
dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: *************************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5                    # 最小空闲连接数
      maximum-pool-size: 20              # 最大连接池大小
      auto-commit: true                  # 自动提交
      idle-timeout: 600000               # 空闲连接超时时间(毫秒)
      pool-name: "master-pool"           # 连接池名称
      max-lifetime: 1800000              # 连接最大生命周期(毫秒)
      connection-timeout: 30000          # 连接超时时间(毫秒)
      connection-test-query: "SELECT 1"  # 连接测试查询
      leak-detection-threshold: 60000    # 连接泄漏检测阈值(毫秒)
```

## 📊 HikariCP 参数详解

### 核心参数

| 参数名 | 类型 | 默认值 | 说明 | 推荐值 |
|--------|------|--------|------|--------|
| `minimum-idle` | int | 10 | 最小空闲连接数 | 5-10 |
| `maximum-pool-size` | int | 10 | 最大连接池大小 | 20-50 |
| `connection-timeout` | long | 30000 | 连接超时时间(ms) | 30000 |
| `idle-timeout` | long | 600000 | 空闲连接超时时间(ms) | 600000 |
| `max-lifetime` | long | 1800000 | 连接最大生命周期(ms) | 1800000 |
| `auto-commit` | boolean | true | 是否自动提交事务 | true |

### 高级参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `connection-test-query` | String | null | 连接测试查询语句 |
| `leak-detection-threshold` | long | 0 | 连接泄漏检测阈值(ms) |
| `validation-timeout` | long | 5000 | 连接验证超时时间(ms) |
| `initialization-fail-timeout` | long | 1 | 初始化失败超时时间 |
| `isolate-internal-queries` | boolean | false | 隔离内部查询 |
| `allow-pool-suspension` | boolean | false | 允许池挂起 |
| `read-only` | boolean | false | 只读模式 |
| `register-mbeans` | boolean | false | 注册 MBeans 用于监控 |

## 🚀 性能优化配置

### 1. MySQL 连接 URL 优化

```yaml
# 性能优化的 MySQL URL 参数
url: ************************************?
  useSSL=false&
  serverTimezone=Asia/Shanghai&
  allowPublicKeyRetrieval=true&
  nullCatalogMeansCurrent=true&
  rewriteBatchedStatements=true&
  useUnicode=true&
  characterEncoding=utf8&
  autoReconnect=true&
  failOverReadOnly=false&
  cachePrepStmts=true&                   # 启用预处理语句缓存
  prepStmtCacheSize=250&                 # 预处理语句缓存大小
  prepStmtCacheSqlLimit=2048&            # 预处理语句缓存SQL长度限制
  useServerPrepStmts=true&               # 使用服务器端预处理语句
  useLocalSessionState=true&             # 使用本地会话状态
  useLocalTransactionState=true&         # 使用本地事务状态
  cacheResultSetMetadata=true&           # 缓存结果集元数据
  cacheServerConfiguration=true&         # 缓存服务器配置
  elideSetAutoCommits=true&              # 省略设置自动提交
  maintainTimeStats=false                # 不维护时间统计
```

### 2. MySQL URL 参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `cachePrepStmts` | 启用预处理语句缓存 | true |
| `prepStmtCacheSize` | 预处理语句缓存大小 | 250 |
| `prepStmtCacheSqlLimit` | 缓存SQL长度限制 | 2048 |
| `useServerPrepStmts` | 使用服务器端预处理 | true |
| `rewriteBatchedStatements` | 重写批处理语句 | true |
| `cacheResultSetMetadata` | 缓存结果集元数据 | true |
| `cacheServerConfiguration` | 缓存服务器配置 | true |
| `useLocalSessionState` | 使用本地会话状态 | true |
| `useLocalTransactionState` | 使用本地事务状态 | true |
| `elideSetAutoCommits` | 省略设置自动提交 | true |
| `maintainTimeStats` | 维护时间统计 | false |

## 🎯 环境配置建议

### 开发环境配置

```yaml
dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: ****************************************************************************
    username: dev_user
    password: dev_password
    hikari:
      minimum-idle: 2
      maximum-pool-size: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: "dev-master-pool"
      connection-test-query: "SELECT 1"
      leak-detection-threshold: 60000
      register-mbeans: true              # 便于开发调试
```

### 测试环境配置

```yaml
dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************************
    username: test_user
    password: test_password
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 1800000
      pool-name: "test-master-pool"
      connection-test-query: "SELECT 1"
      leak-detection-threshold: 30000
      register-mbeans: true
      validation-timeout: 5000
```

### 生产环境配置

```yaml
dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: ***********************************************************************************************************************
    username: ${DB_USERNAME:prod_user}   # 使用环境变量
    password: ${DB_PASSWORD:prod_pass}   # 使用环境变量
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      connection-timeout: 30000
      idle-timeout: 300000               # 5分钟
      max-lifetime: 1800000              # 30分钟
      pool-name: "prod-master-pool"
      connection-test-query: "SELECT 1"
      leak-detection-threshold: 60000
      register-mbeans: true
      validation-timeout: 5000
      initialization-fail-timeout: 1
      isolate-internal-queries: false
      allow-pool-suspension: false
      read-only: false
```

## 🔒 安全配置

### 1. SSL 连接配置

```yaml
# 启用 SSL 的安全连接
url: ************************************?
  useSSL=true&
  requireSSL=true&
  verifyServerCertificate=true&
  serverTimezone=Asia/Shanghai&
  allowPublicKeyRetrieval=false
```

### 2. 环境变量配置

```yaml
dataSources:
  master:
    username: ${DB_MASTER_USERNAME:root}
    password: ${DB_MASTER_PASSWORD:password}
    url: ${DB_MASTER_URL:************************************}
```

### 3. 敏感信息加密

```bash
# 使用 Jasypt 加密敏感信息
password: ENC(encrypted_password_here)
```

## 📈 监控和调优

### 1. JMX 监控配置

```yaml
hikari:
  register-mbeans: true                  # 启用 JMX 监控
  pool-name: "application-pool"          # 设置有意义的池名称
```

### 2. 连接泄漏检测

```yaml
hikari:
  leak-detection-threshold: 60000        # 60秒检测连接泄漏
```

### 3. 性能监控指标

- **活跃连接数**：当前正在使用的连接数
- **空闲连接数**：当前空闲的连接数
- **等待连接数**：等待获取连接的线程数
- **连接创建时间**：创建新连接的平均时间
- **连接使用时间**：连接被借出的平均时间

## ⚠️ 注意事项

### 性能相关
1. `maximum-pool-size` 不是越大越好，建议根据 CPU 核数设置
2. `minimum-idle` 应该根据应用的基础负载设置
3. 启用预处理语句缓存可以显著提升性能

### 安全相关
1. 生产环境必须启用 SSL 连接
2. 使用环境变量管理敏感信息
3. 定期更新数据库驱动版本

### 稳定性相关
1. 设置合理的连接超时时间
2. 启用连接泄漏检测
3. 定期监控连接池状态

## 🎯 最佳实践

1. **连接池大小计算公式**：`connections = ((core_count * 2) + effective_spindle_count)`
2. **开发环境**：小连接池，启用详细监控
3. **生产环境**：合理连接池，启用安全配置
4. **监控告警**：设置连接池使用率告警
5. **定期检查**：定期检查连接泄漏和性能指标

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. 连接池耗尽
```
症状：获取连接超时
原因：连接泄漏或连接池配置过小
解决：
- 启用连接泄漏检测
- 检查代码中是否正确关闭连接
- 适当增加 maximum-pool-size
```

#### 2. 连接频繁创建销毁
```
症状：性能下降，大量连接创建日志
原因：minimum-idle 设置过小
解决：
- 增加 minimum-idle 值
- 调整 idle-timeout 参数
```

#### 3. 数据库连接超时
```
症状：连接建立失败
原因：网络问题或数据库负载过高
解决：
- 增加 connection-timeout 值
- 检查网络连通性
- 优化数据库性能
```

### 调试配置

```yaml
# 开启详细日志用于调试
hikari:
  register-mbeans: true
  leak-detection-threshold: 10000       # 10秒检测泄漏

# 日志配置
logging:
  level:
    com.zaxxer.hikari: DEBUG
    com.zaxxer.hikari.HikariConfig: DEBUG
    com.zaxxer.hikari.pool.HikariPool: DEBUG
```

## 🔧 完整配置模板

### ShardingSphere + HikariCP 完整配置

```yaml
# sharding.yaml
mode:
  type: Standalone

dataSources:
  master:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: *************************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 600000
      pool-name: "master-pool"
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: "SELECT 1"
      leak-detection-threshold: 60000
      validation-timeout: 5000
      register-mbeans: true

  ds_0:
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    url: *****************************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 600000
      pool-name: "ds_0-pool"
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: "SELECT 1"
      leak-detection-threshold: 60000
      validation-timeout: 5000
      register-mbeans: true

rules:
  - !SINGLE
    tables:
      - "*.*"
    defaultDataSource: master

  - !SHARDING
    tables:
      orders:
        actualDataNodes: ds_0.orders_${0..5}
        tableStrategy:
          standard:
            shardingColumn: order_no
            shardingAlgorithmName: table_inline
    shardingAlgorithms:
      table_inline:
        type: INLINE
        props:
          algorithm-expression: orders_${order_no.hashCode() % 6}

props:
  sql-show: true
  executor-size: 16
  max-connections-size-per-query: 1
  check-table-metadata-enabled: false
  system-log-level: INFO
```

## 📊 性能基准测试

### 不同配置的性能对比

| 配置场景 | 连接池大小 | TPS | 平均响应时间 | 适用场景 |
|----------|------------|-----|--------------|----------|
| 小型应用 | 5-10 | 1000 | 50ms | 开发/测试环境 |
| 中型应用 | 20-30 | 5000 | 30ms | 中等并发 |
| 大型应用 | 50-100 | 10000+ | 20ms | 高并发生产环境 |

### 压测建议

```bash
# 使用 JMeter 或 wrk 进行压力测试
wrk -t12 -c400 -d30s --script=test.lua http://localhost:8080/api/orders

# 监控连接池状态
jconsole # 连接到应用查看 HikariCP MBeans
```

## 📚 参考资料

- [HikariCP 官方文档](https://github.com/brettwooldridge/HikariCP)
- [HikariCP 配置详解](https://github.com/brettwooldridge/HikariCP#configuration-knobs-baby)
- [ShardingSphere 官方文档](https://shardingsphere.apache.org/)
- [MySQL Connector/J 配置](https://dev.mysql.com/doc/connector-j/8.0/en/)
- [数据库连接池最佳实践](https://vladmihalcea.com/the-anatomy-of-connection-pooling/)

## 📝 更新日志

- **v1.0** - 初始版本，包含基础配置
- **v1.1** - 添加性能优化和故障排查
- **v1.2** - 完善监控和最佳实践
