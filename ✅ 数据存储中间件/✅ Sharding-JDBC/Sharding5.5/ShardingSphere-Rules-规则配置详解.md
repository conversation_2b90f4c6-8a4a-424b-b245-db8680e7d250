# ShardingSphere Rules 规则配置详解

## 📋 概述

ShardingSphere 的 `rules` 配置是核心功能的配置部分，用于定义分片、读写分离、数据脱敏、影子库等各种数据处理规则。本文档详细介绍所有可用的规则类型和配置方式。

## 🔧 规则类型概览

ShardingSphere 5.x 支持以下规则类型：

| 规则类型 | 标识符 | 功能描述 |
|----------|--------|----------|
| 分片规则 | `!SHARDING` | 数据分库分表 |
| 单表规则 | `!SINGLE` | 单表路由 |
| 读写分离规则 | `!READWRITE_SPLITTING` | 主从读写分离 |
| 数据库发现规则 | `!DATABASE_DISCOVERY` | 数据库高可用 |
| 数据脱敏规则 | `!ENCRYPT` | 敏感数据加密 |
| 影子库规则 | `!SHADOW` | 影子库测试 |
| SQL 解析规则 | `!SQL_PARSER` | SQL 解析配置 |
| SQL 翻译规则 | `!SQL_TRANSLATOR` | SQL 方言翻译 |

## 🎯 1. 分片规则 (!SHARDING)

### 基础分片配置

```yaml
rules:
  - !SHARDING
    tables:
      # 表名
      t_order:
        # 实际数据节点
        actualDataNodes: ds_${0..1}.t_order_${0..1}
        # 分库策略
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: database_inline
        # 分表策略
        tableStrategy:
          standard:
            shardingColumn: order_id
            shardingAlgorithmName: table_inline
        # 主键生成策略
        keyGenerateStrategy:
          column: order_id
          keyGeneratorName: snowflake
    
    # 绑定表配置
    bindingTables:
      - t_order,t_order_item
    
    # 广播表配置
    broadcastTables:
      - t_config
    
    # 分片算法配置
    shardingAlgorithms:
      database_inline:
        type: INLINE
        props:
          algorithm-expression: ds_${user_id % 2}
      table_inline:
        type: INLINE
        props:
          algorithm-expression: t_order_${order_id % 2}
    
    # 主键生成器配置
    keyGenerators:
      snowflake:
        type: SNOWFLAKE
        props:
          worker-id: 123
```

### 分片策略类型

#### 1. 标准分片策略 (standard)
```yaml
tableStrategy:
  standard:
    shardingColumn: order_id          # 分片键
    shardingAlgorithmName: table_inline
```

#### 2. 复合分片策略 (complex)
```yaml
tableStrategy:
  complex:
    shardingColumns: user_id,order_id  # 多个分片键
    shardingAlgorithmName: complex_algorithm
```

#### 3. Hint 分片策略 (hint)
```yaml
tableStrategy:
  hint:
    shardingAlgorithmName: hint_algorithm
```

#### 4. 无分片策略 (none)
```yaml
tableStrategy:
  none:
```

### 分片算法类型

#### 1. 行表达式算法 (INLINE)
```yaml
shardingAlgorithms:
  table_inline:
    type: INLINE
    props:
      algorithm-expression: t_order_${order_id % 4}
```

#### 2. 取模算法 (MOD)
```yaml
shardingAlgorithms:
  table_mod:
    type: MOD
    props:
      sharding-count: 4
```

#### 3. 哈希取模算法 (HASH_MOD)
```yaml
shardingAlgorithms:
  table_hash_mod:
    type: HASH_MOD
    props:
      sharding-count: 4
```

#### 4. 范围算法 (VOLUME_RANGE)
```yaml
shardingAlgorithms:
  table_volume_range:
    type: VOLUME_RANGE
    props:
      range-lower: 10
      range-upper: 45
      sharding-volume: 10
```

#### 5. 边界范围算法 (BOUNDARY_RANGE)
```yaml
shardingAlgorithms:
  table_boundary_range:
    type: BOUNDARY_RANGE
    props:
      sharding-ranges: 1,5,10
```

#### 6. 自动时间范围算法 (AUTO_INTERVAL)
```yaml
shardingAlgorithms:
  table_auto_interval:
    type: AUTO_INTERVAL
    props:
      datetime-lower: 2021-01-01 00:00:00
      datetime-upper: 2021-01-03 00:00:00
      sharding-seconds: 86400
```

#### 7. 自定义算法 (CLASS_BASED)
```yaml
shardingAlgorithms:
  table_class_based:
    type: CLASS_BASED
    props:
      strategy: STANDARD
      algorithmClassName: com.example.MyShardingAlgorithm
```

## 🔧 自定义算法详解

### 1. 自定义分片算法实现

#### 标准分片算法接口
```java
package com.example.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.util.Collection;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025-06-14 20:01:00
 * @description 自定义订单分片算法
 */
public class OrderShardingAlgorithm implements StandardShardingAlgorithm<String> {

    private Properties props;

    @Override
    public void init(Properties props) {
        this.props = props;
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        // 精确分片逻辑
        String orderNo = shardingValue.getValue();

        // 提取订单号后4位进行分片
        String suffix = orderNo.substring(orderNo.length() - 4);
        int hashCode = suffix.hashCode();
        int index = Math.abs(hashCode) % availableTargetNames.size();

        return availableTargetNames.stream()
                .sorted()
                .skip(index)
                .findFirst()
                .orElse(null);
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       RangeShardingValue<String> shardingValue) {
        // 范围分片逻辑 - 返回所有可能的分片
        return availableTargetNames;
    }

    @Override
    public String getType() {
        return "ORDER_CUSTOM";
    }
}
```

#### 复合分片算法接口
```java
package com.example.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.util.Collection;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-14 20:01:00
 * @description 自定义复合分片算法 - 用户ID + 时间
 */
public class UserTimeComplexShardingAlgorithm implements ComplexKeysShardingAlgorithm<Comparable<?>> {

    @Override
    public void init(Properties props) {
        // 初始化配置
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        // 获取分片键值
        Collection<Comparable<?>> userIds = shardingValue.getColumnNameAndShardingValuesMap().get("user_id");
        Collection<Comparable<?>> createTimes = shardingValue.getColumnNameAndShardingValuesMap().get("create_time");

        return availableTargetNames.stream()
                .filter(targetName -> {
                    // 根据用户ID和时间进行复合分片逻辑
                    for (Comparable<?> userId : userIds) {
                        for (Comparable<?> createTime : createTimes) {
                            if (isTargetMatch(targetName, userId, createTime)) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());
    }

    private boolean isTargetMatch(String targetName, Comparable<?> userId, Comparable<?> createTime) {
        // 自定义匹配逻辑
        long userIdLong = ((Number) userId).longValue();
        long createTimeLong = ((Number) createTime).longValue();

        // 根据用户ID取模 + 时间因子
        int userMod = (int) (userIdLong % 4);
        int timeMod = (int) ((createTimeLong / 86400000) % 2); // 按天取模

        String expectedSuffix = "_" + userMod + timeMod;
        return targetName.endsWith(expectedSuffix);
    }

    @Override
    public String getType() {
        return "USER_TIME_COMPLEX";
    }
}
```

#### Hint 分片算法接口
```java
package com.example.sharding.algorithm;

import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingValue;

import java.util.Collection;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-14 20:01:00
 * @description 自定义 Hint 分片算法
 */
public class CustomHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    @Override
    public void init(Properties props) {
        // 初始化配置
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {
        // 从 Hint 中获取分片值
        Collection<String> hintValues = shardingValue.getValues();

        return availableTargetNames.stream()
                .filter(targetName -> {
                    for (String hintValue : hintValues) {
                        if (isHintMatch(targetName, hintValue)) {
                            return true;
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList());
    }

    private boolean isHintMatch(String targetName, String hintValue) {
        // 根据 Hint 值进行匹配
        if ("vip".equals(hintValue)) {
            return targetName.contains("vip");
        } else if ("normal".equals(hintValue)) {
            return !targetName.contains("vip");
        }
        return false;
    }

    @Override
    public String getType() {
        return "CUSTOM_HINT";
    }
}
```

### 2. 自定义主键生成器实现

```java
package com.example.sharding.keygen;

import org.apache.shardingsphere.sharding.spi.KeyGenerateAlgorithm;

import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2025-06-14 20:01:00
 * @description 自定义主键生成器 - 业务前缀 + 时间戳 + 序列号
 */
public class BusinessKeyGenerateAlgorithm implements KeyGenerateAlgorithm {

    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private String businessPrefix = "BIZ";
    private int sequenceLength = 6;

    @Override
    public void init(Properties props) {
        if (props.containsKey("business.prefix")) {
            this.businessPrefix = props.getProperty("business.prefix");
        }
        if (props.containsKey("sequence.length")) {
            this.sequenceLength = Integer.parseInt(props.getProperty("sequence.length"));
        }
    }

    @Override
    public Comparable<?> generateKey() {
        long timestamp = System.currentTimeMillis();
        long sequence = SEQUENCE.incrementAndGet() % (long) Math.pow(10, sequenceLength);

        return String.format("%s%d%0" + sequenceLength + "d",
                           businessPrefix, timestamp, sequence);
    }

    @Override
    public String getType() {
        return "BUSINESS_KEY";
    }
}
```

### 3. 配置文件中使用自定义算法

```yaml
rules:
  - !SHARDING
    tables:
      t_order:
        actualDataNodes: ds_${0..1}.t_order_${0..3}
        # 使用自定义分库算法
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: custom_user_db
        # 使用自定义分表算法
        tableStrategy:
          standard:
            shardingColumn: order_no
            shardingAlgorithmName: custom_order_table
        # 使用自定义主键生成器
        keyGenerateStrategy:
          column: order_id
          keyGeneratorName: business_key

      t_user_log:
        actualDataNodes: ds_0.t_user_log_${0..7}
        # 使用复合分片算法
        tableStrategy:
          complex:
            shardingColumns: user_id,create_time
            shardingAlgorithmName: user_time_complex

      t_vip_order:
        actualDataNodes: ds_0.t_vip_order,ds_0.t_normal_order
        # 使用 Hint 分片算法
        tableStrategy:
          hint:
            shardingAlgorithmName: custom_hint

    # 自定义分片算法配置
    shardingAlgorithms:
      custom_user_db:
        type: CLASS_BASED
        props:
          strategy: STANDARD
          algorithmClassName: com.example.sharding.algorithm.UserDbShardingAlgorithm

      custom_order_table:
        type: CLASS_BASED
        props:
          strategy: STANDARD
          algorithmClassName: com.example.sharding.algorithm.OrderShardingAlgorithm

      user_time_complex:
        type: CLASS_BASED
        props:
          strategy: COMPLEX
          algorithmClassName: com.example.sharding.algorithm.UserTimeComplexShardingAlgorithm

      custom_hint:
        type: CLASS_BASED
        props:
          strategy: HINT
          algorithmClassName: com.example.sharding.algorithm.CustomHintShardingAlgorithm

    # 自定义主键生成器配置
    keyGenerators:
      business_key:
        type: CLASS_BASED
        props:
          strategy: STANDARD
          algorithmClassName: com.example.sharding.keygen.BusinessKeyGenerateAlgorithm
          business.prefix: ORD
          sequence.length: 6
```

### 4. 使用 Hint 进行强制路由

```java
package com.example.service;

import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.stereotype.Service;

@Service
public class OrderService {

    public void createVipOrder(Order order) {
        // 使用 Hint 强制路由到 VIP 表
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue("t_vip_order", "vip");
            // 执行业务逻辑
            orderMapper.insert(order);
        }
    }

    public void createNormalOrder(Order order) {
        // 使用 Hint 强制路由到普通表
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue("t_vip_order", "normal");
            // 执行业务逻辑
            orderMapper.insert(order);
        }
    }

    public void queryOrdersByDatabase(String dbHint) {
        // 强制路由到指定数据库
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addDatabaseShardingValue("t_order", dbHint);
            // 执行查询
            List<Order> orders = orderMapper.selectAll();
        }
    }
}
```

### 5. 自定义算法的 SPI 配置

#### 创建 SPI 配置文件
```
# 文件路径：src/main/resources/META-INF/services/org.apache.shardingsphere.sharding.spi.ShardingAlgorithm
com.example.sharding.algorithm.OrderShardingAlgorithm
com.example.sharding.algorithm.UserTimeComplexShardingAlgorithm
com.example.sharding.algorithm.CustomHintShardingAlgorithm
```

```
# 文件路径：src/main/resources/META-INF/services/org.apache.shardingsphere.sharding.spi.KeyGenerateAlgorithm
com.example.sharding.keygen.BusinessKeyGenerateAlgorithm
```

### 6. 高级自定义算法示例

#### 地理位置分片算法
```java
/**
 * 根据地理位置进行分片的算法
 */
public class GeoLocationShardingAlgorithm implements StandardShardingAlgorithm<String> {

    private static final Map<String, String> REGION_MAPPING = Map.of(
        "BJ", "north",    // 北京 -> 北方数据中心
        "SH", "south",    // 上海 -> 南方数据中心
        "GZ", "south",    // 广州 -> 南方数据中心
        "SZ", "south"     // 深圳 -> 南方数据中心
    );

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        String cityCode = shardingValue.getValue();
        String region = REGION_MAPPING.getOrDefault(cityCode, "default");

        return availableTargetNames.stream()
                .filter(name -> name.contains(region))
                .findFirst()
                .orElse(availableTargetNames.iterator().next());
    }

    // ... 其他方法实现
}
```

#### 一致性哈希分片算法
```java
/**
 * 一致性哈希分片算法 - 便于扩容
 */
public class ConsistentHashShardingAlgorithm implements StandardShardingAlgorithm<Long> {

    private final TreeMap<Long, String> ring = new TreeMap<>();
    private final int virtualNodes = 150;

    @Override
    public void init(Properties props) {
        // 初始化一致性哈希环
        // 实际使用时应该从配置中获取节点信息
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<Long> shardingValue) {
        Long key = shardingValue.getValue();
        Long hash = hash(key.toString());

        Map.Entry<Long, String> entry = ring.ceilingEntry(hash);
        if (entry == null) {
            entry = ring.firstEntry();
        }

        return entry.getValue();
    }

    private Long hash(String key) {
        // 使用 MD5 或其他哈希算法
        return (long) key.hashCode();
    }

    // ... 其他方法实现
}
```

### 7. 自定义算法最佳实践

#### 性能优化建议
```java
/**
 * 高性能分片算法实现要点
 */
public class HighPerformanceShardingAlgorithm implements StandardShardingAlgorithm<String> {

    // 1. 使用缓存避免重复计算
    private final Map<String, String> cache = new ConcurrentHashMap<>();

    // 2. 预计算分片映射
    private final String[] targetArray;

    public HighPerformanceShardingAlgorithm() {
        // 初始化时预计算
        this.targetArray = new String[0]; // 实际使用时从配置获取
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        String key = shardingValue.getValue();

        // 使用缓存
        return cache.computeIfAbsent(key, k -> {
            // 3. 使用位运算提高性能（当分片数为2的幂时）
            int hash = k.hashCode();
            int index = hash & (targetArray.length - 1); // 等价于 hash % targetArray.length
            return targetArray[index];
        });
    }

    // ... 其他方法实现
}
```

#### 异常处理和日志记录
```java
/**
 * 带异常处理和日志的分片算法
 */
@Slf4j
public class RobustShardingAlgorithm implements StandardShardingAlgorithm<String> {

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        try {
            String shardingKey = shardingValue.getValue();

            // 参数验证
            if (shardingKey == null || shardingKey.trim().isEmpty()) {
                log.warn("分片键为空，使用默认分片: {}", availableTargetNames.iterator().next());
                return availableTargetNames.iterator().next();
            }

            // 分片逻辑
            String result = calculateShard(availableTargetNames, shardingKey);

            // 记录分片结果（开发环境）
            if (log.isDebugEnabled()) {
                log.debug("分片计算: key={}, result={}", shardingKey, result);
            }

            return result;

        } catch (Exception e) {
            log.error("分片计算异常，使用默认分片", e);
            return availableTargetNames.iterator().next();
        }
    }

    private String calculateShard(Collection<String> targets, String key) {
        // 具体分片逻辑
        int hash = key.hashCode();
        int index = Math.abs(hash) % targets.size();
        return targets.stream().sorted().skip(index).findFirst().orElse(null);
    }

    // ... 其他方法实现
}
```

### 8. 测试自定义算法

#### 单元测试示例
```java
package com.example.sharding.algorithm;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;

import java.util.Arrays;
import java.util.Collection;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 自定义分片算法单元测试
 */
class OrderShardingAlgorithmTest {

    private OrderShardingAlgorithm algorithm;
    private Collection<String> availableTargets;

    @BeforeEach
    void setUp() {
        algorithm = new OrderShardingAlgorithm();
        algorithm.init(new Properties());
        availableTargets = Arrays.asList("t_order_0", "t_order_1", "t_order_2", "t_order_3");
    }

    @Test
    void testPreciseSharding() {
        // 测试精确分片
        PreciseShardingValue<String> shardingValue =
            new PreciseShardingValue<>("order_no", "ORD20250614001");

        String result = algorithm.doSharding(availableTargets, shardingValue);

        assertNotNull(result);
        assertTrue(availableTargets.contains(result));
    }

    @Test
    void testShardingConsistency() {
        // 测试分片一致性 - 相同输入应该得到相同结果
        String orderNo = "ORD20250614001";
        PreciseShardingValue<String> shardingValue =
            new PreciseShardingValue<>("order_no", orderNo);

        String result1 = algorithm.doSharding(availableTargets, shardingValue);
        String result2 = algorithm.doSharding(availableTargets, shardingValue);

        assertEquals(result1, result2);
    }

    @Test
    void testShardingDistribution() {
        // 测试分片分布均匀性
        Map<String, Integer> distribution = new HashMap<>();

        for (int i = 0; i < 1000; i++) {
            String orderNo = "ORD2025061400" + String.format("%03d", i);
            PreciseShardingValue<String> shardingValue =
                new PreciseShardingValue<>("order_no", orderNo);

            String result = algorithm.doSharding(availableTargets, shardingValue);
            distribution.merge(result, 1, Integer::sum);
        }

        // 验证分布相对均匀（允许一定偏差）
        int expectedCount = 1000 / availableTargets.size();
        for (Integer count : distribution.values()) {
            assertTrue(Math.abs(count - expectedCount) < expectedCount * 0.2);
        }
    }
}
```

### 9. 自定义算法配置管理

#### 配置参数化
```java
/**
 * 支持配置参数的分片算法
 */
public class ConfigurableShardingAlgorithm implements StandardShardingAlgorithm<String> {

    private int shardingCount = 4;
    private String shardingPattern = "suffix"; // suffix, prefix, hash
    private boolean enableCache = true;

    @Override
    public void init(Properties props) {
        if (props.containsKey("sharding.count")) {
            this.shardingCount = Integer.parseInt(props.getProperty("sharding.count"));
        }
        if (props.containsKey("sharding.pattern")) {
            this.shardingPattern = props.getProperty("sharding.pattern");
        }
        if (props.containsKey("cache.enabled")) {
            this.enableCache = Boolean.parseBoolean(props.getProperty("cache.enabled"));
        }
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        String key = shardingValue.getValue();

        int index = switch (shardingPattern) {
            case "suffix" -> calculateSuffixIndex(key);
            case "prefix" -> calculatePrefixIndex(key);
            case "hash" -> calculateHashIndex(key);
            default -> calculateHashIndex(key);
        };

        return availableTargetNames.stream()
                .sorted()
                .skip(index % availableTargetNames.size())
                .findFirst()
                .orElse(null);
    }

    private int calculateSuffixIndex(String key) {
        // 基于后缀的分片逻辑
        return Integer.parseInt(key.substring(key.length() - 2)) % shardingCount;
    }

    private int calculatePrefixIndex(String key) {
        // 基于前缀的分片逻辑
        return key.substring(0, 2).hashCode() % shardingCount;
    }

    private int calculateHashIndex(String key) {
        // 基于哈希的分片逻辑
        return Math.abs(key.hashCode()) % shardingCount;
    }

    // ... 其他方法实现
}
```

#### 对应的配置文件
```yaml
shardingAlgorithms:
  configurable_algorithm:
    type: CLASS_BASED
    props:
      strategy: STANDARD
      algorithmClassName: com.example.sharding.algorithm.ConfigurableShardingAlgorithm
      sharding.count: 8
      sharding.pattern: hash
      cache.enabled: true
```

### 10. 动态分片算法

#### 支持动态扩容的分片算法
```java
/**
 * 支持动态扩容的一致性哈希分片算法
 */
public class DynamicConsistentHashAlgorithm implements StandardShardingAlgorithm<String> {

    private volatile ConsistentHash<String> consistentHash;
    private final Object lock = new Object();

    @Override
    public void init(Properties props) {
        // 初始化一致性哈希环
        refreshHashRing();
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        // 检查是否需要重新构建哈希环
        if (needRefresh(availableTargetNames)) {
            synchronized (lock) {
                if (needRefresh(availableTargetNames)) {
                    refreshHashRing(availableTargetNames);
                }
            }
        }

        return consistentHash.get(shardingValue.getValue());
    }

    private boolean needRefresh(Collection<String> currentTargets) {
        return consistentHash == null ||
               !consistentHash.getNodes().equals(new HashSet<>(currentTargets));
    }

    private void refreshHashRing() {
        // 从配置中心获取最新的节点信息
        // 这里简化处理
        refreshHashRing(Arrays.asList("node1", "node2", "node3"));
    }

    private void refreshHashRing(Collection<String> nodes) {
        this.consistentHash = new ConsistentHash<>(nodes, 150); // 150个虚拟节点
    }

    // ... 其他方法实现
}
```

这样，我已经为您的文档补充了非常详细的自定义算法用法，包括：

## 🔧 **新增内容概览**

### **1. 完整的自定义算法实现**
- **标准分片算法** - 实现 `StandardShardingAlgorithm` 接口
- **复合分片算法** - 实现 `ComplexKeysShardingAlgorithm` 接口
- **Hint 分片算法** - 实现 `HintShardingAlgorithm` 接口
- **自定义主键生成器** - 实现 `KeyGenerateAlgorithm` 接口

### **2. 实际业务场景示例**
- **订单分片算法** - 基于订单号后缀进行分片
- **用户时间复合算法** - 用户ID + 时间的复合分片
- **地理位置分片** - 根据城市代码进行地域分片
- **一致性哈希算法** - 支持动态扩容的分片算法

### **3. 高级特性和最佳实践**
- **性能优化** - 缓存、预计算、位运算优化
- **异常处理** - 完善的错误处理和日志记录
- **参数配置** - 支持配置参数化的算法
- **动态扩容** - 支持运行时动态调整的算法

### **4. 完整的开发流程**
- **SPI 配置** - 如何注册自定义算法
- **配置文件使用** - 在 YAML 中配置自定义算法
- **Hint 强制路由** - 在代码中使用 Hint 进行强制路由
- **单元测试** - 完整的测试用例示例

### **5. 实用工具和技巧**
- **测试分片分布** - 验证分片算法的均匀性
- **一致性测试** - 确保相同输入得到相同结果
- **配置管理** - 灵活的参数配置方式

这些补充内容让您的团队能够：
1. **快速上手** - 提供完整的代码模板
2. **避免踩坑** - 包含异常处理和最佳实践
3. **性能优化** - 提供性能优化的具体方法
4. **测试验证** - 完整的测试方案
5. **生产就绪** - 考虑了实际生产环境的需求

现在这个文档已经成为一个非常全面的 ShardingSphere 自定义算法开发指南！
```

### 主键生成策略

#### 1. 雪花算法 (SNOWFLAKE)
```yaml
keyGenerators:
  snowflake:
    type: SNOWFLAKE
    props:
      worker-id: 123
      max-vibration-offset: 1
```

#### 2. UUID (UUID)
```yaml
keyGenerators:
  uuid:
    type: UUID
```

#### 3. 自定义生成器 (CLASS_BASED)
```yaml
keyGenerators:
  custom:
    type: CLASS_BASED
    props:
      strategy: STANDARD
      algorithmClassName: com.example.MyKeyGenerator
```

## 🎯 2. 单表规则 (!SINGLE)

```yaml
rules:
  - !SINGLE
    tables:
      - "*.*"                    # 所有表
      - "t_config"              # 指定表
      - "sys_*"                 # 通配符表
    defaultDataSource: ds_0     # 默认数据源
```

## 🎯 3. 读写分离规则 (!READWRITE_SPLITTING)

```yaml
rules:
  - !READWRITE_SPLITTING
    dataSources:
      readwrite_ds:
        writeDataSourceName: write_ds
        readDataSourceNames:
          - read_ds_0
          - read_ds_1
        transactionalReadQueryStrategy: PRIMARY  # 事务内读策略
        loadBalancerName: round_robin
    
    # 负载均衡算法
    loadBalancers:
      round_robin:
        type: ROUND_ROBIN
      random:
        type: RANDOM
      weight:
        type: WEIGHT
        props:
          read_ds_0: 1
          read_ds_1: 2
```

## 🎯 4. 数据库发现规则 (!DATABASE_DISCOVERY)

```yaml
rules:
  - !DATABASE_DISCOVERY
    dataSources:
      readwrite_ds:
        dataSourceNames:
          - ds_0
          - ds_1
          - ds_2
        discoveryHeartbeatName: mgr_heartbeat
        discoveryTypeName: mgr
    
    # 发现类型
    discoveryTypes:
      mgr:
        type: MySQL.MGR
        props:
          group-name: 92504d5b-6dec-11e8-91ea-246e9612aaf1
          keep-alive-cron: '0/5 * * * * ?'
    
    # 心跳检测
    discoveryHeartbeats:
      mgr_heartbeat:
        props:
          keep-alive-cron: '0/5 * * * * ?'
          keep-alive-sql: 'SELECT 1'
```

## 🎯 5. 数据脱敏规则 (!ENCRYPT)

```yaml
rules:
  - !ENCRYPT
    tables:
      t_user:
        columns:
          username:
            cipherColumn: username_cipher     # 密文列
            assistedQueryColumn: username_assisted  # 辅助查询列
            plainColumn: username_plain       # 明文列
            encryptorName: aes_encryptor
          pwd:
            cipherColumn: pwd_cipher
            encryptorName: md5_encryptor
    
    # 加密器配置
    encryptors:
      aes_encryptor:
        type: AES
        props:
          aes-key-value: 123456abc
      md5_encryptor:
        type: MD5
    
    # 查询配置
    queryWithCipherColumn: true
```

## 🎯 6. 影子库规则 (!SHADOW)

```yaml
rules:
  - !SHADOW
    dataSources:
      shadow-data-source:
        productionDataSourceName: ds
        shadowDataSourceName: ds_shadow
    
    tables:
      t_order:
        dataSourceNames:
          - shadow-data-source
        shadowAlgorithmNames:
          - user-id-insert-match-algorithm
          - order-id-update-match-algorithm
    
    # 影子算法
    shadowAlgorithms:
      user-id-insert-match-algorithm:
        type: VALUE_MATCH
        props:
          operation: insert
          column: user_id
          value: 1
      order-id-update-match-algorithm:
        type: REGEX_MATCH
        props:
          operation: update
          column: order_id
          regex: '[1]'
```

## 🎯 7. SQL 解析规则 (!SQL_PARSER)

```yaml
rules:
  - !SQL_PARSER
    sqlCommentParseEnabled: true
    sqlStatementCache:
      initialCapacity: 2000
      maximumSize: 65535
    parseTreeCache:
      initialCapacity: 128
      maximumSize: 1024
```

## 🎯 8. SQL 翻译规则 (!SQL_TRANSLATOR)

```yaml
rules:
  - !SQL_TRANSLATOR
    type: NATIVE
    useOriginalSQLWhenTranslatingFailed: false
```

## 📝 完整配置示例

### 复杂业务场景配置

```yaml
rules:
  # 单表规则 - 配置表和系统表
  - !SINGLE
    tables:
      - "sys_*"
      - "t_config"
    defaultDataSource: ds_0
  
  # 分片规则 - 订单表分片
  - !SHARDING
    tables:
      t_order:
        actualDataNodes: ds_${0..1}.t_order_${0..3}
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: database_inline
        tableStrategy:
          standard:
            shardingColumn: order_id
            shardingAlgorithmName: table_inline
        keyGenerateStrategy:
          column: order_id
          keyGeneratorName: snowflake
      
      t_order_item:
        actualDataNodes: ds_${0..1}.t_order_item_${0..3}
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: database_inline
        tableStrategy:
          standard:
            shardingColumn: order_id
            shardingAlgorithmName: table_inline
    
    bindingTables:
      - t_order,t_order_item
    
    broadcastTables:
      - t_config
    
    shardingAlgorithms:
      database_inline:
        type: INLINE
        props:
          algorithm-expression: ds_${user_id % 2}
      table_inline:
        type: INLINE
        props:
          algorithm-expression: t_order_${order_id % 4}
    
    keyGenerators:
      snowflake:
        type: SNOWFLAKE
        props:
          worker-id: 1
  
  # 读写分离规则
  - !READWRITE_SPLITTING
    dataSources:
      readwrite_ds:
        writeDataSourceName: ds_0
        readDataSourceNames:
          - ds_0_read
          - ds_1_read
        loadBalancerName: round_robin
    
    loadBalancers:
      round_robin:
        type: ROUND_ROBIN
  
  # 数据脱敏规则
  - !ENCRYPT
    tables:
      t_user:
        columns:
          username:
            cipherColumn: username_cipher
            encryptorName: aes_encryptor
          phone:
            cipherColumn: phone_cipher
            encryptorName: aes_encryptor
    
    encryptors:
      aes_encryptor:
        type: AES
        props:
          aes-key-value: 123456abc
    
    queryWithCipherColumn: true
```

## ⚠️ 注意事项

### 规则优先级
1. **SINGLE** 规则优先级最低
2. **SHARDING** 规则优先级较高
3. 具体表配置优先于通配符配置

### 性能考虑
1. 绑定表可以避免跨库关联查询
2. 广播表适用于小数据量的配置表
3. 合理选择分片键，避免数据倾斜

### 兼容性
1. 不同版本的 ShardingSphere 规则配置可能有差异
2. 某些规则组合可能存在冲突
3. 建议在测试环境充分验证配置

## 🎯 最佳实践

### 1. 分片键选择原则

#### 合适的分片键特征
- **高基数**：分片键值分布均匀，避免数据倾斜
- **查询频繁**：大部分查询都包含分片键
- **稳定性**：分片键值不经常变更
- **业务相关**：符合业务逻辑，便于理解和维护

#### 常见分片键选择

```yaml
# 用户维度分片 - 适用于用户相关的业务表
databaseStrategy:
  standard:
    shardingColumn: user_id
    shardingAlgorithmName: user_db_hash

# 时间维度分片 - 适用于日志、订单等时序数据
tableStrategy:
  standard:
    shardingColumn: create_time
    shardingAlgorithmName: time_range

# 地域维度分片 - 适用于地域相关业务
databaseStrategy:
  standard:
    shardingColumn: region_code
    shardingAlgorithmName: region_hash
```

### 2. 分片算法选择指南

| 场景 | 推荐算法 | 配置示例 |
|------|----------|----------|
| 均匀分布的数值 | MOD | `type: MOD` |
| 字符串类型 | HASH_MOD | `type: HASH_MOD` |
| 时间范围查询 | AUTO_INTERVAL | `type: AUTO_INTERVAL` |
| 自定义逻辑 | CLASS_BASED | `type: CLASS_BASED` |
| 简单表达式 | INLINE | `type: INLINE` |

### 3. 性能优化配置

#### 绑定表配置
```yaml
# 避免跨库 JOIN 查询
bindingTables:
  - t_order,t_order_item,t_order_detail
  - t_user,t_user_profile
```

#### 广播表配置
```yaml
# 小数据量的配置表设为广播表
broadcastTables:
  - t_config
  - t_dict
  - t_region
```

#### 分片数量建议
```yaml
# 分片数量 = 2^n，便于扩容
actualDataNodes: ds_${0..3}.t_order_${0..7}  # 4库8表
```

### 4. 常见配置模板

#### 电商订单分片模板
```yaml
rules:
  - !SHARDING
    tables:
      # 订单主表
      t_order:
        actualDataNodes: ds_${0..3}.t_order_${0..7}
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: user_db_mod
        tableStrategy:
          standard:
            shardingColumn: order_id
            shardingAlgorithmName: order_table_mod
        keyGenerateStrategy:
          column: order_id
          keyGeneratorName: snowflake

      # 订单明细表
      t_order_item:
        actualDataNodes: ds_${0..3}.t_order_item_${0..7}
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: user_db_mod
        tableStrategy:
          standard:
            shardingColumn: order_id
            shardingAlgorithmName: order_table_mod

    bindingTables:
      - t_order,t_order_item

    shardingAlgorithms:
      user_db_mod:
        type: MOD
        props:
          sharding-count: 4
      order_table_mod:
        type: MOD
        props:
          sharding-count: 8

    keyGenerators:
      snowflake:
        type: SNOWFLAKE
        props:
          worker-id: 1
```

#### 日志分片模板（按时间）
```yaml
rules:
  - !SHARDING
    tables:
      t_log:
        actualDataNodes: ds_0.t_log_${2024..2025}${01..12}
        tableStrategy:
          standard:
            shardingColumn: create_time
            shardingAlgorithmName: log_time_interval

    shardingAlgorithms:
      log_time_interval:
        type: AUTO_INTERVAL
        props:
          datetime-lower: 2024-01-01 00:00:00
          datetime-upper: 2025-12-31 23:59:59
          sharding-seconds: 2592000  # 按月分片
```

#### 多租户分片模板
```yaml
rules:
  - !SHARDING
    tables:
      t_tenant_data:
        actualDataNodes: ds_${0..7}.t_tenant_data_${0..3}
        databaseStrategy:
          standard:
            shardingColumn: tenant_id
            shardingAlgorithmName: tenant_db_hash
        tableStrategy:
          standard:
            shardingColumn: data_id
            shardingAlgorithmName: data_table_mod

    shardingAlgorithms:
      tenant_db_hash:
        type: HASH_MOD
        props:
          sharding-count: 8
      data_table_mod:
        type: MOD
        props:
          sharding-count: 4
```

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. 路由错误
```yaml
# 问题：SQL 路由到错误的分片
# 原因：分片键配置错误或算法不匹配
# 解决：检查分片键和算法配置

# 调试配置
props:
  sql-show: true  # 显示路由后的 SQL
```

#### 2. 跨库查询
```yaml
# 问题：出现跨库 JOIN 查询
# 原因：未配置绑定表或查询条件不包含分片键
# 解决：配置绑定表或优化查询条件

bindingTables:
  - t_order,t_order_item  # 配置绑定表
```

#### 3. 数据倾斜
```yaml
# 问题：某些分片数据量过大
# 原因：分片键分布不均匀
# 解决：更换分片键或使用复合分片策略

tableStrategy:
  complex:
    shardingColumns: user_id,create_time  # 复合分片键
    shardingAlgorithmName: complex_algorithm
```

### 调试配置

```yaml
# 开启详细日志
props:
  sql-show: true
  sql-simple: false
  system-log-level: DEBUG

# 日志配置
logging:
  level:
    org.apache.shardingsphere: DEBUG
    org.apache.shardingsphere.infra.route: DEBUG
```

## 📊 性能监控

### 关键指标监控

1. **路由性能**
   - SQL 解析时间
   - 路由计算时间
   - 分片命中率

2. **数据分布**
   - 各分片数据量
   - 查询分布情况
   - 热点分片识别

3. **查询性能**
   - 跨分片查询比例
   - 平均响应时间
   - 慢查询统计

### 监控配置

```yaml
props:
  # 启用性能监控
  show-process-list-enabled: true

  # 慢查询阈值
  proxy-backend-query-fetch-size: 1000

  # 连接池监控
  max-connections-size-per-query: 1
```

## 📚 参考资料

- [ShardingSphere 官方文档](https://shardingsphere.apache.org/)
- [分片规则配置](https://shardingsphere.apache.org/document/current/cn/user-manual/shardingsphere-jdbc/yaml-config/rules/sharding/)
- [读写分离配置](https://shardingsphere.apache.org/document/current/cn/user-manual/shardingsphere-jdbc/yaml-config/rules/readwrite-splitting/)
- [数据脱敏配置](https://shardingsphere.apache.org/document/current/cn/user-manual/shardingsphere-jdbc/yaml-config/rules/encrypt/)
- [分片算法详解](https://shardingsphere.apache.org/document/current/cn/user-manual/common-config/builtin-algorithm/sharding/)
- [性能调优指南](https://shardingsphere.apache.org/document/current/cn/reference/performance/)

## 📝 更新日志

- **v1.0** - 初始版本，包含基础规则配置
- **v1.1** - 添加最佳实践和性能优化
- **v1.2** - 完善故障排查和监控配置
- **v1.3** - 增加常见业务场景模板
