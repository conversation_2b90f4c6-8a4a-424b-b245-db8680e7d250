# Sharding-JDBC5.2分片算法运用

## Maven依赖

```xml
    <mybatis.plus.version>3.5.3.1</mybatis.plus.version>
    <shardingsphere.version>5.2.0</shardingsphere.version>

    <dependency>
        <groupId>org.apache.shardingsphere</groupId>
        <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
        <version>${shardingsphere.version}</version>
    </dependency>

    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.version}</version>
    </dependency>
```

## yaml配置

```yaml
spring:
  shardingsphere:
    datasource: ##数据源配置
      default-data-source-name: ds0
      names: ds0,ds1
      ds0:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.jc.jdbc.Driver
        jdbc-url: *******************************************************************************
        username: root
        password: cqt@1234
        pool-name: ds0-pool
        maximum-pool-size: 100
        minimum-idle: 30
        max-lifetime: 600000
        connection-timeout: 3000
        connection-test-query: SELECT 1 FROM DUAL
        validation-timeout: 10000
      ds1:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.jc.jdbc.Driver
        jdbc-url: ******************************************************************************
        username: root
        password: cqt@1234
        pool-name: ds1-pool
        maximum-pool-size: 100
        minimum-idle: 30
        max-lifetime: 600000
        connection-timeout: 3000
        connection-test-query: SELECT 1 FROM DUAL
        validation-timeout: 10000
    props:
      sql-show: true
      sql-simple: true
    rules:
      sharding:
        tables:
          cdr:
            actual-data-nodes: ds0.cdr
            table-strategy:
              hint:
                sharding-algorithm-name: cdr-table-hint
          t_user:
            actual-data-nodes: ds$->{0..1}.t_user_$->{0..2}
            table-strategy:
              standard:
                sharding-column: id
                sharding-algorithm-name: t-user_table-inline
            database-strategy:
              standard:
                sharding-column: city_id
                sharding-algorithm-name: t-user-database-inline
        sharding-algorithms:
          # 指定分配字段值
          cdr-table-hint:
            # 策略类型 org.apache.shardingsphere.sharding.algorithm.sharding.classbased.ClassBasedShardingAlgorithm
            type: CLASS_BASED
            props:
              # 分片策略类型: STANDARD, COMPLEX, HINT
              strategy: HINT
              # 自定义分片算法
              algorithmClassName: com.cqt.starter.sharding.algorithms.MyHintAlgorithms
          # inline表达式计算分片值
          t-user_table-inline:
            type: INLINE
            props:
              # 根据id取模 分表
              algorithm-expression: t_user_$->{id % 3}
          t-user-database-inline:
            type: INLINE
            props:
              # 根据city_id取模 分库
              algorithm-expression: ds$->{city_id % 2}
        key-generators:
          snowflake:
            type: SNOWFLAKE

```

## yaml配置原理

### 自带算法

![image-20230620224329977](images/image-20230620224329977.png)

### ClassBasedShardingAlgorithm

> org.apache.shardingsphere.sharding.algorithm.sharding.classbased.ClassBasedShardingAlgorithm
>
> 通过此算法, 调用自定义的分片算法, 

#### 支持策略类型

![image-20230620224606041](images/image-20230620224606041.png)

#### 源码如何实现

```java
public final class ClassBasedShardingAlgorithm implements StandardShardingAlgorithm<Comparable<?>>, ComplexKeysShardingAlgorithm<Comparable<?>>, HintShardingAlgorithm<Comparable<?>> {
    
    private static final String STRATEGY_KEY = "strategy";
    
    private static final String ALGORITHM_CLASS_NAME_KEY = "algorithmClassName";
    
    @Getter
    private Properties props;
    
    private ClassBasedShardingAlgorithmStrategyType strategy;
    
    private String algorithmClassName;
    
    private StandardShardingAlgorithm standardShardingAlgorithm;
    
    private ComplexKeysShardingAlgorithm complexKeysShardingAlgorithm;
    
    private HintShardingAlgorithm hintShardingAlgorithm;
    
    @Override
    public void init(final Properties props) {
        this.props = props;
        // 获取yml配置的策略类型: STANDARD, COMPLEX, HINT
        strategy = getStrategy(props);
        // 自定义算法全限定名: com.cqt.starter.sharding.algorithms.MyHintAlgorithms
        algorithmClassName = getAlgorithmClassName(props);
        // 实例化自定义算法对象
        initAlgorithmInstance(props);
    }
    
    private ClassBasedShardingAlgorithmStrategyType getStrategy(final Properties props) {
        String strategy = props.getProperty(STRATEGY_KEY);
        Preconditions.checkNotNull(strategy, "Properties `%s` can not be null when uses class based sharding strategy.", STRATEGY_KEY);
        return ClassBasedShardingAlgorithmStrategyType.valueOf(strategy.toUpperCase().trim());
    }
    
    private String getAlgorithmClassName(final Properties props) {
        String result = props.getProperty(ALGORITHM_CLASS_NAME_KEY);
        Preconditions.checkNotNull(result, "Properties `%s` can not be null when uses class based sharding strategy.", ALGORITHM_CLASS_NAME_KEY);
        return result;
    }
    
    private void initAlgorithmInstance(final Properties props) {
        switch (strategy) {
            case STANDARD:
                standardShardingAlgorithm = ClassBasedShardingAlgorithmFactory.newInstance(algorithmClassName, StandardShardingAlgorithm.class, props);
                break;
            case COMPLEX:
                complexKeysShardingAlgorithm = ClassBasedShardingAlgorithmFactory.newInstance(algorithmClassName, ComplexKeysShardingAlgorithm.class, props);
                break;
            case HINT:
                hintShardingAlgorithm = ClassBasedShardingAlgorithmFactory.newInstance(algorithmClassName, HintShardingAlgorithm.class, props);
                break;
            default:
                break;
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public String doSharding(final Collection<String> availableTargetNames, final PreciseShardingValue<Comparable<?>> shardingValue) {
        return standardShardingAlgorithm.doSharding(availableTargetNames, shardingValue);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public Collection<String> doSharding(final Collection<String> availableTargetNames, final RangeShardingValue<Comparable<?>> shardingValue) {
        return standardShardingAlgorithm.doSharding(availableTargetNames, shardingValue);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public Collection<String> doSharding(final Collection<String> availableTargetNames, final ComplexKeysShardingValue<Comparable<?>> shardingValue) {
        return complexKeysShardingAlgorithm.doSharding(availableTargetNames, shardingValue);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public Collection<String> doSharding(final Collection<String> availableTargetNames, final HintShardingValue<Comparable<?>> shardingValue) {
        return hintShardingAlgorithm.doSharding(availableTargetNames, shardingValue);
    }
    
    @Override
    public String getType() {
        // yml需要配置此type
        return "CLASS_BASED";
    }
}
```

##### 实例化自定义算法对象

```java
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ClassBasedShardingAlgorithmFactory {
    
    /**
     * Create sharding algorithm.
     * 
     * @param shardingAlgorithmClassName sharding algorithm class name
     * @param superShardingAlgorithmClass sharding algorithm super class
     * @param props properties
     * @param <T> class generic type
     * @return sharding algorithm instance
     */
    @SuppressWarnings("unchecked")
    @SneakyThrows(ReflectiveOperationException.class)
    public static <T extends ShardingAlgorithm> T newInstance(final String shardingAlgorithmClassName, final Class<T> superShardingAlgorithmClass, final Properties props) {
        Class<?> algorithmClass = Class.forName(shardingAlgorithmClassName);
        if (!superShardingAlgorithmClass.isAssignableFrom(algorithmClass)) {
            throw new ShardingAlgorithmClassImplementationException(shardingAlgorithmClassName, superShardingAlgorithmClass);
        }
        T result = (T) algorithmClass.getDeclaredConstructor().newInstance();
        result.init(convertToStringTypedProperties(props));
        return result;
    }
    
    private static Properties convertToStringTypedProperties(final Properties props) {
        Properties result = new Properties();
        props.forEach((key, value) -> result.setProperty(key.toString(), null == value ? null : value.toString()));
        return result;
    }
}
```

### 自定义分片算法

```java
public class MyHintAlgorithms implements HintShardingAlgorithm<String> {

    private final static Logger log = LoggerFactory.getLogger(MyHintAlgorithms.class);

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, HintShardingValue<String> shardingValue) {
        log.info("availableTargetNames: {}", availableTargetNames);
        log.info("shardingValue: {}", shardingValue);
        String logicTableName = shardingValue.getLogicTableName();
        Optional<String> optional = shardingValue.getValues().stream().findFirst();
        if (optional.isPresent()) {
            return Lists.newArrayList(logicTableName + "_" + optional.get());
        }
        return Collections.emptyList();
    }

    @Override
    public Properties getProps() {
        return null;
    }

    @Override
    public void init(Properties properties) {
        log.info("properties: {}", properties);
    }
}
```

