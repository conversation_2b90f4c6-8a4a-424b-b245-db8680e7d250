## TCP缓存池

```shell 
#net.ipv4.tcp_rmem
# 配置读缓冲的大小，三个值，第一个是这个读缓冲的最小值，第三个是最大值，中间的是默认值。默认值是“4096 87380 6291456”，
# 建议修改成“4096 87380 16777216”。
echo "4096 87380 16777216" > /proc/sys/net/ipv4/tcp_rmem

# net.ipv4.tcp_wmem
# 配置写缓冲的大小，三个值，第一个是这个写缓冲的最小值，第三个是最大值，中间的是默认值。默认值是“4096 16384 4194304”，
# 建议修改成“4096 65536 16777216”。
echo "4096 65536 16777216"> /proc/sys/net/ipv4/tcp_wmem

```

## 文件描述符限制

```shell

ulimit -n 1048576

```