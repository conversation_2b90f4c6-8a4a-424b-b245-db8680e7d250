# Redis单节点流量高, CPU单核使用率高的问题

## 查询cpu单核使用率

```shell
# 按1 查询具体核的使用率
top
```

## 在redis安装iftop

> 按p 回车显示端口, 按1 2 3 按某一列排序

```shell
yum -y install epel-release && yum install -y iftop
![](./images/1720144240289.png)
```

### iftop使用

```shell
# 显示端口
iftop -npP

# 默认监控第一块网卡上的流量
iftop

# 指定监控eth1网卡上的流量
iftop -i eth1

# 直接显示IP, 不进行DNS反解析
iftop -n

# 直接显示连接埠编号, 不显示服务名称
iftop -N

# 只显示在/dev/wlan0无线网卡接口的SSH数据包
iftop -i wlan0 -f "dst port 22"

# 显示某个网段进出封包流量
iftop -F ***********/24
iftop -F ***********/*************
```

### 快捷键

```shell
参数      含义
P        通过此键可切换暂停/继续显示
h        通过此键可在交互参数界面/状态输出界面之间来回切换
b        通过此键可切换是否显示平均流量图形条
B        通过此键可切换显示2秒、10秒、40秒内的平均流量
T        通过此键可切换是否显示每个连接的总流量
j/k      按j键或k键可以向上或向下滚动屏幕显示当前的连接信息
l        通过此键可打开iftop输出过滤功能，比如输入要显示的IP，按回车后，屏幕就只显示与这个IP相关的流量信息
L        通过此键可切换显示流量刻度范围，刻度不同，流量图形条会跟着变化
q        通过此键可退出iftop流量监控界面
n        通过此键可使iftop输出结果以IP或主机名的方式显示
s        通过此键可切换是否显示源主机信息
d        通过此键可切换是否显示远端目标主机信息
t        通过此键可切换iftop显示格式，连续按此键可依次显示：以两行显示发送接收流量、以一行显示发送接收流量、只显示发送流量/接收流量
N        通过此键可切换显示端口号/端口号对应服务名称
S        通过此键可切换是否显示本地源主机的端口信息
D        通过此键可切换是否显示远端目标主机的端口信息
p        通过此键可切换是否显示端口信息
1/2/3    根据最近 2 秒、10 秒、40 秒的平均网络流量排序
<        通过此键可根据左边的本地主机名或IP地址进行排序
>        通过此键可根据远端目标主机的主机名或IP地址进行排序
o        通过此键可切换是否固定显示当前的连接
```

## 在Java服务器 安装lsof

> 查询端口属于哪个进程

```shell
yum install -y lsof
lsof -i:49062
netstat -ap|grep 49062
```

## tcpdump抓取端口 49062 的包

> 抓java服务连接redis端口的包

```shell
tcpdump -i eth0 port 49062 -w 49062.pcap
```

![](./images/1720145673947.png)

## 查询进程的网络流量

```shell
yum install epel-release -y && yum install nethogs -y

nethogs -d 1
```

![](./images/1720144411844.png)

### 使用

```shell
# 显示eth1网卡上的流量
nethogs eth1
```
