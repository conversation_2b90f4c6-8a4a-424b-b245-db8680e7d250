# CPU**占用过高分析**

## 分析进程的线程cpu占用情况

1. `top`命令找出有问题`Java`进程及其线程号：
    1. 开启线程显示模式
    1. 按`CPU`使用率排序
    1. 记下`Java`进程号及其`CPU`高的线程号
1. 手动转成十六进制（可以用`printf %x 1234`）。
1. `jstack`有问题的`Java`进程。
1. `grep`十六进制的线程`id`，找到线程栈。

```bash
# 查看进程cpu情况
top -c

# 查询进程的线程cpu占用
ps -H -eo pid,tid,%cpu | grep {pid}
ps -H -eo pid,tid,%cpu | grep 2702

# 打印线程id 十六进制
printf "%x\n" {tid}
printf "%x\n" 20608

# 线程栈
jstack {pid} | grep {tid} -A30
jstack 2702 | grep 2702 -A30

```

## 方便脚本 

> https://github.com/oldratlee/useful-scripts

```
wget --no-check-certificate https://raw.kgithub.com/oldratlee/useful-scripts/release-2.x/bin/show-busy-java-threads
chmod +x show-busy-java-threads
```

```bash
show-busy-java-threads
# 从所有运行的Java进程中找出最消耗CPU的线程（缺省5个），打印出其线程栈

# 缺省会自动从所有的Java进程中找出最消耗CPU的线程，这样用更方便
# 当然你可以通过 -p 选项 手动指定要分析的Java进程Id，以保证只会显示你关心的那个Java进程的信息
show-busy-java-threads -p <指定的Java进程Id>
show-busy-java-threads -p 42
show-busy-java-threads -p 42,47

show-busy-java-threads -c <要展示示的线程栈个数>

show-busy-java-threads <重复执行的间隔秒数> [<重复执行的次数>]
# 多次执行；这2个参数的使用方式类似vmstat命令

show-busy-java-threads -a <运行输出的记录到的文件>
# 记录到文件以方便回溯查看

show-busy-java-threads -S <存储jstack输出文件的目录>
# 指定jstack输出文件的存储目录，方便记录以后续分析

##############################
# 注意：
##############################
# 如果Java进程的用户 与 执行脚本的当前用户 不同，则jstack不了这个Java进程
# 为了能切换到Java进程的用户，需要加sudo来执行，即可以解决：
sudo show-busy-java-threads

show-busy-java-threads -s <指定jstack命令的全路径>
# 对于sudo方式的运行，JAVA_HOME环境变量不能传递给root，
# 而root用户往往没有配置JAVA_HOME且不方便配置，不能找到jstack命令。
# 这时显式指定jstack命令的路径就反而显得更方便了

# -m 选项：执行jstack命令时加上 -m 选项，显示上Native的栈帧，一般应用排查不需要使用
show-busy-java-threads -m
# -F 选项：执行jstack命令时加上 -F 选项（如果直接jstack无响应时，用于强制jstack），一般情况不需要使用
show-busy-java-threads -F
# -l 选项：执行jstack命令时加上 -l 选项，显示上更多相关锁的信息，一般情况不需要使用
# 注意：和 -m -F 选项一起使用时，可能会大大增加jstack操作的耗时
show-busy-java-threads -l

# 帮助信息
$ show-busy-java-threads -h
Usage: show-busy-java-threads [OPTION]... [delay [count]]
Find out the highest cpu consumed threads of java processes,
and print the stack of these threads.

Example:
  show-busy-java-threads       # show busy java threads info
  show-busy-java-threads 1     # update every 1 second, (stop by eg: CTRL+C)
  show-busy-java-threads 3 10  # update every 3 seconds, update 10 times

Output control:
  -p, --pid <java pid(s)>   find out the highest cpu consumed threads from
                            the specified java process.
                            support pid list(eg: 42,47).
                            default from all java process.
  -c, --count <num>         set the thread count to show, default is 5.
                            set count 0 to show all threads.
  -a, --append-file <file>  specifies the file to append output as log.
  -S, --store-dir <dir>     specifies the directory for storing
                            the intermediate files, and keep files.
                            default store intermediate files at tmp dir,
                            and auto remove after run. use this option to keep
                            files so as to review jstack/top/ps output later.
  delay                     the delay between updates in seconds.
  count                     the number of updates.
                            delay/count arguments imitates the style of
                            vmstat command.

jstack control:
  -s, --jstack-path <path>  specifies the path of jstack command.
  -F, --force               set jstack to force a thread dump.
                            use when jstack does not respond (process is hung).
  -m, --mix-native-frames   set jstack to print both java and
                            native frames (mixed mode).
  -l, --lock-info           set jstack with long listing.
                            prints additional information about locks.

CPU usage calculation control:
  -i, --cpu-sample-interval specifies the delay between cpu samples to get
                            thread cpu usage percentage during this interval.
                            default is 0.5 (second).
                            set interval 0 to get the percentage of time spent
                            running during the *entire lifetime* of a process.

Miscellaneous:
  -h, --help                display this help and exit.
  -V, --version             display version information and exit.
```

## arthas分析

> https://arthas.aliyun.com/doc/

安装

```
curl -O https://arthas.aliyun.com/arthas-boot.jar
java -jar arthas-boot.jar

```

```bash
dashboard

trace com.cqt.redis.util.RedissonUtil isExistString
trace com.cqt.hmyc.web.bind.service.axe.AxeBindCacheService checkExtNumAndTelX

# 当前最忙的前 N 个线程并打印堆栈
thread -n 3
```

