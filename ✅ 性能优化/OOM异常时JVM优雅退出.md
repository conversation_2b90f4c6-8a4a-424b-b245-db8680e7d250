# OOM异常时JVM优雅退出

> https://www.bilibili.com/read/cv17442601/
>
现在考虑一种需求：当程序发生OOM时，我们既不希望它简单地将异常抛出，也不希望暴力地将该进程杀死，除此之外，考虑这样一种业务场景：现在有多条任务线程正在工作，并且有一个守护线程保证有指定数量的任务线程正在执行，此时一旦由于某种原因，导致任务线程发生了OOM异常，那么这条线程随即被杀死，但进程不受影响。此时守护线程发现任务线程数量不足，又会将任务线程拉起，但这并没有解决问题，OOM在未来仍然会出现，并且反复出现，在外部观察进程在正常工作，但内部运行效率已经受到了非常大的影响。

无论是上面的哪种情形，我们都希望程序在OOM发生的时候，可以优雅地结束这个JVM进程。

为了实现JVM在OOM发生时的优雅退出，我们先来了解一下，关于OOM的JVM参数有哪些：

-XX:+HeapDumpOnOutOfMemoryError

    在程序发生OOM异常时，输出堆的dump文件，这个参数建议谨慎开启，因为一旦OOM发生时堆内存占用很大，那么生成的dump文件会非常巨大，直接将磁盘打满都有可能发生。

-XX:OnOutOfMemoryError

    在程序发生OOM异常时，执行指定命令，该参数接下来会详细介绍，也是JVM优雅退出的关键参数

-XX:+ExitOnOutOfMemoryError

    在程序发生OOM异常时，强制退出

-XX:+CrashOnOutOfMemoryError

    在程序发生OOM异常时，强制退出，并生成Crash日志
