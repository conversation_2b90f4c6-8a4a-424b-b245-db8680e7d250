> 服务端close之后 我客户端是netty还是持续给服务端发送包 服务端也ack了 过1秒之后 [TCP ZeroWindow]
> netty日志exceptionCaught包 Connection Reset
>
根据你的描述，服务端在 WebSocket 连接中执行了 `close` 操作（可能是发送了 `WebSocket Connection Close` 帧或直接调用
`ctx.close()`），但客户端（使用 Netty）仍然持续向服务端发送数据包，服务端甚至还回复了 `ACK`，随后出现了 `[TCP ZeroWindow]`
，并最终在 Netty 日志中触发了 `exceptionCaught` 异常，异常信息为 `Connection Reset`。以下是详细分析和解决方案。

---
![img.png](img.png)
![img_1.png](img_1.png)

### 1. 场景分析

#### 1.1 事件时间线

1. **服务端执行 `close`**：
    - 服务端发送 `WebSocket Connection Close` 帧（`Opcode=8`）或直接关闭 TCP 连接（`ctx.close()`）。
    - 正常情况下，服务端会发送 TCP `FIN` 包，进入 `FIN_WAIT` 状态，等待客户端的确认。

2. **客户端持续发送数据**：
    - 客户端（Netty）未及时检测到服务端的关闭操作（例如未处理 `CloseWebSocketFrame` 或 `channelInactive` 事件）。
    - 客户端继续调用 `writeAndFlush` 发送数据（可能是 WebSocket 数据帧或普通 TCP 数据）。

3. **服务端回复 `ACK`**：
    - 服务端在关闭连接后仍回复 `ACK`，可能是因为：
        - 服务端尚未完全关闭 TCP 连接（仍在 `FIN_WAIT` 状态）。
        - 服务端操作系统 TCP 栈自动回复 `ACK`，但实际上数据未被处理。

4. **出现 `[TCP ZeroWindow]`**：
    - 服务端发送 `[TCP ZeroWindow]`，表示其接收窗口为 0，无法接收更多数据。
    - 这是服务端关闭连接后的常见行为，因为服务端已不打算接收数据。

5. **1 秒后触发 `exceptionCaught`**：
    - 客户端的 Netty 检测到连接异常（可能是服务端发送了 `RST` 包，或客户端尝试写入数据失败）。
    - 日志中出现 `exceptionCaught` 异常，信息为 `Connection Reset`（通常对应 `IOException: Connection reset by peer`）。

#### 1.2 抓包数据推测

结合之前的抓包（例如数据包 1493 `[RST, ACK]` 和 `[TCP ZeroWindow]`），可能的抓包数据如下：

- 服务端发送 `WebSocket Connection Close` 或 TCP `FIN` 包。
- 客户端发送数据（例如 `WebSocket Binary` 帧）。
- 服务端回复 `ACK`，但随后发送 `[TCP ZeroWindow]`。
- 最终服务端发送 `RST` 包，导致客户端触发 `exceptionCaught`。

---

### 2. 问题分析

#### 2.1 客户端未及时停止发送

- **原因**：
    - 客户端未正确处理服务端的 `WebSocket Connection Close` 帧。
    - 客户端未监听 `channelInactive` 事件，未能及时停止发送数据。
    - Netty 的 `Channel` 状态（`isActive()` 或 `isOpen()`）未及时更新，导致客户端认为连接仍可用。
- **影响**：
    - 客户端持续调用 `writeAndFlush`，将数据放入缓冲区。
    - Netty 尝试将缓冲区数据写入底层 socket，但因连接已断开，最终触发异常。

#### 2.2 服务端回复 `ACK`

- **原因**：
    - 服务端关闭连接后，TCP 连接未立即销毁（可能处于 `FIN_WAIT` 或 `CLOSE_WAIT` 状态）。
    - 操作系统 TCP 栈自动回复 `ACK`，确认收到客户端的数据，但实际上服务端已不处理这些数据。
- **影响**：
    - 客户端误以为连接仍可用，继续发送数据。

#### 2.3 `[TCP ZeroWindow]`

- **原因**：
    - 服务端关闭连接后，接收窗口设为 0（`Win=0`），通知客户端停止发送数据。
    - 这是 TCP 流控机制的一部分，表明服务端已不接受新数据。
- **影响**：
    - 客户端继续发送数据，导致服务端最终发送 `RST` 包。

#### 2.4 `exceptionCaught` 和 `Connection Reset`

- **原因**：
    - 服务端发送 `RST` 包（可能是因为客户端持续发送数据，违反了 TCP 状态）。
    - Netty 检测到 `RST` 包或写入失败，触发 `exceptionCaught` 事件。
- **异常**：`IOException: Connection reset by peer`，表示对端（服务端）重置了连接。

---

### 3. 解决方案

#### 3.1 客户端正确处理关闭事件

- **监听 `CloseWebSocketFrame`**：
    - 在客户端的 `ChannelHandler` 中处理 `CloseWebSocketFrame`，停止发送数据并关闭连接：
      ```java
      public class WebSocketClientHandler extends SimpleChannelInboundHandler<Object> {
          private static final Logger log = LoggerFactory.getLogger(WebSocketClientHandler.class);
  
          @Override
          protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
              if (msg instanceof CloseWebSocketFrame) {
                  log.info("收到服务端关闭帧，关闭连接: {}", ctx.channel());
                  ctx.close(); // 关闭客户端连接
              } else {
                  log.debug("收到数据: {}", msg);
                  ctx.fireChannelRead(msg);
              }
          }
  
          @Override
          public void channelInactive(ChannelHandlerContext ctx) throws Exception {
              log.info("连接断开: {}", ctx.channel());
              reconnect(); // 触发重连
          }
  
          @Override
          public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
              log.error("连接异常: ", cause);
              ctx.close();
          }
      }
      ```
- **监听 `channelInactive`**：
    - 确保在 `channelInactive` 中停止发送数据，并触发重连逻辑。

#### 3.2 检查 `Channel` 状态

- 在发送数据前检查 `Channel` 是否可用：
  ```java
  public void sendData(Channel channel, Object data) {
      if (channel.isActive() && channel.isWritable()) {
          channel.writeAndFlush(data).addListener(future -> {
              if (!future.isSuccess()) {
                  log.error("发送失败: {}", future.cause());
                  channel.close();
              }
          });
      } else {
          log.warn("Channel 不可用，停止发送: {}", channel);
      }
  }
  ```

#### 3.3 服务端优化关闭逻辑

- **确保发送 `CloseWebSocketFrame`**：
    - 服务端关闭连接前，发送 `CloseWebSocketFrame`：
      ```java
      ctx.writeAndFlush(new CloseWebSocketFrame()).addListener(ChannelFutureListener.CLOSE);
      ```
- **日志记录**：
    - 服务端关闭连接时记录日志，方便排查：
      ```java
      log.info("服务端关闭连接: {}", ctx.channel());
      ctx.close();
      ```

#### 3.4 客户端流量控制

- **响应 `[TCP ZeroWindow]`**：
    - 客户端检测到服务端窗口为 0 时，暂停发送数据：
      ```java
      if (!channel.isWritable()) {
          log.warn("服务端窗口为 0，暂停发送数据");
          // 等待窗口恢复
      }
      ```
- **减少发送频率**：
    - 客户端降低数据发送频率，避免压垮服务端。

#### 3.5 抓包分析

- 检查是否有遗漏的 `RST` 或 `FIN` 包：
  ```bash
  sudo tcpdump -i eth0 host ************* and host ************ and port 10095 -w websocket-close.pcap
  ```
- 在 Wireshark 中：
    - 过滤 `tcp.flags.reset == 1` 查看 `RST` 包。
    - 过滤 `tcp.flags.fin == 1` 查看 `FIN` 包。
    - 分析 `[TCP ZeroWindow]` 前后的数据包。

#### 3.6 添加心跳和重连

- **心跳检测**：
    - 使用 `IdleStateHandler` 检测连接状态：
      ```java
      ch.pipeline().addLast(new IdleStateHandler(30, 30, 0, TimeUnit.SECONDS));
      ch.pipeline().addLast(new SimpleChannelInboundHandler<Object>() {
          @Override
          public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
              if (evt instanceof IdleStateEvent) {
                  if (((IdleStateEvent) evt).state() == IdleState.ALL_IDLE) {
                      log.warn("长时间未收发数据，关闭连接: {}", ctx.channel());
                      ctx.close();
                  }
              }
              super.userEventTriggered(ctx, evt);
          }
      });
      ```
- **重连机制**：
    - 客户端在 `channelInactive` 或 `exceptionCaught` 中触发重连：
      ```java
      private void reconnect() {
          log.info("尝试重新连接 WebSocket...");
          // 实现重连逻辑
      }
      ```

---

### 4. 结论

- **问题**：
    - 服务端关闭连接后，客户端未及时停止发送数据。
    - 服务端回复 `ACK` 和 `[TCP ZeroWindow]`，最终发送 `RST`，触发客户端 `exceptionCaught`。
- **原因**：
    - 客户端未正确处理 `CloseWebSocketFrame` 或 `channelInactive`。
    - 客户端未检测 `Channel` 状态，继续发送数据。
- **建议**：
    - 客户端处理关闭事件和 `channelInactive`（方案 3.1）。
    - 检查 `Channel` 状态（方案 3.2）。
    - 服务端优化关闭逻辑（方案 3.3）。
    - 客户端实现流量控制（方案 3.4）。
    - 添加心跳和重连机制（方案 3.6）。

---

### 5. 验证

1. 应用优化后，重启服务。
2. 模拟服务端关闭连接，检查客户端是否停止发送数据。
3. 抓包确认是否仍有 `[TCP ZeroWindow]` 或 `RST` 包。
4. 验证客户端重连逻辑是否生效。

通过上述步骤，可以解决客户端持续发送数据的问题，避免 `Connection Reset` 异常，提高 WebSocket 服务的稳定性。