# OOM时执行shell脚本并钉钉告警

## Dockerfile

```dockerfile
FROM openjdk:8-jdk-slim

# 维护者信息
MAINTAINER cqt
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8

RUN echo "" > /etc/apt/sources.list \
    && echo "deb http://mirrors.aliyun.com/debian/ bullseye main non-free contrib" | tee -a /etc/apt/sources.list \
	&& echo "deb-src http://mirrors.aliyun.com/debian/ bullseye main non-free contrib" | tee -a /etc/apt/sources.list \
	&& echo "deb http://mirrors.aliyun.com/debian-security/ bullseye-security main" | tee -a /etc/apt/sources.list \
	&& echo "deb-src http://mirrors.aliyun.com/debian-security/ bullseye-security main" | tee -a /etc/apt/sources.list \
	&& echo "deb http://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib" | tee -a /etc/apt/sources.list \
	&& echo "deb-src http://mirrors.aliyun.com/debian/ bullseye-updates main non-free contrib" | tee -a /etc/apt/sources.list \
	&& echo "deb http://mirrors.aliyun.com/debian/ bullseye-backports main non-free contrib" | tee -a /etc/apt/sources.list \
	&& echo "deb-src http://mirrors.aliyun.com/debian/ bullseye-backports main non-free contrib" | tee -a /etc/apt/sources.list

RUN apt-get update && apt-get install -y curl fontconfig

# 构建镜像时传参数据
ARG APP_NAME
ARG JAVA_OPTS

# 设置环境变量
ENV APP_NAME ${APP_NAME}
ENV APP_JAR ${APP_NAME}.jar
ENV APP_PORT ${APP_PORT}

WORKDIR /home/<USER>
# 添加jar包到容器中
ADD ${APP_JAR} ${APP_JAR}

ENTRYPOINT java -jar \
    -XX:+UseContainerSupport \
    -XX:InitialRAMPercentage=60.0 \
    -XX:MaxRAMPercentage=60.0 \
    -XX:MinRAMPercentage=60.0 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/oom/java-dumps/${APP_NAME}/${APP_NAME}-$(date "+%Y%m%d%H%M%S").hprof \
    -XX:OnOutOfMemoryError="/oom/oom.sh ${APP_NAME} %p" \
     ${JAVA_OPTS} \
     ${APP_JAR} \
```

## JVM参数 -XX:OnOutOfMemoryError

> 可在oom时, 输出dump文件后, 执行shell脚本
>
> 可以钉钉告警, 下线服务, kill -15 %p , 让容器退出, 等容器重启

```shell
-XX:OnOutOfMemoryError="/oom/oom.sh ${APP_NAME}"
```

![](./images/1714292416156.png)

## oom.sh

> oom.txt 在 /home/<USER>/oom.txt

```shell
#!/bin/bash

# 检查参数是否为空
if [ -z "$1" ]; then
  echo "请输入服务名称作为参数"
  exit 1
fi

# 设置要查找的域名
DOMAIN="$1"

# 查找匹配的 IP 地址
IP=$(grep -w "$DOMAIN" /etc/hosts | awk '{ print $1 }')

# 检查是否找到匹配的 IP
if [ -z "$IP" ]; then
  IP=$(docker inspect --format '{{ .NetworkSettings.IPAddress }}' $(hostname))
  echo "未找到匹配的 IP 地址"
else
  echo "域名 $DOMAIN 的 IP 地址为: $IP"
fi

CURRENT_DATE=$(date +"%Y-%m-%d %H:%M:%S")
MESSAGE="告警: 当前时间: $CURRENT_DATE, 服务器ip: $IP,  应用: $DOMAIN 触发 OOM 报警，请尽快处理"
echo "$MESSAGE"
echo "$MESSAGE" >> /home/<USER>/logs/oom.txt 2>&1
echo "---\n" >> /home/<USER>/logs/oom.txt  2>&1

curl 'https://oapi.dingtalk.com/robot/send?access_token=xxxxxxxxx' \
 -H 'Content-Type: application/json' \
 -d "{\"msgtype\": \"text\",\"text\": {\"content\":\"$MESSAGE\"}}" >> /home/<USER>/logs/oom.txt 2>&1
echo "---\n" >> /home/<USER>/logs/oom.txt  2>&1


if [ -n "$2" ]; then
  echo "Executing kill -15 $2"
  kill -15 "$2"
else
  echo "No second argument provided."
fi
```

## jdk8 一些排查命令

```shell
jmap -heap 查看是否内存分配过小；
jmap -histo 查看是否有明显的对象分配过多且没有释放情况；
jmap -dump 导出 JVM 当前内存快照，使用 JDK 自带或 MAT 等工具分析快照。
```
