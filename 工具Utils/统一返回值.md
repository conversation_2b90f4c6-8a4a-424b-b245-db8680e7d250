# 1. 返回值实现

```java
@Data
public class ResponseData implements Serializable {

    private static final long serialVersionUID = -2413208356148137534L;

    private String msg;

    private Object data;

    private Integer code;

    private Boolean success;

    private final static String SUCCESS_MSG = "操作成功";
    private final static String FAIL_MSG = "操作成功";

    private final static Integer SUCCESS_CODE = 200;
    private final static Integer ERROR_CODE = 400;

    private final static String KEY_DATA = "data";
    private final static String KEY_TOTAL = "recordsTotal";

    public ResponseData() {
    }

    private ResponseData(String msg, Integer code, Boolean success, Object data) {
        this.msg = msg;
        this.data = data;
        this.code = code;
        this.success = success;
    }

    private ResponseData(String msg, Integer code, Boolean success) {
        this.msg = msg;
        this.code = code;
        this.success = success;
    }

    public static ResponseData successWithData(Object data) {
        return new ResponseData(SUCCESS_MSG, SUCCESS_CODE, true, data);
    }

    public static ResponseData successWithPageList(Object data, Long recordsTotal) {
        Map<String, Object> finalData = new HashMap<>(16);
        finalData.put(KEY_DATA, data);
        finalData.put(KEY_TOTAL, recordsTotal);
        return new ResponseData(SUCCESS_MSG, SUCCESS_CODE, true, finalData);
    }

    public static ResponseData successWithData(Object data, String msg) {

        return new ResponseData(msg, SUCCESS_CODE, true, data);
    }

    public static ResponseData successWithData(Object data, String msg, Integer code) {
        return new ResponseData(msg, code, true, data);
    }

    public static ResponseData success() {
        return new ResponseData(SUCCESS_MSG, SUCCESS_CODE, true);
    }

    public static ResponseData success(String msg, Integer code) {
        return new ResponseData(msg, code, true);
    }

    public static ResponseData success(String msg) {
        return new ResponseData(msg, SUCCESS_CODE, true);
    }

    public static ResponseData fail(String errMsg, Integer errCode) {
        return new ResponseData(errMsg, errCode, false);
    }

    public static ResponseData fail(String errMsg) {
        return new ResponseData(errMsg, ERROR_CODE, false);
    }

    public static ResponseData fail() {
        return new ResponseData(FAIL_MSG, ERROR_CODE, false);
    }

}
```

# 2. 状态码, 提示信息枚举
只要getter方法, 

```java
@Getter
public enum ResponseEnum {

    /**
     *
     */
    INCOMPLETE_PARAMETERS(-1000, "参数不完整."),

    INCORRECT_PARAMETERS(-1002, "参数格式不正确"),

    APP_NOT_REGISTERED(-1004, "您的应用未注册."),

    SIGN_INVALID(-1006, "签名sign不正确."),

    TIMESTAMP_INVALID(-1008, "时间戳时间不能与当前时间相30分钟，且不能快于当前时间."),

    TOKEN_CREATE_FAIL(-1010, "系统错误, 生成Token失败."),

    CONTENT_TYPE_ERROR(-2000, "请求方式出错."),

    REQUEST_BODY_EMPTY(-2002, "请求体为空."),

    AUTHENTICATION_FAILED(-2004, "您认证未通过, 请确认token是否正常."),

    APP_DISABLE(-2006, "您的应用已被禁用, 请联系管理员."),

    DATA_ENCRYPTION_ERROR(-2008, "请求数据AES加密出错."),


    SYSTEM_ERROR_WRITER_FILE(-3000, "系统错误, 写入文件出错."),

    UNKNOWN_EXCEPTION(-4000, "未知异常全局捕获"),

    TOKEN_CREATE_SUCCESS(200, "Token获取成功."),

    DATA_PUSH_SUCCESS(200, "数据推送成功.")




    ;

    private Integer code;

    private String msg;

    ResponseEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
```

