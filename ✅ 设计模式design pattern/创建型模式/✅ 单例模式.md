# 1. 懒汉
> 先声明对象, 调用时再创建对象

```java
@Slf4j
public class LazySingleton {

    private static LazySingleton INSTANCE;

    public static LazySingleton getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new LazySingleton();
        }
        return INSTANCE;
    }

    public static void main(String[] args) throws IOException {
        System.out.println(new LazySingleton());
        System.out.println(new LazySingleton());

        System.out.println(LazySingleton.getInstance());
        System.out.println(LazySingleton.getInstance());

        ConcurrencyTester tester = ThreadUtil.concurrencyTest(1000, () -> {
            log.info("{}", LazySingleton.getInstance());
        });
        tester.close();
    }
}
```
# 2. 懒汉+同步锁
> 获取对象代码块, 添加synchronized

```java
@Slf4j
public class LazyLockSingleton {

    private static LazyLockSingleton INSTANCE;

    public static LazyLockSingleton getInstance() {
        synchronized (LazyLockSingleton.class) {
            if (INSTANCE == null) {
                INSTANCE = new LazyLockSingleton();
            }
        }

        return INSTANCE;
    }

    public static void main(String[] args) throws IOException {
        System.out.println(new LazyLockSingleton());
        System.out.println(new LazyLockSingleton());

        System.out.println(LazyLockSingleton.getInstance());
        System.out.println(LazyLockSingleton.getInstance());
        ConcurrencyTester tester = ThreadUtil.concurrencyTest(100, () -> {
            log.info("{}", LazyLockSingleton.getInstance());
        });
        tester.close();

    }
}
```
# 3. 懒汉+同步锁+Volatile
> 获取对象, 双重校验
> 线程安全, 保证高性能

```java
@Slf4j
public class LazyLockVolatileSingleton {

    private static volatile LazyLockVolatileSingleton INSTANCE;

    public static LazyLockVolatileSingleton getInstance() {
        if (INSTANCE == null) {

            synchronized (LazyLockVolatileSingleton.class) {
                if (INSTANCE == null) {
                    INSTANCE = new LazyLockVolatileSingleton();
                }
            }
        }

        return INSTANCE;
    }

    public static void main(String[] args) throws IOException {
        System.out.println(new LazyLockVolatileSingleton());
        System.out.println(new LazyLockVolatileSingleton());

        System.out.println(LazyLockVolatileSingleton.getInstance());
        System.out.println(LazyLockVolatileSingleton.getInstance());
        ConcurrencyTester tester = ThreadUtil.concurrencyTest(100, () -> {
            log.info("{}", LazyLockVolatileSingleton.getInstance());
        });
        tester.close();
    }
}
```
# 4. 饿汉+静态变量
> 类加载后就创建对象

```java
@Slf4j
public class HungrySingleton {

    private final static HungrySingleton INSTANCE = new HungrySingleton();

    public static HungrySingleton getInstance() {
        return INSTANCE;
    }

    public static void main(String[] args) {
        System.out.println(HungrySingleton.getInstance());
        System.out.println(HungrySingleton.getInstance());
        System.out.println(HungrySingleton.getInstance());

    }
}
```
# 5. 饿汉+静态代码块
> 类加载时初始化对象

```java
@Slf4j
public class HungryStaticCodeSingleton {

    private final static HungryStaticCodeSingleton INSTANCE;

    static {
        INSTANCE = new HungryStaticCodeSingleton();
    }

    public static HungryStaticCodeSingleton getInstance() {
        return INSTANCE;
    }

    public static void main(String[] args) {
        System.out.println(HungryStaticCodeSingleton.getInstance());
        System.out.println(HungryStaticCodeSingleton.getInstance());
        System.out.println(HungryStaticCodeSingleton.getInstance());

    }
}
```
# 6. 静态内部类
> 初始化实例, 只有一个线程, 由JVM保证单例

```java
public class StaticInnerClassSingleton {

    public static class StaticInnerClass {
        private static final StaticInnerClassSingleton INSTANCE = new StaticInnerClassSingleton();
    }

    public static StaticInnerClassSingleton getInstance() {
        return StaticInnerClass.INSTANCE;
    }

    public static void main(String[] args) {
        System.out.println(StaticInnerClassSingleton.getInstance());
        System.out.println(StaticInnerClassSingleton.getInstance());
        System.out.println(StaticInnerClassSingleton.getInstance());

    }
}
```
# 7. 枚举
> 线程安全
> 支持序列化
> 反序列化

```java
public enum SingletonEnum {

    /**
     *
     */
    INSTANCE;

    public static SingletonEnum getInstance() {

        return INSTANCE;
    }

    public static void main(String[] args) {
        System.out.println(SingletonEnum.getInstance());
    }
}
```
# 总结
> 推荐枚举


