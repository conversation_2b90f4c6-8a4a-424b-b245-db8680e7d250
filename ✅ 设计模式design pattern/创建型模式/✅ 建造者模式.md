> 链式调用

# lombok实现
```java
@Data
@Builder
public class UserInfo {

    private String username;

    private String password;

    public static void main(String[] args) {
        UserInfo userInfo = UserInfo.builder()
                .username("admin")
                .password("123456")
                .build();
    }
}
```
## 编译后的代码
```java
public class UserInfo {
    private String username;
    private String password;

    UserInfo(final String username, final String password) {
        this.username = username;
        this.password = password;
    }

    public static UserInfoBuilder builder() {
        return new UserInfoBuilder();
    }

    public String getUsername() {
        return this.username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setUsername(final String username) {
        this.username = username;
    }

    public void setPassword(final String password) {
        this.password = password;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof UserInfo)) {
            return false;
        } else {
            UserInfo other = (UserInfo)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$username = this.getUsername();
                Object other$username = other.getUsername();
                if (this$username == null) {
                    if (other$username != null) {
                        return false;
                    }
                } else if (!this$username.equals(other$username)) {
                    return false;
                }

                Object this$password = this.getPassword();
                Object other$password = other.getPassword();
                if (this$password == null) {
                    if (other$password != null) {
                        return false;
                    }
                } else if (!this$password.equals(other$password)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof UserInfo;
    }

    public int hashCode() {
        int PRIME = true;
        int result = 1;
        Object $username = this.getUsername();
        result = result * 59 + ($username == null ? 43 : $username.hashCode());
        Object $password = this.getPassword();
        result = result * 59 + ($password == null ? 43 : $password.hashCode());
        return result;
    }

    public String toString() {
        return "UserInfo(username=" + this.getUsername() + ", password=" + this.getPassword() + ")";
    }

    public static class UserInfoBuilder {
        private String username;
        private String password;

        UserInfoBuilder() {
        }

        public UserInfoBuilder username(final String username) {
            this.username = username;
            return this;
        }

        public UserInfoBuilder password(final String password) {
            this.password = password;
            return this;
        }

        public UserInfo build() {
            return new UserInfo(this.username, this.password);
        }

        public String toString() {
            return "UserInfo.UserInfoBuilder(username=" + this.username + ", password=" + this.password + ")";
        }
    }
}
```
# StringBuilder 实现append
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680700948777-7047d37b-7025-4e00-b0bd-58775cd2e7b4.png#averageHue=%23222428&clientId=uf820bead-ab19-4&from=paste&height=112&id=u5da8231b&originHeight=224&originWidth=822&originalType=binary&ratio=2&rotation=0&showTitle=false&size=22960&status=done&style=none&taskId=u356faa06-527f-4bc8-9895-160aeced150&title=&width=411)
