# 概念

> 一个请求, 经过一批处理器, 组成一个链, 逐一执行, 判断其中处理器是否满足条件, 满足则中断链, 结束请求,  
> 或整个链每个处理器都执行完,
> 请求参数或响应参数可以放在一个上下文参数 Context 中, 在链中传递,
> 这就是责任链

## 使用场景

> -   ip 鉴权->号码鉴权->企业鉴权...

# 实现方法

## 1. byte-buddy  包 实现 ElementMatcher 接口

```xml
<dependency>
    <groupId>net.bytebuddy</groupId>
    <artifactId>byte-buddy</artifactId>
    <version>1.12.16</version>
</dependency>
```

### 具体处理器实现

> 需要多例
> 返回 true, 则表示 Matchers, 通过匹配, 继续下一个链,  
> 返回 false, 则表示 Matchers 不通过匹配, 后续链不执行, 直接返回

```java
@Slf4j
@Component
@RequiredArgsConstructor
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class IpMatchers implements ElementMatcher<context> {
	@Override
    public boolean matches(Context context) {
        if(true) {
            return true;
        }
        return false;
    }
}
```

### 责任链管理

> context: 上下文参数, 可传递请求参数或响应参数,
> matchs = true 表示责任链均匹配通过
> false 表示责任链存在执行不匹配的情况

```java
boolean matches = ElementMatchers.any()
         // and 具体处理器
        .and(SpringUtil.getBean(IpMatchers.class))
         // 上下文参数
        .matches(context);
if (!matches) {
    return Result.fail(context.getResultCodeEnum());
}
```

## 2. 自定义抽象类实现

> 可通过外部配置, 动态生成责任链,

### 责任链抽象类

```java
public abstract class AbstractChainHandler<T> {

    /**
     - 下一个链
     */
    protected AbstractChainHandler<T> nextChain;

    private void next(AbstractChainHandler<T> chainHandler) {
        nextChain = chainHandler;
    }

    protected Boolean isEnd() {

        return nextChain == null;
    }

    /**
     - 还存在下一个节点, 转到下一个节点
     *
     - @param context 上下文
     */
    protected void nextHandler(T context) {
        if (!isEnd()) {
            nextChain.doHandler(context);
        }
    }

    /**
     - 处理事件
     *
     - @param context 上下文
     */
    public abstract void doHandler(T context);

    public static class Builder<T> {

        private AbstractChainHandler<T> head;

        private AbstractChainHandler<T> tail;

        public void addHandler(AbstractChainHandler<T> abstractChainHandler) {
            if (head == null) {
                head = tail = abstractChainHandler;
                return;
            }

            tail.next(abstractChainHandler);
            tail = abstractChainHandler;
        }

        public AbstractChainHandler<T> build() {
            return head;
        }
    }
}
```

### 具体处理器实现

```java
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class AddAbstractChainHandler extends AbstractChainHandler<Context> {


    @Override
    public void doHandler(Context context) {


    }

}

```

### 责任链创建

```java
Context context = new Context();

# 构造责任链(可for 通过builder动态构建)
AbstractChainHandler.Builder<Context> builder = new AbstractChainHandler.Builder<>();
builder.addHandler(new AddAbstractChainHandler());

AbstractChainHandler<TransformContext> chainHandler = builder.build();
// 执行
chainHandler.doHandler(context);
```

## 3. Apache Commons Chain

### 依赖

```xml
 <!--责任链实现-->
<dependency>
    <groupId>commons-chain</groupId>
    <artifactId>commons-chain</artifactId>
    <version>1.2</version>
</dependency>
```

### 接口

```java
public interface Command {

    public static final boolean CONTINUE_PROCESSING = false;

    public static final boolean PROCESSING_COMPLETE = true;

    boolean execute(Context context) throws Exception;


}
```

### 具体实现类

> 返回 true, 表示终止责任链, 不进行后续 Command
> 返回 false, 表示继续执行
> ContextBase

```java
@Slf4j
public class Run1Command implements Command {

    @Override
    public boolean execute(Context context) throws Exception {
        log.info("run1....{}", context.get("code"));

        return Command.CONTINUE_PROCESSING;
    }
}

@Slf4j
public class Run2Command implements Command {

    @Override
    public boolean execute(Context context) throws Exception {
        log.info("run2....");


        return Command.CONTINUE_PROCESSING;
    }
}

@Slf4j
public class Run3Command implements Command {

    @Override
    public boolean execute(Context context) throws Exception {
        log.info("run3....");


        return Command.PROCESSING_COMPLETE;
    }
}
```

### 责任链构造

> 内部使用 Command 数组存储处理器
> protected Command[] commands = new Command[0];
>  

```java
public class RunChainBuilder extends ChainBase {

    public RunChainBuilder() {

        addCommand(new Run1Command());
        addCommand(new Run2Command());
        addCommand(new Run3Command());

    }

}
```

### 请求测试

```java
    @Test
    public void command() throws Exception {
        RunChainBuilder builder = new RunChainBuilder();
    	// 是个Map
        ContextBase contextBase = new ContextBase();
        contextBase.put("code", 1);
        boolean execute = builder.execute(contextBase);
        System.out.println(execute);
    }
```

### 上下文 Context

```java
public class ContextBase extends HashMap implements Context {
}

public interface Context extends Map {


}
```

## pie 实现责任链

https://github.com/feiniaojin/pie
