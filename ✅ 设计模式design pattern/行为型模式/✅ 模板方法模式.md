> 将一些相同逻辑的操作抽象在一个方法里, 方法内部的部分逻辑有子类实现, 整体流程一致 不变, 主要工作是实现父类方法
> 定义了一些算法流程, 其中一些算法, 在子类实现, 子类重写父类方法

# 模板方法抽象类
```java
public abstract class AbstractTemplate {

    // 抽象方法 为protected 对外不暴露
    protected abstract String doEat();

    protected abstract String doShop();

    public void execute() {

        System.out.println("模板方法开始");
        String doEat = doEat();
        System.out.println(doEat);

        String doShop = doShop();
        System.out.println(doShop);

        System.out.println("模板方法结束");
    }
}
```
# 方法实现
```java
public class MethodImpl extends AbstractTemplate{

    @Override
    protected String doEat() {
        return "吃午饭.....";
    }

    @Override
    protected String doShop() {
        return "淘宝中...";
    }
}
```
# 调用模板方法
```java
public class TemplateClient {

    public static void main(String[] args) {
        MethodImpl method = new MethodImpl();
        method.execute();
    }
}
```
