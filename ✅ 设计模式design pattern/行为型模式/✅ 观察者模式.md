# 观察者模式
## demo
> 有多个观察者，主题数据发生变化时，通知所有观察者，观察者实现再去处理各自的业务逻辑。

### 观察者接口
```java
public abstract class Observer {

    protected Subject subject;

    public abstract void update();
}
```
### 主题管理
```java
public class Subject {

    /**
     * 观察者列表
     */
    private List<Observer> observerList = new ArrayList<>();

    /**
     * 观察的数据
     */
    private int state;

    public void setState(int state) {
        this.state = state;
        // 数据变更，通知观察者
        notifyAllObservers();
    }

    public int getState() {
        return state;
    }

    /**
     * 添加定义的观察者
     */
    public void attach(Observer observer) {
        observerList.add(observer);
    }

    /**
     * 通知所有观察者
     */
    public void notifyAllObservers() {
        for (Observer observer : observerList) {
            observer.update();
        }
    }
}
```
### 具体观察者
```java
public class OneObserver extends Observer {

    public OneObserver(Subject subject) {
        this.subject = subject;
        // 实例化观察者对象后，将当前对象加入观察者列表
        this.subject.attach(this);
    }

    @Override
    public void update() {
        System.out.println("第一个观察者收到数据变化： " + this.subject.getState());
    }
}
```
```java
public class TwoObserver extends Observer {

    public TwoObserver(Subject subject) {
        this.subject = subject;
        // 实例化观察者对象后，将当前对象加入观察者列表
        this.subject.attach(this);
    }

    @Override
    public void update() {
        System.out.println("第二个观察者收到数据变化： " + this.subject.getState());
    }
}
```
### 测试
```java
public class ObserverTest {

    public static void main(String[] args) {
        Subject subject = new Subject();
        new OneObserver(subject);
        new TwoObserver(subject);
        new ThreeObserver(subject);

        // 设置数据变化
        subject.setState(111);
    }
}
```
![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1683211632432-7c964445-256b-4f20-b50f-d3d9a8946571.png#averageHue=%23303134&clientId=u7cd13d39-8df1-4&from=paste&height=91&id=u74e7b23f&originHeight=182&originWidth=620&originalType=binary&ratio=2&rotation=0&showTitle=false&size=36816&status=done&style=none&taskId=u563bbe90-cafb-4103-94d0-45ee9703ad0&title=&width=310)
## Spring
1. 事件监听方式
     方式一：实现ApplicationListener<DataChangeEvent>接口
     方式二：使用@EventListener注解
2. 异步：
     使用@Async("线程池bean名称")， 需要指定线程池！
 3. 消息过滤： SpEL
     @EventListener(condition = "#dataChangeEvent.price > 100")  只通知price大于100的消息
### 定义事件类
```java
public class DataChangeEvent extends ApplicationEvent {

    private static final long serialVersionUID = -7228585740390669999L;

    private String data;

    private Integer price;

    public String getData() {
        return data;
    }

    public Integer getPrice() {
        return price;
    }

    public DataChangeEvent(Object source, String data, Integer price) {
        super(source);
        this.data = data;
        this.price = price;
    }
}
```
### 监听事件变化
```java
@Component
public class DataChangeEventListener implements ApplicationListener<DataChangeEvent> {
    /**
     * 1. 事件监听方式
     * 方式一：实现ApplicationListener<DataChangeEvent>接口
     * 方式二：使用@EventListener注解
     * 2. 异步：
     * 使用@Async， 需要指定线程池！
     * 3. 消息过滤：
     * SpEL
     *
     * @EventListener(condition = "#dataChangeEvent.price > 100")  只通知price大于100的消息
     */

//    @Async
//    @EventListener
    @EventListener(condition = "#dataChangeEvent.price > 100")
    public void listener(DataChangeEvent dataChangeEvent) {
        System.out.println("收到数据变化通知：" + dataChangeEvent.getData() + "---" + dataChangeEvent.getPrice());
    }

    @Override
    public void onApplicationEvent(DataChangeEvent event) {
        System.out.println("收到数据变化通知2：" + event.getData() + "---" + event.getPrice());
    }
}

```
### 发布事件
```java
@RestController
public class DataChangeEventNotify {

    @Autowired
    private ApplicationEventPublisher publisher;

    @GetMapping("publish")
    public void publish(String data, Integer price) {
        publisher.publishEvent(new DataChangeEvent(this, data, price));
    }
}
```
## Guava
### 定义事件类
> 就是普通的实体类 get set

```java
public class GuavaEvent {

    private String data;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public GuavaEvent(String data) {
        this.data = data;
    }
}
```
### 定义EventBus Bean
```java
@Configuration
public class GuavaEventConfig {

    @Bean
    public EventBus eventBus() {
        return new EventBus();
    }
}
```
### 监听器实现
```java
@Component
public class GuavaListener {

    @Autowired
    private EventBus eventBus;

    /**
     * 项目自启动，注册监听器
     */
    @PostConstruct()
    public void init() {
        eventBus.register(this);
    }

    /**
     * 监听方法
     */
    @Subscribe
    @AllowConcurrentEvents
    public void listener(GuavaEvent guavaEvent) {
        System.out.println("guava事件监听到：" + guavaEvent.getData());
    }
    
    /**
     * 发布消息
     */
    public void post(GuavaEvent guavaEvent) {
        eventBus.post(guavaEvent);
    }
}
```
