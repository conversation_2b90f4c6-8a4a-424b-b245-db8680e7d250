> 有一批算法, 根据条件动态执行某个算法, 可由外部控制, 
> 策略接口 Strategy	定义了策略方法
> 具体策略实现类(策略类型) StrategyImpl		
> 根据测量类型, 执行不同的策略


## 基于spring实现
### 1. 策略接口Strategy
```java
public interface Strategy {

    void execute(Context context);

    String getType();
}
```
### 2. 具体实现类
```java
@Slf4j
@Component
public class Strategy1Impl implements Strategy {
    @Override
    public void execute(Context context) {
        log.info("strategy one ........");
    }

    @Override
    public String getType() {
        return "one";
    }
}
```
```java
@Slf4j
@Component
public class Strategy2Impl implements Strategy {
    @Override
    public void execute(Context context) {
        log.info("strategy two ........");
    }

    @Override
    public String getType() {
        return "two";
    }
}
```
### 3. 策略管理类
> 初始化策略, 存于Map, 通过类型获取

```java
@Component
public class StrategyManager {

    public static final Map<String, Strategy> STRATEGY_MAP = new ConcurrentHashMap<>();

    @Resource
    private List<Strategy> strategyList;

    @PostConstruct
    public void init() {
        for (Strategy strategy : strategyList) {
            STRATEGY_MAP.put(strategy.getType(), strategy);
        }
    }
}
```
### 4. 测试
```java
@Component
public class StrategyClient {
    public void run(String type) {
        Strategy strategy = StrategyManager.STRATEGY_MAP.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到策略");
        }
        strategy.execute(new ContextBase());

    }
}
```
```java
    @Resource
    private StrategyClient strategyClient;

    @Test
    public void test() {

        strategyClient.run("one");

    }
```
