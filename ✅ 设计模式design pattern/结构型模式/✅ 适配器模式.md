> 适配器模式是一种常见的设计模式，用于将一个已存在的类的接口转换成客户端所期望的接口。

# Demo

1. 定义目标接口
```java
interface MediaPlayer {
    void play(String audioType, String fileName);
}
```

2. 定义被适配的类
```java
class AdvancedMediaPlayerImpl implements AdvancedMediaPlayer {
    public void playVlc(String fileName) {
        System.out.println("Playing vlc file. Name: " + fileName);
    }

    public void playMp4(String fileName) {
        System.out.println("Playing mp4 file. Name: " + fileName);
    }
}

interface AdvancedMediaPlayer {
    void playVlc(String fileName);
    void playMp4(String fileName);
}
```

3. 实现适配器类
```java
class MediaAdapter implements MediaPlayer {
    AdvancedMediaPlayer advancedMusicPlayer;

    MediaAdapter(String audioType){
        if(audioType.equalsIgnoreCase("vlc") ){
            advancedMusicPlayer = new AdvancedMediaPlayerImpl();
        }
    }

    public void play(String audioType, String fileName) {
        if(audioType.equalsIgnoreCase("vlc")){
            advancedMusicPlayer.playVlc(fileName);
        }
    }
}
```

4. 在客户端中使用适配器
```java
public class AudioPlayer implements MediaPlayer {
    MediaAdapter mediaAdapter; 

    public void play(String audioType, String fileName) {
        if(audioType.equalsIgnoreCase("mp3")){
            System.out.println("Playing mp3 file. Name: " + fileName);         
        }
        else if(audioType.equalsIgnoreCase("vlc")){
            mediaAdapter = new MediaAdapter(audioType);
            mediaAdapter.play(audioType, fileName);
        }
    }
}
```
> 该适配器模式实例将音频播放器（MediaPlayer）和高级音频播放器（AdvancedMediaPlayer）进行了适配。MediaPlayer接口是一个标准接口，AdvancedMediaPlayer是一个特殊化的接口，而音频播放器AudioPlayer是一个中间类，用于调用适配器来实现MediaPlayer接口。MediaAdapter类是一个适配器类，将AdvancedMediaPlayer转化成适合MediaPlayer使用的形式。这种适配器模式将使用到不同的类型的对象，即MediaPlayer和AdvancedMediaPlayer对象，使用MediaPlayer接口方法来处理不同类型的对象。

# 实际项目或框架使用
## Spring MVC
```java
/** List of HandlerAdapters used by this servlet. */
@Nullable
private List<HandlerAdapter> handlerAdapters;

protected HandlerAdapter getHandlerAdapter(Object handler) throws ServletException {
    if (this.handlerAdapters != null) {
        for (HandlerAdapter adapter : this.handlerAdapters) {
            if (adapter.supports(handler)) {
                return adapter;
            }
        }
    }
    throw new ServletException("No adapter for handler [" + handler +
            "]: The DispatcherServlet configuration needs to include a HandlerAdapter that supports this handler");
}
```
## Spring框架中的JDBC抽象层在处理不同类型的数据库

# 使用场景
适配器模式是一种常见的设计模式，它通常在以下情况下使用：

1. 系统需要使用已经存在的类，但是该类的接口不符合系统的需求。
2. 需要通过接口转换实现不同类之间的协同作用。
3. 用于在多个类之间共享相同的功能，但具体的类不同。
4. 在修改系统接口时，适配器可以帮助在不影响现有代码的情况下重构接口。
5. 可以被视为“组合模式”的变体，即组成适配器的类所使用的方法在许多其他对象中也可以使用。

总之，适配器模式是在现有的类和代码组合中实现新的功能和需求的一种简单有效的方式。可以说，无论何时当您遇到可复用的类，但它与您现有的应用环境不兼容时，您可以考虑适配器模式。
