![image.png](https://cdn.nlark.com/yuque/0/2023/png/684952/1680701480456-5e3509a2-11e4-4dda-a9ac-1ad7b20efa46.png#averageHue=%23f6f6f6&clientId=uae7449c8-988b-4&from=paste&height=720&id=ub360d5d1&originHeight=1440&originWidth=2050&originalType=binary&ratio=2&rotation=0&showTitle=false&size=235991&status=done&style=none&taskId=u881bae69-4901-49aa-9444-afb35dd3098&title=&width=1025)
```
Spring源码中应用的设计模式使用:
 - 工厂模式：Spring中的BeanFactory就是简单工厂模式的体现，根据传入一个唯一的标识来获得Bean对象
 - 单例模式：Spring依赖注入Bean实例默认是单例的。Spring的依赖注入（包括lazy-init方式）都是发生在AbstractBeanFactory的getBean里。getBean的doGetBean方法调用getSingleton进行bean的创建。
 - 装饰器模式：Spring中用到的包装器模式在类名上有两种表现：一种是类名中含有Wrapper，另一种是类名中含有Decorator。
 - 代理模式：AOP底层，就是动态代理模式的实现观察者模式：spring的事件驱动模型使用的是 
 - 观察者模式 ，Spring中Observer模式常用的地方是listener的实现。如：ApplicationContextEvent、ApplicationListener
 - 策略模式：Spring框架的资源访问Resource接口。该接口提供了更强的资源访问能力，Spring 框架本身大量使用了 Resource 接口来访问底层资源。 
  - UrlResource：访问网络资源的实现类。
  - ClassPathResource：访问类加载路径里资源的实现类。
  - FileSystemResource：访问文件系统里资源的实现类。
  - ServletContextResource：访问相对于 ServletContext 路径里的资源的实现类.
  - InputStreamResource：访问输入流资源的实现类。
  - ByteArrayResource：访问字节数组资源的实现类。
Mybatis源码中应用的设计模式使用:
 - Builder模式：例如SqlSessionFactoryBuilder、XMLConfigBuilder、XMLMapperBuilder、XMLStatementBuilder、CacheBuilder；
 - 工厂模式：例如SqlSessionFactory、ObjectFactory、MapperProxyFactory；
 - 单例模式：例如ErrorContext和LogFactory；
 - 代理模式：Mybatis实现的核心，比如MapperProxy、ConnectionLogger，用的jdk的动态代理；还有executor.loader包使用了cglib或者javassist达到延迟加载的效果；
 - 组合模式：例如SqlNode和各个子类ChooseSqlNode等；
 - 模板方法模式：例如BaseExecutor和SimpleExecutor，还有BaseTypeHandler和所有的子类例如IntegerTypeHandler；
 - 适配器模式：例如Log的Mybatis接口和它对jdbc、log4j等各种日志框架的适配实现；
 - 装饰者模式：例如Cache包中的cache.decorators子包中等各个装饰者的实现；
 - 迭代器模式：例如迭代器模式PropertyTokenizer；
```

# 1. 责任链设计模式
> 请求在一个链条上处理，链条上的受理者处理完毕之后决定是继续往后传递还是中断当前处理流程。

# 2.观察者模式
> 观察者模式，又称发布-订阅模式，它是一种通知机制，让发送通知的一方（被观察方）和接收通知的一方（观察者）能彼此分离，互不影响。


# 3. 工厂模式
> 自己不再主动创建对象，而是让工厂来帮我们创建对象。

## 简单工厂模式
