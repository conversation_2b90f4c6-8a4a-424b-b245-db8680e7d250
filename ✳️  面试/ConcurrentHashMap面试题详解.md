# ConcurrentHashMap面试题详解

> 本文档详细解析ConcurrentHashMap的线程安全机制、JDK版本演进、性能特点和使用场景。

## 目录

- [基础概念](#基础概念)
- [JDK 1.7实现](#jdk-17实现)
- [JDK 1.8实现](#jdk-18实现)
- [版本对比](#版本对比)
- [性能分析](#性能分析)

---

## 基础概念

### 1. ConcurrentHashMap解决了什么问题？⭐⭐⭐⭐

#### 问题分析
ConcurrentHashMap是为了解决HashMap线程不安全和Hashtable性能低下的问题而设计的。

#### 标准答案

**问题背景：**

```mermaid
graph TB
    subgraph "HashMap问题"
        A[HashMap] --> A1[线程不安全]
        A1 --> A2[数据丢失]
        A1 --> A3[死循环(JDK1.7)]
        A1 --> A4[数据覆盖(JDK1.8)]
    end
    
    subgraph "Hashtable问题"
        B[Hashtable] --> B1[线程安全]
        B1 --> B2[synchronized方法]
        B2 --> B3[性能低下]
        B3 --> B4[锁粒度太大]
    end
    
    subgraph "ConcurrentHashMap优势"
        C[ConcurrentHashMap] --> C1[线程安全]
        C --> C2[高性能]
        C1 --> C3[分段锁(1.7)]
        C1 --> C4[CAS+synchronized(1.8)]
        C2 --> C5[细粒度锁]
        C2 --> C6[读操作无锁]
    end
    
    style A2 fill:#ffcdd2
    style A3 fill:#ffcdd2
    style B4 fill:#fff3e0
    style C5 fill:#c8e6c9
    style C6 fill:#c8e6c9
```

#### 对比分析

| 特性 | HashMap | Hashtable | ConcurrentHashMap |
|------|---------|-----------|-------------------|
| **线程安全** | ❌ 否 | ✅ 是 | ✅ 是 |
| **性能** | 🔥 最高 | 🐌 最低 | ⚡ 高 |
| **锁机制** | 无锁 | 方法级synchronized | 分段锁/CAS+synchronized |
| **null值** | 允许 | 不允许 | 不允许 |
| **迭代器** | fail-fast | fail-fast | 弱一致性 |
| **适用场景** | 单线程 | 低并发 | 高并发 |

#### 代码示例
```java
public class ConcurrentHashMapIntroduction {
    
    // 演示HashMap线程安全问题
    public void demonstrateHashMapProblem() throws InterruptedException {
        Map<Integer, Integer> hashMap = new HashMap<>();
        int threadCount = 10;
        int operationsPerThread = 1000;
        
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // 多线程并发操作HashMap
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    hashMap.put(threadId * operationsPerThread + j, j);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        
        int expectedSize = threadCount * operationsPerThread;
        int actualSize = hashMap.size();
        
        System.out.println("HashMap并发测试:");
        System.out.println("期望大小: " + expectedSize);
        System.out.println("实际大小: " + actualSize);
        System.out.println("数据丢失: " + (expectedSize - actualSize));
    }
    
    // 演示Hashtable性能问题
    public void demonstrateHashtablePerformance() throws InterruptedException {
        Map<Integer, Integer> hashtable = new Hashtable<>();
        int threadCount = 10;
        int operationsPerThread = 10000;
        
        long start = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    hashtable.put(threadId * operationsPerThread + j, j);
                    hashtable.get(threadId * operationsPerThread + j);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        long hashtableTime = System.currentTimeMillis() - start;
        
        System.out.println("Hashtable性能测试: " + hashtableTime + "ms");
    }
    
    // 演示ConcurrentHashMap优势
    public void demonstrateConcurrentHashMapAdvantage() throws InterruptedException {
        Map<Integer, Integer> concurrentMap = new ConcurrentHashMap<>();
        int threadCount = 10;
        int operationsPerThread = 10000;
        
        long start = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    concurrentMap.put(threadId * operationsPerThread + j, j);
                    concurrentMap.get(threadId * operationsPerThread + j);
                }
                latch.countDown();
            }).start();
        }
        
        latch.await();
        long concurrentMapTime = System.currentTimeMillis() - start;
        
        System.out.println("ConcurrentHashMap性能测试: " + concurrentMapTime + "ms");
        System.out.println("数据完整性: " + (concurrentMap.size() == threadCount * operationsPerThread));
    }
}
```

---

## JDK 1.7实现

### 2. JDK 1.7 ConcurrentHashMap分段锁原理？⭐⭐⭐⭐⭐

#### 问题分析
JDK 1.7的ConcurrentHashMap采用分段锁(Segment)机制，是经典的并发设计。

#### 标准答案

**分段锁结构：**

```mermaid
graph TB
    subgraph "ConcurrentHashMap结构"
        A[ConcurrentHashMap] --> B[Segment数组]
        B --> C[Segment0]
        B --> D[Segment1]
        B --> E[Segment2]
        B --> F[SegmentN]
        
        C --> C1[ReentrantLock]
        C --> C2[HashEntry数组]
        
        C2 --> C3[HashEntry链表]
        C3 --> C4[key1, value1, next]
        C3 --> C5[key2, value2, next]
    end
    
    subgraph "并发访问"
        G[线程1] --> H[访问Segment0]
        I[线程2] --> J[访问Segment1]
        K[线程3] --> L[访问Segment0]
        
        H --> M[获取锁成功]
        J --> N[获取锁成功]
        L --> O[等待锁释放]
    end
    
    style M fill:#c8e6c9
    style N fill:#c8e6c9
    style O fill:#fff3e0
```

#### 核心特点

1. **分段设计**：将整个Map分为多个Segment，每个Segment独立加锁
2. **并发度**：默认16个Segment，最大支持16个线程同时写操作
3. **锁粒度**：锁定单个Segment而非整个Map
4. **读操作**：大部分读操作不需要加锁

#### 源码分析
```java
public class JDK7ConcurrentHashMapAnalysis {
    
    // 模拟JDK 1.7 ConcurrentHashMap核心结构
    static class Segment<K, V> extends ReentrantLock {
        // 每个Segment内部的HashEntry数组
        volatile HashEntry<K, V>[] table;
        // 元素个数
        volatile int count;
        // 修改次数，用于fail-fast
        int modCount;
        
        // 阈值，超过后需要扩容
        int threshold;
        // 负载因子
        final float loadFactor;
        
        Segment(float lf, int threshold, HashEntry<K, V>[] tab) {
            this.loadFactor = lf;
            this.threshold = threshold;
            this.table = tab;
        }
        
        // put操作需要获取锁
        V put(K key, int hash, V value, boolean onlyIfAbsent) {
            lock();  // 获取Segment锁
            try {
                // 在锁保护下进行put操作
                return doPut(key, hash, value, onlyIfAbsent);
            } finally {
                unlock();  // 释放锁
            }
        }
        
        private V doPut(K key, int hash, V value, boolean onlyIfAbsent) {
            // 具体的put逻辑
            HashEntry<K, V>[] tab = table;
            int index = (tab.length - 1) & hash;
            HashEntry<K, V> first = tab[index];
            
            // 遍历链表查找key
            for (HashEntry<K, V> e = first; e != null; e = e.next) {
                if (e.hash == hash && key.equals(e.key)) {
                    V oldValue = e.value;
                    if (!onlyIfAbsent) {
                        e.value = value;  // 更新值
                    }
                    return oldValue;
                }
            }
            
            // 创建新节点，头插法
            HashEntry<K, V> newEntry = new HashEntry<>(hash, key, value, first);
            tab[index] = newEntry;
            ++count;
            
            // 检查是否需要扩容
            if (count >= threshold) {
                rehash();
            }
            
            return null;
        }
        
        // get操作通常不需要加锁
        V get(Object key, int hash) {
            // 使用volatile读取table
            HashEntry<K, V>[] tab = table;
            int index = (tab.length - 1) & hash;
            HashEntry<K, V> e = tab[index];
            
            // 遍历链表
            while (e != null) {
                if (e.hash == hash && key.equals(e.key)) {
                    return e.value;  // volatile读取
                }
                e = e.next;
            }
            return null;
        }
        
        // 扩容操作
        private void rehash() {
            HashEntry<K, V>[] oldTable = table;
            int oldCapacity = oldTable.length;
            int newCapacity = oldCapacity << 1;  // 扩容2倍
            
            threshold = (int) (newCapacity * loadFactor);
            HashEntry<K, V>[] newTable = new HashEntry[newCapacity];
            
            // 重新hash所有元素
            for (int i = 0; i < oldCapacity; i++) {
                HashEntry<K, V> e = oldTable[i];
                while (e != null) {
                    HashEntry<K, V> next = e.next;
                    int idx = e.hash & (newCapacity - 1);
                    e.next = newTable[idx];
                    newTable[idx] = e;
                    e = next;
                }
            }
            
            table = newTable;
        }
    }
    
    // HashEntry节点
    static class HashEntry<K, V> {
        final int hash;
        final K key;
        volatile V value;  // volatile保证可见性
        volatile HashEntry<K, V> next;  // volatile保证链表的可见性
        
        HashEntry(int hash, K key, V value, HashEntry<K, V> next) {
            this.hash = hash;
            this.key = key;
            this.value = value;
            this.next = next;
        }
    }
    
    // 演示分段锁的并发优势
    public void demonstrateSegmentConcurrency() {
        System.out.println("=== 分段锁并发演示 ===");
        
        // 模拟16个Segment的ConcurrentHashMap
        int segmentCount = 16;
        System.out.println("Segment数量: " + segmentCount);
        System.out.println("理论最大并发写线程: " + segmentCount);
        
        // 计算key分布到不同Segment
        String[] keys = {"key1", "key2", "key3", "key4", "key5"};
        
        for (String key : keys) {
            int hash = key.hashCode();
            int segmentIndex = (hash >>> 28) & (segmentCount - 1);  // 高4位确定Segment
            int elementIndex = hash & 15;  // 低4位确定数组位置
            
            System.out.printf("Key: %s, Hash: %d, Segment: %d, Index: %d%n",
                             key, hash, segmentIndex, elementIndex);
        }
        
        System.out.println("\n分析:");
        System.out.println("1. 不同key可能分布在不同Segment");
        System.out.println("2. 访问不同Segment的操作可以并发执行");
        System.out.println("3. 只有访问同一Segment的操作才需要竞争锁");
    }
}
```

---

## JDK 1.8实现

### 3. JDK 1.8 ConcurrentHashMap实现原理？⭐⭐⭐⭐⭐

#### 问题分析
JDK 1.8对ConcurrentHashMap进行了重大重构，采用CAS+synchronized的方式替代分段锁。

#### 标准答案

**JDK 1.8结构：**

```mermaid
graph TB
    subgraph "JDK 1.8 ConcurrentHashMap"
        A[ConcurrentHashMap] --> B[Node数组]
        B --> C[数组位置0]
        B --> D[数组位置1]
        B --> E[数组位置2]

        C --> C1[链表头节点]
        C1 --> C2[Node链表]

        D --> D1[TreeBin节点]
        D1 --> D2[红黑树结构]

        E --> E1[null]
    end

    subgraph "并发控制"
        F[CAS操作] --> F1[数组元素的原子更新]
        G[synchronized] --> G1[链表/红黑树的同步]
        H[volatile] --> H1[数组和节点的可见性]
    end

    subgraph "操作流程"
        I[put操作] --> J{数组位置是否为空?}
        J -->|是| K[CAS设置头节点]
        J -->|否| L[synchronized同步块]
        L --> M[链表操作或红黑树操作]
    end

    style K fill:#c8e6c9
    style M fill:#e3f2fd
```

#### 核心改进

1. **取消分段锁**：不再使用Segment，直接对数组元素加锁
2. **CAS+synchronized**：空位置用CAS，有冲突用synchronized
3. **红黑树优化**：链表长度≥8时转换为红黑树
4. **更细粒度**：锁粒度从Segment级别降到数组元素级别

#### 源码分析
```java
public class JDK8ConcurrentHashMapAnalysis {

    // 模拟JDK 1.8 ConcurrentHashMap核心实现
    static class Node<K, V> {
        final int hash;
        final K key;
        volatile V val;          // volatile保证可见性
        volatile Node<K, V> next; // volatile保证链表可见性

        Node(int hash, K key, V val, Node<K, V> next) {
            this.hash = hash;
            this.key = key;
            this.val = val;
            this.next = next;
        }
    }

    // 红黑树节点
    static class TreeNode<K, V> extends Node<K, V> {
        TreeNode<K, V> parent;
        TreeNode<K, V> left;
        TreeNode<K, V> right;
        TreeNode<K, V> prev;
        boolean red;

        TreeNode(int hash, K key, V val, Node<K, V> next) {
            super(hash, key, val, next);
        }
    }

    // 红黑树容器
    static class TreeBin<K, V> extends Node<K, V> {
        TreeNode<K, V> root;
        volatile TreeNode<K, V> first;
        volatile Thread waiter;
        volatile int lockState;

        // 读锁状态
        static final int WRITER = 1;
        static final int WAITER = 2;
        static final int READER = 4;

        TreeBin(TreeNode<K, V> b) {
            super(-2, null, null, null);  // hash值为-2表示TreeBin
            this.first = b;
            TreeNode<K, V> r = null;
            // 构建红黑树...
        }
    }

    // 模拟put操作的核心逻辑
    public void simulatePutOperation() {
        System.out.println("=== JDK 1.8 put操作流程 ===");

        // 假设的数组和相关变量
        Node<String, Integer>[] table = new Node[16];
        String key = "testKey";
        Integer value = 100;
        int hash = key.hashCode();

        System.out.println("1. 计算hash值: " + hash);

        // 计算数组索引
        int index = (table.length - 1) & hash;
        System.out.println("2. 计算数组索引: " + index);

        // 模拟put逻辑
        Node<String, Integer> f = table[index];

        if (f == null) {
            System.out.println("3. 数组位置为空，使用CAS设置头节点");
            // 实际代码: casTabAt(tab, i, null, new Node<K,V>(hash, key, value, null))
            table[index] = new Node<>(hash, key, value, null);
            System.out.println("   CAS操作成功，无需加锁");
        } else {
            System.out.println("3. 数组位置不为空，需要同步处理");

            synchronized (f) {  // 对头节点加锁
                System.out.println("   获取synchronized锁成功");

                if (f.hash >= 0) {
                    System.out.println("   处理链表结构");
                    // 遍历链表，查找或添加节点
                    processLinkedList(f, key, value, hash);
                } else if (f instanceof TreeBin) {
                    System.out.println("   处理红黑树结构");
                    // 在红黑树中查找或添加节点
                    processRedBlackTree((TreeBin<String, Integer>) f, key, value, hash);
                }
            }
        }

        System.out.println("4. put操作完成");
    }

    private void processLinkedList(Node<String, Integer> head, String key, Integer value, int hash) {
        int binCount = 0;
        Node<String, Integer> e = head;

        while (e != null) {
            if (e.hash == hash && key.equals(e.key)) {
                System.out.println("     找到相同key，更新value");
                e.val = value;
                return;
            }
            e = e.next;
            binCount++;
        }

        System.out.println("     未找到相同key，添加新节点到链表尾部");
        // 实际会添加到链表尾部

        if (binCount >= 7) {  // 实际阈值是8
            System.out.println("     链表长度达到阈值，考虑转换为红黑树");
        }
    }

    private void processRedBlackTree(TreeBin<String, Integer> treeBin, String key, Integer value, int hash) {
        System.out.println("     在红黑树中查找或插入节点");
        // 红黑树的查找和插入逻辑
    }

    // 演示get操作（无锁读取）
    public void simulateGetOperation() {
        System.out.println("=== JDK 1.8 get操作流程 ===");

        Node<String, Integer>[] table = new Node[16];
        String key = "testKey";
        int hash = key.hashCode();
        int index = (table.length - 1) & hash;

        System.out.println("1. 计算hash和索引: " + hash + " -> " + index);

        // 模拟已有数据
        table[index] = new Node<>(hash, key, 100, null);

        // get操作
        Node<String, Integer> e = table[index];  // volatile读取

        System.out.println("2. 读取数组位置的头节点");

        if (e != null) {
            if (e.hash == hash && key.equals(e.key)) {
                System.out.println("3. 头节点匹配，直接返回: " + e.val);
            } else {
                System.out.println("3. 头节点不匹配，遍历链表/红黑树");
                // 遍历查找
                while (e != null) {
                    if (e.hash == hash && key.equals(e.key)) {
                        System.out.println("   找到匹配节点: " + e.val);
                        break;
                    }
                    e = e.next;  // volatile读取
                }
            }
        }

        System.out.println("4. get操作完成（全程无锁）");
    }

    // 演示CAS操作
    public void demonstrateCASOperation() {
        System.out.println("=== CAS操作演示 ===");

        // 模拟CAS更新数组元素
        System.out.println("CAS操作特点:");
        System.out.println("1. 原子性：要么成功要么失败，不会被中断");
        System.out.println("2. 无锁：不需要获取锁，避免线程阻塞");
        System.out.println("3. 高效：在无竞争情况下性能很高");
        System.out.println("4. ABA问题：需要注意ABA问题的处理");

        System.out.println("\nConcurrentHashMap中的CAS使用:");
        System.out.println("• 初始化数组");
        System.out.println("• 设置数组元素");
        System.out.println("• 更新size计数");
        System.out.println("• 扩容操作的协调");
    }
}
```

---

## 版本对比

### 4. JDK 1.7 vs 1.8 ConcurrentHashMap对比？⭐⭐⭐⭐⭐

#### 问题分析
这是ConcurrentHashMap的核心面试题，需要深入理解两个版本的设计差异。

#### 标准答案

**详细对比表：**

| 特性 | JDK 1.7 | JDK 1.8 |
|------|---------|---------|
| **数据结构** | Segment + HashEntry数组 + 链表 | Node数组 + 链表 + 红黑树 |
| **锁机制** | 分段锁(ReentrantLock) | CAS + synchronized |
| **锁粒度** | Segment级别 | 数组元素级别 |
| **并发度** | Segment数量(默认16) | 数组长度 |
| **内存占用** | 较高(Segment开销) | 较低 |
| **扩容机制** | 单个Segment扩容 | 整体协助式扩容 |
| **查询性能** | O(1) ~ O(n) | O(1) ~ O(log n) |
| **size计算** | 遍历所有Segment | baseCount + counterCells |

**性能对比图：**

```mermaid
graph TB
    subgraph "JDK 1.7 分段锁"
        A[16个Segment] --> A1[最多16个写线程并发]
        A --> A2[锁竞争在Segment内部]
        A --> A3[内存开销较大]

        A1 --> A4[并发度受限]
        A2 --> A5[热点Segment成为瓶颈]
        A3 --> A6[每个Segment都有锁对象]
    end

    subgraph "JDK 1.8 CAS+synchronized"
        B[数组长度个位置] --> B1[理论上数组长度个写线程并发]
        B --> B2[锁竞争在数组元素级别]
        B --> B3[内存开销较小]

        B1 --> B4[并发度更高]
        B2 --> B5[锁粒度更细]
        B3 --> B6[无额外锁对象开销]
    end

    style A4 fill:#fff3e0
    style A5 fill:#ffcdd2
    style B4 fill:#c8e6c9
    style B5 fill:#c8e6c9
```

#### 代码对比示例
```java
public class VersionComparison {

    // JDK 1.7 风格的操作
    public void jdk7StyleOperation() {
        System.out.println("=== JDK 1.7 操作特点 ===");

        // 1. 分段锁机制
        System.out.println("1. 分段锁机制:");
        System.out.println("   - 默认16个Segment");
        System.out.println("   - 每个Segment一个ReentrantLock");
        System.out.println("   - 最多16个线程同时写操作");

        // 2. size计算复杂
        System.out.println("\n2. size计算:");
        System.out.println("   - 需要遍历所有Segment");
        System.out.println("   - 可能需要多次尝试");
        System.out.println("   - 性能开销较大");

        // 3. 扩容机制
        System.out.println("\n3. 扩容机制:");
        System.out.println("   - 单个Segment独立扩容");
        System.out.println("   - 不影响其他Segment");
        System.out.println("   - 扩容时需要获取写锁");
    }

    // JDK 1.8 风格的操作
    public void jdk8StyleOperation() {
        System.out.println("=== JDK 1.8 操作特点 ===");

        // 1. CAS + synchronized
        System.out.println("1. 锁机制:");
        System.out.println("   - 空位置使用CAS");
        System.out.println("   - 有冲突使用synchronized");
        System.out.println("   - 锁粒度更细(数组元素级别)");

        // 2. size计算优化
        System.out.println("\n2. size计算:");
        System.out.println("   - baseCount + counterCells");
        System.out.println("   - 类似LongAdder的思想");
        System.out.println("   - 性能大幅提升");

        // 3. 协助式扩容
        System.out.println("\n3. 扩容机制:");
        System.out.println("   - 多线程协助扩容");
        System.out.println("   - 分工合作，提高效率");
        System.out.println("   - 扩容期间仍可正常访问");
    }

    // 性能对比测试
    public void performanceComparison() throws InterruptedException {
        int threadCount = 32;  // 超过JDK1.7的默认Segment数量
        int operationsPerThread = 10000;

        System.out.println("=== 性能对比测试 ===");
        System.out.println("线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);

        // 模拟JDK 1.7的性能特点
        System.out.println("\nJDK 1.7 特点:");
        System.out.println("- 32个线程竞争16个Segment");
        System.out.println("- 平均每个Segment有2个线程竞争");
        System.out.println("- 热点Segment可能成为瓶颈");

        // 模拟JDK 1.8的性能特点
        System.out.println("\nJDK 1.8 特点:");
        System.out.println("- 32个线程可以访问不同的数组位置");
        System.out.println("- 锁竞争更分散");
        System.out.println("- 整体并发性能更好");

        // 实际测试ConcurrentHashMap
        testConcurrentHashMapPerformance(threadCount, operationsPerThread);
    }

    private void testConcurrentHashMapPerformance(int threadCount, int operations) throws InterruptedException {
        ConcurrentHashMap<Integer, Integer> map = new ConcurrentHashMap<>();

        long start = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operations; j++) {
                    int key = threadId * operations + j;
                    map.put(key, j);
                    map.get(key);
                }
                latch.countDown();
            }).start();
        }

        latch.await();
        long time = System.currentTimeMillis() - start;

        System.out.println("\n实际测试结果:");
        System.out.println("总耗时: " + time + "ms");
        System.out.println("总操作数: " + (threadCount * operations * 2));
        System.out.println("QPS: " + (threadCount * operations * 2 * 1000L / time));
        System.out.println("最终size: " + map.size());
    }
}
```

---

## 性能分析

### 5. ConcurrentHashMap性能优化建议？⭐⭐⭐

#### 问题分析
了解ConcurrentHashMap的性能特点，在实际使用中进行优化。

#### 标准答案

**性能优化策略：**

```mermaid
graph TB
    subgraph "初始化优化"
        A[合理设置初始容量] --> A1[避免频繁扩容]
        A --> A2[预估数据量]
        A --> A3[考虑负载因子0.75]
    end

    subgraph "操作优化"
        B[使用批量操作] --> B1[putAll批量插入]
        B --> B2[compute系列方法]
        B --> B3[merge原子更新]
    end

    subgraph "并发优化"
        C[减少锁竞争] --> C1[避免热点key]
        C --> C2[分散访问模式]
        C --> C3[合理的线程数量]
    end

    subgraph "内存优化"
        D[及时清理] --> D1[remove不需要的元素]
        D --> D2[避免内存泄漏]
        D --> D3[监控内存使用]
    end

    style A1 fill:#c8e6c9
    style B2 fill:#e3f2fd
    style C1 fill:#fff3e0
```

#### 优化实践代码
```java
public class ConcurrentHashMapOptimization {

    // 1. 初始化优化
    public void optimizeInitialization() {
        System.out.println("=== 初始化优化 ===");

        // ❌ 不好的做法
        ConcurrentHashMap<String, Integer> badMap = new ConcurrentHashMap<>();

        // ✅ 好的做法：预估容量
        int expectedSize = 10000;
        int initialCapacity = (int) (expectedSize / 0.75) + 1;
        ConcurrentHashMap<String, Integer> goodMap = new ConcurrentHashMap<>(initialCapacity);

        // ✅ 更好的做法：同时设置并发级别
        ConcurrentHashMap<String, Integer> betterMap = new ConcurrentHashMap<>(
            initialCapacity,    // 初始容量
            0.75f,             // 负载因子
            Runtime.getRuntime().availableProcessors()  // 并发级别
        );

        System.out.println("初始化建议:");
        System.out.println("1. 预估数据量，设置合适的初始容量");
        System.out.println("2. 考虑并发级别，通常设置为CPU核心数");
        System.out.println("3. 负载因子一般使用默认值0.75");
    }

    // 2. 高效的批量操作
    public void efficientBatchOperations() {
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();

        System.out.println("=== 批量操作优化 ===");

        // ✅ 使用putAll批量插入
        Map<String, Integer> batchData = new HashMap<>();
        for (int i = 0; i < 1000; i++) {
            batchData.put("key" + i, i);
        }
        map.putAll(batchData);  // 比逐个put效率更高

        // ✅ 使用compute系列方法
        map.compute("counter", (k, v) -> v == null ? 1 : v + 1);
        map.computeIfAbsent("newKey", k -> k.length());
        map.computeIfPresent("existingKey", (k, v) -> v * 2);

        // ✅ 使用merge进行原子更新
        map.merge("sum", 10, Integer::sum);  // 原子地累加

        System.out.println("批量操作优势:");
        System.out.println("1. 减少锁的获取和释放次数");
        System.out.println("2. 提高缓存命中率");
        System.out.println("3. 原子性保证数据一致性");
    }

    // 3. 避免热点key问题
    public void avoidHotspotKeys() {
        System.out.println("=== 避免热点key ===");

        ConcurrentHashMap<String, AtomicLong> counters = new ConcurrentHashMap<>();

        // ❌ 不好的做法：所有线程竞争同一个key
        String hotKey = "globalCounter";

        // ✅ 好的做法：分散热点key
        int threadCount = Runtime.getRuntime().availableProcessors();
        for (int i = 0; i < threadCount; i++) {
            counters.put("counter_" + i, new AtomicLong(0));
        }

        // 使用时根据线程ID选择不同的counter
        // long threadId = Thread.currentThread().getId();
        // String key = "counter_" + (threadId % threadCount);
        // counters.get(key).incrementAndGet();

        System.out.println("热点key优化策略:");
        System.out.println("1. 将热点key分散为多个key");
        System.out.println("2. 使用线程本地变量");
        System.out.println("3. 采用分段计数的思想");
        System.out.println("4. 最终汇总时再合并结果");
    }

    // 4. 内存优化
    public void memoryOptimization() {
        System.out.println("=== 内存优化 ===");

        ConcurrentHashMap<String, CacheEntry> cache = new ConcurrentHashMap<>();

        // 定期清理过期数据
        ScheduledExecutorService cleaner = Executors.newScheduledThreadPool(1);
        cleaner.scheduleAtFixedRate(() -> {
            long now = System.currentTimeMillis();
            cache.entrySet().removeIf(entry ->
                now - entry.getValue().timestamp > 300000  // 5分钟过期
            );
        }, 60, 60, TimeUnit.SECONDS);  // 每分钟清理一次

        // 使用弱引用避免内存泄漏
        Map<String, WeakReference<Object>> weakCache = new ConcurrentHashMap<>();

        System.out.println("内存优化建议:");
        System.out.println("1. 定期清理过期数据");
        System.out.println("2. 设置合理的缓存大小限制");
        System.out.println("3. 考虑使用弱引用");
        System.out.println("4. 监控内存使用情况");
    }

    static class CacheEntry {
        Object value;
        long timestamp;

        CacheEntry(Object value) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
        }
    }

    // 5. 性能监控
    public void performanceMonitoring() {
        System.out.println("=== 性能监控 ===");

        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();

        // 监控map的基本指标
        System.out.println("监控指标:");
        System.out.println("1. size(): " + map.size());
        System.out.println("2. isEmpty(): " + map.isEmpty());

        // JDK 8+ 可以使用mappingCount()获取更准确的元素数量
        System.out.println("3. mappingCount(): " + map.mappingCount());

        // 监控内存使用
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("4. 内存使用: " + (usedMemory / 1024 / 1024) + "MB");

        System.out.println("\n监控建议:");
        System.out.println("1. 定期检查map的大小");
        System.out.println("2. 监控内存使用情况");
        System.out.println("3. 关注GC频率和耗时");
        System.out.println("4. 使用JVM监控工具");
    }

    // 6. 最佳实践总结
    public void bestPractices() {
        System.out.println("=== ConcurrentHashMap最佳实践 ===");

        System.out.println("✅ 推荐做法:");
        System.out.println("1. 预估容量，避免频繁扩容");
        System.out.println("2. 使用compute系列方法进行原子操作");
        System.out.println("3. 分散热点key，避免锁竞争");
        System.out.println("4. 及时清理不需要的数据");
        System.out.println("5. 合理设置线程池大小");

        System.out.println("\n❌ 避免做法:");
        System.out.println("1. 不要在高并发下使用HashMap");
        System.out.println("2. 不要忽略初始容量设置");
        System.out.println("3. 不要创建过多的热点key");
        System.out.println("4. 不要忘记清理过期数据");
        System.out.println("5. 不要在synchronized块中使用");
    }
}
```
