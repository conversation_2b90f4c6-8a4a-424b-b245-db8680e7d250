# Java版本特性面试题详解

## 1. Java 8 有哪些重要特性？⭐⭐⭐⭐⭐

### 问题分析
Java 8是Java历史上最重要的版本之一，引入了函数式编程特性，是面试的重点。

### 标准答案

**Java 8 核心特性：Lambda表达式、Stream API、函数式接口、方法引用、Optional、新时间API**

#### 1. Lambda表达式
```java
// 传统写法
List<String> names = Arrays.asList("<PERSON>", "<PERSON>", "<PERSON>");
Collections.sort(names, new Comparator<String>() {
    @Override
    public int compare(String a, String b) {
        return a.compareTo(b);
    }
});

// Lambda表达式
Collections.sort(names, (a, b) -> a.compareTo(b));
// 或者更简洁
Collections.sort(names, String::compareTo);
```

#### 2. Stream API
```java
List<String> names = Arrays.asList("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>");

// 过滤、转换、收集
List<String> result = names.stream()
    .filter(name -> name.length() > 3)     // 过滤长度大于3的
    .map(String::toUpperCase)              // 转换为大写
    .sorted()                              // 排序
    .collect(Collectors.toList());         // 收集为List

// 并行流
long count = names.parallelStream()
    .filter(name -> name.startsWith("A"))
    .count();
```

#### 3. 函数式接口
```java
@FunctionalInterface
public interface Calculator {
    int calculate(int a, int b);
}

// 使用Lambda实现
Calculator add = (a, b) -> a + b;
Calculator multiply = (a, b) -> a * b;

// 内置函数式接口
Predicate<String> isEmpty = String::isEmpty;
Function<String, Integer> length = String::length;
Consumer<String> printer = System.out::println;
Supplier<String> supplier = () -> "Hello World";
```

#### 4. 方法引用
```java
// 静态方法引用
Function<String, Integer> parseInt = Integer::parseInt;

// 实例方法引用
String str = "Hello";
Supplier<String> upperCase = str::toUpperCase;

// 构造方法引用
Supplier<List<String>> listSupplier = ArrayList::new;
Function<String, StringBuilder> sbCreator = StringBuilder::new;
```

#### 5. Optional类
```java
// 避免空指针异常
Optional<String> optional = Optional.ofNullable(getString());

// 传统写法
if (optional.isPresent()) {
    System.out.println(optional.get());
}

// 函数式写法
optional.ifPresent(System.out::println);

// 链式调用
String result = optional
    .filter(s -> s.length() > 5)
    .map(String::toUpperCase)
    .orElse("DEFAULT");
```

#### 6. 新时间API (java.time)
```java
// LocalDate、LocalTime、LocalDateTime
LocalDate date = LocalDate.now();
LocalTime time = LocalTime.now();
LocalDateTime dateTime = LocalDateTime.now();

// 格式化和解析
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
String formatted = dateTime.format(formatter);
LocalDateTime parsed = LocalDateTime.parse("2024-01-01 12:00:00", formatter);

// 时间计算
LocalDate tomorrow = date.plusDays(1);
LocalDate lastWeek = date.minusWeeks(1);
```

#### 7. 接口默认方法和静态方法
```java
public interface Vehicle {
    // 抽象方法
    void start();
    
    // 默认方法
    default void stop() {
        System.out.println("Vehicle stopped");
    }
    
    // 静态方法
    static void checkEngine() {
        System.out.println("Engine checked");
    }
}
```

## 2. Java 9 有哪些新特性？⭐⭐⭐⭐

### 问题分析
Java 9引入了模块系统，这是Java平台的重大变革。

### 标准答案

**Java 9 核心特性：模块系统(Jigsaw)、JShell、集合工厂方法、Stream API增强**

#### 1. 模块系统 (Project Jigsaw)
```java
// module-info.java
module com.example.myapp {
    requires java.base;          // 依赖java.base模块
    requires java.logging;       // 依赖日志模块
    
    exports com.example.api;     // 导出包
    
    provides com.example.spi.Service 
        with com.example.impl.ServiceImpl;  // 提供服务实现
}
```

#### 2. JShell (交互式编程环境)
```bash
# 启动JShell
$ jshell

# 直接执行Java代码
jshell> int x = 10
x ==> 10

jshell> System.out.println("Hello " + x)
Hello 10

jshell> List<String> list = List.of("a", "b", "c")
list ==> [a, b, c]
```

#### 3. 集合工厂方法
```java
// 创建不可变集合
List<String> list = List.of("a", "b", "c");
Set<String> set = Set.of("x", "y", "z");
Map<String, Integer> map = Map.of(
    "one", 1,
    "two", 2,
    "three", 3
);

// 注意：这些集合是不可变的
// list.add("d");  // 抛出UnsupportedOperationException
```

#### 4. Stream API增强
```java
// takeWhile - 取满足条件的元素直到遇到不满足的
Stream.of(1, 2, 3, 4, 5, 6)
    .takeWhile(n -> n < 4)
    .forEach(System.out::println);  // 输出: 1, 2, 3

// dropWhile - 跳过满足条件的元素直到遇到不满足的
Stream.of(1, 2, 3, 4, 5, 6)
    .dropWhile(n -> n < 4)
    .forEach(System.out::println);  // 输出: 4, 5, 6

// ofNullable - 创建可能为null的Stream
Stream<String> stream = Stream.ofNullable(getString());
```

#### 5. Optional增强
```java
Optional<String> optional = Optional.of("Hello");

// ifPresentOrElse
optional.ifPresentOrElse(
    System.out::println,           // 存在时执行
    () -> System.out.println("Empty")  // 不存在时执行
);

// or - 提供备选Optional
Optional<String> result = optional.or(() -> Optional.of("Default"));

// stream - 转换为Stream
Stream<String> stream = optional.stream();
```

## 3. Java 10-11 有哪些重要特性？⭐⭐⭐⭐

### 问题分析
Java 10-11是LTS版本的重要节点，引入了局部变量类型推断等特性。

### 标准答案

#### Java 10 特性

**1. 局部变量类型推断 (var关键字)**
```java
// 传统写法
List<String> list = new ArrayList<String>();
Map<String, Integer> map = new HashMap<String, Integer>();

// 使用var
var list = new ArrayList<String>();  // 推断为ArrayList<String>
var map = new HashMap<String, Integer>();
var str = "Hello World";             // 推断为String
var number = 42;                     // 推断为int

// 注意：var只能用于局部变量
public class Example {
    // private var field;  // 编译错误
    
    public void method() {
        var local = "OK";   // 正确
    }
}
```

**2. 应用程序类数据共享 (AppCDS)**
```bash
# 创建类列表
java -Xshare:off -XX:+UseAppCDS -XX:DumpLoadedClassList=classes.lst MyApp

# 创建共享归档
java -Xshare:dump -XX:+UseAppCDS -XX:SharedClassListFile=classes.lst \
     -XX:SharedArchiveFile=app.jsa -cp myapp.jar

# 使用共享归档启动
java -Xshare:on -XX:+UseAppCDS -XX:SharedArchiveFile=app.jsa \
     -cp myapp.jar MyApp
```

#### Java 11 特性 (LTS版本)

**1. HTTP Client API**
```java
// 同步请求
HttpClient client = HttpClient.newHttpClient();
HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.example.com/data"))
    .header("Content-Type", "application/json")
    .GET()
    .build();

HttpResponse<String> response = client.send(request, 
    HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());

// 异步请求
CompletableFuture<HttpResponse<String>> future = client.sendAsync(request,
    HttpResponse.BodyHandlers.ofString());
future.thenAccept(resp -> System.out.println(resp.body()));
```

**2. String新方法**
```java
String str = "  Hello World  ";

// isBlank - 检查是否为空白
System.out.println("".isBlank());        // true
System.out.println("  ".isBlank());      // true

// strip - 去除首尾空白(支持Unicode)
System.out.println(str.strip());         // "Hello World"
System.out.println(str.stripLeading());  // "Hello World  "
System.out.println(str.stripTrailing()); // "  Hello World"

// repeat - 重复字符串
System.out.println("Java".repeat(3));    // "JavaJavaJava"

// lines - 按行分割
"Line1\nLine2\nLine3".lines()
    .forEach(System.out::println);
```

**3. 文件操作增强**
```java
// 读取文件内容
String content = Files.readString(Paths.get("file.txt"));

// 写入文件内容
Files.writeString(Paths.get("output.txt"), "Hello World");

// 使用指定编码
String content = Files.readString(Paths.get("file.txt"), StandardCharsets.UTF_8);
```

## 4. Java 12-15 有哪些新特性？⭐⭐⭐

### 问题分析
这些版本引入了Switch表达式、文本块等语法糖特性。

### 标准答案

#### Java 12 特性

**1. Switch表达式 (预览)**
```java
// 传统switch语句
String result;
switch (day) {
    case MONDAY:
    case FRIDAY:
    case SUNDAY:
        result = "6";
        break;
    case TUESDAY:
        result = "7";
        break;
    default:
        result = "Unknown";
}

// 新的switch表达式
String result = switch (day) {
    case MONDAY, FRIDAY, SUNDAY -> "6";
    case TUESDAY -> "7";
    case THURSDAY, SATURDAY -> "8";
    default -> "Unknown";
};
```

#### Java 13 特性

**1. 文本块 (预览)**
```java
// 传统字符串拼接
String html = "<html>\n" +
              "    <body>\n" +
              "        <p>Hello World</p>\n" +
              "    </body>\n" +
              "</html>";

// 文本块
String html = """
              <html>
                  <body>
                      <p>Hello World</p>
                  </body>
              </html>
              """;

// JSON示例
String json = """
              {
                  "name": "John",
                  "age": 30,
                  "city": "New York"
              }
              """;
```

#### Java 14 特性

**1. instanceof模式匹配 (预览)**
```java
// 传统写法
if (obj instanceof String) {
    String str = (String) obj;
    System.out.println(str.length());
}

// 模式匹配
if (obj instanceof String str) {
    System.out.println(str.length());  // 直接使用str
}

// 复杂示例
public String formatValue(Object obj) {
    return switch (obj) {
        case Integer i -> String.format("int %d", i);
        case Long l -> String.format("long %d", l);
        case Double d -> String.format("double %f", d);
        case String s -> String.format("String %s", s);
        default -> obj.toString();
    };
}
```

**2. Records (预览)**
```java
// 传统数据类
public class Person {
    private final String name;
    private final int age;
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String name() { return name; }
    public int age() { return age; }
    
    // equals, hashCode, toString...
}

// Record类
public record Person(String name, int age) {
    // 自动生成构造器、访问器、equals、hashCode、toString
    
    // 可以添加自定义方法
    public boolean isAdult() {
        return age >= 18;
    }
}

// 使用
Person person = new Person("Alice", 25);
System.out.println(person.name());     // Alice
System.out.println(person.age());      // 25
System.out.println(person.isAdult());  // true
```

#### Java 15 特性

**1. 密封类 (预览)**
```java
// 密封类 - 限制哪些类可以继承
public sealed class Shape 
    permits Circle, Rectangle, Triangle {
}

public final class Circle extends Shape {
    private final double radius;
    
    public Circle(double radius) {
        this.radius = radius;
    }
}

public final class Rectangle extends Shape {
    private final double width, height;
    
    public Rectangle(double width, double height) {
        this.width = width;
        this.height = height;
    }
}

public non-sealed class Triangle extends Shape {
    // non-sealed允许进一步继承
}

// 使用模式匹配
public double calculateArea(Shape shape) {
    return switch (shape) {
        case Circle c -> Math.PI * c.radius() * c.radius();
        case Rectangle r -> r.width() * r.height();
        case Triangle t -> calculateTriangleArea(t);
    };
}
```

## 5. Java 16-17 有哪些重要特性？⭐⭐⭐⭐

### 问题分析
Java 17是新的LTS版本，正式化了许多预览特性。

### 标准答案

#### Java 16 特性

**1. Records正式版**
```java
// Record支持泛型
public record Pair<T, U>(T first, U second) {
    // 紧凑构造器
    public Pair {
        Objects.requireNonNull(first);
        Objects.requireNonNull(second);
    }

    // 静态工厂方法
    public static <T, U> Pair<T, U> of(T first, U second) {
        return new Pair<>(first, second);
    }
}

// 嵌套Record
public record Person(String name, Address address) {
    public record Address(String street, String city) {}
}
```

**2. instanceof模式匹配正式版**
```java
// 复杂的模式匹配
public String processValue(Object obj) {
    if (obj instanceof String s && s.length() > 5) {
        return s.toUpperCase();
    } else if (obj instanceof Integer i && i > 0) {
        return "Positive: " + i;
    } else if (obj instanceof List<?> list && !list.isEmpty()) {
        return "List size: " + list.size();
    }
    return "Unknown";
}
```

**3. Vector API (孵化)**
```java
// 向量化计算，提高数学运算性能
import jdk.incubator.vector.*;

public class VectorExample {
    static final VectorSpecies<Float> SPECIES = FloatVector.SPECIES_256;

    public void vectorAdd(float[] a, float[] b, float[] c) {
        int i = 0;
        int upperBound = SPECIES.loopBound(a.length);

        for (; i < upperBound; i += SPECIES.length()) {
            var va = FloatVector.fromArray(SPECIES, a, i);
            var vb = FloatVector.fromArray(SPECIES, b, i);
            var vc = va.add(vb);
            vc.intoArray(c, i);
        }

        // 处理剩余元素
        for (; i < a.length; i++) {
            c[i] = a[i] + b[i];
        }
    }
}
```

#### Java 17 特性 (LTS版本)

**1. 密封类正式版**
```java
// 密封接口
public sealed interface JsonValue
    permits JsonObject, JsonArray, JsonString, JsonNumber, JsonBoolean, JsonNull {
}

// 实现类
public final class JsonString implements JsonValue {
    private final String value;

    public JsonString(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}

// 模式匹配与密封类结合
public String formatJson(JsonValue value) {
    return switch (value) {
        case JsonString s -> "\"" + s.getValue() + "\"";
        case JsonNumber n -> n.getValue().toString();
        case JsonBoolean b -> Boolean.toString(b.getValue());
        case JsonNull n -> "null";
        case JsonObject o -> formatObject(o);
        case JsonArray a -> formatArray(a);
    };
}
```

**2. 强封装JDK内部API**
```java
// Java 17之前可以访问
// sun.misc.Unsafe unsafe = sun.misc.Unsafe.getUnsafe();

// Java 17开始强制封装，需要使用公共API
// 使用VarHandle替代Unsafe
import java.lang.invoke.MethodHandles;
import java.lang.invoke.VarHandle;

public class SafeFieldAccess {
    private volatile int value;
    private static final VarHandle VALUE_HANDLE;

    static {
        try {
            VALUE_HANDLE = MethodHandles.lookup()
                .findVarHandle(SafeFieldAccess.class, "value", int.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void setValue(int newValue) {
        VALUE_HANDLE.setVolatile(this, newValue);
    }

    public int getValue() {
        return (int) VALUE_HANDLE.getVolatile(this);
    }
}
```

## 6. Java 18-21 有哪些新特性？⭐⭐⭐

### 问题分析
这些版本引入了虚拟线程等重要特性，是现代Java开发的重点。

### 标准答案

#### Java 18 特性

**1. 简单Web服务器**
```bash
# 启动简单的文件服务器
jwebserver -p 8080 -d /path/to/directory

# 或者在代码中使用
HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0);
server.createContext("/", new SimpleFileServer.createFileHandler(Path.of(".")));
server.start();
```

**2. UTF-8默认字符集**
```java
// Java 18之前需要显式指定编码
FileReader reader = new FileReader("file.txt", StandardCharsets.UTF_8);

// Java 18开始UTF-8是默认编码
FileReader reader = new FileReader("file.txt");  // 默认UTF-8
```

#### Java 19 特性

**1. 虚拟线程 (预览)**
```java
// 传统线程
Thread thread = new Thread(() -> {
    System.out.println("Hello from thread");
});
thread.start();

// 虚拟线程
Thread virtualThread = Thread.ofVirtual().start(() -> {
    System.out.println("Hello from virtual thread");
});

// 使用ExecutorService创建虚拟线程
try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
    for (int i = 0; i < 1000000; i++) {
        executor.submit(() -> {
            // 可以创建百万级虚拟线程
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
}
```

**2. 结构化并发 (孵化)**
```java
import jdk.incubator.concurrent.StructuredTaskScope;

public class StructuredConcurrencyExample {
    public String fetchUserData(String userId) throws Exception {
        try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
            // 并行执行多个任务
            var userTask = scope.fork(() -> fetchUser(userId));
            var preferencesTask = scope.fork(() -> fetchPreferences(userId));
            var historyTask = scope.fork(() -> fetchHistory(userId));

            // 等待所有任务完成
            scope.join();
            scope.throwIfFailed();

            // 组合结果
            return combineResults(
                userTask.resultNow(),
                preferencesTask.resultNow(),
                historyTask.resultNow()
            );
        }
    }
}
```

#### Java 20 特性

**1. 作用域值 (孵化)**
```java
// 替代ThreadLocal的新机制
public class ScopedValueExample {
    private static final ScopedValue<String> USER_ID = ScopedValue.newInstance();

    public void processRequest(String userId) {
        ScopedValue.where(USER_ID, userId)
            .run(() -> {
                // 在这个作用域内，所有代码都可以访问USER_ID
                doSomething();
                callAnotherMethod();
            });
    }

    private void doSomething() {
        String userId = USER_ID.get();  // 获取作用域值
        System.out.println("Processing for user: " + userId);
    }
}
```

#### Java 21 特性 (LTS版本)

**1. 虚拟线程正式版**
```java
// 虚拟线程的最佳实践
public class VirtualThreadExample {
    public static void main(String[] args) {
        // 处理大量并发请求
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<Future<String>> futures = new ArrayList<>();

            for (int i = 0; i < 100000; i++) {
                final int taskId = i;
                Future<String> future = executor.submit(() -> {
                    // 模拟I/O密集型任务
                    try {
                        Thread.sleep(1000);
                        return "Task " + taskId + " completed";
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        return "Task " + taskId + " interrupted";
                    }
                });
                futures.add(future);
            }

            // 收集结果
            for (Future<String> future : futures) {
                try {
                    System.out.println(future.get());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
```

**2. 字符串模板 (预览)**
```java
// 字符串插值
String name = "World";
int count = 42;

// 传统方式
String message1 = String.format("Hello %s, count is %d", name, count);

// 字符串模板 (预览)
String message2 = STR."Hello \{name}, count is \{count}";

// 支持表达式
String message3 = STR."Result: \{count * 2 + 1}";

// 多行字符串模板
String html = STR."""
    <html>
        <body>
            <h1>Hello \{name}</h1>
            <p>Count: \{count}</p>
        </body>
    </html>
    """;
```

**3. 序列集合**
```java
// 新的集合接口，提供统一的首尾访问方法
public interface SequencedCollection<E> extends Collection<E> {
    SequencedCollection<E> reversed();
    void addFirst(E e);
    void addLast(E e);
    E getFirst();
    E getLast();
    E removeFirst();
    E removeLast();
}

// 使用示例
List<String> list = new ArrayList<>();
list.addFirst("first");   // 在开头添加
list.addLast("last");     // 在末尾添加
String first = list.getFirst();  // 获取第一个元素
String last = list.getLast();    // 获取最后一个元素

// 反转视图
List<String> reversed = list.reversed();
```

## 7. Java 22-24 有哪些最新特性？⭐⭐⭐

### 问题分析
这些是最新版本的特性，展示Java语言的发展方向。

### 标准答案

#### Java 22 特性

**1. 未命名变量和模式**
```java
// 未命名变量 (用_表示不使用的变量)
for (int i = 0, _ = sideEffect(); i < 10; i++) {
    // 不使用第二个变量
}

// 在模式匹配中使用
switch (obj) {
    case Point(var x, var _) -> processX(x);  // 只关心x坐标
    case Rectangle(var _, var height) -> processHeight(height);  // 只关心高度
}

// 在异常处理中
try {
    riskyOperation();
} catch (Exception _) {
    // 不关心异常的具体信息
    handleError();
}
```

**2. 外部函数和内存API (预览)**
```java
// 调用C库函数
import java.lang.foreign.*;

public class ForeignFunctionExample {
    public static void main(String[] args) throws Throwable {
        // 查找C库函数
        Linker linker = Linker.nativeLinker();
        SymbolLookup stdlib = linker.defaultLookup();

        MethodHandle strlen = linker.downcallHandle(
            stdlib.find("strlen").orElseThrow(),
            FunctionDescriptor.of(ValueLayout.JAVA_LONG, ValueLayout.ADDRESS)
        );

        // 分配内存并调用函数
        try (Arena arena = Arena.ofConfined()) {
            MemorySegment cString = arena.allocateUtf8String("Hello World");
            long length = (long) strlen.invoke(cString);
            System.out.println("Length: " + length);
        }
    }
}
```

#### Java 23 特性

**1. 字符串模板正式版**
```java
// 字符串模板的高级用法
public class StringTemplateExample {
    // 自定义模板处理器
    public static final StringTemplate.Processor<String, RuntimeException> UPPER =
        StringTemplate.Processor.of((StringTemplate st) -> {
            StringBuilder sb = new StringBuilder();
            Iterator<String> fragments = st.fragments().iterator();
            Iterator<Object> values = st.values().iterator();

            sb.append(fragments.next());
            while (values.hasNext()) {
                sb.append(values.next().toString().toUpperCase());
                sb.append(fragments.next());
            }
            return sb.toString();
        });

    public static void main(String[] args) {
        String name = "world";
        String result = UPPER."Hello \{name}!";  // "Hello WORLD!"
        System.out.println(result);
    }
}
```

**2. 模式匹配增强**
```java
// 更强大的模式匹配
public class PatternMatchingExample {
    public String processData(Object data) {
        return switch (data) {
            // 数组模式
            case int[] {1, 2, var rest} -> "Starts with 1,2: " + Arrays.toString(rest);

            // 嵌套模式
            case Person(var name, Address(var street, "Beijing")) ->
                name + " lives on " + street + " in Beijing";

            // 守卫条件
            case String s when s.length() > 10 -> "Long string: " + s;
            case String s when s.startsWith("Hello") -> "Greeting: " + s;

            // 类型模式与条件
            case Integer i when i > 0 -> "Positive: " + i;
            case Integer i when i < 0 -> "Negative: " + i;
            case Integer i -> "Zero";

            default -> "Unknown type";
        };
    }
}
```

#### Java 24 特性 (预期)

**1. 值类型 (Project Valhalla - 预期)**
```java
// 值类型 - 无身份的数据类型
public value class Point {
    private final int x;
    private final int y;

    public Point(int x, int y) {
        this.x = x;
        this.y = y;
    }

    public int x() { return x; }
    public int y() { return y; }
}

// 值类型的优势：
// 1. 内存效率更高（无对象头）
// 2. 缓存友好（连续存储）
// 3. 避免空指针异常
Point[] points = new Point[1000];  // 直接存储值，不是引用
```

**2. 纤程增强 (Project Loom)**
```java
// 更好的异步编程模型
public class FiberExample {
    public CompletableFuture<String> processAsync() {
        return CompletableFuture.supplyAsync(() -> {
            // 在虚拟线程中执行
            try {
                // 模拟异步I/O
                Thread.sleep(1000);
                return "Processed";
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return "Interrupted";
            }
        }, Executors.newVirtualThreadPerTaskExecutor());
    }
}
```

## 8. 各版本特性在实际开发中的应用？⭐⭐⭐⭐⭐

### 问题分析
这是综合性问题，考查对各版本特性的实际应用理解。

### 标准答案

#### 版本选择策略

**1. LTS版本选择建议**
```java
// 企业级应用推荐的LTS版本
// Java 8  (2014-2030) - 最广泛使用，稳定性最高
// Java 11 (2018-2026) - 现代化特性，性能提升
// Java 17 (2021-2029) - 最新LTS，推荐新项目使用
// Java 21 (2023-2031) - 虚拟线程，适合高并发场景

// 版本迁移路径
Java 8 → Java 11 → Java 17 → Java 21
```

**2. 特性应用场景对比**

| 特性 | 版本 | 应用场景 | 性能影响 |
|------|------|----------|----------|
| **Lambda + Stream** | Java 8 | 数据处理、集合操作 | 提升开发效率 |
| **模块系统** | Java 9+ | 大型项目、微服务 | 减少内存占用 |
| **var关键字** | Java 10+ | 简化代码、提高可读性 | 无性能影响 |
| **HTTP Client** | Java 11+ | 替代第三方HTTP库 | 减少依赖 |
| **Records** | Java 14+ | 数据传输对象 | 减少样板代码 |
| **Switch表达式** | Java 14+ | 复杂条件判断 | 提高代码质量 |
| **文本块** | Java 15+ | JSON、SQL、HTML | 提高可读性 |
| **虚拟线程** | Java 21+ | 高并发I/O密集型 | 显著提升并发性能 |

#### 实际项目应用示例

**1. 微服务项目 (Java 17)**
```java
// 使用Records作为DTO
public record UserDTO(
    @JsonProperty("user_id") String userId,
    @JsonProperty("user_name") String userName,
    @JsonProperty("email") String email,
    @JsonProperty("created_at") LocalDateTime createdAt
) {
    // 验证方法
    public UserDTO {
        Objects.requireNonNull(userId, "User ID cannot be null");
        Objects.requireNonNull(userName, "User name cannot be null");
        if (email != null && !email.contains("@")) {
            throw new IllegalArgumentException("Invalid email format");
        }
    }

    // 业务方法
    public boolean isRecentUser() {
        return createdAt.isAfter(LocalDateTime.now().minusDays(30));
    }
}

// 使用密封类定义API响应
public sealed interface ApiResponse<T>
    permits SuccessResponse, ErrorResponse {
}

public record SuccessResponse<T>(T data, String message)
    implements ApiResponse<T> {}

public record ErrorResponse<T>(String error, int code)
    implements ApiResponse<T> {}

// 使用模式匹配处理响应
public String handleResponse(ApiResponse<UserDTO> response) {
    return switch (response) {
        case SuccessResponse<UserDTO>(var user, var message) ->
            "Success: " + user.userName() + " - " + message;
        case ErrorResponse<UserDTO>(var error, var code) ->
            "Error " + code + ": " + error;
    };
}
```

**2. 高并发Web应用 (Java 21)**
```java
@RestController
public class UserController {

    private final UserService userService;

    // 使用虚拟线程处理大量并发请求
    @GetMapping("/users/{id}")
    public CompletableFuture<ResponseEntity<UserDTO>> getUser(@PathVariable String id) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 模拟数据库查询
                UserDTO user = userService.findById(id);
                return ResponseEntity.ok(user);
            } catch (UserNotFoundException e) {
                return ResponseEntity.notFound().build();
            }
        }, Executors.newVirtualThreadPerTaskExecutor());
    }

    // 批量处理用户数据
    @PostMapping("/users/batch")
    public CompletableFuture<List<UserDTO>> processBatchUsers(
            @RequestBody List<String> userIds) {

        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<UserDTO>> futures = userIds.stream()
                .map(id -> CompletableFuture.supplyAsync(
                    () -> userService.findById(id), executor))
                .toList();

            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                    .map(CompletableFuture::join)
                    .toList());
        }
    }
}
```

**3. 数据处理应用 (Java 17+)**
```java
public class DataProcessor {

    // 使用Stream API和Records处理数据
    public List<ProcessedData> processUserData(List<RawUserData> rawData) {
        return rawData.parallelStream()
            .filter(data -> data.isValid())
            .map(this::enrichData)
            .map(this::transform)
            .sorted(Comparator.comparing(ProcessedData::priority).reversed())
            .toList();
    }

    // 使用文本块构建SQL
    private static final String COMPLEX_QUERY = """
        SELECT u.user_id, u.user_name, u.email,
               p.preference_id, p.preference_value,
               h.last_login, h.login_count
        FROM users u
        LEFT JOIN user_preferences p ON u.user_id = p.user_id
        LEFT JOIN user_history h ON u.user_id = h.user_id
        WHERE u.status = 'ACTIVE'
          AND u.created_at >= ?
          AND (p.preference_type = 'NOTIFICATION' OR p.preference_type IS NULL)
        ORDER BY h.last_login DESC
        LIMIT ?
        """;

    // 使用Switch表达式处理不同数据类型
    public String formatValue(Object value) {
        return switch (value) {
            case null -> "N/A";
            case String s when s.isEmpty() -> "Empty";
            case String s -> "String: " + s;
            case Integer i when i > 0 -> "Positive: " + i;
            case Integer i when i < 0 -> "Negative: " + i;
            case Integer i -> "Zero";
            case Double d -> String.format("%.2f", d);
            case LocalDateTime dt -> dt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            case List<?> list -> "List[" + list.size() + "]";
            default -> value.getClass().getSimpleName() + ": " + value;
        };
    }
}
```

#### 性能优化建议

**1. Java 8 → Java 17 迁移收益**
```java
// 性能提升点
public class PerformanceComparison {

    // 1. 字符串拼接优化 (Java 9+)
    public String buildString(List<String> items) {
        // Java 8: StringBuilder手动优化
        // Java 9+: 编译器自动优化字符串拼接
        return items.stream()
            .map(item -> "Item: " + item)  // 自动优化
            .collect(Collectors.joining(", "));
    }

    // 2. 集合工厂方法 (Java 9+)
    public Set<String> createSet() {
        // Java 8: 需要多步创建
        Set<String> set = new HashSet<>();
        set.add("a");
        set.add("b");
        set.add("c");
        return Collections.unmodifiableSet(set);

        // Java 9+: 一步创建不可变集合
        return Set.of("a", "b", "c");  // 更高效的内存布局
    }

    // 3. 局部变量类型推断 (Java 10+)
    public void processData() {
        // 提高代码可读性，无性能影响
        var users = fetchUsers();
        var filtered = users.stream()
            .filter(user -> user.isActive())
            .toList();
    }
}
```

**2. 虚拟线程最佳实践 (Java 21+)**
```java
public class VirtualThreadBestPractices {

    // ✅ 适合虚拟线程的场景：I/O密集型
    public void ioIntensiveTask() {
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<String>> futures = IntStream.range(0, 10000)
                .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 网络请求、数据库查询等I/O操作
                        return httpClient.send(request, BodyHandlers.ofString()).body();
                    } catch (Exception e) {
                        return "Error: " + e.getMessage();
                    }
                }, executor))
                .toList();

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    // ❌ 不适合虚拟线程的场景：CPU密集型
    public void cpuIntensiveTask() {
        // CPU密集型任务应该使用平台线程
        try (var executor = Executors.newWorkStealingPool()) {
            List<CompletableFuture<Long>> futures = IntStream.range(0, 1000)
                .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                    // CPU密集型计算
                    return fibonacci(40);
                }, executor))
                .toList();
        }
    }

    private long fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }
}
```

## 总结

### 🎯 Java版本特性面试要点

#### 必须掌握的核心特性 (⭐⭐⭐⭐⭐)

1. **Java 8**：Lambda表达式、Stream API、Optional、新时间API
2. **Java 11**：HTTP Client、String新方法、var关键字
3. **Java 17**：Records、密封类、Switch表达式、文本块
4. **Java 21**：虚拟线程、字符串模板、序列集合

#### 版本选择建议

| 场景 | 推荐版本 | 理由 |
|------|----------|------|
| **传统企业应用** | Java 8/11 | 稳定性高，生态成熟 |
| **新项目开发** | Java 17/21 | 现代化特性，长期支持 |
| **高并发应用** | Java 21+ | 虚拟线程，性能提升 |
| **微服务架构** | Java 17+ | 模块系统，Records |

#### 面试加分点

1. **能说出具体版本的关键特性**：不只是知道有什么，还要知道怎么用
2. **理解特性的适用场景**：什么时候用Lambda，什么时候用虚拟线程
3. **掌握迁移策略**：从老版本升级的注意事项和收益
4. **性能影响认知**：哪些特性影响性能，哪些只是语法糖

#### 实际应用经验

- **Java 8**：函数式编程思维，Stream API的性能特点
- **Java 11+**：HTTP Client替代第三方库的考虑
- **Java 17+**：Records在DTO中的应用，密封类的设计模式
- **Java 21+**：虚拟线程的适用场景和性能测试经验

通过上面的流程图，你可以更好地理解：
- Java版本的演进时间线和重点特性
- Lambda表达式的语法结构和函数式接口
- Stream API的操作流程和性能特点
- 虚拟线程与传统线程的架构差异

掌握这些Java版本特性不仅能在面试中展现技术深度，更重要的是能在实际项目中选择合适的特性提升开发效率和系统性能！
```
