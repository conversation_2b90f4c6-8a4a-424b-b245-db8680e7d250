## rabbitmq的延时队列功能
插件延时队列, 当量大时会导致内存暴增, 上百万数据延时, 频繁创建channel, 达到maxchannel, 抛出异常, 导致消息发不了
 插件延时队列的原理:
 	消息先发到延时交换机, 每条延时消息都有一个定时器? 存于内存中,   
不太使用大量消息
后改成死信消息, 根据延时的时间段, 如按小时分队列, 延时时间差不多的消息放同一个队列
缺点时, 消息是先进先出, 可能后面已到期的消息要等待先进但未到期的消息到期才能被消费,
消息先发送到一个私信队列, 消息到期会转发到正常队列, 消费正常队列即可, 这样的消息会持久化的硬盘, 内存占用极少, 消息有上千万也没出现问题.

# 双机房保证接口正常, 当一侧机房down掉, 要再次调用异地机房,保证接口畅通, 熔断机制
接口参数有地市编码
根据地市编码提前平均分流

# 责任链

# 策略模式

# 模版方法模式

# 分库分表

# 接口响应时长突增大于200ms
查看监控发现io等待时长特别长,  日志打印导致io增加,   日志是同步写的, 后暂时关闭了日志

后面发现是硬盘格式不是ext
