# AQS面试题详解

## 1. AQS 是什么？AQS 的原理是什么？⭐⭐⭐⭐⭐

### 问题分析
这是Java并发编程的核心知识点，考查对AQS框架的深度理解。

### 标准答案

**AQS（AbstractQueuedSynchronizer）：抽象队列同步器**

#### 什么是AQS
AQS是Java并发包中的核心基础组件，位于`java.util.concurrent.locks`包下，为构建锁和同步器提供了一个框架。

**核心特点：**
- 抽象类，不能直接实例化
- 基于FIFO队列的阻塞锁和同步器框架
- 大多数同步器都是基于AQS实现的

#### AQS的核心原理

**1. 核心思想**
```java
// AQS的核心思想
if (资源空闲) {
    设置当前线程为工作线程;
    设置资源状态为锁定;
} else {
    将线程加入等待队列;
    阻塞当前线程;
}
```

**2. 核心组件**
- **state变量**：表示同步状态（volatile int）
- **CLH队列**：FIFO双向链表，存储等待线程
- **ConditionObject**：条件变量，支持等待/通知机制

**3. 核心方法**
```java
public abstract class AbstractQueuedSynchronizer {
    // 状态变量
    private volatile int state;
    
    // 独占模式获取锁（需要子类实现）
    protected boolean tryAcquire(int arg) {
        throw new UnsupportedOperationException();
    }
    
    // 独占模式释放锁（需要子类实现）
    protected boolean tryRelease(int arg) {
        throw new UnsupportedOperationException();
    }
    
    // 共享模式获取锁（需要子类实现）
    protected int tryAcquireShared(int arg) {
        throw new UnsupportedOperationException();
    }
    
    // 共享模式释放锁（需要子类实现）
    protected boolean tryReleaseShared(int arg) {
        throw new UnsupportedOperationException();
    }
    
    // 模板方法：获取独占锁
    public final void acquire(int arg) {
        if (!tryAcquire(arg) &&
            acquireQueued(addWaiter(Node.EXCLUSIVE), arg))
            selfInterrupt();
    }
    
    // 模板方法：释放独占锁
    public final boolean release(int arg) {
        if (tryRelease(arg)) {
            Node h = head;
            if (h != null && h.waitStatus != 0)
                unparkSuccessor(h);
            return true;
        }
        return false;
    }
}
```

#### AQS的工作流程

**获取锁的流程：**
1. 调用tryAcquire()尝试获取锁
2. 获取成功：直接返回
3. 获取失败：创建Node节点加入CLH队列
4. 在队列中自旋或阻塞等待
5. 前驱节点释放锁时被唤醒，重新尝试获取

**释放锁的流程：**
1. 调用tryRelease()释放锁
2. 释放成功：唤醒队列中的后继节点
3. 后继节点被唤醒后重新尝试获取锁

#### CLH队列结构
```java
static final class Node {
    // 节点状态
    static final int CANCELLED =  1;  // 取消状态
    static final int SIGNAL    = -1;  // 需要唤醒后继节点
    static final int CONDITION = -2;  // 在条件队列中等待
    static final int PROPAGATE = -3;  // 共享模式下需要传播
    
    volatile int waitStatus;          // 等待状态
    volatile Node prev;               // 前驱节点
    volatile Node next;               // 后继节点
    volatile Thread thread;           // 等待的线程
    Node nextWaiter;                  // 条件队列中的下一个节点
}
```

#### AQS的两种模式

**1. 独占模式（Exclusive）**
- 同一时刻只能有一个线程获取锁
- 典型实现：ReentrantLock、ReentrantReadWriteLock的写锁

**2. 共享模式（Shared）**
- 同一时刻可以有多个线程获取锁
- 典型实现：Semaphore、CountDownLatch、ReentrantReadWriteLock的读锁

#### 基于AQS的同步器实现示例
```java
// 自定义独占锁
public class CustomLock {
    private final Sync sync = new Sync();
    
    private static class Sync extends AbstractQueuedSynchronizer {
        @Override
        protected boolean tryAcquire(int arg) {
            // 尝试获取锁：state从0变为1
            return compareAndSetState(0, 1);
        }
        
        @Override
        protected boolean tryRelease(int arg) {
            // 释放锁：state从1变为0
            setState(0);
            return true;
        }
        
        @Override
        protected boolean isHeldExclusively() {
            return getState() == 1;
        }
    }
    
    public void lock() {
        sync.acquire(1);
    }
    
    public void unlock() {
        sync.release(1);
    }
}
```

## 2. Semaphore 有什么用？Semaphore 的原理是什么？⭐⭐⭐⭐

### 问题分析
考查对信号量机制的理解，以及在并发控制中的应用。

### 标准答案

**Semaphore（信号量）：控制同时访问特定资源的线程数量**

#### Semaphore的作用
1. **限流控制**：限制同时执行的线程数量
2. **资源池管理**：管理有限的资源（如数据库连接池）
3. **并发控制**：控制对共享资源的访问

#### 核心原理
```java
public class Semaphore implements java.io.Serializable {
    private final Sync sync;
    
    // 基于AQS的同步器
    abstract static class Sync extends AbstractQueuedSynchronizer {
        Sync(int permits) {
            setState(permits);  // state表示许可证数量
        }
        
        // 获取许可证
        protected int tryAcquireShared(int acquires) {
            for (;;) {
                int available = getState();
                int remaining = available - acquires;
                if (remaining < 0 ||
                    compareAndSetState(available, remaining))
                    return remaining;
            }
        }
        
        // 释放许可证
        protected boolean tryReleaseShared(int releases) {
            for (;;) {
                int current = getState();
                int next = current + releases;
                if (compareAndSetState(current, next))
                    return true;
            }
        }
    }
}
```

#### 使用示例
```java
public class SemaphoreDemo {
    // 创建信号量，允许3个线程同时访问
    private static final Semaphore semaphore = new Semaphore(3);
    
    public static void main(String[] args) {
        // 模拟10个线程访问资源
        for (int i = 0; i < 10; i++) {
            new Thread(new Worker(i)).start();
        }
    }
    
    static class Worker implements Runnable {
        private int id;
        
        public Worker(int id) {
            this.id = id;
        }
        
        @Override
        public void run() {
            try {
                // 获取许可证
                semaphore.acquire();
                System.out.println("线程" + id + "获得许可证，开始工作");
                
                // 模拟工作
                Thread.sleep(2000);
                
                System.out.println("线程" + id + "工作完成，释放许可证");
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                // 释放许可证
                semaphore.release();
            }
        }
    }
}
```

#### 实际应用场景
```java
// 数据库连接池控制
public class DatabaseConnectionPool {
    private final Semaphore semaphore;
    private final List<Connection> connections;
    
    public DatabaseConnectionPool(int maxConnections) {
        this.semaphore = new Semaphore(maxConnections);
        this.connections = new ArrayList<>(maxConnections);
        // 初始化连接池
        for (int i = 0; i < maxConnections; i++) {
            connections.add(createConnection());
        }
    }
    
    public Connection getConnection() throws InterruptedException {
        semaphore.acquire();  // 获取许可证
        return connections.remove(0);
    }
    
    public void releaseConnection(Connection connection) {
        connections.add(connection);
        semaphore.release();  // 释放许可证
    }
}

// 限流器
public class RateLimiter {
    private final Semaphore semaphore;
    
    public RateLimiter(int maxRequests) {
        this.semaphore = new Semaphore(maxRequests);
    }
    
    public boolean tryAcquire() {
        return semaphore.tryAcquire();
    }
    
    public void release() {
        semaphore.release();
    }
}
```

## 3. CountDownLatch 有什么用？原理是什么？⭐⭐⭐⭐

### 问题分析
考查对倒计时门闩的理解，以及在多线程协调中的应用。

### 标准答案

**CountDownLatch（倒计时门闩）：让一个或多个线程等待其他线程完成操作**

#### CountDownLatch的特点
1. **一次性使用**：计数器减到0后不能重置
2. **倒计时机制**：从初始值倒数到0
3. **阻塞等待**：await()方法阻塞直到计数为0

#### 核心原理
```java
public class CountDownLatch {
    private final Sync sync;
    
    // 基于AQS的同步器
    private static final class Sync extends AbstractQueuedSynchronizer {
        Sync(int count) {
            setState(count);  // state表示计数值
        }
        
        // 共享模式获取：计数为0时返回1，否则返回-1
        protected int tryAcquireShared(int acquires) {
            return (getState() == 0) ? 1 : -1;
        }
        
        // 共享模式释放：计数减1
        protected boolean tryReleaseShared(int releases) {
            for (;;) {
                int c = getState();
                if (c == 0)
                    return false;
                int nextc = c-1;
                if (compareAndSetState(c, nextc))
                    return nextc == 0;
            }
        }
    }
    
    public void await() throws InterruptedException {
        sync.acquireSharedInterruptibly(1);
    }
    
    public void countDown() {
        sync.releaseShared(1);
    }
}
```

#### 使用示例
```java
public class CountDownLatchDemo {
    public static void main(String[] args) throws InterruptedException {
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 启动5个工作线程
        for (int i = 0; i < threadCount; i++) {
            new Thread(new Worker(latch, i)).start();
        }

        System.out.println("主线程等待所有工作线程完成...");
        latch.await();  // 等待计数器归零
        System.out.println("所有工作线程已完成，主线程继续执行");
    }

    static class Worker implements Runnable {
        private final CountDownLatch latch;
        private final int id;

        public Worker(CountDownLatch latch, int id) {
            this.latch = latch;
            this.id = id;
        }

        @Override
        public void run() {
            try {
                System.out.println("工作线程" + id + "开始工作");
                Thread.sleep(2000);  // 模拟工作
                System.out.println("工作线程" + id + "完成工作");
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                latch.countDown();  // 计数器减1
            }
        }
    }
}
```

#### 实际应用场景
```java
// 场景1：等待多个服务启动完成
public class ServiceManager {
    private final CountDownLatch startupLatch;

    public ServiceManager(int serviceCount) {
        this.startupLatch = new CountDownLatch(serviceCount);
    }

    public void startService(String serviceName) {
        new Thread(() -> {
            try {
                System.out.println(serviceName + " 正在启动...");
                Thread.sleep(3000);  // 模拟启动时间
                System.out.println(serviceName + " 启动完成");
            } catch (InterruptedException e) {
                e.printStackTrace();
            } finally {
                startupLatch.countDown();
            }
        }).start();
    }

    public void waitForAllServicesStarted() throws InterruptedException {
        startupLatch.await();
        System.out.println("所有服务启动完成，系统可以对外提供服务");
    }
}

// 场景2：并行计算后汇总结果
public class ParallelCalculator {
    public static void main(String[] args) throws InterruptedException {
        int taskCount = 4;
        CountDownLatch latch = new CountDownLatch(taskCount);
        AtomicInteger result = new AtomicInteger(0);

        // 启动4个计算任务
        for (int i = 0; i < taskCount; i++) {
            final int start = i * 25;
            final int end = (i + 1) * 25;

            new Thread(() -> {
                int sum = 0;
                for (int j = start; j < end; j++) {
                    sum += j;
                }
                result.addAndGet(sum);
                System.out.println("计算任务[" + start + "-" + end + "]完成，结果：" + sum);
                latch.countDown();
            }).start();
        }

        latch.await();  // 等待所有计算完成
        System.out.println("最终结果：" + result.get());
    }
}
```

## 4. CyclicBarrier 有什么用？原理是什么？⭐⭐⭐

### 问题分析
考查对循环屏障的理解，以及与CountDownLatch的区别。

### 标准答案

**CyclicBarrier（循环屏障）：让一组线程到达一个屏障点时被阻塞，直到最后一个线程到达屏障点，屏障才会开门**

#### CyclicBarrier的特点
1. **可重复使用**：屏障可以重置和重复使用
2. **同步等待**：所有线程必须同时到达屏障点
3. **回调支持**：可以在屏障开放时执行回调操作

#### 核心原理
```java
public class CyclicBarrier {
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition trip = lock.newCondition();
    private final int parties;           // 参与的线程数
    private final Runnable barrierCommand; // 屏障开放时的回调
    private Generation generation = new Generation(); // 当前代
    private int count;                   // 当前等待的线程数

    // 等待所有线程到达屏障点
    public int await() throws InterruptedException, BrokenBarrierException {
        return dowait(false, 0L);
    }

    private int dowait(boolean timed, long nanos)
            throws InterruptedException, BrokenBarrierException {
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            final Generation g = generation;

            if (g.broken)
                throw new BrokenBarrierException();

            if (Thread.interrupted()) {
                breakBarrier();
                throw new InterruptedException();
            }

            int index = --count;
            if (index == 0) {  // 最后一个线程到达
                boolean ranAction = false;
                try {
                    final Runnable command = barrierCommand;
                    if (command != null)
                        command.run();  // 执行回调
                    ranAction = true;
                    nextGeneration();   // 开启下一代
                    return 0;
                } finally {
                    if (!ranAction)
                        breakBarrier();
                }
            }

            // 不是最后一个线程，等待
            for (;;) {
                try {
                    if (!timed)
                        trip.await();
                    else if (nanos > 0L)
                        nanos = trip.awaitNanos(nanos);
                } catch (InterruptedException ie) {
                    if (g == generation && !g.broken) {
                        breakBarrier();
                        throw ie;
                    } else {
                        Thread.currentThread().interrupt();
                    }
                }

                if (g.broken)
                    throw new BrokenBarrierException();

                if (g != generation)
                    return index;

                if (timed && nanos <= 0L) {
                    breakBarrier();
                    throw new TimeoutException();
                }
            }
        } finally {
            lock.unlock();
        }
    }
}
```

#### 使用示例
```java
public class CyclicBarrierDemo {
    public static void main(String[] args) {
        int threadCount = 3;

        // 创建屏障，当3个线程都到达时执行回调
        CyclicBarrier barrier = new CyclicBarrier(threadCount, () -> {
            System.out.println("所有线程都到达屏障点，开始下一阶段！");
        });

        // 启动3个线程
        for (int i = 0; i < threadCount; i++) {
            new Thread(new Worker(barrier, i)).start();
        }
    }

    static class Worker implements Runnable {
        private final CyclicBarrier barrier;
        private final int id;

        public Worker(CyclicBarrier barrier, int id) {
            this.barrier = barrier;
            this.id = id;
        }

        @Override
        public void run() {
            try {
                // 第一阶段工作
                System.out.println("线程" + id + "完成第一阶段工作");
                Thread.sleep((id + 1) * 1000);

                // 等待其他线程完成第一阶段
                barrier.await();

                // 第二阶段工作
                System.out.println("线程" + id + "开始第二阶段工作");
                Thread.sleep(2000);

                // 等待其他线程完成第二阶段
                barrier.await();

                System.out.println("线程" + id + "全部工作完成");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
```

#### CyclicBarrier vs CountDownLatch

| 特性 | CyclicBarrier | CountDownLatch |
|------|---------------|----------------|
| **可重用性** | 可重复使用 | 一次性使用 |
| **等待方式** | 所有线程相互等待 | 一个/多个线程等待其他线程 |
| **计数方向** | 递减到0后重置 | 只能递减到0 |
| **回调支持** | 支持屏障开放时回调 | 不支持回调 |
| **异常处理** | 支持BrokenBarrierException | 相对简单 |
| **使用场景** | 多阶段任务同步 | 等待初始化完成 |

## 5. ReentrantLock 和 synchronized 的区别？⭐⭐⭐⭐⭐

### 问题分析
这是Java锁机制的经典对比题，考查对不同锁实现的深度理解。

### 标准答案

#### 核心区别对比

| 特性 | ReentrantLock | synchronized |
|------|---------------|--------------|
| **锁类型** | API层面的锁 | JVM层面的锁 |
| **实现方式** | 基于AQS | 基于Monitor |
| **可中断性** | 支持中断 | 不支持中断 |
| **超时获取** | 支持超时 | 不支持超时 |
| **公平性** | 支持公平/非公平 | 非公平锁 |
| **条件变量** | 支持多个Condition | 只有一个wait/notify |
| **性能** | 高并发下性能更好 | JVM优化后性能接近 |
| **使用复杂度** | 需要手动释放 | 自动释放 |

#### 详细对比分析

**1. 可中断性**
```java
// ReentrantLock支持中断
public class InterruptibleLockDemo {
    private final ReentrantLock lock = new ReentrantLock();

    public void interruptibleMethod() throws InterruptedException {
        // 可中断的锁获取
        lock.lockInterruptibly();
        try {
            // 业务逻辑
            Thread.sleep(5000);
        } finally {
            lock.unlock();
        }
    }
}

// synchronized不支持中断
public class SynchronizedDemo {
    public synchronized void method() {
        try {
            Thread.sleep(5000);  // 无法被中断
        } catch (InterruptedException e) {
            // 即使收到中断信号，也无法立即释放锁
        }
    }
}
```

**2. 超时获取**
```java
public class TimeoutLockDemo {
    private final ReentrantLock lock = new ReentrantLock();

    public boolean tryLockWithTimeout() {
        try {
            // 尝试在3秒内获取锁
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    // 业务逻辑
                    return true;
                } finally {
                    lock.unlock();
                }
            } else {
                System.out.println("获取锁超时");
                return false;
            }
        } catch (InterruptedException e) {
            return false;
        }
    }
}
```

**3. 公平性**
```java
public class FairLockDemo {
    // 公平锁：按照请求顺序获取锁
    private final ReentrantLock fairLock = new ReentrantLock(true);

    // 非公平锁：允许插队
    private final ReentrantLock unfairLock = new ReentrantLock(false);

    public void fairMethod() {
        fairLock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 获得公平锁");
        } finally {
            fairLock.unlock();
        }
    }
}
```

**4. 条件变量**
```java
public class ConditionDemo {
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition notEmpty = lock.newCondition();
    private final Condition notFull = lock.newCondition();
    private final Queue<String> queue = new LinkedList<>();
    private final int capacity = 10;

    public void put(String item) throws InterruptedException {
        lock.lock();
        try {
            while (queue.size() == capacity) {
                notFull.await();  // 队列满时等待
            }
            queue.offer(item);
            notEmpty.signal();  // 通知消费者
        } finally {
            lock.unlock();
        }
    }

    public String take() throws InterruptedException {
        lock.lock();
        try {
            while (queue.isEmpty()) {
                notEmpty.await();  // 队列空时等待
            }
            String item = queue.poll();
            notFull.signal();  // 通知生产者
            return item;
        } finally {
            lock.unlock();
        }
    }
}
```

#### 使用建议

**选择ReentrantLock的场景：**
- 需要可中断的锁获取
- 需要超时获取锁
- 需要公平锁
- 需要多个条件变量
- 高并发场景下的性能要求

**选择synchronized的场景：**
- 简单的同步需求
- 不需要高级功能
- 代码简洁性要求高
- JVM自动优化

## 6. ReentrantReadWriteLock 的原理？⭐⭐⭐⭐

### 问题分析
考查对读写锁的理解，以及读写分离的并发控制机制。

### 标准答案

**ReentrantReadWriteLock（读写锁）：允许多个读线程同时访问，但写线程与读线程、写线程与写线程之间互斥**

#### 核心原理
```java
public class ReentrantReadWriteLock implements ReadWriteLock {
    private final ReadLock readerLock;
    private final WriteLock writerLock;
    private final Sync sync;

    // 基于AQS的同步器
    abstract static class Sync extends AbstractQueuedSynchronizer {
        // 高16位表示读锁状态，低16位表示写锁状态
        static final int SHARED_SHIFT   = 16;
        static final int SHARED_UNIT    = (1 << SHARED_SHIFT);
        static final int MAX_COUNT      = (1 << SHARED_SHIFT) - 1;
        static final int EXCLUSIVE_MASK = (1 << SHARED_SHIFT) - 1;

        // 获取读锁数量
        static int sharedCount(int c)    { return c >>> SHARED_SHIFT; }
        // 获取写锁数量
        static int exclusiveCount(int c) { return c & EXCLUSIVE_MASK; }

        // 读锁获取
        protected final int tryAcquireShared(int unused) {
            Thread current = Thread.currentThread();
            int c = getState();

            // 如果有写锁且不是当前线程持有，获取失败
            if (exclusiveCount(c) != 0 &&
                getExclusiveOwnerThread() != current)
                return -1;

            int r = sharedCount(c);
            if (!readerShouldBlock() &&
                r < MAX_COUNT &&
                compareAndSetState(c, c + SHARED_UNIT)) {
                // 读锁获取成功
                if (r == 0) {
                    firstReader = current;
                    firstReaderHoldCount = 1;
                } else if (firstReader == current) {
                    firstReaderHoldCount++;
                } else {
                    // 记录每个线程的读锁重入次数
                    HoldCounter rh = cachedHoldCounter;
                    if (rh == null || rh.tid != getThreadId(current))
                        cachedHoldCounter = rh = readHolds.get();
                    else if (rh.count == 0)
                        readHolds.set(rh);
                    rh.count++;
                }
                return 1;
            }
            return fullTryAcquireShared(current);
        }

        // 写锁获取
        protected final boolean tryAcquire(int acquires) {
            Thread current = Thread.currentThread();
            int c = getState();
            int w = exclusiveCount(c);

            if (c != 0) {
                // 有读锁或者写锁被其他线程持有
                if (w == 0 || current != getExclusiveOwnerThread())
                    return false;

                // 写锁重入
                if (w + exclusiveCount(acquires) > MAX_COUNT)
                    throw new Error("Maximum lock count exceeded");
                setState(c + acquires);
                return true;
            }

            if (writerShouldBlock() ||
                !compareAndSetState(c, c + acquires))
                return false;

            setExclusiveOwnerThread(current);
            return true;
        }
    }
}
```

#### 使用示例
```java
public class ReadWriteLockDemo {
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private final Lock readLock = rwLock.readLock();
    private final Lock writeLock = rwLock.writeLock();
    private final Map<String, String> cache = new HashMap<>();

    // 读操作
    public String get(String key) {
        readLock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 正在读取");
            Thread.sleep(1000);  // 模拟读取耗时
            return cache.get(key);
        } catch (InterruptedException e) {
            return null;
        } finally {
            readLock.unlock();
        }
    }

    // 写操作
    public void put(String key, String value) {
        writeLock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 正在写入");
            Thread.sleep(2000);  // 模拟写入耗时
            cache.put(key, value);
        } catch (InterruptedException e) {
            // 处理异常
        } finally {
            writeLock.unlock();
        }
    }

    public static void main(String[] args) {
        ReadWriteLockDemo demo = new ReadWriteLockDemo();

        // 启动多个读线程
        for (int i = 0; i < 3; i++) {
            new Thread(() -> {
                demo.get("key1");
            }, "Reader-" + i).start();
        }

        // 启动写线程
        new Thread(() -> {
            demo.put("key1", "value1");
        }, "Writer").start();
    }
}
```

#### 锁降级示例
```java
public class LockDowngradeDemo {
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private final Lock readLock = rwLock.readLock();
    private final Lock writeLock = rwLock.writeLock();
    private volatile boolean dataReady = false;
    private String data;

    public void processData() {
        readLock.lock();
        try {
            if (!dataReady) {
                // 释放读锁，获取写锁
                readLock.unlock();
                writeLock.lock();
                try {
                    // 双重检查
                    if (!dataReady) {
                        data = loadData();  // 加载数据
                        dataReady = true;
                    }
                    // 锁降级：在释放写锁前获取读锁
                    readLock.lock();
                } finally {
                    writeLock.unlock();  // 释放写锁
                }
            }

            // 使用数据（持有读锁）
            useData(data);
        } finally {
            readLock.unlock();
        }
    }

    private String loadData() {
        // 模拟数据加载
        return "loaded data";
    }

    private void useData(String data) {
        System.out.println("使用数据：" + data);
    }
}
```

## 7. AQS中的公平锁和非公平锁的区别？⭐⭐⭐⭐

### 问题分析
考查对锁公平性的理解，以及对性能和公平性权衡的认识。

### 标准答案

#### 公平锁 vs 非公平锁

| 特性 | 公平锁 | 非公平锁 |
|------|--------|----------|
| **获取顺序** | 严格按照请求顺序 | 允许插队 |
| **性能** | 相对较低 | 相对较高 |
| **吞吐量** | 较低 | 较高 |
| **响应时间** | 平均响应时间较长 | 平均响应时间较短 |
| **饥饿问题** | 不会发生饥饿 | 可能发生饥饿 |

#### 实现原理对比
```java
// 公平锁的tryAcquire实现
protected final boolean tryAcquire(int acquires) {
    final Thread current = Thread.currentThread();
    int c = getState();
    if (c == 0) {
        // 关键：检查是否有前驱节点在等待
        if (!hasQueuedPredecessors() &&
            compareAndSetState(0, acquires)) {
            setExclusiveOwnerThread(current);
            return true;
        }
    }
    else if (current == getExclusiveOwnerThread()) {
        int nextc = c + acquires;
        if (nextc < 0)
            throw new Error("Maximum lock count exceeded");
        setState(nextc);
        return true;
    }
    return false;
}

// 非公平锁的tryAcquire实现
protected final boolean tryAcquire(int acquires) {
    final Thread current = Thread.currentThread();
    int c = getState();
    if (c == 0) {
        // 关键：直接尝试获取锁，不检查队列
        if (compareAndSetState(0, acquires)) {
            setExclusiveOwnerThread(current);
            return true;
        }
    }
    else if (current == getExclusiveOwnerThread()) {
        int nextc = c + acquires;
        if (nextc < 0)
            throw new Error("Maximum lock count exceeded");
        setState(nextc);
        return true;
    }
    return false;
}
```

#### 性能测试对比
```java
public class FairVsUnfairTest {
    private static final int THREAD_COUNT = 10;
    private static final int ITERATIONS = 100000;

    public static void main(String[] args) throws InterruptedException {
        System.out.println("测试公平锁性能：");
        testLock(new ReentrantLock(true));  // 公平锁

        System.out.println("\n测试非公平锁性能：");
        testLock(new ReentrantLock(false)); // 非公平锁
    }

    private static void testLock(ReentrantLock lock) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < THREAD_COUNT; i++) {
            new Thread(() -> {
                for (int j = 0; j < ITERATIONS; j++) {
                    lock.lock();
                    try {
                        // 模拟业务操作
                    } finally {
                        lock.unlock();
                    }
                }
                latch.countDown();
            }).start();
        }

        latch.await();
        long endTime = System.currentTimeMillis();
        System.out.println("执行时间：" + (endTime - startTime) + "ms");
    }
}
```

## 总结

### 🎯 AQS核心要点

1. **AQS是什么**：Java并发包的基础框架，基于CLH队列实现
2. **核心组件**：state状态变量 + CLH队列 + 条件变量
3. **两种模式**：独占模式（ReentrantLock）+ 共享模式（Semaphore）
4. **模板方法**：tryAcquire/tryRelease + tryAcquireShared/tryReleaseShared

### 🔧 基于AQS的同步器

5. **Semaphore**：信号量，控制并发访问数量
6. **CountDownLatch**：倒计时门闩，一次性同步工具
7. **CyclicBarrier**：循环屏障，可重复使用的同步点
8. **ReentrantLock**：可重入锁，功能比synchronized更强大
9. **ReentrantReadWriteLock**：读写锁，读写分离提高并发性能

### 🚀 性能优化要点

10. **公平性权衡**：公平锁保证顺序，非公平锁提高性能
11. **锁选择策略**：简单场景用synchronized，复杂场景用ReentrantLock
12. **读写分离**：读多写少场景使用读写锁
13. **避免锁竞争**：合理设计锁粒度，减少持锁时间

通过上面的流程图，你可以更直观地理解：
- AQS的整体架构和CLH队列结构
- 获取锁的完整流程和状态转换
- CountDownLatch和CyclicBarrier的工作机制

掌握AQS原理是理解Java并发编程的关键，它是构建高性能并发应用的基础！
```
```
