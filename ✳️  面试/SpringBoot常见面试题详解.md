# SpringBoot常见面试题详解

## 基础篇

### 1. 什么是SpringBoot？它解决了什么问题？

**答案：**
SpringBoot是Spring团队提供的一个用来简化Spring应用初始搭建以及开发过程的框架。它基于"约定优于配置"的理念，通过自动配置机制大大减少了配置工作。

**解决的问题：**
- **配置复杂**：传统Spring项目需要大量XML配置
- **依赖管理困难**：需要手动管理各种jar包版本兼容性
- **部署复杂**：需要外部容器如Tomcat
- **开发效率低**：项目搭建耗时较长

**SpringBoot的优势：**
- 快速创建独立的Spring应用
- 直接嵌入Tomcat、Jetty等容器
- 提供starter依赖简化构建配置
- 自动配置Spring和第三方库
- 提供生产就绪的功能，如监控、健康检查等
- 无需XML配置

### 2. SpringBoot的核心注解有哪些？

**答案：**

**@SpringBootApplication**
这是SpringBoot的核心注解，它是一个组合注解，包含：
- `@SpringBootConfiguration`：标识这是一个配置类
- `@EnableAutoConfiguration`：启用自动配置
- `@ComponentScan`：启用组件扫描

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

**其他重要注解：**
- `@RestController`：组合了@Controller和@ResponseBody
- `@RequestMapping`：映射HTTP请求
- `@Autowired`：自动装配
- `@Value`：注入配置值
- `@ConfigurationProperties`：绑定配置属性

### 3. SpringBoot的启动流程是什么？

**答案：**

SpringBoot启动流程可以分为以下几个阶段：

```mermaid
graph TD
    A[SpringApplication.run] --> B[创建SpringApplication实例]
    B --> C[推断应用类型]
    C --> D[加载ApplicationContextInitializer]
    D --> E[加载ApplicationListener]
    E --> F[推断主配置类]
    F --> G[运行run方法]
    G --> H[准备Environment]
    H --> I[创建ApplicationContext]
    I --> J[准备ApplicationContext]
    J --> K[刷新ApplicationContext]
    K --> L[调用Runners]
    L --> M[启动完成]
```

**详细步骤：**

1. **创建SpringApplication实例**
   - 推断应用类型（SERVLET、REACTIVE、NONE）
   - 加载ApplicationContextInitializer
   - 加载ApplicationListener
   - 推断主配置类

2. **运行run方法**
   - 启动StopWatch计时
   - 获取SpringApplicationRunListeners
   - 准备Environment
   - 打印Banner
   - 创建ApplicationContext
   - 准备ApplicationContext
   - 刷新ApplicationContext
   - 调用ApplicationRunner和CommandLineRunner

## 进阶篇

### 4. SpringBoot自动配置原理是什么？

**答案：**

SpringBoot自动配置的核心是`@EnableAutoConfiguration`注解，它通过Spring的`@Import`注解导入`AutoConfigurationImportSelector`类。

```mermaid
graph TD
    A[@EnableAutoConfiguration] --> B[AutoConfigurationImportSelector]
    B --> C[加载spring.factories]
    C --> D[获取所有自动配置类]
    D --> E[过滤条件注解]
    E --> F[@ConditionalOnClass]
    E --> G[@ConditionalOnMissingBean]
    E --> H[@ConditionalOnProperty]
    F --> I[满足条件的配置类生效]
    G --> I
    H --> I
```

**自动配置原理：**

1. **加载自动配置类**
   - 从`META-INF/spring.factories`文件中加载所有的自动配置类
   - SpringBoot启动时会扫描所有jar包下的该文件

2. **条件注解过滤**
   - `@ConditionalOnClass`：当类路径下存在指定类时生效
   - `@ConditionalOnMissingBean`：当容器中不存在指定Bean时生效
   - `@ConditionalOnProperty`：当配置文件中存在指定属性时生效

3. **配置类生效**
   - 满足条件的配置类会被加载到Spring容器中
   - 自动创建相应的Bean

**示例：**
```java
@Configuration
@ConditionalOnClass(DataSource.class)
@ConditionalOnMissingBean(DataSource.class)
@EnableConfigurationProperties(DataSourceProperties.class)
public class DataSourceAutoConfiguration {
    
    @Bean
    @ConditionalOnProperty(prefix = "spring.datasource", name = "url")
    public DataSource dataSource(DataSourceProperties properties) {
        return DataSourceBuilder.create()
                .url(properties.getUrl())
                .username(properties.getUsername())
                .password(properties.getPassword())
                .build();
    }
}
```

### 5. SpringBoot Starter的工作原理？

**答案：**

Starter是SpringBoot的核心功能之一，它将常用的依赖组合起来，避免了依赖地狱问题。

```mermaid
graph TD
    A[引入Starter依赖] --> B[传递依赖自动引入]
    B --> C[自动配置类生效]
    C --> D[Bean自动创建]
    D --> E[开箱即用]
    
    F[spring-boot-starter-web] --> G[spring-webmvc]
    F --> H[spring-boot-starter-tomcat]
    F --> I[spring-boot-starter-json]
    
    G --> J[WebMvcAutoConfiguration]
    H --> K[EmbeddedTomcatConfiguration]
    I --> L[JacksonAutoConfiguration]
```

**Starter工作原理：**

1. **依赖管理**
   - Starter本身不包含代码，只包含依赖描述
   - 通过Maven/Gradle的传递依赖机制引入所需jar包

2. **自动配置**
   - 每个Starter都有对应的AutoConfiguration类
   - 通过条件注解决定是否生效

3. **配置属性**
   - 提供ConfigurationProperties类绑定配置
   - 支持IDE智能提示

**自定义Starter示例：**
```java
// 1. 配置属性类
@ConfigurationProperties(prefix = "myservice")
public class MyServiceProperties {
    private String name = "default";
    private int timeout = 1000;
    // getter/setter
}

// 2. 自动配置类
@Configuration
@ConditionalOnClass(MyService.class)
@EnableConfigurationProperties(MyServiceProperties.class)
public class MyServiceAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public MyService myService(MyServiceProperties properties) {
        return new MyService(properties.getName(), properties.getTimeout());
    }
}

// 3. spring.factories文件
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
com.example.MyServiceAutoConfiguration
```

### 6. SpringBoot的配置文件加载顺序是什么？

**答案：**

SpringBoot支持多种配置文件格式和位置，按照优先级从高到低的顺序：

```mermaid
graph TD
    A[命令行参数] --> B[JNDI属性]
    B --> C[系统环境变量]
    C --> D[操作系统环境变量]
    D --> E[jar包外的application-{profile}.properties]
    E --> F[jar包内的application-{profile}.properties]
    F --> G[jar包外的application.properties]
    G --> H[jar包内的application.properties]
    H --> I[@PropertySource注解]
    I --> J[默认属性]
```

**配置文件位置优先级：**
1. `./config/`（当前目录下的config目录）
2. `./`（当前目录）
3. `classpath:/config/`（类路径下的config目录）
4. `classpath:/`（类路径根目录）

**Profile配置：**
```yaml
# application.yml
spring:
  profiles:
    active: dev

---
# application-dev.yml
server:
  port: 8080
database:
  url: **********************************

---
# application-prod.yml
server:
  port: 80
database:
  url: *************************************
```

## 高级篇

### 7. SpringBoot的事件机制是如何工作的？

**答案：**

SpringBoot基于Spring的事件机制，提供了应用生命周期中的各种事件。

```mermaid
sequenceDiagram
    participant App as Application
    participant Listener as ApplicationListener
    participant Context as ApplicationContext
    participant Publisher as ApplicationEventPublisher
    
    App->>Context: 启动应用
    Context->>Publisher: 发布ApplicationStartingEvent
    Publisher->>Listener: 通知监听器
    Context->>Publisher: 发布ApplicationEnvironmentPreparedEvent
    Publisher->>Listener: 通知监听器
    Context->>Publisher: 发布ApplicationContextInitializedEvent
    Publisher->>Listener: 通知监听器
    Context->>Publisher: 发布ApplicationPreparedEvent
    Publisher->>Listener: 通知监听器
    Context->>Publisher: 发布ApplicationStartedEvent
    Publisher->>Listener: 通知监听器
    Context->>Publisher: 发布ApplicationReadyEvent
    Publisher->>Listener: 通知监听器
```

**SpringBoot事件类型：**

1. **ApplicationStartingEvent**：应用启动开始
2. **ApplicationEnvironmentPreparedEvent**：环境准备完成
3. **ApplicationContextInitializedEvent**：ApplicationContext初始化完成
4. **ApplicationPreparedEvent**：ApplicationContext准备完成
5. **ApplicationStartedEvent**：应用启动完成
6. **ApplicationReadyEvent**：应用就绪
7. **ApplicationFailedEvent**：应用启动失败

**自定义事件监听器：**
```java
// 方式1：实现ApplicationListener接口
@Component
public class MyApplicationListener implements ApplicationListener<ApplicationReadyEvent> {
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        System.out.println("应用启动完成，可以接收请求了");
    }
}

// 方式2：使用@EventListener注解
@Component
public class MyEventListener {
    
    @EventListener
    public void handleApplicationReady(ApplicationReadyEvent event) {
        System.out.println("应用就绪事件处理");
    }
    
    @EventListener
    @Async
    public void handleCustomEvent(CustomEvent event) {
        // 异步处理自定义事件
    }
}

// 自定义事件
public class CustomEvent extends ApplicationEvent {
    private String message;
    
    public CustomEvent(Object source, String message) {
        super(source);
        this.message = message;
    }
}

// 发布自定义事件
@Service
public class MyService {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void doSomething() {
        // 业务逻辑
        eventPublisher.publishEvent(new CustomEvent(this, "操作完成"));
    }
}
```

### 8. SpringBoot的健康检查机制是如何实现的？

**答案：**

SpringBoot Actuator提供了强大的健康检查功能，通过`/actuator/health`端点暴露应用健康状态。

```mermaid
graph TD
    A[Health Endpoint] --> B[HealthIndicatorRegistry]
    B --> C[DataSourceHealthIndicator]
    B --> D[RedisHealthIndicator]
    B --> E[DiskSpaceHealthIndicator]
    B --> F[CustomHealthIndicator]
    
    C --> G[检查数据库连接]
    D --> H[检查Redis连接]
    E --> I[检查磁盘空间]
    F --> J[自定义检查逻辑]
    
    G --> K[UP/DOWN状态]
    H --> K
    I --> K
    J --> K
    
    K --> L[聚合健康状态]
```

**健康检查配置：**
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    defaults:
      enabled: true
    db:
      enabled: true
    redis:
      enabled: true
    diskspace:
      enabled: true
      threshold: 10MB
```

**自定义健康检查器：**
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 执行健康检查逻辑
            boolean isHealthy = performHealthCheck();
            
            if (isHealthy) {
                return Health.up()
                        .withDetail("status", "服务正常")
                        .withDetail("timestamp", System.currentTimeMillis())
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", "服务异常")
                        .withDetail("error", "检查失败")
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("status", "检查异常")
                    .withException(e)
                    .build();
        }
    }
    
    private boolean performHealthCheck() {
        // 实际的健康检查逻辑
        return true;
    }
}

// 响应式健康检查器
@Component
public class ReactiveCustomHealthIndicator implements ReactiveHealthIndicator {
    
    @Override
    public Mono<Health> health() {
        return Mono.fromCallable(this::performHealthCheck)
                .map(result -> result ? Health.up().build() : Health.down().build())
                .onErrorReturn(Health.down().build());
    }
    
    private boolean performHealthCheck() {
        // 健康检查逻辑
        return true;
    }
}
```

### 9. SpringBoot如何实现优雅关闭？

**答案：**

SpringBoot提供了优雅关闭机制，确保应用在关闭时能够完成正在处理的请求。

```mermaid
sequenceDiagram
    participant Signal as 关闭信号
    participant Hook as ShutdownHook
    participant Context as ApplicationContext
    participant Web as WebServer
    participant Bean as DisposableBean
    
    Signal->>Hook: 接收SIGTERM信号
    Hook->>Context: 开始关闭流程
    Context->>Web: 停止接收新请求
    Web->>Web: 等待现有请求完成
    Context->>Bean: 调用@PreDestroy方法
    Bean->>Bean: 清理资源
    Context->>Context: 销毁Bean
    Context->>Hook: 关闭完成
```

**优雅关闭配置：**
```yaml
server:
  shutdown: graceful
spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
```

**实现优雅关闭的方式：**

1. **使用@PreDestroy注解**
```java
@Service
public class MyService {
    
    private ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    @PreDestroy
    public void cleanup() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
    }
}
```

2. **实现DisposableBean接口**
```java
@Component
public class ResourceManager implements DisposableBean {
    
    private Connection connection;
    
    @Override
    public void destroy() throws Exception {
        if (connection != null && !connection.isClosed()) {
            connection.close();
        }
    }
}
```

3. **使用ApplicationListener**
```java
@Component
public class GracefulShutdownListener implements ApplicationListener<ContextClosedEvent> {
    
    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        // 执行清理逻辑
        System.out.println("应用正在关闭，执行清理操作...");
    }
}
```

### 10. SpringBoot的内存优化策略有哪些？

**答案：**

SpringBoot应用的内存优化可以从多个维度进行：

```mermaid
mindmap
  root((内存优化))
    JVM参数调优
      堆内存设置
      垃圾回收器选择
      元空间配置
    SpringBoot配置
      Lazy初始化
      条件Bean
      Profile隔离
    应用层优化
      连接池配置
      缓存策略
      异步处理
    监控诊断
      Actuator监控
      内存分析工具
      GC日志分析
```

**1. JVM参数优化**
```bash
# 堆内存设置
-Xms512m -Xmx1024m

# 新生代和老年代比例
-XX:NewRatio=2

# 垃圾回收器选择
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200

# 元空间设置
-XX:MetaspaceSize=128m
-XX:MaxMetaspaceSize=256m

# GC日志
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
```

**2. SpringBoot配置优化**
```yaml
spring:
  main:
    lazy-initialization: true  # 延迟初始化
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

**3. 代码层面优化**
```java
// 使用@Lazy注解延迟加载
@Service
@Lazy
public class HeavyService {
    // 重量级服务
}

// 条件Bean避免不必要的实例化
@Bean
@ConditionalOnProperty(name = "feature.enabled", havingValue = "true")
public ExpensiveBean expensiveBean() {
    return new ExpensiveBean();
}

// 使用对象池减少GC压力
@Configuration
public class ObjectPoolConfig {
    
    @Bean
    public GenericObjectPool<ExpensiveObject> objectPool() {
        GenericObjectPoolConfig<ExpensiveObject> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(100);
        config.setMaxIdle(20);
        config.setMinIdle(5);
        
        return new GenericObjectPool<>(new ExpensiveObjectFactory(), config);
    }
}
```

## 实战篇

### 11. 如何在SpringBoot中实现分布式锁？

**答案：**

分布式锁是分布式系统中确保数据一致性的重要机制，SpringBoot中可以通过Redis、Zookeeper等实现。

```mermaid
sequenceDiagram
    participant Client1 as 客户端1
    participant Client2 as 客户端2
    participant Redis as Redis服务器
    participant Lock as 分布式锁
    
    Client1->>Redis: SET lock_key unique_value NX EX 30
    Redis-->>Client1: OK (获取锁成功)
    Client2->>Redis: SET lock_key unique_value NX EX 30
    Redis-->>Client2: NULL (获取锁失败)
    Client1->>Lock: 执行业务逻辑
    Client1->>Redis: DEL lock_key (释放锁)
    Client2->>Redis: SET lock_key unique_value NX EX 30
    Redis-->>Client2: OK (获取锁成功)
```

**Redis分布式锁实现：**
```java
@Component
public class RedisDistributedLock {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String LOCK_PREFIX = "distributed_lock:";
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else return 0 end";
    
    public boolean tryLock(String key, String value, long expireTime) {
        String lockKey = LOCK_PREFIX + key;
        Boolean result = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, value, Duration.ofSeconds(expireTime));
        return Boolean.TRUE.equals(result);
    }
    
    public boolean unlock(String key, String value) {
        String lockKey = LOCK_PREFIX + key;
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(UNLOCK_SCRIPT);
        script.setResultType(Long.class);
        
        Long result = redisTemplate.execute(script, 
                Collections.singletonList(lockKey), value);
        return Long.valueOf(1).equals(result);
    }
}

// 使用注解方式实现分布式锁
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {
    String key();
    long expireTime() default 30;
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}

@Aspect
@Component
public class DistributedLockAspect {
    
    @Autowired
    private RedisDistributedLock distributedLock;
    
    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = distributedLock.key();
        String lockValue = UUID.randomUUID().toString();
        long expireTime = distributedLock.expireTime();
        
        boolean locked = false;
        try {
            locked = this.distributedLock.tryLock(lockKey, lockValue, expireTime);
            if (!locked) {
                throw new RuntimeException("获取分布式锁失败");
            }
            return joinPoint.proceed();
        } finally {
            if (locked) {
                this.distributedLock.unlock(lockKey, lockValue);
            }
        }
    }
}

// 业务使用
@Service
public class OrderService {
    
    @DistributedLock(key = "order:#{#orderId}", expireTime = 60)
    public void processOrder(String orderId) {
        // 处理订单逻辑
    }
}
```

### 12. SpringBoot中如何实现接口限流？

**答案：**

接口限流是保护系统稳定性的重要手段，可以通过多种方式实现。

```mermaid
graph TD
    A[请求到达] --> B{限流检查}
    B -->|通过| C[执行业务逻辑]
    B -->|拒绝| D[返回限流响应]
    
    E[令牌桶算法] --> F[固定速率产生令牌]
    F --> G[请求消耗令牌]
    
    H[滑动窗口算法] --> I[统计时间窗口内请求数]
    I --> J[超过阈值则限流]
```

**基于Redis的滑动窗口限流：**
```java
@Component
public class SlidingWindowRateLimiter {
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1] " +
        "local window = tonumber(ARGV[1]) " +
        "local limit = tonumber(ARGV[2]) " +
        "local current = tonumber(ARGV[3]) " +
        "redis.call('zremrangebyscore', key, 0, current - window * 1000) " +
        "local count = redis.call('zcard', key) " +
        "if count < limit then " +
        "  redis.call('zadd', key, current, current) " +
        "  redis.call('expire', key, window) " +
        "  return 1 " +
        "else " +
        "  return 0 " +
        "end";
    
    public boolean isAllowed(String key, int windowSize, int limit) {
        String rateLimitKey = "rate_limit:" + key;
        long currentTime = System.currentTimeMillis();
        
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(RATE_LIMIT_SCRIPT);
        script.setResultType(Long.class);
        
        Long result = redisTemplate.execute(script,
                Collections.singletonList(rateLimitKey),
                String.valueOf(windowSize),
                String.valueOf(limit),
                String.valueOf(currentTime));
        
        return Long.valueOf(1).equals(result);
    }
}

// 限流注解
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    String key() default "";
    int limit() default 10;
    int window() default 60;
    String message() default "请求过于频繁，请稍后再试";
}

// 限流切面
@Aspect
@Component
public class RateLimitAspect {
    
    @Autowired
    private SlidingWindowRateLimiter rateLimiter;
    
    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        String key = generateKey(joinPoint, rateLimit.key());
        
        if (!rateLimiter.isAllowed(key, rateLimit.window(), rateLimit.limit())) {
            throw new RateLimitException(rateLimit.message());
        }
        
        return joinPoint.proceed();
    }
    
    private String generateKey(ProceedingJoinPoint joinPoint, String keyExpression) {
        if (StringUtils.hasText(keyExpression)) {
            return keyExpression;
        }
        
        // 默认使用方法签名作为key
        return joinPoint.getSignature().toShortString();
    }
}

// 使用示例
@RestController
public class ApiController {
    
    @GetMapping("/api/data")
    @RateLimit(limit = 100, window = 60, message = "接口调用过于频繁")
    public ResponseEntity<String> getData() {
        return ResponseEntity.ok("data");
    }
    
    @PostMapping("/api/submit")
    @RateLimit(key = "submit:#{request.remoteAddr}", limit = 5, window = 60)
    public ResponseEntity<String> submit(HttpServletRequest request) {
        return ResponseEntity.ok("submitted");
    }
}
```

这份面试题涵盖了SpringBoot的核心概念、原理分析和实际应用，从基础到高级，包含了详细的代码示例和流程图。每个问题都提供了深入的技术原理解释，适合不同层次的面试准备。