# 个人信息

* 姓名：林石墙                            性别：男
* 手机：13023828639                邮箱：<EMAIL>
* 工作年限：3年9个月                期望职位：Java后端开发

---

# 专业技能

1. Java基础扎实，熟悉多线程，线程池原理，JVM，对线程安全有深刻理解。
2. 熟悉常用的设计模式，来提高程序可扩展性和可维护性。
3. 熟悉SpringBoot、Spring、SpringMVC、Mybatis等开源框架运用和实现原理。
4. 熟悉SpringCloud Alibaba微服务架构，熟悉gateway，nacos。
5. 熟悉关系型数据库MySQL基本使用、索引、事务等；有SQL调优经验。
6. 熟练运用NoSQL数据库Redis，搭建Redis Cluster集群。
7. 熟悉MQ使用场景，熟练使用RabbitMQ、Kafka。
8. 熟悉Linux环境，常用的运维命令。
9. 了解Docker容器化技术、了解k8s。

---

# 教育经历

- 福建工程学院    2015年9月 ~ 2019年6月    网络工程-本科

---

# 工作经历

- 北京承启通科技有限公司 - Java后端开发（2021年3月 ~ 至今）

- 南威软件股份有限公司 - Java后端开发（2019年6月 ~ 2021年2月）

---

# 项目经历

##### 隐私号码保护服务平台（2021年3月 ~ 至今）

- 项目内容

  >基于三大运营商，平台为企业客户分配隐私号码，保证通话过程中号码信息不被泄露。并可通过平台对通话进行录音，提升服务安全性及服务质量。提供多种业务模式(AXB、AX、AXE、AXEYB等)，企业通过HTTP接口分配绑定关系，管理隐私号码池，可查看通话记录和号码的使用情况统计。
  >

- 技术栈

  >SpringCloud Alibaba +  Mybatis Plus + RabbitMQ + Sharding-JDBC + MySQL + Redis + Xxl-job + Vue

- 工作内容

  1. 使用Redis缓存绑定关系和号码池，提升接口性能，接口QPS 可达到3500，tp99<100ms。
  2. 使用RabbitMQ的死信队列和延时插件实现绑定关系过期自动回收隐私号码，保证号码可重复利用。
  3. 对于数据量上千万的绑定关系表使用Sharding-JDBC实现分库分表，减轻数据库压力，提升sql效率。
  4. 使用Spring Cloud Gateway和resilience4j的熔断器功能实现单侧机房接口异常自动熔断调用异地机房接口，保证接口可用性。
  5. 使用模板方法模式配合RabbitMQ和Redis实现话单推送客户定制化接口失败重推，减少重复代码。

##### 固话号码管控平台（2021年3月 ~ 至今）

- 项目内容

  >基于三大运营商，平台为企业客户提供固话号码、95号码、400号码，企业客户通过SIP协议对接平台freeswitch实现通话服务，并对号码使用，通话进行管控，如被叫号码黑名单、IP白名单、号码变换、呼叫频次等。平台还实现对freeswitch服务器集群配置文件可视化配置。

- 技术栈

  >SpringCloud Alibaba +  Mybatis Plus + RabbitMQ + MySQL + Redis + Vue

- 工作内容

  1. 运用责任链模式实现号码变换功能和话务管控接口，解耦各个管控功能。
  2. 完成freeswitch配置文件（xml）页面可视化配置后端接口开发。
  3. 使用Redis缓存各种配置信息，优先查询缓存，提高接口性能且降低数据库的压力。

##### 数据服务平台（2019年6月 ~ 2021年2月）

* 项目内容 
  > 平台提供数据共享交换功能，对资源库的数据库表可视化配置配置请求参数和响应参数，动态生成接口服务对外提供统一接口规范；对接第三方接口，通过可视化配置接口参数，转换为统一接口规范并对外提供；接入第三方数据资源汇聚入库。
  
* 技术栈
  > Springboot +  Mybatis + MySQL + Redis + Vue
  
* 工作任务
  1. 实现监控管理、统计分析、服务管理、资源目录、服务申请等功能模块开发和维护。
  2. 利用 Redis 实现接口调用统计，流量控制，接口鉴权，防止接口被恶意调用。
  3. 借助 kafka 实现单向网络数据传输问题，使用CompletableFuture实现接口异步转同步。
  4. 第三方接口对接，解析参数、返回值转换成通用接口并发布。
  5. 通过ftp、http接口、excel上报等方式采集各种部门的数据，汇聚入库。

---

# 自我评价

1. 对技术有追求、有良好的代码风格，注重代码质量。
2. 勤奋好学，渴望建立完整的技术知识体系，技术方面能得到全面发展。
3. 责任心强，做事认真负责，有良好的团队协作能力。

感谢您花时间阅读我的简历，期待能有机会和你共事。