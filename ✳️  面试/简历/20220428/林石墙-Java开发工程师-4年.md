# 个人信息

* 姓名：林石墙                           性别：男
* 手机：13023828639              邮箱：<EMAIL>
* 工作经验：4年                         期望职位：Java开发工程师

---

# 专业技能

- 具备扎实的Java基础以及面向对象编程思想；熟悉线程池原理，JVM、JMM相关知识，如垃圾回收算法、类加载机制等。
- 熟悉常用的设计模式，来提高程序可扩展性和可维护性。
- 熟练掌握SpringBoot、Spring、SpringMVC、Mybatis等开源框架运用和实现原理。
- 熟悉SpringCloud Alibaba微服务架构，熟悉Gateway、Nacos、OpenFeign等。
- 熟悉MySQL常用操作，具备优化SQL、表结构设计、分库分表等经验；熟悉MVCC机制、事务、所有等MySQL理论知识。
- 熟练掌握NoSql数据库Redis，对其原理有深入了解，如主从同步、持久化、淘汰机制、部署方式等。
- 熟悉MQ使用场景，熟悉RabbitMQ、Kafka、RocketMQ。
- 熟悉Linux环境，常用的运维命令；了解Docker容器化技术、了解k8s；

---


# 项目经验

#### 隐私号码保护平台&nbsp;&nbsp;&nbsp;&nbsp;北京承启通科技有限公司&nbsp;&nbsp;&nbsp;&nbsp;（2021年3月 ~ 至今）

- 项目介绍

  - 隐私号码保护平台是通过使用虚拟号码来保护用户的真实号码不被泄露，隐私号码可用于通话和短信。平台还提供多种绑定模式，例如AXB、AX、AXE、AXEYB等，可以满足不同场景和需求下的实际应用。平台还开放了丰富的API接口，帮助用户快速接入平台，实现个性化需求的定制化开发。平台可以将通话记录、短信记录等信息生成话单，然后推送给用户。平台还提供了通话录音功能，帮助用户更好地追溯和确认通话内容。平台可以为用户提供通话接通率和振铃率等多种统计报表功能。

- 项目架构

  - 采用分布式部署方案，分布式框架使用的是SpringCloud Alibaba以实现项目的高并发和高可用，并配合使用SpringBoot微服务进行整体架构的基础搭建。项目中的缓存使用的是Redis分布式缓存。项目中数据存储于MySQL。定时任务采用XXL-Job。消息中间件采用RabbitMQ。

- 技术栈

  - SpringCloud Alibaba +  Mybatis Plus + RabbitMQ + Sharding-JDBC + MySQL + Redis + XXL-Job + Vue

- 工作内容

  - 通过appId和secretKey实现接口鉴权功能，以确保 API 接口的安全性。

  - 使用Redis缓存绑定关系和号码池，提升接口性能，接口QPS 可达到3500，tp99<100ms。

  - 使用Redisson分布式锁，分配时锁定号码池，确保号码不会重复分配。

  - 使用RabbitMQ的死信队列和延时插件实现绑定关系过期自动回收隐私号码，保证号码可循环利用。

  - 对于数据量上千万的绑定关系表使用Sharding-JDBC，通过基因法实现分库分表，提高查询速度，减轻数据库压力。

  - 使用Spring Cloud Gateway和resilience4j的熔断器功能实现单侧机房接口异常自动熔断调用异地机房接口，保证接口可用性。

  - 运用模板方法模式配合RabbitMQ和Redis实现话单推送客户定制化接口失败重推，减少重复代码。

  - 通过随机权重算法控制不同供应商接口权重，实现接口流量调度。

----

#### 数据服务平台&nbsp;&nbsp;&nbsp;&nbsp;南威软件股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;（2019年6月 ~ 2021年2月）

* 项目介绍

  - 数据服务平台是一种提供数据共享和交换功能的平台。旨在帮助机构更高效地管理和利用数据资源。平台可以对内部数据库表进行可视化配置，快速生成HTTP接口，并将其对外服务。同时，还可以接入第三方接口，并将其转换为平台统一接口规范，以便外部用户访问。平台还可以接入外部的数据，包括通过API、文件导入接等多种方法来实现数据的快速接入，并将这些数据进行可视化的管理和共享。

* 项目架构

  - 采用分布式部署方案，使用Dubbo实现远程调用。并配合使用SpringBoot微服务进行整体架构的基础搭建。项目中的缓存使用的是Redis分布式缓存。项目中数据存储于MySQL。消息中间件采用Kafka。

* 技术栈
  - Springboot +  Mybatis + MySQL + Kafka + Redis + Vue

* 工作内容
  - 通过定时任务监控HTTP接口状态并异常短信通知。

  - 利用 Redis 实现接口调用统计、流量控制、接口鉴权，防止接口被恶意调用。

  - 借助 Kafka 解决单向网络接口调用问题，使用CompletableFuture实现接口异步转同步。

  - 使用反射机制实现第三方定制化接口对接，解析参数、返回值转换成通用接口并发布。

  - 通过FTP、HTTP接口、Excel上报、Kafka消息等方式采集各种部门的数据，汇聚入库。

---

# 教育经历

- 福建工程学院        网络工程 - 本科        2015年9月 ~ 2019年6月 

---

# 工作经历

- 北京承启通科技有限公司 - Java开发工程师（2021年3月 ~ 至今）

- 南威软件股份有限公司 - Java开发工程师（2019年6月 ~ 2021年2月）

---

# 个人总结

- 对技术有追求、有良好的代码风格，注重代码质量。
- 较强的执行力，善于学习总结。
- 经常复盘项目和技术方案，反思自己，进一步提升自己的业务和技术水平。

感谢您花时间阅读我的简历，期待能有机会和你共事。