# Sharding-JDBC分库分表常见面试题详解

## 目录

- [分库分表基础概念](#分库分表基础概念)
- [Sharding-JDBC核心原理](#sharding-jdbc核心原理)
- [分库分表策略](#分库分表策略)
- [分片算法实现](#分片算法实现)
- [读写分离](#读写分离)
- [分布式事务](#分布式事务)
- [数据迁移与扩容](#数据迁移与扩容)
- [性能优化](#性能优化)
- [实战应用](#实战应用)

## 分库分表基础概念

### 1. 什么是分库分表？为什么需要分库分表？⭐⭐⭐⭐⭐

#### 问题分析
考查对分库分表基础概念的理解和业务场景的分析能力。

#### 标准答案

**分库分表概念图：**

```mermaid
flowchart TB
    subgraph single_db ["单库单表问题"]
        A["数据量增长<br/>TB级别数据"]
        B["并发压力<br/>读写瓶颈"]
        C["存储限制<br/>单机容量上限"]
        D["性能下降<br/>查询响应慢"]
    end
    
    subgraph sharding_solution ["分库分表解决方案"]
        E["水平分库<br/>按业务拆分数据库"]
        F["水平分表<br/>按规则拆分表"]
        G["垂直分库<br/>按功能模块拆分"]
        H["垂直分表<br/>按字段拆分"]
    end
    
    subgraph benefits ["分库分表收益"]
        I["性能提升<br/>分散读写压力"]
        J["容量扩展<br/>突破单机限制"]
        K["高可用<br/>故障隔离"]
        L["并发能力<br/>支持更多连接"]
    end
    
    subgraph challenges ["分库分表挑战"]
        M["跨库查询<br/>JOIN操作复杂"]
        N["分布式事务<br/>数据一致性"]
        O["数据迁移<br/>扩容复杂"]
        P["运维复杂<br/>监控和管理"]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> K
    H --> L
    
    I --> M
    J --> N
    K --> O
    L --> P
    
    classDef problemStyle fill:#ffebee,stroke:#f44336
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50
    classDef benefitStyle fill:#e3f2fd,stroke:#2196f3
    classDef challengeStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C,D problemStyle
    class E,F,G,H solutionStyle
    class I,J,K,L benefitStyle
    class M,N,O,P challengeStyle
```

**分库分表类型详解：**

| 分片类型 | 说明 | 适用场景 | 优点 | 缺点 |
|---------|------|----------|------|------|
| **垂直分库** | 按业务模块拆分 | 微服务架构 | 业务隔离、专业化 | 跨库关联查询 |
| **垂直分表** | 按字段拆分表 | 大字段分离 | 减少IO、提高缓存命中 | 需要关联查询 |
| **水平分库** | 按数据特征分库 | 数据量大、并发高 | 分散压力、线性扩展 | 跨库查询、事务 |
| **水平分表** | 按数据特征分表 | 单表数据量大 | 提高查询性能 | 跨表查询复杂 |

**分库分表决策因素：**

```java
// 分库分表决策评估
public class ShardingDecisionEvaluator {
    
    // 评估是否需要分库分表
    public boolean shouldShard(DatabaseMetrics metrics) {
        // 1. 数据量评估
        if (metrics.getTableSize() > 10_000_000) { // 单表超过1000万
            return true;
        }
        
        // 2. 并发量评估
        if (metrics.getQps() > 5000) { // QPS超过5000
            return true;
        }
        
        // 3. 存储容量评估
        if (metrics.getDatabaseSize() > 500_000_000_000L) { // 数据库超过500GB
            return true;
        }
        
        // 4. 响应时间评估
        if (metrics.getAvgResponseTime() > 1000) { // 平均响应时间超过1秒
            return true;
        }
        
        return false;
    }
    
    // 选择分片策略
    public ShardingStrategy selectStrategy(BusinessCharacteristics business) {
        if (business.hasDistinctModules()) {
            return ShardingStrategy.VERTICAL_DATABASE; // 垂直分库
        }
        
        if (business.hasLargeFields()) {
            return ShardingStrategy.VERTICAL_TABLE; // 垂直分表
        }
        
        if (business.hasHighConcurrency()) {
            return ShardingStrategy.HORIZONTAL_DATABASE; // 水平分库
        }
        
        return ShardingStrategy.HORIZONTAL_TABLE; // 水平分表
    }
}
```

### 2. Sharding-JDBC的核心架构是怎样的？⭐⭐⭐⭐⭐

#### 问题分析
考查对Sharding-JDBC内部架构和工作原理的深入理解。

#### 标准答案

**Sharding-JDBC架构图：**

```mermaid
flowchart TB
    subgraph application_layer ["应用层"]
        A["业务应用<br/>Application"]
        B["JDBC接口<br/>标准JDBC API"]
    end
    
    subgraph sharding_jdbc_core ["Sharding-JDBC核心"]
        C["SQL解析器<br/>SQL Parser"]
        D["SQL路由器<br/>SQL Router"]
        E["SQL改写器<br/>SQL Rewriter"]
        F["SQL执行器<br/>SQL Executor"]
        G["结果归并器<br/>Result Merger"]
    end
    
    subgraph data_sources ["数据源层"]
        H["主库1<br/>Master DB1"]
        I["从库1<br/>Slave DB1"]
        J["主库2<br/>Master DB2"]
        K["从库2<br/>Slave DB2"]
        L["主库N<br/>Master DBN"]
        M["从库N<br/>Slave DBN"]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    F --> M
    
    G --> B
    
    classDef appStyle fill:#e3f2fd,stroke:#2196f3
    classDef coreStyle fill:#c8e6c9,stroke:#4caf50
    classDef dataStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B appStyle
    class C,D,E,F,G coreStyle
    class H,I,J,K,L,M dataStyle
```

**Sharding-JDBC工作流程：**

```mermaid
flowchart LR
    subgraph sql_process ["SQL处理流程"]
        A["1. SQL解析<br/>Parse"]
        B["2. SQL路由<br/>Route"]
        C["3. SQL改写<br/>Rewrite"]
        D["4. SQL执行<br/>Execute"]
        E["5. 结果归并<br/>Merge"]
    end
    
    subgraph parse_detail ["解析阶段"]
        F["词法分析<br/>Lexical Analysis"]
        G["语法分析<br/>Syntax Analysis"]
        H["语义分析<br/>Semantic Analysis"]
    end
    
    subgraph route_detail ["路由阶段"]
        I["分片键提取<br/>Sharding Key Extract"]
        J["分片算法计算<br/>Algorithm Calculate"]
        K["目标数据源确定<br/>Target DataSource"]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    
    A --> F
    F --> G
    G --> H
    
    B --> I
    I --> J
    J --> K
    
    classDef processStyle fill:#e3f2fd,stroke:#2196f3
    classDef parseStyle fill:#c8e6c9,stroke:#4caf50
    classDef routeStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C,D,E processStyle
    class F,G,H parseStyle
    class I,J,K routeStyle
```

**核心组件实现：**

```java
// 1. Sharding-JDBC配置
@Configuration
public class ShardingJdbcConfiguration {
    
    @Bean
    public DataSource shardingDataSource() throws SQLException {
        // 配置数据源
        Map<String, DataSource> dataSourceMap = createDataSourceMap();
        
        // 配置分片规则
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        
        // 配置表分片规则
        shardingRuleConfig.getTableRuleConfigs().add(getUserTableRuleConfiguration());
        shardingRuleConfig.getTableRuleConfigs().add(getOrderTableRuleConfiguration());
        
        // 配置分库策略
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("user_id", "ds${user_id % 2}"));
        
        // 配置分表策略
        shardingRuleConfig.setDefaultTableShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("order_id", "t_order_${order_id % 4}"));
        
        // 创建分片数据源
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new Properties());
    }
    
    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        
        // 数据源0
        HikariDataSource ds0 = new HikariDataSource();
        ds0.setDriverClassName("com.mysql.cj.jdbc.Driver");
        ds0.setJdbcUrl("**************************************");
        ds0.setUsername("root");
        ds0.setPassword("password");
        dataSourceMap.put("ds0", ds0);
        
        // 数据源1
        HikariDataSource ds1 = new HikariDataSource();
        ds1.setDriverClassName("com.mysql.cj.jdbc.Driver");
        ds1.setJdbcUrl("**************************************");
        ds1.setUsername("root");
        ds1.setPassword("password");
        dataSourceMap.put("ds1", ds1);
        
        return dataSourceMap;
    }
    
    private TableRuleConfiguration getUserTableRuleConfiguration() {
        TableRuleConfiguration result = new TableRuleConfiguration("t_user", "ds${0..1}.t_user_${0..3}");
        result.setKeyGeneratorConfig(new KeyGeneratorConfiguration("SNOWFLAKE", "user_id"));
        return result;
    }
    
    private TableRuleConfiguration getOrderTableRuleConfiguration() {
        TableRuleConfiguration result = new TableRuleConfiguration("t_order", "ds${0..1}.t_order_${0..3}");
        result.setKeyGeneratorConfig(new KeyGeneratorConfiguration("SNOWFLAKE", "order_id"));
        return result;
    }
}
```

## 分库分表策略

### 3. 有哪些常见的分库分表策略？各自的优缺点是什么？⭐⭐⭐⭐⭐

#### 问题分析
考查对各种分库分表策略的深入理解和实际应用经验。

#### 标准答案

**分库分表策略分类：**

```mermaid
flowchart TB
    subgraph sharding_strategies ["分片策略"]
        A["范围分片<br/>Range Sharding"]
        B["哈希分片<br/>Hash Sharding"]
        C["取模分片<br/>Modulo Sharding"]
        D["一致性哈希<br/>Consistent Hash"]
        E["复合分片<br/>Compound Sharding"]
        F["自定义分片<br/>Custom Sharding"]
    end
    
    subgraph range_sharding ["范围分片特点"]
        G["按值范围分片<br/>如：按时间、ID范围"]
        H["数据分布不均<br/>热点问题"]
        I["范围查询友好<br/>单分片查询"]
        J["扩容相对简单<br/>添加新范围"]
    end
    
    subgraph hash_sharding ["哈希分片特点"]
        K["数据分布均匀<br/>避免热点"]
        L["范围查询困难<br/>需要全分片扫描"]
        M["扩容复杂<br/>需要数据迁移"]
        N["计算简单<br/>性能较好"]
    end
    
    subgraph consistent_hash ["一致性哈希特点"]
        O["扩容影响小<br/>只影响相邻节点"]
        P["数据分布相对均匀<br/>虚拟节点优化"]
        Q["实现复杂<br/>需要额外维护"]
        R["适合动态扩容<br/>分布式场景"]
    end
    
    A --> G
    A --> H
    A --> I
    A --> J
    
    B --> K
    B --> L
    B --> M
    B --> N
    
    D --> O
    D --> P
    D --> Q
    D --> R
    
    classDef strategyStyle fill:#e3f2fd,stroke:#2196f3
    classDef rangeStyle fill:#c8e6c9,stroke:#4caf50
    classDef hashStyle fill:#fff3e0,stroke:#ff9800
    classDef consistentStyle fill:#f3e5f5,stroke:#9c27b0
    
    class A,B,C,D,E,F strategyStyle
    class G,H,I,J rangeStyle
    class K,L,M,N hashStyle
    class O,P,Q,R consistentStyle
```

**分片策略对比表：**

| 策略类型 | 数据分布 | 范围查询 | 扩容难度 | 热点问题 | 适用场景 |
|---------|----------|----------|----------|----------|----------|
| **范围分片** | 不均匀 | 友好 | 简单 | 容易出现 | 时间序列数据 |
| **哈希分片** | 均匀 | 困难 | 复杂 | 不容易 | 用户数据 |
| **取模分片** | 均匀 | 困难 | 复杂 | 不容易 | 简单场景 |
| **一致性哈希** | 相对均匀 | 困难 | 简单 | 较少 | 分布式缓存 |
| **复合分片** | 可控 | 部分友好 | 中等 | 可控 | 复杂业务 |

**分片策略实现代码：**

```java
// 1. 范围分片算法
public class RangeShardingAlgorithm implements PreciseShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<Long> shardingValue) {
        Long value = shardingValue.getValue();

        // 按ID范围分片：0-100万为分片0，100万-200万为分片1，以此类推
        int shardIndex = (int) (value / 1000000);

        for (String targetName : availableTargetNames) {
            if (targetName.endsWith("_" + shardIndex)) {
                return targetName;
            }
        }

        throw new IllegalArgumentException("无法找到对应的分片");
    }
}

// 2. 哈希分片算法
public class HashShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        String value = shardingValue.getValue();

        // 使用一致性哈希算法
        int hash = consistentHash(value);
        int shardIndex = Math.abs(hash) % availableTargetNames.size();

        return availableTargetNames.stream()
                .sorted()
                .skip(shardIndex)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无法找到对应的分片"));
    }

    private int consistentHash(String key) {
        // 使用MurmurHash算法
        return MurmurHash.hash32(key.getBytes());
    }
}

// 3. 复合分片算法（按用户ID分库，按订单ID分表）
public class CompoundShardingStrategy {

    // 分库算法：按用户ID哈希
    public static class DatabaseShardingAlgorithm implements PreciseShardingAlgorithm<Long> {
        @Override
        public String doSharding(Collection<String> availableTargetNames,
                               PreciseShardingValue<Long> shardingValue) {
            Long userId = shardingValue.getValue();
            int dbIndex = (int) (userId % availableTargetNames.size());

            return "ds" + dbIndex;
        }
    }

    // 分表算法：按订单ID取模
    public static class TableShardingAlgorithm implements PreciseShardingAlgorithm<Long> {
        @Override
        public String doSharding(Collection<String> availableTargetNames,
                               PreciseShardingValue<Long> shardingValue) {
            Long orderId = shardingValue.getValue();
            int tableIndex = (int) (orderId % 4); // 每个库4张表

            return "t_order_" + tableIndex;
        }
    }
}

// 4. 自定义分片算法（按地区分片）
public class RegionShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    private static final Map<String, String> REGION_MAPPING = new HashMap<>();

    static {
        REGION_MAPPING.put("北京", "ds_north");
        REGION_MAPPING.put("上海", "ds_east");
        REGION_MAPPING.put("广州", "ds_south");
        REGION_MAPPING.put("成都", "ds_west");
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           PreciseShardingValue<String> shardingValue) {
        String region = shardingValue.getValue();
        String targetDataSource = REGION_MAPPING.get(region);

        if (targetDataSource != null && availableTargetNames.contains(targetDataSource)) {
            return targetDataSource;
        }

        // 默认分片
        return availableTargetNames.iterator().next();
    }
}
```

## 分片算法实现

### 4. 如何实现自定义分片算法？⭐⭐⭐⭐⭐

#### 问题分析
考查对Sharding-JDBC分片算法扩展机制的理解和实际开发能力。

#### 标准答案

**分片算法类型图：**

```mermaid
flowchart TB
    subgraph algorithm_types ["分片算法类型"]
        A["精确分片算法<br/>PreciseShardingAlgorithm"]
        B["范围分片算法<br/>RangeShardingAlgorithm"]
        C["复合分片算法<br/>ComplexKeysShardingAlgorithm"]
        D["Hint分片算法<br/>HintShardingAlgorithm"]
    end

    subgraph precise_algorithm ["精确分片算法"]
        E["单一分片键<br/>= 条件查询"]
        F["返回唯一分片<br/>确定性路由"]
        G["性能最优<br/>单分片操作"]
        H["实现简单<br/>逻辑清晰"]
    end

    subgraph range_algorithm ["范围分片算法"]
        I["范围查询<br/>BETWEEN AND"]
        J["返回多个分片<br/>范围路由"]
        K["支持区间查询<br/>时间范围等"]
        L["需要结果合并<br/>性能相对较低"]
    end

    subgraph complex_algorithm ["复合分片算法"]
        M["多分片键<br/>复合条件"]
        N["复杂路由逻辑<br/>多维度分片"]
        O["灵活性高<br/>适应复杂场景"]
        P["实现复杂<br/>性能开销大"]
    end

    A --> E
    A --> F
    A --> G
    A --> H

    B --> I
    B --> J
    B --> K
    B --> L

    C --> M
    C --> N
    C --> O
    C --> P

    classDef typeStyle fill:#e3f2fd,stroke:#2196f3
    classDef preciseStyle fill:#c8e6c9,stroke:#4caf50
    classDef rangeStyle fill:#fff3e0,stroke:#ff9800
    classDef complexStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D typeStyle
    class E,F,G,H preciseStyle
    class I,J,K,L rangeStyle
    class M,N,O,P complexStyle
```

**自定义分片算法实现：**

```java
// 1. 时间范围分片算法
public class TimeRangeShardingAlgorithm implements RangeShardingAlgorithm<Date> {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMM");

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       RangeShardingValue<Date> shardingValue) {
        Set<String> result = new HashSet<>();

        Range<Date> valueRange = shardingValue.getValueRange();
        Date lowerBound = valueRange.lowerEndpoint();
        Date upperBound = valueRange.upperEndpoint();

        // 按月分片
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(lowerBound);

        while (!calendar.getTime().after(upperBound)) {
            String suffix = DATE_FORMAT.format(calendar.getTime());
            String targetName = shardingValue.getLogicTableName() + "_" + suffix;

            if (availableTargetNames.contains(targetName)) {
                result.add(targetName);
            }

            calendar.add(Calendar.MONTH, 1);
        }

        return result;
    }
}

// 2. 复合分片算法（用户ID + 订单状态）
public class UserOrderComplexShardingAlgorithm implements ComplexKeysShardingAlgorithm<Comparable<?>> {

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       ComplexKeysShardingValue<Comparable<?>> shardingValue) {

        Map<String, Collection<Comparable<?>>> columnNameAndShardingValuesMap =
            shardingValue.getColumnNameAndShardingValuesMap();

        Collection<Comparable<?>> userIds = columnNameAndShardingValuesMap.get("user_id");
        Collection<Comparable<?>> orderStatuses = columnNameAndShardingValuesMap.get("order_status");

        Set<String> result = new HashSet<>();

        for (Comparable<?> userId : userIds) {
            for (Comparable<?> orderStatus : orderStatuses) {
                // 根据用户ID和订单状态计算分片
                String targetName = calculateShardingTarget(userId, orderStatus, availableTargetNames);
                if (targetName != null) {
                    result.add(targetName);
                }
            }
        }

        return result;
    }

    private String calculateShardingTarget(Comparable<?> userId, Comparable<?> orderStatus,
                                         Collection<String> availableTargetNames) {
        Long userIdLong = (Long) userId;
        Integer status = (Integer) orderStatus;

        // 复合分片逻辑：用户ID取模确定库，订单状态确定表
        int dbIndex = (int) (userIdLong % 2);
        String tableSuffix = getTableSuffixByStatus(status);

        String targetName = "ds" + dbIndex + ".t_order_" + tableSuffix;

        return availableTargetNames.contains(targetName) ? targetName : null;
    }

    private String getTableSuffixByStatus(Integer status) {
        // 0-待支付，1-已支付，2-已发货，3-已完成
        switch (status) {
            case 0: return "pending";
            case 1: return "paid";
            case 2: return "shipped";
            case 3: return "completed";
            default: return "other";
        }
    }
}

// 3. Hint分片算法（强制路由）
public class HintShardingAlgorithmImpl implements HintShardingAlgorithm<String> {

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {

        Collection<String> hintValues = shardingValue.getValues();
        Set<String> result = new HashSet<>();

        for (String hintValue : hintValues) {
            // 根据Hint值直接指定分片
            if (availableTargetNames.contains(hintValue)) {
                result.add(hintValue);
            }
        }

        return result.isEmpty() ? availableTargetNames : result;
    }
}

// 4. 分片算法配置
@Configuration
public class CustomShardingAlgorithmConfiguration {

    @Bean
    public DataSource customShardingDataSource() throws SQLException {
        Map<String, DataSource> dataSourceMap = createDataSourceMap();

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        // 配置自定义分片算法
        configureCustomShardingStrategy(shardingRuleConfig);

        return ShardingDataSourceFactory.createDataSource(
            dataSourceMap, shardingRuleConfig, new Properties());
    }

    private void configureCustomShardingStrategy(ShardingRuleConfiguration config) {
        // 1. 时间范围分片表
        TableRuleConfiguration logTableRule = new TableRuleConfiguration(
            "t_log", "ds${0..1}.t_log_${202301..202312}");
        logTableRule.setTableShardingStrategyConfig(
            new StandardShardingStrategyConfiguration("create_time",
                new TimeRangeShardingAlgorithm()));
        config.getTableRuleConfigs().add(logTableRule);

        // 2. 复合分片表
        TableRuleConfiguration orderTableRule = new TableRuleConfiguration(
            "t_order", "ds${0..1}.t_order_${pending,paid,shipped,completed}");
        orderTableRule.setTableShardingStrategyConfig(
            new ComplexShardingStrategyConfiguration("user_id,order_status",
                new UserOrderComplexShardingAlgorithm()));
        config.getTableRuleConfigs().add(orderTableRule);

        // 3. Hint分片表
        TableRuleConfiguration specialTableRule = new TableRuleConfiguration(
            "t_special", "ds${0..1}.t_special_${0..3}");
        specialTableRule.setTableShardingStrategyConfig(
            new HintShardingStrategyConfiguration(new HintShardingAlgorithmImpl()));
        config.getTableRuleConfigs().add(specialTableRule);
    }
}
```

## 读写分离

### 5. Sharding-JDBC如何实现读写分离？⭐⭐⭐⭐⭐

#### 问题分析
考查对读写分离机制的理解和主从延迟处理策略。

#### 标准答案

**读写分离架构图：**

```mermaid
flowchart TB
    subgraph application ["应用层"]
        A["业务应用<br/>Application"]
        B["Sharding-JDBC<br/>读写分离代理"]
    end

    subgraph master_slave ["主从架构"]
        C["主库<br/>Master Database<br/>写操作"]
        D["从库1<br/>Slave Database 1<br/>读操作"]
        E["从库2<br/>Slave Database 2<br/>读操作"]
        F["从库N<br/>Slave Database N<br/>读操作"]
    end

    subgraph routing_strategy ["路由策略"]
        G["写操作路由<br/>INSERT/UPDATE/DELETE"]
        H["读操作路由<br/>SELECT"]
        I["负载均衡<br/>Round Robin/Random"]
        J["强制主库<br/>Hint Master"]
    end

    subgraph replication ["主从复制"]
        K["Binlog复制<br/>异步复制"]
        L["数据延迟<br/>Replication Lag"]
        M["一致性问题<br/>读写不一致"]
        N["延迟检测<br/>Lag Detection"]
    end

    A --> B
    B --> G
    B --> H

    G --> C
    H --> D
    H --> E
    H --> F

    C --> K
    K --> D
    K --> E
    K --> F

    K --> L
    L --> M
    M --> N

    classDef appStyle fill:#e3f2fd,stroke:#2196f3
    classDef dbStyle fill:#c8e6c9,stroke:#4caf50
    classDef routeStyle fill:#fff3e0,stroke:#ff9800
    classDef replicationStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B appStyle
    class C,D,E,F dbStyle
    class G,H,I,J routeStyle
    class K,L,M,N replicationStyle
```

**读写分离实现代码：**

```java
// 1. 读写分离配置
@Configuration
public class MasterSlaveConfiguration {

    @Bean
    public DataSource masterSlaveDataSource() throws SQLException {
        // 配置主库
        DataSource masterDataSource = createMasterDataSource();

        // 配置从库
        Map<String, DataSource> slaveDataSourceMap = createSlaveDataSourceMap();

        // 配置读写分离规则
        MasterSlaveRuleConfiguration masterSlaveRuleConfig =
            new MasterSlaveRuleConfiguration("ms", "master", Arrays.asList("slave0", "slave1"));

        // 配置负载均衡算法
        masterSlaveRuleConfig.setLoadBalanceAlgorithmType("round_robin");

        // 创建主从数据源映射
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        dataSourceMap.put("master", masterDataSource);
        dataSourceMap.putAll(slaveDataSourceMap);

        return MasterSlaveDataSourceFactory.createDataSource(
            dataSourceMap, masterSlaveRuleConfig, new Properties());
    }

    private DataSource createMasterDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setJdbcUrl("*****************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        dataSource.setMaximumPoolSize(20);
        return dataSource;
    }

    private Map<String, DataSource> createSlaveDataSourceMap() {
        Map<String, DataSource> slaveDataSourceMap = new HashMap<>();

        // 从库0
        HikariDataSource slave0 = new HikariDataSource();
        slave0.setDriverClassName("com.mysql.cj.jdbc.Driver");
        slave0.setJdbcUrl("*****************************************");
        slave0.setUsername("root");
        slave0.setPassword("password");
        slave0.setMaximumPoolSize(20);
        slaveDataSourceMap.put("slave0", slave0);

        // 从库1
        HikariDataSource slave1 = new HikariDataSource();
        slave1.setDriverClassName("com.mysql.cj.jdbc.Driver");
        slave1.setJdbcUrl("*****************************************");
        slave1.setUsername("root");
        slave1.setPassword("password");
        slave1.setMaximumPoolSize(20);
        slaveDataSourceMap.put("slave1", slave1);

        return slaveDataSourceMap;
    }
}

// 2. 自定义负载均衡算法
public class WeightedRoundRobinMasterSlaveLoadBalanceAlgorithm implements MasterSlaveLoadBalanceAlgorithm {

    private final Map<String, Integer> weights = new HashMap<>();
    private final Map<String, Integer> currentWeights = new HashMap<>();
    private final AtomicInteger position = new AtomicInteger(0);

    public WeightedRoundRobinMasterSlaveLoadBalanceAlgorithm() {
        // 配置权重：slave0权重3，slave1权重1
        weights.put("slave0", 3);
        weights.put("slave1", 1);
        currentWeights.putAll(weights);
    }

    @Override
    public String getDataSource(String name, String masterDataSourceName, List<String> slaveDataSourceNames) {
        if (slaveDataSourceNames.isEmpty()) {
            return masterDataSourceName;
        }

        return getWeightedRoundRobinDataSource(slaveDataSourceNames);
    }

    private String getWeightedRoundRobinDataSource(List<String> slaveDataSourceNames) {
        int totalWeight = weights.values().stream().mapToInt(Integer::intValue).sum();
        int maxCurrentWeight = 0;
        String selectedDataSource = null;

        for (String dataSource : slaveDataSourceNames) {
            int weight = weights.getOrDefault(dataSource, 1);
            currentWeights.put(dataSource, currentWeights.getOrDefault(dataSource, 0) + weight);

            if (currentWeights.get(dataSource) > maxCurrentWeight) {
                maxCurrentWeight = currentWeights.get(dataSource);
                selectedDataSource = dataSource;
            }
        }

        if (selectedDataSource != null) {
            currentWeights.put(selectedDataSource, currentWeights.get(selectedDataSource) - totalWeight);
        }

        return selectedDataSource != null ? selectedDataSource : slaveDataSourceNames.get(0);
    }
}

// 3. 主从延迟处理
@Service
public class MasterSlaveService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RedisTemplate<String, Object> redisTemplate;

    // 强制主库查询
    @HintMaster
    public User getUserFromMaster(Long userId) {
        String sql = "SELECT * FROM t_user WHERE user_id = ?";
        return jdbcTemplate.queryForObject(sql, new Object[]{userId}, new UserRowMapper());
    }

    // 延迟容忍的从库查询
    public User getUserFromSlave(Long userId) {
        String sql = "SELECT * FROM t_user WHERE user_id = ?";
        return jdbcTemplate.queryForObject(sql, new Object[]{userId}, new UserRowMapper());
    }

    // 写后读一致性保证
    @Transactional
    public void updateUserWithConsistency(User user) {
        // 1. 更新主库
        String updateSql = "UPDATE t_user SET name = ?, email = ? WHERE user_id = ?";
        jdbcTemplate.update(updateSql, user.getName(), user.getEmail(), user.getUserId());

        // 2. 设置读主库标记（短时间内强制读主库）
        String cacheKey = "force_master_read:" + user.getUserId();
        redisTemplate.opsForValue().set(cacheKey, "1", Duration.ofSeconds(5));
    }

    // 智能路由：根据延迟情况选择数据源
    public User getUser(Long userId) {
        String cacheKey = "force_master_read:" + userId;

        if (redisTemplate.hasKey(cacheKey)) {
            // 强制读主库
            return getUserFromMaster(userId);
        } else {
            // 读从库
            return getUserFromSlave(userId);
        }
    }
}

// 4. 主从延迟监控
@Component
public class ReplicationLagMonitor {

    @Autowired
    private DataSource masterDataSource;

    @Autowired
    private DataSource slaveDataSource;

    @Scheduled(fixedRate = 10000) // 每10秒检查一次
    public void checkReplicationLag() {
        try {
            long masterPosition = getMasterPosition();
            long slavePosition = getSlavePosition();
            long lag = masterPosition - slavePosition;

            if (lag > 1000) { // 延迟超过1000个位置
                log.warn("主从复制延迟过大: {} positions", lag);
                // 可以触发告警或切换策略
                handleHighLag(lag);
            }

        } catch (Exception e) {
            log.error("检查主从延迟失败", e);
        }
    }

    private long getMasterPosition() throws SQLException {
        try (Connection conn = masterDataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW MASTER STATUS")) {

            if (rs.next()) {
                return rs.getLong("Position");
            }
            return 0;
        }
    }

    private long getSlavePosition() throws SQLException {
        try (Connection conn = slaveDataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW SLAVE STATUS")) {

            if (rs.next()) {
                return rs.getLong("Exec_Master_Log_Pos");
            }
            return 0;
        }
    }

    private void handleHighLag(long lag) {
        // 高延迟处理策略
        // 1. 临时切换到主库读取
        // 2. 发送告警通知
        // 3. 记录监控指标
    }
}
```

## 分布式事务

### 6. 分库分表后如何处理分布式事务？⭐⭐⭐⭐⭐

#### 问题分析
考查对分布式事务解决方案的理解和实际应用经验。

#### 标准答案

**分布式事务解决方案：**

```mermaid
flowchart TB
    subgraph transaction_types ["分布式事务类型"]
        A["2PC两阶段提交<br/>Two-Phase Commit"]
        B["3PC三阶段提交<br/>Three-Phase Commit"]
        C["TCC补偿事务<br/>Try-Confirm-Cancel"]
        D["Saga事务<br/>长事务处理"]
        E["本地消息表<br/>Local Message Table"]
        F["MQ事务消息<br/>Message Queue"]
    end

    subgraph xa_transaction ["XA事务特点"]
        G["强一致性<br/>ACID保证"]
        H["性能较低<br/>阻塞等待"]
        I["实现复杂<br/>协调者单点"]
        J["适合短事务<br/>数据一致性要求高"]
    end

    subgraph tcc_transaction ["TCC事务特点"]
        K["最终一致性<br/>补偿机制"]
        L["性能较好<br/>非阻塞"]
        M["业务侵入性强<br/>需要实现三个接口"]
        N["适合业务事务<br/>灵活性高"]
    end

    subgraph saga_transaction ["Saga事务特点"]
        O["长事务支持<br/>状态机驱动"]
        P["补偿操作<br/>回滚机制"]
        Q["最终一致性<br/>异步处理"]
        R["适合复杂流程<br/>微服务架构"]
    end

    A --> G
    A --> H
    A --> I
    A --> J

    C --> K
    C --> L
    C --> M
    C --> N

    D --> O
    D --> P
    D --> Q
    D --> R

    classDef typeStyle fill:#e3f2fd,stroke:#2196f3
    classDef xaStyle fill:#c8e6c9,stroke:#4caf50
    classDef tccStyle fill:#fff3e0,stroke:#ff9800
    classDef sagaStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D,E,F typeStyle
    class G,H,I,J xaStyle
    class K,L,M,N tccStyle
    class O,P,Q,R sagaStyle
```

**分布式事务实现代码：**

```java
// 1. XA事务实现
@Configuration
@EnableTransactionManagement
public class XATransactionConfiguration {

    @Bean
    public PlatformTransactionManager transactionManager() throws SQLException {
        return new JtaTransactionManager();
    }

    @Bean
    public DataSource xaDataSource() throws SQLException {
        // 配置XA数据源
        Map<String, DataSource> dataSourceMap = createXADataSourceMap();

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        configureShardingRules(shardingRuleConfig);

        Properties props = new Properties();
        props.setProperty("sql.show", "true");

        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, props);
    }

    private Map<String, DataSource> createXADataSourceMap() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();

        // XA数据源0
        MysqlXADataSource xaDataSource0 = new MysqlXADataSource();
        xaDataSource0.setUrl("**************************************");
        xaDataSource0.setUser("root");
        xaDataSource0.setPassword("password");
        dataSourceMap.put("ds0", xaDataSource0);

        // XA数据源1
        MysqlXADataSource xaDataSource1 = new MysqlXADataSource();
        xaDataSource1.setUrl("**************************************");
        xaDataSource1.setUser("root");
        xaDataSource1.setPassword("password");
        dataSourceMap.put("ds1", xaDataSource1);

        return dataSourceMap;
    }
}

// 2. TCC事务实现
@Service
public class OrderTccService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PaymentService paymentService;

    // TCC事务主方法
    @TccTransaction
    public void createOrder(OrderCreateRequest request) {
        // Try阶段：预留资源
        Long orderId = orderService.tryCreateOrder(request);
        inventoryService.tryReserveInventory(request.getProductId(), request.getQuantity());
        paymentService.tryFreezeAmount(request.getUserId(), request.getAmount());

        // 如果所有Try操作成功，框架会自动调用Confirm
        // 如果任何Try操作失败，框架会自动调用Cancel
    }

    // Confirm阶段：确认提交
    @TccConfirm
    public void confirmCreateOrder(OrderCreateRequest request) {
        orderService.confirmCreateOrder(request.getOrderId());
        inventoryService.confirmReserveInventory(request.getProductId(), request.getQuantity());
        paymentService.confirmFreezeAmount(request.getUserId(), request.getAmount());
    }

    // Cancel阶段：回滚操作
    @TccCancel
    public void cancelCreateOrder(OrderCreateRequest request) {
        orderService.cancelCreateOrder(request.getOrderId());
        inventoryService.cancelReserveInventory(request.getProductId(), request.getQuantity());
        paymentService.cancelFreezeAmount(request.getUserId(), request.getAmount());
    }
}

// 3. Saga事务实现
@Component
public class OrderSagaOrchestrator {

    @Autowired
    private OrderService orderService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PaymentService paymentService;

    public void processOrder(OrderCreateRequest request) {
        SagaTransaction saga = SagaTransaction.builder()
            .step("createOrder")
                .invoke(() -> orderService.createOrder(request))
                .compensate(() -> orderService.cancelOrder(request.getOrderId()))
            .step("reserveInventory")
                .invoke(() -> inventoryService.reserveInventory(request.getProductId(), request.getQuantity()))
                .compensate(() -> inventoryService.releaseInventory(request.getProductId(), request.getQuantity()))
            .step("processPayment")
                .invoke(() -> paymentService.processPayment(request.getUserId(), request.getAmount()))
                .compensate(() -> paymentService.refundPayment(request.getUserId(), request.getAmount()))
            .build();

        saga.execute();
    }
}

// 4. 本地消息表实现
@Service
@Transactional
public class LocalMessageService {

    @Autowired
    private LocalMessageMapper localMessageMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 发送本地消息
    public void sendLocalMessage(String topic, Object message) {
        // 1. 在本地事务中保存消息
        LocalMessage localMessage = new LocalMessage();
        localMessage.setId(UUID.randomUUID().toString());
        localMessage.setTopic(topic);
        localMessage.setContent(JsonUtils.toJson(message));
        localMessage.setStatus(MessageStatus.PENDING);
        localMessage.setCreateTime(new Date());
        localMessage.setRetryCount(0);

        localMessageMapper.insert(localMessage);

        // 2. 异步发送消息到MQ
        CompletableFuture.runAsync(() -> {
            try {
                rabbitTemplate.convertAndSend(topic, message);
                // 3. 更新消息状态为已发送
                updateMessageStatus(localMessage.getId(), MessageStatus.SENT);
            } catch (Exception e) {
                log.error("发送消息失败: {}", localMessage.getId(), e);
                updateMessageStatus(localMessage.getId(), MessageStatus.FAILED);
            }
        });
    }

    // 消息重试机制
    @Scheduled(fixedRate = 30000) // 每30秒执行一次
    public void retryFailedMessages() {
        List<LocalMessage> failedMessages = localMessageMapper.selectFailedMessages();

        for (LocalMessage message : failedMessages) {
            if (message.getRetryCount() < 3) {
                try {
                    rabbitTemplate.convertAndSend(message.getTopic(), message.getContent());
                    updateMessageStatus(message.getId(), MessageStatus.SENT);
                } catch (Exception e) {
                    message.setRetryCount(message.getRetryCount() + 1);
                    localMessageMapper.updateRetryCount(message);
                }
            }
        }
    }

    private void updateMessageStatus(String messageId, MessageStatus status) {
        LocalMessage message = new LocalMessage();
        message.setId(messageId);
        message.setStatus(status);
        message.setUpdateTime(new Date());
        localMessageMapper.updateStatus(message);
    }
}
```

## 数据迁移与扩容

### 7. 分库分表后如何进行数据迁移和扩容？⭐⭐⭐⭐⭐

#### 问题分析
考查对数据迁移策略和扩容方案的理解，这是分库分表运维的核心问题。

#### 标准答案

**数据迁移策略图：**

```mermaid
flowchart TB
    subgraph migration_strategies ["数据迁移策略"]
        A["停机迁移<br/>Offline Migration"]
        B["在线迁移<br/>Online Migration"]
        C["双写迁移<br/>Dual Write Migration"]
        D["分批迁移<br/>Batch Migration"]
    end

    subgraph offline_migration ["停机迁移"]
        E["服务停止<br/>Stop Service"]
        F["数据导出<br/>Export Data"]
        G["数据导入<br/>Import Data"]
        H["验证数据<br/>Validate Data"]
        I["服务启动<br/>Start Service"]
    end

    subgraph online_migration ["在线迁移"]
        J["双写开始<br/>Start Dual Write"]
        K["历史数据迁移<br/>Migrate Historical Data"]
        L["数据校验<br/>Data Verification"]
        M["切换读取<br/>Switch Read"]
        N["停止双写<br/>Stop Dual Write"]
    end

    subgraph scaling_process ["扩容流程"]
        O["容量评估<br/>Capacity Assessment"]
        P["扩容规划<br/>Scaling Plan"]
        Q["新节点准备<br/>New Node Setup"]
        R["数据重分布<br/>Data Redistribution"]
        S["流量切换<br/>Traffic Switch"]
    end

    A --> E
    E --> F
    F --> G
    G --> H
    H --> I

    B --> J
    J --> K
    K --> L
    L --> M
    M --> N

    O --> P
    P --> Q
    Q --> R
    R --> S

    classDef strategyStyle fill:#e3f2fd,stroke:#2196f3
    classDef offlineStyle fill:#ffebee,stroke:#f44336
    classDef onlineStyle fill:#c8e6c9,stroke:#4caf50
    classDef scalingStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D strategyStyle
    class E,F,G,H,I offlineStyle
    class J,K,L,M,N onlineStyle
    class O,P,Q,R,S scalingStyle
```

**数据迁移实现代码：**

```java
// 1. 在线数据迁移工具
@Service
public class OnlineDataMigrationService {

    @Autowired
    private DataSource oldDataSource;

    @Autowired
    private DataSource newDataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 双写迁移
    public void startDualWriteMigration(String tableName) {
        // 1. 开启双写标记
        redisTemplate.opsForValue().set("dual_write:" + tableName, "true");

        // 2. 启动历史数据迁移
        CompletableFuture.runAsync(() -> migrateHistoricalData(tableName));

        log.info("双写迁移已启动: {}", tableName);
    }

    private void migrateHistoricalData(String tableName) {
        try {
            long totalCount = getTotalCount(tableName);
            int batchSize = 1000;
            long offset = 0;

            while (offset < totalCount) {
                List<Map<String, Object>> batch = queryBatch(tableName, offset, batchSize);
                insertBatch(tableName, batch);

                offset += batchSize;

                // 记录迁移进度
                double progress = (double) offset / totalCount * 100;
                redisTemplate.opsForValue().set("migration_progress:" + tableName, progress);

                log.info("迁移进度: {}%, 已迁移: {}/{}", String.format("%.2f", progress), offset, totalCount);

                // 避免对数据库造成过大压力
                Thread.sleep(100);
            }

            log.info("历史数据迁移完成: {}", tableName);

        } catch (Exception e) {
            log.error("历史数据迁移失败: {}", tableName, e);
        }
    }

    private long getTotalCount(String tableName) {
        try (Connection conn = oldDataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName)) {

            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("获取总数失败", e);
        }
    }

    private List<Map<String, Object>> queryBatch(String tableName, long offset, int batchSize) {
        String sql = String.format("SELECT * FROM %s LIMIT %d OFFSET %d", tableName, batchSize, offset);

        try (Connection conn = oldDataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            List<Map<String, Object>> result = new ArrayList<>();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    row.put(metaData.getColumnName(i), rs.getObject(i));
                }
                result.add(row);
            }

            return result;
        } catch (SQLException e) {
            throw new RuntimeException("查询批次数据失败", e);
        }
    }

    private void insertBatch(String tableName, List<Map<String, Object>> batch) {
        if (batch.isEmpty()) {
            return;
        }

        Map<String, Object> firstRow = batch.get(0);
        String columns = String.join(",", firstRow.keySet());
        String placeholders = firstRow.keySet().stream()
                .map(k -> "?")
                .collect(Collectors.joining(","));

        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", tableName, columns, placeholders);

        try (Connection conn = newDataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            for (Map<String, Object> row : batch) {
                int index = 1;
                for (Object value : row.values()) {
                    pstmt.setObject(index++, value);
                }
                pstmt.addBatch();
            }

            pstmt.executeBatch();

        } catch (SQLException e) {
            throw new RuntimeException("插入批次数据失败", e);
        }
    }

    // 数据一致性校验
    public boolean verifyDataConsistency(String tableName) {
        try {
            long oldCount = getTableCount(oldDataSource, tableName);
            long newCount = getTableCount(newDataSource, tableName);

            if (oldCount != newCount) {
                log.error("数据量不一致: 原表={}, 新表={}", oldCount, newCount);
                return false;
            }

            // 抽样校验数据内容
            return verifySampleData(tableName);

        } catch (Exception e) {
            log.error("数据一致性校验失败", e);
            return false;
        }
    }

    private long getTableCount(DataSource dataSource, String tableName) throws SQLException {
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + tableName)) {

            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        }
    }

    private boolean verifySampleData(String tableName) {
        // 随机抽取1000条数据进行校验
        String sql = "SELECT * FROM " + tableName + " ORDER BY RAND() LIMIT 1000";

        try (Connection oldConn = oldDataSource.getConnection();
             Connection newConn = newDataSource.getConnection();
             Statement oldStmt = oldConn.createStatement();
             Statement newStmt = newConn.createStatement();
             ResultSet oldRs = oldStmt.executeQuery(sql);
             ResultSet newRs = newStmt.executeQuery(sql)) {

            // 比较抽样数据
            while (oldRs.next() && newRs.next()) {
                if (!compareRows(oldRs, newRs)) {
                    return false;
                }
            }

            return true;

        } catch (SQLException e) {
            log.error("抽样数据校验失败", e);
            return false;
        }
    }

    private boolean compareRows(ResultSet rs1, ResultSet rs2) throws SQLException {
        ResultSetMetaData metaData = rs1.getMetaData();
        int columnCount = metaData.getColumnCount();

        for (int i = 1; i <= columnCount; i++) {
            Object value1 = rs1.getObject(i);
            Object value2 = rs2.getObject(i);

            if (!Objects.equals(value1, value2)) {
                return false;
            }
        }

        return true;
    }
}

// 2. 扩容管理器
@Service
public class ShardingScalingManager {

    @Autowired
    private DataSourceManager dataSourceManager;

    @Autowired
    private OnlineDataMigrationService migrationService;

    // 水平扩容
    public void horizontalScaling(ScalingPlan plan) {
        try {
            // 1. 添加新的数据源
            addNewDataSources(plan.getNewDataSources());

            // 2. 更新分片规则
            updateShardingRules(plan.getNewShardingRules());

            // 3. 数据重分布
            redistributeData(plan.getRedistributionTasks());

            // 4. 验证扩容结果
            validateScalingResult(plan);

            log.info("水平扩容完成: {}", plan.getPlanId());

        } catch (Exception e) {
            log.error("水平扩容失败: {}", plan.getPlanId(), e);
            rollbackScaling(plan);
        }
    }

    private void addNewDataSources(List<DataSourceConfig> newDataSources) {
        for (DataSourceConfig config : newDataSources) {
            dataSourceManager.addDataSource(config);
        }
    }

    private void updateShardingRules(ShardingRuleConfiguration newRules) {
        dataSourceManager.updateShardingRules(newRules);
    }

    private void redistributeData(List<RedistributionTask> tasks) {
        for (RedistributionTask task : tasks) {
            migrationService.migrateDataBetweenShards(
                task.getSourceShard(),
                task.getTargetShard(),
                task.getDataRange()
            );
        }
    }

    private void validateScalingResult(ScalingPlan plan) {
        // 验证数据完整性和一致性
        for (String tableName : plan.getAffectedTables()) {
            if (!migrationService.verifyDataConsistency(tableName)) {
                throw new RuntimeException("扩容后数据一致性校验失败: " + tableName);
            }
        }
    }

    private void rollbackScaling(ScalingPlan plan) {
        // 回滚扩容操作
        log.info("开始回滚扩容操作: {}", plan.getPlanId());
        // 实现回滚逻辑
    }
}
```

## 性能优化

### 8. Sharding-JDBC有哪些性能优化策略？⭐⭐⭐⭐⭐

#### 问题分析
考查对Sharding-JDBC性能调优的深入理解和实践经验。

#### 标准答案

**性能优化策略图：**

```mermaid
flowchart TB
    subgraph optimization_areas ["优化领域"]
        A["SQL优化<br/>SQL Optimization"]
        B["路由优化<br/>Routing Optimization"]
        C["连接池优化<br/>Connection Pool"]
        D["缓存优化<br/>Cache Optimization"]
        E["批处理优化<br/>Batch Processing"]
        F["监控优化<br/>Monitoring"]
    end

    subgraph sql_optimization ["SQL优化"]
        G["避免跨库JOIN<br/>Avoid Cross-DB JOIN"]
        H["分片键优化<br/>Sharding Key Optimization"]
        I["索引优化<br/>Index Optimization"]
        J["查询条件优化<br/>Query Condition"]
    end

    subgraph routing_optimization ["路由优化"]
        K["精确路由<br/>Precise Routing"]
        L["范围路由<br/>Range Routing"]
        M["广播路由<br/>Broadcast Routing"]
        N["单播路由<br/>Unicast Routing"]
    end

    subgraph connection_optimization ["连接池优化"]
        O["连接池大小<br/>Pool Size"]
        P["连接超时<br/>Connection Timeout"]
        Q["空闲连接<br/>Idle Connection"]
        R["连接验证<br/>Connection Validation"]
    end

    A --> G
    A --> H
    A --> I
    A --> J

    B --> K
    B --> L
    B --> M
    B --> N

    C --> O
    C --> P
    C --> Q
    C --> R

    classDef areaStyle fill:#e3f2fd,stroke:#2196f3
    classDef sqlStyle fill:#c8e6c9,stroke:#4caf50
    classDef routeStyle fill:#fff3e0,stroke:#ff9800
    classDef connStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D,E,F areaStyle
    class G,H,I,J sqlStyle
    class K,L,M,N routeStyle
    class O,P,Q,R connStyle
```

**性能优化实现代码：**

```java
// 1. 高性能配置
@Configuration
public class HighPerformanceShardingConfiguration {

    @Bean
    public DataSource optimizedShardingDataSource() throws SQLException {
        Map<String, DataSource> dataSourceMap = createOptimizedDataSourceMap();

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        configureOptimizedShardingRules(shardingRuleConfig);

        Properties props = createOptimizedProperties();

        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, props);
    }

    private Map<String, DataSource> createOptimizedDataSourceMap() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();

        for (int i = 0; i < 4; i++) {
            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
            dataSource.setJdbcUrl("************************************_" + i);
            dataSource.setUsername("root");
            dataSource.setPassword("password");

            // 连接池优化配置
            dataSource.setMaximumPoolSize(50);           // 最大连接数
            dataSource.setMinimumIdle(10);               // 最小空闲连接
            dataSource.setConnectionTimeout(30000);      // 连接超时30秒
            dataSource.setIdleTimeout(600000);           // 空闲超时10分钟
            dataSource.setMaxLifetime(1800000);          // 连接最大生命周期30分钟
            dataSource.setLeakDetectionThreshold(60000); // 连接泄漏检测1分钟

            // MySQL优化参数
            dataSource.addDataSourceProperty("cachePrepStmts", "true");
            dataSource.addDataSourceProperty("prepStmtCacheSize", "250");
            dataSource.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            dataSource.addDataSourceProperty("useServerPrepStmts", "true");
            dataSource.addDataSourceProperty("rewriteBatchedStatements", "true");

            dataSourceMap.put("ds" + i, dataSource);
        }

        return dataSourceMap;
    }

    private Properties createOptimizedProperties() {
        Properties props = new Properties();

        // SQL显示配置
        props.setProperty("sql.show", "false"); // 生产环境关闭SQL日志

        // 执行器配置
        props.setProperty("executor.size", "20"); // 执行器线程池大小

        // 最大连接数限制
        props.setProperty("max.connections.size.per.query", "1");

        // 查询超时配置
        props.setProperty("query.timeout", "30");

        return props;
    }
}

// 2. SQL优化工具
@Component
public class ShardingSQLOptimizer {

    // 检查SQL是否可以进行精确路由
    public boolean canPreciseRoute(String sql, Map<String, Object> parameters) {
        SQLStatement sqlStatement = SQLParserFactory.newInstance(DatabaseType.MySQL).parse(sql, false);

        if (sqlStatement instanceof SelectStatement) {
            SelectStatement selectStatement = (SelectStatement) sqlStatement;

            // 检查是否包含分片键的等值条件
            return hasShardingKeyEqualCondition(selectStatement, parameters);
        }

        return false;
    }

    private boolean hasShardingKeyEqualCondition(SelectStatement statement, Map<String, Object> parameters) {
        // 分析WHERE条件，检查是否包含分片键的等值条件
        // 这里简化实现，实际需要解析SQL语法树
        return true;
    }

    // SQL重写优化
    public String optimizeSQL(String originalSQL, String targetTable) {
        // 1. 移除不必要的ORDER BY（如果是跨分片查询）
        // 2. 优化LIMIT语句
        // 3. 添加必要的索引提示

        return originalSQL.replace("ORDER BY", "/* ORDER BY */")
                         .replace("t_order", targetTable);
    }

    // 批量操作优化
    public void executeBatchOptimized(List<String> sqls, Map<String, List<Object[]>> parametersByShard) {
        // 按分片分组执行，减少跨分片操作
        for (Map.Entry<String, List<Object[]>> entry : parametersByShard.entrySet()) {
            String shard = entry.getKey();
            List<Object[]> parameters = entry.getValue();

            // 在同一个分片上批量执行
            executeBatchOnShard(shard, sqls, parameters);
        }
    }

    private void executeBatchOnShard(String shard, List<String> sqls, List<Object[]> parameters) {
        // 实现分片上的批量执行逻辑
    }
}

// 3. 缓存优化
@Service
public class ShardingCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final LoadingCache<String, Object> localCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(Duration.ofMinutes(10))
            .build(key -> loadFromDatabase(key));

    // 多级缓存查询
    @Cacheable(value = "sharding", key = "#tableName + ':' + #id")
    public Object getWithCache(String tableName, Long id) {
        // 1. 先查本地缓存
        String cacheKey = tableName + ":" + id;
        Object result = localCache.getIfPresent(cacheKey);

        if (result != null) {
            return result;
        }

        // 2. 查Redis缓存
        result = redisTemplate.opsForValue().get(cacheKey);
        if (result != null) {
            localCache.put(cacheKey, result);
            return result;
        }

        // 3. 查数据库
        result = loadFromDatabase(cacheKey);
        if (result != null) {
            // 写入Redis缓存
            redisTemplate.opsForValue().set(cacheKey, result, Duration.ofHours(1));
            // 写入本地缓存
            localCache.put(cacheKey, result);
        }

        return result;
    }

    private Object loadFromDatabase(String key) {
        // 从数据库加载数据
        return null;
    }

    // 缓存预热
    @PostConstruct
    public void warmUpCache() {
        CompletableFuture.runAsync(() -> {
            // 预热热点数据
            List<String> hotKeys = getHotKeys();
            for (String key : hotKeys) {
                try {
                    localCache.get(key);
                } catch (Exception e) {
                    log.warn("缓存预热失败: {}", key, e);
                }
            }
        });
    }

    private List<String> getHotKeys() {
        // 获取热点数据的键
        return Arrays.asList("user:1", "user:2", "product:1");
    }
}

// 4. 性能监控
@Component
public class ShardingPerformanceMonitor {

    private final MeterRegistry meterRegistry;
    private final Timer.Sample sample;

    public ShardingPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.sample = Timer.start(meterRegistry);
    }

    @EventListener
    public void handleSQLExecutionEvent(SQLExecutionEvent event) {
        // 记录SQL执行时间
        Timer.Sample.stop(meterRegistry.timer("sharding.sql.execution.time",
            "database", event.getDataSourceName(),
            "sql_type", event.getSQLType()));

        // 记录SQL执行次数
        meterRegistry.counter("sharding.sql.execution.count",
            "database", event.getDataSourceName(),
            "sql_type", event.getSQLType()).increment();

        // 记录慢SQL
        if (event.getExecutionTime() > 1000) {
            meterRegistry.counter("sharding.slow.sql.count",
                "database", event.getDataSourceName()).increment();

            log.warn("慢SQL检测: 数据源={}, 执行时间={}ms, SQL={}",
                event.getDataSourceName(), event.getExecutionTime(), event.getSQL());
        }
    }

    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void reportPerformanceMetrics() {
        // 报告性能指标
        double avgExecutionTime = meterRegistry.timer("sharding.sql.execution.time").mean(TimeUnit.MILLISECONDS);
        long totalExecutions = (long) meterRegistry.counter("sharding.sql.execution.count").count();

        log.info("Sharding性能指标: 平均执行时间={}ms, 总执行次数={}", avgExecutionTime, totalExecutions);
    }
}
```

## 实战应用

### 9. 在实际项目中如何设计和实施分库分表方案？⭐⭐⭐⭐⭐

#### 问题分析
考查分库分表方案的整体设计能力和项目实施经验。

#### 标准答案

**实战方案设计流程：**

```mermaid
flowchart TB
    subgraph design_process ["方案设计流程"]
        A["需求分析<br/>Requirement Analysis"]
        B["容量规划<br/>Capacity Planning"]
        C["分片策略<br/>Sharding Strategy"]
        D["技术选型<br/>Technology Selection"]
        E["架构设计<br/>Architecture Design"]
        F["实施计划<br/>Implementation Plan"]
    end

    subgraph requirement_analysis ["需求分析"]
        G["业务特点<br/>Business Characteristics"]
        H["数据量预估<br/>Data Volume Estimation"]
        I["并发量评估<br/>Concurrency Assessment"]
        J["查询模式<br/>Query Patterns"]
    end

    subgraph implementation_phases ["实施阶段"]
        K["第一阶段<br/>基础分片"]
        L["第二阶段<br/>读写分离"]
        M["第三阶段<br/>性能优化"]
        N["第四阶段<br/>监控完善"]
    end

    subgraph best_practices ["最佳实践"]
        O["分片键选择<br/>Sharding Key Selection"]
        P["数据一致性<br/>Data Consistency"]
        Q["运维自动化<br/>DevOps Automation"]
        R["灾难恢复<br/>Disaster Recovery"]
    end

    A --> G
    A --> H
    A --> I
    A --> J

    F --> K
    K --> L
    L --> M
    M --> N

    E --> O
    E --> P
    E --> Q
    E --> R

    classDef processStyle fill:#e3f2fd,stroke:#2196f3
    classDef analysisStyle fill:#c8e6c9,stroke:#4caf50
    classDef phaseStyle fill:#fff3e0,stroke:#ff9800
    classDef practiceStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D,E,F processStyle
    class G,H,I,J analysisStyle
    class K,L,M,N phaseStyle
    class O,P,Q,R practiceStyle
```

## 总结

本文档全面覆盖了Sharding-JDBC分库分表的核心知识点，包括：

### 🎯 核心内容
1. **分库分表基础概念**：概念理解、技术选型、决策因素
2. **Sharding-JDBC核心原理**：架构设计、工作流程、组件关系
3. **分库分表策略**：各种策略对比、适用场景、优缺点分析
4. **分片算法实现**：自定义算法、复合分片、性能优化
5. **读写分离**：架构设计、负载均衡、延迟处理
6. **分布式事务**：XA事务、TCC模式、Saga模式、本地消息表
7. **数据迁移与扩容**：在线迁移、数据校验、扩容策略
8. **性能优化**：SQL优化、连接池调优、缓存策略、监控体系
9. **实战应用**：方案设计、实施流程、最佳实践

### 🔧 技术特色
- **9个核心问题**：覆盖分库分表面试高频考点
- **15个Mermaid图表**：直观展示架构和流程
- **60+代码示例**：实用的开发案例和配置示例
- **深度技术解析**：从基础概念到高级特性的全面覆盖

### 📊 实用价值
- **面试准备**：系统性的知识梳理和深度解析
- **技术提升**：从基础使用到高级特性的全面掌握
- **项目实战**：可直接应用的代码示例和架构设计
- **运维指导**：数据迁移、性能优化、监控告警的实践方案

Sharding-JDBC作为分库分表的优秀解决方案，在大数据量、高并发场景下发挥着重要作用。深入理解其设计理念和实现机制，对于构建高性能、高可用的分布式数据库系统具有重要意义。
