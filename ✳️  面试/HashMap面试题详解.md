# HashMap面试题详解

> 本文档深入解析HashMap的底层实现原理、性能特点和线程安全问题，涵盖JDK1.7到JDK1.8的重要变化。

## 目录
- [时间复杂度分析](#时间复杂度分析)
- [底层实现原理](#底层实现原理)
- [容量为2的幂次的原因](#容量为2的幂次的原因)
- [线程安全问题](#线程安全问题)
- [性能优化](#性能优化)

---

## 时间复杂度分析

### 1. HashMap查询、删除的时间复杂度？⭐⭐⭐⭐

#### 问题分析
HashMap的时间复杂度与其底层数据结构密切相关，需要分情况讨论。

#### 标准答案

**HashMap时间复杂度分析：**

```mermaid
graph TB
    subgraph "理想情况 - 无哈希冲突"
        A[计算hash值] --> A1[O(1)]
        A1 --> A2[定位数组索引] 
        A2 --> A3[O(1)]
        A3 --> A4[直接访问]
        A4 --> A5[总复杂度: O(1)]
    end
    
    subgraph "哈希冲突 - 链表"
        B[计算hash值] --> B1[O(1)]
        B1 --> B2[定位数组索引]
        B2 --> B3[O(1)]
        B3 --> B4[遍历链表]
        B4 --> B5[O(n)]
        B5 --> B6[总复杂度: O(n)]
    end
    
    subgraph "哈希冲突 - 红黑树"
        C[计算hash值] --> C1[O(1)]
        C1 --> C2[定位数组索引]
        C2 --> C3[O(1)]
        C3 --> C4[红黑树查找]
        C4 --> C5[O(log n)]
        C5 --> C6[总复杂度: O(log n)]
    end
    
    style A5 fill:#c8e6c9
    style B6 fill:#ffcdd2
    style C6 fill:#fff3e0
```

#### 详细分析

| 情况 | 查询复杂度 | 插入复杂度 | 删除复杂度 | 说明 |
|------|-----------|-----------|-----------|------|
| **无冲突** | O(1) | O(1) | O(1) | 理想情况，直接定位 |
| **链表冲突** | O(n) | O(1) | O(n) | 需要遍历链表 |
| **红黑树冲突** | O(log n) | O(log n) | O(log n) | JDK1.8优化 |
| **平均情况** | O(1) | O(1) | O(1) | 负载因子控制在0.75 |

#### 代码示例
```java
public class HashMapComplexityDemo {
    
    // 演示不同情况下的时间复杂度
    public void demonstrateComplexity() {
        Map<String, Integer> map = new HashMap<>();
        
        // 1. 理想情况 - O(1)
        map.put("key1", 1);
        map.put("key2", 2);
        map.put("key3", 3);
        
        // 查询 - O(1)
        Integer value = map.get("key1");
        
        // 删除 - O(1)
        map.remove("key1");
        
        // 2. 哈希冲突情况演示
        Map<BadHashKey, Integer> conflictMap = new HashMap<>();
        
        // 故意制造哈希冲突
        for (int i = 0; i < 10; i++) {
            conflictMap.put(new BadHashKey(i), i);
        }
        
        // 此时查询可能是O(n)或O(log n)
        Integer conflictValue = conflictMap.get(new BadHashKey(5));
    }
    
    // 故意制造哈希冲突的Key
    static class BadHashKey {
        private int value;
        
        public BadHashKey(int value) {
            this.value = value;
        }
        
        @Override
        public int hashCode() {
            return 1;  // 所有对象都返回相同的hash值，制造冲突
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            BadHashKey that = (BadHashKey) obj;
            return value == that.value;
        }
    }
    
    // 性能测试
    public void performanceTest() {
        int size = 1000000;
        Map<Integer, Integer> map = new HashMap<>(size);
        
        // 插入测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            map.put(i, i);
        }
        long insertTime = System.currentTimeMillis() - start;
        
        // 查询测试
        start = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            map.get(i);
        }
        long queryTime = System.currentTimeMillis() - start;
        
        // 删除测试
        start = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            map.remove(i);
        }
        long deleteTime = System.currentTimeMillis() - start;
        
        System.out.println("插入" + size + "个元素耗时: " + insertTime + "ms");
        System.out.println("查询" + size + "个元素耗时: " + queryTime + "ms");
        System.out.println("删除" + size + "个元素耗时: " + deleteTime + "ms");
        
        // 平均每个操作的时间复杂度接近O(1)
        System.out.println("平均插入时间: " + (insertTime / (double) size) + "ms");
        System.out.println("平均查询时间: " + (queryTime / (double) size) + "ms");
        System.out.println("平均删除时间: " + (deleteTime / (double) size) + "ms");
    }
}
```

---

## 底层实现原理

### 2. HashMap底层实现原理？⭐⭐⭐⭐⭐

#### 问题分析
这是HashMap最核心的问题，需要详细说明JDK1.7和JDK1.8的实现差异。

#### 标准答案

**HashMap结构演进：**

```mermaid
graph TB
    subgraph "JDK 1.7 - 数组+链表"
        A[Entry数组] --> A1[Entry[0]]
        A --> A2[Entry[1]]
        A --> A3[Entry[2]]
        A --> A4[Entry[3]]
        
        A1 --> B1[key1,value1,next]
        B1 --> B2[key5,value5,next]
        B2 --> B3[key9,value9,null]
        
        A2 --> C1[key2,value2,null]
        A3 --> D1[key3,value3,next]
        D1 --> D2[key7,value7,null]
    end
    
    subgraph "JDK 1.8+ - 数组+链表+红黑树"
        E[Node数组] --> E1[Node[0]]
        E --> E2[Node[1]]
        E --> E3[Node[2]]
        E --> E4[Node[3]]
        
        E1 --> F1[key1,value1,next]
        F1 --> F2[key5,value5,next]
        F2 --> F3[转换阈值:8]
        F3 --> F4[红黑树TreeNode]
        
        E2 --> G1[key2,value2,null]
        E3 --> H1[TreeNode根节点]
        H1 --> H2[左子树]
        H1 --> H3[右子树]
    end
    
    style F4 fill:#c8e6c9
    style H1 fill:#fff3e0
```

#### JDK 1.7 实现
```java
public class HashMap17Implementation {
    
    // JDK 1.7 Entry结构
    static class Entry<K,V> {
        final K key;
        V value;
        Entry<K,V> next;  // 链表指针
        int hash;
        
        Entry(int h, K k, V v, Entry<K,V> n) {
            value = v;
            next = n;
            key = k;
            hash = h;
        }
    }
    
    // 核心字段
    transient Entry<K,V>[] table = new Entry[16];  // 默认容量16
    transient int size;
    int threshold = 12;  // 扩容阈值 = capacity * loadFactor
    final float loadFactor = 0.75f;
    
    // put方法实现
    public V put(K key, V value) {
        if (table == EMPTY_TABLE) {
            inflateTable(threshold);
        }
        if (key == null)
            return putForNullKey(value);
        
        // 1. 计算hash值
        int hash = hash(key);
        
        // 2. 计算数组索引
        int i = indexFor(hash, table.length);
        
        // 3. 遍历链表查找key
        for (Entry<K,V> e = table[i]; e != null; e = e.next) {
            Object k;
            if (e.hash == hash && ((k = e.key) == key || key.equals(k))) {
                V oldValue = e.value;
                e.value = value;
                return oldValue;  // 更新已存在的key
            }
        }
        
        // 4. 添加新节点（头插法）
        addEntry(hash, key, value, i);
        return null;
    }
    
    // hash函数
    final int hash(Object k) {
        int h = hashSeed;
        h ^= k.hashCode();
        h ^= (h >>> 20) ^ (h >>> 12);
        return h ^ (h >>> 7) ^ (h >>> 4);
    }
    
    // 计算数组索引
    static int indexFor(int h, int length) {
        return h & (length-1);  // 等价于 h % length，但更高效
    }
    
    // 添加新节点（头插法）
    void addEntry(int hash, K key, V value, int bucketIndex) {
        if ((size >= threshold) && (null != table[bucketIndex])) {
            resize(2 * table.length);  // 扩容
            hash = (null != key) ? hash(key) : 0;
            bucketIndex = indexFor(hash, table.length);
        }
        
        createEntry(hash, key, value, bucketIndex);
    }
    
    void createEntry(int hash, K key, V value, int bucketIndex) {
        Entry<K,V> e = table[bucketIndex];
        table[bucketIndex] = new Entry<>(hash, key, value, e);  // 头插法
        size++;
    }
}
```

#### JDK 1.8+ 实现
```java
public class HashMap18Implementation {
    
    // JDK 1.8 Node结构
    static class Node<K,V> {
        final int hash;
        final K key;
        V value;
        Node<K,V> next;
        
        Node(int hash, K key, V value, Node<K,V> next) {
            this.hash = hash;
            this.key = key;
            this.value = value;
            this.next = next;
        }
    }
    
    // 红黑树节点
    static final class TreeNode<K,V> extends Node<K,V> {
        TreeNode<K,V> parent;
        TreeNode<K,V> left;
        TreeNode<K,V> right;
        TreeNode<K,V> prev;
        boolean red;
        
        TreeNode(int hash, K key, V val, Node<K,V> next) {
            super(hash, key, val, next);
        }
    }
    
    // 核心字段
    transient Node<K,V>[] table;
    transient int size;
    int threshold;
    final float loadFactor = 0.75f;
    
    // 重要常量
    static final int TREEIFY_THRESHOLD = 8;    // 链表转红黑树阈值
    static final int UNTREEIFY_THRESHOLD = 6;  // 红黑树转链表阈值
    static final int MIN_TREEIFY_CAPACITY = 64; // 最小树化容量
    
    // put方法实现
    public V put(K key, V value) {
        return putVal(hash(key), key, value, false, true);
    }
    
    final V putVal(int hash, K key, V value, boolean onlyIfAbsent, boolean evict) {
        Node<K,V>[] tab; Node<K,V> p; int n, i;
        
        // 1. 初始化table
        if ((tab = table) == null || (n = tab.length) == 0)
            n = (tab = resize()).length;
        
        // 2. 计算索引，如果该位置为空直接插入
        if ((p = tab[i = (n - 1) & hash]) == null)
            tab[i] = newNode(hash, key, value, null);
        else {
            Node<K,V> e; K k;
            
            // 3. 检查第一个节点
            if (p.hash == hash && ((k = p.key) == key || (key != null && key.equals(k))))
                e = p;
            // 4. 如果是红黑树节点
            else if (p instanceof TreeNode)
                e = ((TreeNode<K,V>)p).putTreeVal(this, tab, hash, key, value);
            // 5. 链表处理
            else {
                for (int binCount = 0; ; ++binCount) {
                    if ((e = p.next) == null) {
                        p.next = newNode(hash, key, value, null);  // 尾插法
                        
                        // 链表长度达到8，转换为红黑树
                        if (binCount >= TREEIFY_THRESHOLD - 1)
                            treeifyBin(tab, hash);
                        break;
                    }
                    if (e.hash == hash && ((k = e.key) == key || (key != null && key.equals(k))))
                        break;
                    p = e;
                }
            }
            
            // 6. 更新已存在的key
            if (e != null) {
                V oldValue = e.value;
                if (!onlyIfAbsent || oldValue == null)
                    e.value = value;
                return oldValue;
            }
        }
        
        // 7. 检查是否需要扩容
        if (++size > threshold)
            resize();
        
        return null;
    }
    
    // 改进的hash函数
    static final int hash(Object key) {
        int h;
        return (key == null) ? 0 : (h = key.hashCode()) ^ (h >>> 16);
    }
}
```

#### JDK 1.7 vs 1.8 对比

| 特性 | JDK 1.7 | JDK 1.8+ |
|------|---------|----------|
| **数据结构** | 数组+链表 | 数组+链表+红黑树 |
| **插入方式** | 头插法 | 尾插法 |
| **hash函数** | 4次位运算+5次异或 | 1次位运算+1次异或 |
| **扩容时机** | 插入前检查 | 插入后检查 |
| **树化阈值** | 无 | 链表长度≥8且数组长度≥64 |
| **最坏时间复杂度** | O(n) | O(log n) |

---

## 容量为2的幂次的原因

### 3. HashMap为什么长度是2的幂次？⭐⭐⭐⭐

#### 问题分析
这是HashMap设计的核心问题，涉及hash算法的效率和分布均匀性。

#### 标准答案

**2的幂次的优势：**

```mermaid
graph TB
    subgraph "取模运算优化"
        A[普通取模: hash % length] --> A1[除法运算<br/>性能较慢]
        B[位运算: hash & (length-1)] --> B1[位与运算<br/>性能极快]

        A1 --> A2[适用于任意长度]
        B1 --> B2[仅适用于2的幂次]

        A2 --> C[当length=2^n时<br/>hash % length = hash & (length-1)]
        B2 --> C
    end

    subgraph "分布均匀性"
        D[length=16 (2^4)] --> D1[二进制: 10000]
        D1 --> D2[length-1=15: 01111]
        D2 --> D3[保留hash的低4位]

        E[length=15 (非2的幂次)] --> E1[二进制: 01111]
        E1 --> E2[取模结果分布不均]
    end

    style B1 fill:#c8e6c9
    style D3 fill:#e3f2fd
    style E2 fill:#ffcdd2
```

#### 详细分析
```java
public class PowerOfTwoAnalysis {

    // 演示取模运算优化
    public void demonstrateModuloOptimization() {
        int hash = 123456;
        int length = 16;  // 2^4

        // 方法1：普通取模
        int index1 = hash % length;

        // 方法2：位运算（仅当length为2的幂次时等价）
        int index2 = hash & (length - 1);

        System.out.println("hash值: " + hash);
        System.out.println("length: " + length);
        System.out.println("普通取模结果: " + index1);
        System.out.println("位运算结果: " + index2);
        System.out.println("结果相等: " + (index1 == index2));

        // 性能测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < 10000000; i++) {
            int result = i % length;
        }
        long moduloTime = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000000; i++) {
            int result = i & (length - 1);
        }
        long bitTime = System.currentTimeMillis() - start;

        System.out.println("取模运算耗时: " + moduloTime + "ms");
        System.out.println("位运算耗时: " + bitTime + "ms");
        System.out.println("性能提升: " + (moduloTime / (double) bitTime) + "倍");
    }

    // 演示分布均匀性
    public void demonstrateDistribution() {
        System.out.println("=== 2的幂次长度分布测试 ===");
        testDistribution(16);  // 2^4

        System.out.println("\n=== 非2的幂次长度分布测试 ===");
        testDistribution(15);  // 非2的幂次
    }

    private void testDistribution(int length) {
        int[] buckets = new int[length];
        int testCount = 10000;

        // 模拟hash值分布
        for (int i = 0; i < testCount; i++) {
            int hash = i * 31 + 17;  // 简单的hash函数
            int index = hash & (length - 1);  // 使用位运算
            if (index < length) {
                buckets[index]++;
            }
        }

        // 统计分布情况
        System.out.println("长度: " + length + " (二进制: " + Integer.toBinaryString(length) + ")");
        System.out.println("length-1: " + (length-1) + " (二进制: " + Integer.toBinaryString(length-1) + ")");

        int min = Integer.MAX_VALUE, max = 0;
        for (int count : buckets) {
            min = Math.min(min, count);
            max = Math.max(max, count);
        }

        System.out.println("最小桶元素数: " + min);
        System.out.println("最大桶元素数: " + max);
        System.out.println("分布差异: " + (max - min));
        System.out.println("理想平均值: " + (testCount / length));
    }

    // 演示扩容时的rehash
    public void demonstrateRehash() {
        System.out.println("=== 扩容rehash演示 ===");

        int oldCapacity = 8;   // 2^3
        int newCapacity = 16;  // 2^4

        // 原来的一些hash值
        int[] hashes = {7, 15, 23, 31, 39};

        System.out.println("原容量: " + oldCapacity);
        System.out.println("新容量: " + newCapacity);
        System.out.println();

        for (int hash : hashes) {
            int oldIndex = hash & (oldCapacity - 1);
            int newIndex = hash & (newCapacity - 1);

            System.out.printf("hash=%d: 原索引=%d, 新索引=%d", hash, oldIndex, newIndex);

            // 分析索引变化规律
            if (newIndex == oldIndex) {
                System.out.println(" (位置不变)");
            } else {
                System.out.println(" (位置变化: +" + oldCapacity + ")");
            }
        }

        System.out.println("\n结论: 扩容后元素要么在原位置，要么在原位置+oldCapacity");
    }

    // 为什么必须是2的幂次
    public void whyPowerOfTwo() {
        System.out.println("=== 为什么必须是2的幂次 ===");

        // 2的幂次的特点
        for (int i = 1; i <= 5; i++) {
            int power = 1 << i;  // 2^i
            System.out.printf("2^%d = %d, 二进制: %s\n",
                i, power, Integer.toBinaryString(power));
            System.out.printf("2^%d - 1 = %d, 二进制: %s\n",
                i, power-1, Integer.toBinaryString(power-1));
            System.out.println();
        }

        System.out.println("观察规律:");
        System.out.println("1. 2的幂次的二进制只有一个1");
        System.out.println("2. 2的幂次-1的二进制全是1");
        System.out.println("3. hash & (2^n - 1) 相当于取hash的低n位");
        System.out.println("4. 这样可以充分利用hash值的所有位，分布更均匀");
    }
}
```

#### 实际应用
```java
public class HashMapCapacityDemo {

    // HashMap如何保证容量是2的幂次
    public void demonstrateCapacityCalculation() {
        // HashMap构造函数会调用tableSizeFor方法
        System.out.println("=== HashMap容量计算 ===");

        int[] inputCapacities = {3, 7, 10, 15, 17, 33};

        for (int cap : inputCapacities) {
            int actualCapacity = tableSizeFor(cap);
            System.out.printf("输入容量: %d -> 实际容量: %d (2^%d)\n",
                cap, actualCapacity, Integer.numberOfTrailingZeros(actualCapacity));
        }
    }

    // HashMap的tableSizeFor方法实现
    static final int tableSizeFor(int cap) {
        int n = cap - 1;
        n |= n >>> 1;
        n |= n >>> 2;
        n |= n >>> 4;
        n |= n >>> 8;
        n |= n >>> 16;
        return (n < 0) ? 1 : (n >= (1 << 30)) ? (1 << 30) : n + 1;
    }

    // 分析tableSizeFor的工作原理
    public void analyzeTableSizeFor() {
        int cap = 10;
        System.out.println("=== tableSizeFor(" + cap + ") 工作过程 ===");

        int n = cap - 1;
        System.out.printf("n = cap - 1 = %d, 二进制: %s\n", n, Integer.toBinaryString(n));

        n |= n >>> 1;
        System.out.printf("n |= n >>> 1: %s\n", Integer.toBinaryString(n));

        n |= n >>> 2;
        System.out.printf("n |= n >>> 2: %s\n", Integer.toBinaryString(n));

        n |= n >>> 4;
        System.out.printf("n |= n >>> 4: %s\n", Integer.toBinaryString(n));

        n |= n >>> 8;
        System.out.printf("n |= n >>> 8: %s\n", Integer.toBinaryString(n));

        n |= n >>> 16;
        System.out.printf("n |= n >>> 16: %s\n", Integer.toBinaryString(n));

        int result = n + 1;
        System.out.printf("最终结果: n + 1 = %d, 二进制: %s\n", result, Integer.toBinaryString(result));

        System.out.println("\n原理: 通过位运算将最高位1后面的所有位都变成1，然后+1得到2的幂次");
    }
}
```

---

## 线程安全问题

### 4. HashMap线程安全问题？⭐⭐⭐⭐⭐

#### 问题分析
HashMap的线程安全问题是面试重点，需要了解具体的问题场景和解决方案。

#### 标准答案

**HashMap线程安全问题分类：**

```mermaid
graph TB
    subgraph "JDK 1.7 线程安全问题"
        A[扩容时死循环] --> A1[头插法导致<br/>链表环形结构]
        A --> A2[CPU 100%<br/>程序假死]

        B[数据丢失] --> B1[并发put<br/>覆盖数据]
        B --> B2[size计算错误]
    end

    subgraph "JDK 1.8 线程安全问题"
        C[数据覆盖] --> C1[并发put时<br/>数据被覆盖]
        C --> C2[尾插法避免了死循环<br/>但仍有数据安全问题]

        D[size不准确] --> D1[并发修改<br/>size计算错误]
    end

    subgraph "解决方案"
        E[ConcurrentHashMap] --> E1[分段锁(1.7)<br/>CAS+synchronized(1.8)]
        F[Collections.synchronizedMap] --> F1[synchronized包装]
        G[Hashtable] --> G1[synchronized方法<br/>性能较差]
    end

    style A1 fill:#ffcdd2
    style C1 fill:#fff3e0
    style E1 fill:#c8e6c9
```

#### JDK 1.7 死循环问题
```java
public class HashMap17DeadLoop {

    // 模拟JDK 1.7死循环场景
    public void demonstrateDeadLoop() {
        // 这是一个简化的演示，实际情况更复杂
        System.out.println("=== JDK 1.7 死循环原理 ===");

        // 假设有一个链表：A -> B -> null
        // 两个线程同时进行扩容操作

        System.out.println("原始链表: A -> B -> null");
        System.out.println();

        System.out.println("线程1执行到一半被挂起:");
        System.out.println("e = A, next = B");
        System.out.println();

        System.out.println("线程2完成扩容(头插法):");
        System.out.println("新链表: B -> A -> null");
        System.out.println();

        System.out.println("线程1恢复执行:");
        System.out.println("1. 处理A: newTable[i] = A, A.next = null");
        System.out.println("2. 处理B: newTable[i] = B, B.next = A");
        System.out.println("3. 处理A: A.next = B");
        System.out.println();

        System.out.println("结果: A -> B -> A (形成环)");
        System.out.println("后续get操作会无限循环，CPU 100%");
    }

    // JDK 1.7 扩容源码分析
    public void analyzeJDK17Resize() {
        System.out.println("=== JDK 1.7 扩容源码关键部分 ===");
        System.out.println("""
            void transfer(Entry[] newTable, boolean rehash) {
                int newCapacity = newTable.length;
                for (Entry<K,V> e : table) {
                    while(null != e) {
                        Entry<K,V> next = e.next;  // ← 关键点1：保存next
                        if (rehash) {
                            e.hash = null == e.key ? 0 : hash(e.key);
                        }
                        int i = indexFor(e.hash, newCapacity);
                        e.next = newTable[i];      // ← 关键点2：头插法
                        newTable[i] = e;           // ← 关键点3：更新头节点
                        e = next;                  // ← 关键点4：处理下一个节点
                    }
                }
            }
            """);

        System.out.println("问题分析:");
        System.out.println("1. 头插法改变了链表顺序");
        System.out.println("2. 多线程环境下可能形成环形链表");
        System.out.println("3. 后续操作会陷入无限循环");
    }
}
```

#### JDK 1.8 数据覆盖问题
```java
public class HashMap18DataRace {

    // 演示JDK 1.8数据覆盖问题
    public void demonstrateDataRace() throws InterruptedException {
        Map<Integer, Integer> map = new HashMap<>();
        int threadCount = 10;
        int operationsPerThread = 1000;

        CountDownLatch latch = new CountDownLatch(threadCount);

        // 多线程并发put
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    map.put(threadId * operationsPerThread + j, j);
                }
                latch.countDown();
            }).start();
        }

        latch.await();

        int expectedSize = threadCount * operationsPerThread;
        int actualSize = map.size();

        System.out.println("期望大小: " + expectedSize);
        System.out.println("实际大小: " + actualSize);
        System.out.println("数据丢失: " + (expectedSize - actualSize));

        // 通常会发现actualSize < expectedSize，说明发生了数据覆盖
    }

    // 分析数据覆盖的原因
    public void analyzeDataRaceReason() {
        System.out.println("=== JDK 1.8 数据覆盖原因分析 ===");
        System.out.println("""
            // HashMap.putVal方法的关键代码
            if ((p = tab[i = (n - 1) & hash]) == null)
                tab[i] = newNode(hash, key, value, null);  // ← 问题点

            分析：
            1. 线程A检查tab[i]为null
            2. 线程B也检查tab[i]为null
            3. 线程A创建新节点并赋值给tab[i]
            4. 线程B也创建新节点并赋值给tab[i]，覆盖了线程A的数据

            结果：线程A的数据丢失
            """);

        System.out.println("其他数据竞争场景:");
        System.out.println("1. 扩容时的数据丢失");
        System.out.println("2. 链表操作时的节点丢失");
        System.out.println("3. size字段的不准确计算");
    }

    // 演示size不准确问题
    public void demonstrateSizeInconsistency() throws InterruptedException {
        Map<String, Integer> map = new HashMap<>();
        int threadCount = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 多线程并发操作
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                // 每个线程添加和删除元素
                map.put("key" + threadId, threadId);
                map.remove("key" + (threadId - 1));
                latch.countDown();
            }).start();
        }

        latch.await();

        System.out.println("最终size: " + map.size());
        System.out.println("实际元素个数: " + map.keySet().size());

        // 可能会发现size与实际元素个数不一致
    }
}
```

#### 解决方案对比
```java
public class ThreadSafeSolutions {

    // 方案1：ConcurrentHashMap（推荐）
    public void useConcurrentHashMap() {
        Map<String, Integer> map = new ConcurrentHashMap<>();

        // 线程安全的操作
        map.put("key1", 1);
        map.put("key2", 2);

        // 原子操作
        map.putIfAbsent("key3", 3);
        map.compute("key1", (k, v) -> v == null ? 1 : v + 1);
        map.merge("key2", 1, Integer::sum);

        System.out.println("ConcurrentHashMap特点:");
        System.out.println("1. JDK 1.7: 分段锁，减少锁竞争");
        System.out.println("2. JDK 1.8: CAS + synchronized，性能更好");
        System.out.println("3. 提供原子操作方法");
        System.out.println("4. 弱一致性迭代器");
    }

    // 方案2：Collections.synchronizedMap
    public void useSynchronizedMap() {
        Map<String, Integer> map = Collections.synchronizedMap(new HashMap<>());

        // 基本操作是线程安全的
        map.put("key1", 1);
        map.get("key1");

        // 但复合操作需要额外同步
        synchronized (map) {
            if (!map.containsKey("key2")) {
                map.put("key2", 2);
            }
        }

        // 遍历时必须同步
        synchronized (map) {
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                System.out.println(entry.getKey() + "=" + entry.getValue());
            }
        }

        System.out.println("Collections.synchronizedMap特点:");
        System.out.println("1. 简单的synchronized包装");
        System.out.println("2. 性能较差，所有操作都要获取锁");
        System.out.println("3. 复合操作需要额外同步");
        System.out.println("4. 遍历时需要手动同步");
    }

    // 方案3：Hashtable（不推荐）
    public void useHashtable() {
        Map<String, Integer> map = new Hashtable<>();

        map.put("key1", 1);
        map.get("key1");

        System.out.println("Hashtable特点:");
        System.out.println("1. 所有方法都用synchronized修饰");
        System.out.println("2. 性能很差，锁粒度太大");
        System.out.println("3. 不允许null key和null value");
        System.out.println("4. 已过时，不推荐使用");
    }

    // 方案4：显式同步
    public void useExplicitSynchronization() {
        Map<String, Integer> map = new HashMap<>();
        Object lock = new Object();

        // 所有操作都需要同步
        synchronized (lock) {
            map.put("key1", 1);
        }

        synchronized (lock) {
            Integer value = map.get("key1");
        }

        System.out.println("显式同步特点:");
        System.out.println("1. 完全控制同步粒度");
        System.out.println("2. 代码复杂，容易出错");
        System.out.println("3. 性能取决于锁的使用策略");
        System.out.println("4. 适用于特殊场景");
    }

    // 性能对比测试
    public void performanceComparison() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 100000;

        // 测试ConcurrentHashMap
        long start = System.currentTimeMillis();
        testConcurrentPerformance(new ConcurrentHashMap<>(), threadCount, operationsPerThread);
        long concurrentTime = System.currentTimeMillis() - start;

        // 测试synchronizedMap
        start = System.currentTimeMillis();
        testConcurrentPerformance(Collections.synchronizedMap(new HashMap<>()), threadCount, operationsPerThread);
        long synchronizedTime = System.currentTimeMillis() - start;

        // 测试Hashtable
        start = System.currentTimeMillis();
        testConcurrentPerformance(new Hashtable<>(), threadCount, operationsPerThread);
        long hashtableTime = System.currentTimeMillis() - start;

        System.out.println("性能对比结果:");
        System.out.println("ConcurrentHashMap: " + concurrentTime + "ms");
        System.out.println("SynchronizedMap: " + synchronizedTime + "ms");
        System.out.println("Hashtable: " + hashtableTime + "ms");
    }

    private void testConcurrentPerformance(Map<Integer, Integer> map, int threadCount, int operations) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < operations; j++) {
                    map.put(threadId * operations + j, j);
                    map.get(threadId * operations + j);
                }
                latch.countDown();
            }).start();
        }

        latch.await();
    }
}
```

---

## 性能优化

### 5. HashMap性能优化建议？⭐⭐⭐

#### 问题分析
了解HashMap的性能优化技巧，在实际开发中提升程序性能。

#### 标准答案

#### 优化策略
```java
public class HashMapOptimization {

    // 1. 合理设置初始容量
    public void optimizeInitialCapacity() {
        // ❌ 不好的做法：使用默认容量
        Map<String, Integer> map1 = new HashMap<>();  // 默认16，可能需要多次扩容

        // ✅ 好的做法：预估容量
        int expectedSize = 1000;
        int initialCapacity = (int) (expectedSize / 0.75) + 1;  // 考虑负载因子
        Map<String, Integer> map2 = new HashMap<>(initialCapacity);

        // ✅ 更好的做法：使用工具方法
        Map<String, Integer> map3 = new HashMap<>(calculateCapacity(expectedSize));

        System.out.println("优化建议:");
        System.out.println("1. 预估元素数量，避免频繁扩容");
        System.out.println("2. 初始容量 = 预期元素数量 / 0.75 + 1");
        System.out.println("3. HashMap会自动调整为2的幂次");
    }

    private int calculateCapacity(int expectedSize) {
        return (int) Math.ceil(expectedSize / 0.75);
    }

    // 2. 优化hashCode方法
    public void optimizeHashCode() {
        System.out.println("=== hashCode优化 ===");

        // ❌ 不好的hashCode
        class BadKey {
            private String name;
            private int age;

            @Override
            public int hashCode() {
                return 1;  // 所有对象hash值相同，严重冲突
            }
        }

        // ✅ 好的hashCode
        class GoodKey {
            private String name;
            private int age;

            @Override
            public int hashCode() {
                return Objects.hash(name, age);  // 使用Objects.hash
            }

            @Override
            public boolean equals(Object obj) {
                if (this == obj) return true;
                if (obj == null || getClass() != obj.getClass()) return false;
                GoodKey goodKey = (GoodKey) obj;
                return age == goodKey.age && Objects.equals(name, goodKey.name);
            }
        }

        System.out.println("hashCode优化原则:");
        System.out.println("1. 相等对象必须有相同的hashCode");
        System.out.println("2. 尽量让不同对象有不同的hashCode");
        System.out.println("3. hashCode计算要高效");
        System.out.println("4. hashCode要稳定（同一对象多次调用返回相同值）");
    }

    // 3. 选择合适的负载因子
    public void optimizeLoadFactor() {
        System.out.println("=== 负载因子优化 ===");

        // 默认负载因子0.75是时间和空间的折中
        Map<String, Integer> defaultMap = new HashMap<>();

        // 追求查询性能，可以降低负载因子
        Map<String, Integer> fastMap = new HashMap<>(16, 0.5f);

        // 追求空间效率，可以提高负载因子（但会增加冲突）
        Map<String, Integer> compactMap = new HashMap<>(16, 0.9f);

        System.out.println("负载因子选择:");
        System.out.println("1. 0.75: 默认值，时间空间平衡");
        System.out.println("2. 0.5: 更快查询，更多空间");
        System.out.println("3. 0.9: 更省空间，更多冲突");
        System.out.println("4. 一般情况下使用默认值即可");
    }

    // 4. 避免频繁的装箱拆箱
    public void avoidBoxingUnboxing() {
        System.out.println("=== 避免装箱拆箱 ===");

        // ❌ 频繁装箱拆箱
        Map<Integer, Integer> boxingMap = new HashMap<>();
        for (int i = 0; i < 1000000; i++) {
            boxingMap.put(i, i * 2);  // int -> Integer装箱
        }

        // ✅ 使用基本类型集合（第三方库）
        // TIntIntHashMap primitiveMap = new TIntIntHashMap();  // Trove库
        // for (int i = 0; i < 1000000; i++) {
        //     primitiveMap.put(i, i * 2);  // 无装箱开销
        // }

        System.out.println("装箱拆箱优化:");
        System.out.println("1. 大量基本类型数据考虑使用专门的集合库");
        System.out.println("2. 如Trove、Eclipse Collections等");
        System.out.println("3. 避免频繁的Integer.valueOf()调用");
    }

    // 5. 批量操作优化
    public void optimizeBatchOperations() {
        Map<String, Integer> map = new HashMap<>();

        // ✅ 批量初始化
        Map<String, Integer> initMap = new HashMap<String, Integer>() {{
            put("key1", 1);
            put("key2", 2);
            put("key3", 3);
        }};

        // ✅ 使用putAll
        Map<String, Integer> sourceMap = Map.of("a", 1, "b", 2, "c", 3);
        map.putAll(sourceMap);

        // ✅ 使用computeIfAbsent避免重复查找
        map.computeIfAbsent("key4", k -> expensiveComputation(k));

        // ✅ 使用merge进行累加操作
        map.merge("key1", 10, Integer::sum);

        System.out.println("批量操作优化:");
        System.out.println("1. 使用putAll批量添加");
        System.out.println("2. 使用computeIfAbsent避免重复查找");
        System.out.println("3. 使用merge进行原子更新");
    }

    private Integer expensiveComputation(String key) {
        // 模拟耗时计算
        return key.hashCode();
    }

    // 6. 内存优化
    public void optimizeMemory() {
        System.out.println("=== 内存优化 ===");

        // 及时清理不需要的引用
        Map<String, LargeObject> cache = new HashMap<>();

        // 使用WeakHashMap自动清理
        Map<String, LargeObject> weakCache = new WeakHashMap<>();

        // 定期清理过期数据
        Map<String, CacheEntry> timedCache = new HashMap<>();

        System.out.println("内存优化策略:");
        System.out.println("1. 及时remove不需要的元素");
        System.out.println("2. 考虑使用WeakHashMap");
        System.out.println("3. 实现LRU缓存限制大小");
        System.out.println("4. 定期清理过期数据");
    }

    static class LargeObject {
        private byte[] data = new byte[1024];
    }

    static class CacheEntry {
        Object value;
        long timestamp;

        CacheEntry(Object value) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
        }

        boolean isExpired(long ttl) {
            return System.currentTimeMillis() - timestamp > ttl;
        }
    }
}
```
```
