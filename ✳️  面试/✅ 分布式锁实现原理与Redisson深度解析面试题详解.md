# 分布式锁实现原理与Redisson深度解析面试题详解

## 基础篇

### 1. 什么是分布式锁？为什么需要分布式锁？

**答案：**

分布式锁是在分布式系统中实现互斥访问共享资源的一种机制，确保在多个节点并发访问时，同一时刻只有一个节点能够获得锁并执行临界区代码。

**为什么需要分布式锁：**

```mermaid
flowchart TD
    A["单机环境"] --> B["JVM内置锁"]
    B --> C["synchronized"]
    B --> D["ReentrantLock"]
    
    E["分布式环境"] --> F["多个JVM实例"]
    F --> G["JVM锁失效"]
    G --> H["需要分布式锁"]
    
    I["分布式锁应用场景"] --> J["防止重复执行"]
    I --> K["资源互斥访问"]
    I --> L["数据一致性保证"]
    I --> M["定时任务防重"]
    
    J --> N["订单重复创建"]
    K --> O["库存扣减"]
    L --> P["缓存更新"]
    M --> Q["定时任务集群"]
```

**典型应用场景：**

1. **防止重复操作**
   - 订单重复提交
   - 支付重复扣款
   - 消息重复消费

2. **资源互斥访问**
   - 库存扣减操作
   - 账户余额更新
   - 文件写入操作

3. **定时任务防重**
   - 集群环境下的定时任务
   - 数据同步任务
   - 清理任务

**问题示例：**

```java
// 没有分布式锁的问题场景
@Service
public class OrderService {
    
    public void createOrder(String userId, String productId) {
        // 检查是否已存在订单
        if (orderExists(userId, productId)) {
            throw new BusinessException("订单已存在");
        }
        
        // 在高并发情况下，多个实例可能同时通过检查
        // 导致重复创建订单
        Order order = new Order(userId, productId);
        orderRepository.save(order);
    }
}

// 使用分布式锁解决
@Service
public class OrderService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    public void createOrder(String userId, String productId) {
        String lockKey = "order:lock:" + userId + ":" + productId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，最多等待10秒，锁30秒后自动释放
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                if (orderExists(userId, productId)) {
                    throw new BusinessException("订单已存在");
                }
                
                Order order = new Order(userId, productId);
                orderRepository.save(order);
            } else {
                throw new BusinessException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 2. 分布式锁的实现方案有哪些？各有什么优缺点？

**答案：**

分布式锁主要有三种实现方案：基于数据库、基于Redis、基于ZooKeeper。

**实现方案对比：**

```mermaid
flowchart TD
    A["分布式锁实现方案"] --> B["数据库锁"]
    A --> C["Redis锁"]
    A --> D["ZooKeeper锁"]
    
    B --> E["悲观锁"]
    B --> F["乐观锁"]
    
    C --> G["SETNX"]
    C --> H["SET EX PX NX"]
    C --> I["Lua脚本"]
    C --> J["Redisson"]
    
    D --> K["临时节点"]
    D --> L["顺序节点"]
    D --> M["Curator"]
    
    N["对比维度"] --> O["性能"]
    N --> P["可靠性"]
    N --> Q["复杂度"]
    N --> R["一致性"]
```

**1. 数据库分布式锁**

```sql
-- 创建锁表
CREATE TABLE distributed_lock (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    lock_name VARCHAR(100) NOT NULL UNIQUE,
    lock_value VARCHAR(100) NOT NULL,
    expire_time TIMESTAMP NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_lock_name (lock_name),
    INDEX idx_expire_time (expire_time)
);

-- 获取锁（悲观锁）
INSERT INTO distributed_lock (lock_name, lock_value, expire_time) 
VALUES ('order_lock', 'server1_thread1', DATE_ADD(NOW(), INTERVAL 30 SECOND));

-- 释放锁
DELETE FROM distributed_lock 
WHERE lock_name = 'order_lock' AND lock_value = 'server1_thread1';
```

**优点：**
- 实现简单，易于理解
- 强一致性保证
- 不需要额外组件

**缺点：**
- 性能较差，数据库压力大
- 单点故障风险
- 锁超时处理复杂

**2. Redis分布式锁**

```java
// 基础Redis锁实现
public class RedisDistributedLock {
    
    private JedisPool jedisPool;
    
    public boolean tryLock(String lockKey, String requestId, int expireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set(lockKey, requestId, "NX", "PX", expireTime);
            return "OK".equals(result);
        }
    }
    
    public boolean releaseLock(String lockKey, String requestId) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) " +
                       "else return 0 end";
        
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(script, Collections.singletonList(lockKey), 
                                     Collections.singletonList(requestId));
            return "1".equals(result.toString());
        }
    }
}
```

**优点：**
- 性能高，响应快
- 支持过期时间
- 实现相对简单

**缺点：**
- 可能存在锁丢失（主从切换）
- 时钟偏移问题
- 需要考虑Redis高可用

**3. ZooKeeper分布式锁**

```java
// ZooKeeper锁实现示例
public class ZooKeeperDistributedLock {

    private CuratorFramework client;
    private InterProcessMutex lock;

    public ZooKeeperDistributedLock(CuratorFramework client, String lockPath) {
        this.client = client;
        this.lock = new InterProcessMutex(client, lockPath);
    }

    public boolean tryLock(long timeout, TimeUnit unit) throws Exception {
        return lock.acquire(timeout, unit);
    }

    public void unlock() throws Exception {
        lock.release();
    }
}
```

**优点：**
- 强一致性保证
- 自动故障恢复
- 支持阻塞等待

**缺点：**
- 性能相对较低
- 复杂度高
- 依赖ZooKeeper集群

**方案选择建议：**

| 场景 | 推荐方案 | 理由 |
|------|----------|------|
| 高性能要求 | Redis | 响应速度快，吞吐量高 |
| 强一致性要求 | ZooKeeper | CP系统，强一致性保证 |
| 简单场景 | 数据库 | 实现简单，易于维护 |
| 生产环境 | Redisson | 功能完善，久经考验 |

### 3. 基于Redis的分布式锁如何实现？

**答案：**

Redis分布式锁的实现经历了多个版本的演进，从简单的SETNX到完善的Lua脚本实现。

**实现演进过程：**

```mermaid
flowchart TD
    A["Redis锁演进"] --> B["V1: SETNX"]
    A --> C["V2: SETNX + EXPIRE"]
    A --> D["V3: SET EX PX NX"]
    A --> E["V4: Lua脚本"]
    A --> F["V5: Redisson"]

    B --> G["问题：无过期时间"]
    C --> H["问题：非原子操作"]
    D --> I["问题：锁误删"]
    E --> J["问题：不可重入"]
    F --> K["完善解决方案"]

    L["核心要素"] --> M["互斥性"]
    L --> N["防死锁"]
    L --> O["防误删"]
    L --> P["可重入"]
    L --> Q["高性能"]
```

**1. 基础实现（存在问题）**

```java
// V1: 简单SETNX实现（有死锁风险）
public boolean lock(String key, String value) {
    return jedis.setnx(key, value) == 1;
}

// V2: SETNX + EXPIRE（非原子操作）
public boolean lock(String key, String value, int seconds) {
    if (jedis.setnx(key, value) == 1) {
        jedis.expire(key, seconds);
        return true;
    }
    return false;
}
```

**2. 改进实现**

```java
// V3: SET EX PX NX（原子操作）
public boolean lock(String key, String value, int milliseconds) {
    String result = jedis.set(key, value, "NX", "PX", milliseconds);
    return "OK".equals(result);
}

// 安全释放锁（Lua脚本保证原子性）
public boolean unlock(String key, String value) {
    String script =
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";

    Object result = jedis.eval(script, 1, key, value);
    return "1".equals(result.toString());
}
```

**3. 完整实现**

```java
public class RedisDistributedLock {

    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "PX";

    // 获取锁的Lua脚本
    private static final String ACQUIRE_SCRIPT =
        "if redis.call('exists', KEYS[1]) == 0 then " +
        "    redis.call('hset', KEYS[1], ARGV[2], 1) " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]) " +
        "    return nil " +
        "end " +
        "if redis.call('hexists', KEYS[1], ARGV[2]) == 1 then " +
        "    redis.call('hincrby', KEYS[1], ARGV[2], 1) " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]) " +
        "    return nil " +
        "end " +
        "return redis.call('pttl', KEYS[1])";

    // 释放锁的Lua脚本
    private static final String RELEASE_SCRIPT =
        "if redis.call('hexists', KEYS[1], ARGV[2]) == 0 then " +
        "    return nil " +
        "end " +
        "local counter = redis.call('hincrby', KEYS[1], ARGV[2], -1) " +
        "if counter > 0 then " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]) " +
        "    return 0 " +
        "else " +
        "    redis.call('del', KEYS[1]) " +
        "    return 1 " +
        "end";

    private JedisPool jedisPool;

    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(ACQUIRE_SCRIPT, 1, lockKey,
                                     String.valueOf(expireTime), requestId);
            return result == null;
        }
    }

    public boolean unlock(String lockKey, String requestId, long expireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(RELEASE_SCRIPT, 1, lockKey,
                                     String.valueOf(expireTime), requestId);
            return result != null && "1".equals(result.toString());
        }
    }
}
```

**关键技术点：**

1. **原子性保证**：使用Lua脚本确保操作的原子性
2. **防止误删**：通过唯一标识符验证锁的所有权
3. **自动过期**：设置过期时间防止死锁
4. **可重入性**：使用Hash结构记录重入次数

## 进阶篇

### 4. Redis分布式锁的问题和改进方案是什么？

**答案：**

Redis分布式锁在实际使用中存在一些问题，需要通过各种改进方案来解决。

**主要问题分析：**

```mermaid
flowchart TD
    A["Redis锁问题"] --> B["主从切换锁丢失"]
    A --> C["时钟偏移问题"]
    A --> D["锁超时问题"]
    A --> E["网络分区问题"]

    B --> F["Master宕机"]
    F --> G["Slave提升为Master"]
    G --> H["锁数据丢失"]

    C --> I["服务器时钟不同步"]
    I --> J["锁提前过期"]

    D --> K["业务执行时间过长"]
    K --> L["锁自动释放"]
    L --> M["并发安全问题"]

    E --> N["网络分区"]
    N --> O["脑裂问题"]
```

**1. 主从切换锁丢失问题**

```java
// 问题场景演示
public class MasterSlaveIssueDemo {

    public void demonstrateLockLoss() {
        // 1. 客户端A在Master上获取锁成功
        boolean lockAcquired = redisClient.set("lock:order", "clientA", "NX", "EX", 30);

        // 2. Master宕机，锁数据还未同步到Slave
        // 3. Slave被提升为新的Master
        // 4. 客户端B在新Master上获取同样的锁成功
        boolean lockAcquiredByB = redisClient.set("lock:order", "clientB", "NX", "EX", 30);

        // 结果：两个客户端同时持有锁，违反互斥性
    }
}
```

**解决方案：RedLock算法**

```java
public class RedLockImplementation {

    private List<JedisPool> redisPools;
    private int quorum; // 大多数节点数量

    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        int successCount = 0;
        long startTime = System.currentTimeMillis();

        // 尝试在所有Redis实例上获取锁
        for (JedisPool pool : redisPools) {
            try (Jedis jedis = pool.getResource()) {
                String result = jedis.set(lockKey, requestId, "NX", "PX", expireTime);
                if ("OK".equals(result)) {
                    successCount++;
                }
            } catch (Exception e) {
                // 忽略异常，继续尝试其他实例
            }
        }

        long elapsedTime = System.currentTimeMillis() - startTime;

        // 检查是否获取到大多数锁，且剩余时间足够
        if (successCount >= quorum && elapsedTime < expireTime) {
            return true;
        } else {
            // 释放已获取的锁
            releaseLock(lockKey, requestId);
            return false;
        }
    }

    private void releaseLock(String lockKey, String requestId) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) else return 0 end";

        for (JedisPool pool : redisPools) {
            try (Jedis jedis = pool.getResource()) {
                jedis.eval(script, Collections.singletonList(lockKey),
                          Collections.singletonList(requestId));
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }
}
```

**2. 锁超时问题和看门狗机制**

```java
public class WatchDogMechanism {

    private ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private Map<String, ScheduledFuture<?>> watchDogs = new ConcurrentHashMap<>();

    public boolean tryLockWithWatchDog(String lockKey, String requestId, long expireTime) {
        boolean acquired = tryLock(lockKey, requestId, expireTime);

        if (acquired) {
            // 启动看门狗，定期续期
            ScheduledFuture<?> watchDog = scheduler.scheduleAtFixedRate(() -> {
                renewLock(lockKey, requestId, expireTime);
            }, expireTime / 3, expireTime / 3, TimeUnit.MILLISECONDS);

            watchDogs.put(lockKey, watchDog);
        }

        return acquired;
    }

    private void renewLock(String lockKey, String requestId, long expireTime) {
        String script =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('pexpire', KEYS[1], ARGV[2]) " +
            "else " +
            "    return 0 " +
            "end";

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.eval(script, Collections.singletonList(lockKey),
                      Arrays.asList(requestId, String.valueOf(expireTime)));
        }
    }

    public void unlock(String lockKey, String requestId) {
        // 停止看门狗
        ScheduledFuture<?> watchDog = watchDogs.remove(lockKey);
        if (watchDog != null) {
            watchDog.cancel(true);
        }

        // 释放锁
        releaseLock(lockKey, requestId);
    }
}
```

**3. 时钟偏移问题**

```java
public class ClockDriftSolution {

    // 使用相对时间而不是绝对时间
    public boolean tryLockWithRelativeTime(String lockKey, String requestId, long leaseTime) {
        long startTime = System.currentTimeMillis();

        String script =
            "if redis.call('exists', KEYS[1]) == 0 then " +
            "    redis.call('hset', KEYS[1], 'owner', ARGV[1]) " +
            "    redis.call('hset', KEYS[1], 'startTime', ARGV[2]) " +
            "    redis.call('pexpire', KEYS[1], ARGV[3]) " +
            "    return 1 " +
            "end " +
            "return 0";

        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.eval(script, Collections.singletonList(lockKey),
                Arrays.asList(requestId, String.valueOf(startTime), String.valueOf(leaseTime)));
            return "1".equals(result.toString());
        }
    }
}
```

### 5. ZooKeeper分布式锁的实现原理是什么？

**答案：**

ZooKeeper分布式锁基于临时顺序节点实现，利用ZooKeeper的强一致性和顺序性保证。

**ZooKeeper锁实现原理：**

```mermaid
flowchart TD
    A["ZooKeeper分布式锁"] --> B["临时顺序节点"]
    A --> C["Watcher机制"]
    A --> D["顺序性保证"]

    E["锁获取流程"] --> F["创建临时顺序节点"]
    F --> G["获取所有子节点"]
    G --> H["判断是否最小节点"]
    H --> I["是：获取锁成功"]
    H --> J["否：监听前一个节点"]
    J --> K["等待通知"]
    K --> L["重新判断"]

    M["锁释放流程"] --> N["删除自己的节点"]
    N --> O["触发后续节点监听"]
    O --> P["后续节点获取锁"]
```

**详细实现：**

```java
public class ZooKeeperDistributedLock {

    private CuratorFramework client;
    private String lockPath;
    private String currentLockPath;
    private CountDownLatch latch;

    public ZooKeeperDistributedLock(CuratorFramework client, String lockPath) {
        this.client = client;
        this.lockPath = lockPath;
    }

    public boolean tryLock(long timeout, TimeUnit unit) throws Exception {
        // 1. 创建临时顺序节点
        currentLockPath = client.create()
            .creatingParentsIfNeeded()
            .withMode(CreateMode.EPHEMERAL_SEQUENTIAL)
            .forPath(lockPath + "/lock-");

        return waitForLock(timeout, unit);
    }

    private boolean waitForLock(long timeout, TimeUnit unit) throws Exception {
        while (true) {
            // 2. 获取所有子节点并排序
            List<String> children = client.getChildren().forPath(lockPath);
            Collections.sort(children);

            String currentNode = currentLockPath.substring(lockPath.length() + 1);
            int currentIndex = children.indexOf(currentNode);

            // 3. 判断是否为最小节点
            if (currentIndex == 0) {
                return true; // 获取锁成功
            }

            // 4. 监听前一个节点
            String previousNode = children.get(currentIndex - 1);
            String previousPath = lockPath + "/" + previousNode;

            latch = new CountDownLatch(1);

            Stat stat = client.checkExists()
                .usingWatcher(new Watcher() {
                    @Override
                    public void process(WatchedEvent event) {
                        if (event.getType() == Event.EventType.NodeDeleted) {
                            latch.countDown();
                        }
                    }
                })
                .forPath(previousPath);

            // 如果前一个节点已经不存在，重新检查
            if (stat == null) {
                continue;
            }

            // 5. 等待前一个节点删除
            return latch.await(timeout, unit);
        }
    }

    public void unlock() throws Exception {
        if (currentLockPath != null) {
            client.delete().guaranteed().forPath(currentLockPath);
            currentLockPath = null;
        }
    }
}
```

**使用Curator简化实现：**

```java
public class CuratorLockExample {

    private CuratorFramework client;
    private InterProcessMutex lock;

    public void demonstrateLock() throws Exception {
        // 创建Curator客户端
        client = CuratorFrameworkFactory.newClient(
            "localhost:2181",
            new ExponentialBackoffRetry(1000, 3)
        );
        client.start();

        // 创建分布式锁
        lock = new InterProcessMutex(client, "/locks/mylock");

        try {
            // 尝试获取锁，最多等待10秒
            if (lock.acquire(10, TimeUnit.SECONDS)) {
                try {
                    // 执行临界区代码
                    doSomething();
                } finally {
                    // 释放锁
                    lock.release();
                }
            } else {
                System.out.println("无法获取锁");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void doSomething() {
        System.out.println("执行业务逻辑");
    }
}
```

**ZooKeeper锁的优势：**

1. **强一致性**：基于ZAB协议保证强一致性
2. **自动故障恢复**：临时节点在客户端断开时自动删除
3. **公平性**：按照节点创建顺序获取锁
4. **阻塞等待**：支持阻塞等待锁释放

**ZooKeeper锁的劣势：**

1. **性能开销**：网络通信和磁盘IO开销较大
2. **复杂性**：需要维护ZooKeeper集群
3. **延迟较高**：相比Redis有更高的延迟

## 高级篇

### 8. Redisson分布式锁的核心实现原理是什么？

**答案：**

Redisson是Redis官方推荐的Java客户端，提供了完善的分布式锁实现。

**Redisson锁架构：**

```mermaid
flowchart TD
    A["Redisson分布式锁"] --> B["RLock接口"]
    A --> C["RedissonLock实现"]
    A --> D["Lua脚本"]
    A --> E["WatchDog机制"]

    B --> F["tryLock()"]
    B --> G["lock()"]
    B --> H["unlock()"]
    B --> I["isLocked()"]

    C --> J["可重入锁"]
    C --> K["自动续期"]
    C --> L["公平锁"]
    C --> M["读写锁"]

    D --> N["获取锁脚本"]
    D --> O["释放锁脚本"]
    D --> P["续期脚本"]

    E --> Q["定时任务"]
    E --> R["锁续期"]
    E --> S["自动清理"]
```

**核心源码分析：**

```java
// Redisson锁的核心实现
public class RedissonLock extends RedissonExpirable implements RLock {

    // 锁的默认过期时间：30秒
    private static final long LOCK_EXPIRATION_INTERVAL_SECONDS = 30;

    // 获取锁的Lua脚本
    private static final String LOCK_SCRIPT =
        "if (redis.call('exists', KEYS[1]) == 0) then " +
        "    redis.call('hset', KEYS[1], ARGV[2], 1); " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]); " +
        "    return nil; " +
        "end; " +
        "if (redis.call('hexists', KEYS[1], ARGV[2]) == 1) then " +
        "    redis.call('hincrby', KEYS[1], ARGV[2], 1); " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]); " +
        "    return nil; " +
        "end; " +
        "return redis.call('pttl', KEYS[1]);";

    @Override
    public boolean tryLock(long waitTime, long leaseTime, TimeUnit unit)
            throws InterruptedException {

        long time = unit.toMillis(waitTime);
        long current = System.currentTimeMillis();
        long threadId = Thread.currentThread().getId();

        // 尝试获取锁
        Long ttl = tryAcquire(leaseTime, unit, threadId);
        if (ttl == null) {
            return true; // 获取成功
        }

        // 订阅锁释放事件
        RFuture<RedissonLockEntry> future = subscribe(threadId);

        try {
            while (true) {
                // 再次尝试获取锁
                ttl = tryAcquire(leaseTime, unit, threadId);
                if (ttl == null) {
                    return true;
                }

                // 检查等待时间
                time -= System.currentTimeMillis() - current;
                if (time <= 0) {
                    return false;
                }

                // 等待锁释放通知
                if (ttl >= 0 && ttl < time) {
                    getEntry(threadId).getLatch().await(ttl, TimeUnit.MILLISECONDS);
                } else {
                    getEntry(threadId).getLatch().await(time, TimeUnit.MILLISECONDS);
                }
            }
        } finally {
            unsubscribe(future, threadId);
        }
    }

    private Long tryAcquire(long leaseTime, TimeUnit unit, long threadId) {
        return get(tryAcquireAsync(leaseTime, unit, threadId));
    }

    private <T> RFuture<Long> tryAcquireAsync(long leaseTime, TimeUnit unit, long threadId) {
        if (leaseTime != -1) {
            return tryLockInnerAsync(leaseTime, unit, threadId, RedisCommands.EVAL_LONG);
        }

        // 使用看门狗机制
        RFuture<Long> ttlRemainingFuture = tryLockInnerAsync(
            commandExecutor.getConnectionManager().getCfg().getLockWatchdogTimeout(),
            TimeUnit.MILLISECONDS, threadId, RedisCommands.EVAL_LONG);

        ttlRemainingFuture.onComplete((ttlRemaining, e) -> {
            if (e != null) {
                return;
            }

            // 获取锁成功，启动看门狗
            if (ttlRemaining == null) {
                scheduleExpirationRenewal(threadId);
            }
        });

        return ttlRemainingFuture;
    }

    <T> RFuture<T> tryLockInnerAsync(long leaseTime, TimeUnit unit,
                                     long threadId, RedisStrictCommand<T> command) {
        internalLockLeaseTime = unit.toMillis(leaseTime);

        return commandExecutor.evalWriteAsync(getName(), LongCodec.INSTANCE, command,
            LOCK_SCRIPT,
            Collections.singletonList(getName()),
            internalLockLeaseTime, getLockName(threadId));
    }
}
```

**释放锁的实现：**

```java
// 释放锁的Lua脚本
private static final String UNLOCK_SCRIPT =
    "if (redis.call('hexists', KEYS[1], ARGV[3]) == 0) then " +
    "    return nil; " +
    "end; " +
    "local counter = redis.call('hincrby', KEYS[1], ARGV[3], -1); " +
    "if (counter > 0) then " +
    "    redis.call('pexpire', KEYS[1], ARGV[2]); " +
    "    return 0; " +
    "else " +
    "    redis.call('del', KEYS[1]); " +
    "    redis.call('publish', KEYS[2], ARGV[1]); " +
    "    return 1; " +
    "end; " +
    "return nil;";

@Override
public void unlock() {
    try {
        get(unlockAsync(Thread.currentThread().getId()));
    } catch (RedisException e) {
        if (e.getCause() instanceof IllegalMonitorStateException) {
            throw (IllegalMonitorStateException) e.getCause();
        } else {
            throw e;
        }
    }
}

public RFuture<Void> unlockAsync(long threadId) {
    RPromise<Void> result = new RedissonPromise<Void>();

    RFuture<Boolean> future = unlockInnerAsync(threadId);
    future.onComplete((opStatus, e) -> {
        if (e != null) {
            cancelExpirationRenewal(threadId);
            result.tryFailure(e);
            return;
        }

        if (opStatus == null) {
            IllegalMonitorStateException cause =
                new IllegalMonitorStateException("attempt to unlock lock, not locked by current thread");
            result.tryFailure(cause);
            return;
        }

        // 取消看门狗续期
        cancelExpirationRenewal(threadId);
        result.trySuccess(null);
    });

    return result;
}
```

### 9. Redisson的看门狗（WatchDog）机制是如何工作的？

**答案：**

Redisson的看门狗机制是其核心特性之一，用于自动续期锁，防止业务执行时间过长导致锁自动释放。

**看门狗工作原理：**

```mermaid
flowchart TD
    A["获取锁成功"] --> B["启动WatchDog"]
    B --> C["创建定时任务"]
    C --> D["每10秒执行一次"]
    D --> E["检查锁是否存在"]
    E --> F["锁存在：续期30秒"]
    E --> G["锁不存在：停止续期"]
    F --> H["继续定时任务"]
    G --> I["清理资源"]

    J["业务执行完成"] --> K["主动释放锁"]
    K --> L["取消WatchDog"]
    L --> M["清理定时任务"]

    N["WatchDog参数"] --> O["internalLockLeaseTime: 30s"]
    N --> P["renewalInterval: 10s"]
    N --> Q["watchdogTimeout: 30s"]
```

**看门狗核心实现：**

```java
public class RedissonLock {

    // 看门狗超时时间：30秒
    private long internalLockLeaseTime;

    // 续期间隔：看门狗超时时间的1/3
    private static final int RENEWAL_INTERVAL_RATIO = 3;

    // 存储每个线程的续期任务
    private static final ConcurrentMap<String, ExpirationEntry> EXPIRATION_RENEWAL_MAP =
        new ConcurrentHashMap<>();

    // 续期的Lua脚本
    private static final String RENEWAL_SCRIPT =
        "if (redis.call('hexists', KEYS[1], ARGV[2]) == 1) then " +
        "    redis.call('pexpire', KEYS[1], ARGV[1]); " +
        "    return 1; " +
        "end; " +
        "return 0;";

    private void scheduleExpirationRenewal(long threadId) {
        ExpirationEntry entry = new ExpirationEntry();
        ExpirationEntry oldEntry = EXPIRATION_RENEWAL_MAP.putIfAbsent(getEntryName(), entry);

        if (oldEntry != null) {
            oldEntry.addThreadId(threadId);
        } else {
            entry.addThreadId(threadId);
            renewExpiration();
        }
    }

    private void renewExpiration() {
        ExpirationEntry ee = EXPIRATION_RENEWAL_MAP.get(getEntryName());
        if (ee == null) {
            return;
        }

        // 创建续期任务
        Timeout task = commandExecutor.getConnectionManager().newTimeout(new TimerTask() {
            @Override
            public void run(Timeout timeout) throws Exception {
                ExpirationEntry ent = EXPIRATION_RENEWAL_MAP.get(getEntryName());
                if (ent == null) {
                    return;
                }

                Long threadId = ent.getFirstThreadId();
                if (threadId == null) {
                    return;
                }

                // 执行续期
                RFuture<Boolean> future = renewExpirationAsync(threadId);
                future.onComplete((res, e) -> {
                    if (e != null) {
                        log.error("Can't update lock " + getName() + " expiration", e);
                        return;
                    }

                    if (res) {
                        // 续期成功，继续下一次续期
                        renewExpiration();
                    }
                });
            }
        }, internalLockLeaseTime / RENEWAL_INTERVAL_RATIO, TimeUnit.MILLISECONDS);

        ee.setTimeout(task);
    }

    protected RFuture<Boolean> renewExpirationAsync(long threadId) {
        return commandExecutor.evalWriteAsync(getName(), LongCodec.INSTANCE,
            RedisCommands.EVAL_BOOLEAN,
            RENEWAL_SCRIPT,
            Collections.singletonList(getName()),
            internalLockLeaseTime, getLockName(threadId));
    }

    private void cancelExpirationRenewal(Long threadId) {
        ExpirationEntry task = EXPIRATION_RENEWAL_MAP.get(getEntryName());
        if (task == null) {
            return;
        }

        if (threadId != null) {
            task.removeThreadId(threadId);
        }

        if (threadId == null || task.hasNoThreads()) {
            Timeout timeout = task.getTimeout();
            if (timeout != null) {
                timeout.cancel();
            }
            EXPIRATION_RENEWAL_MAP.remove(getEntryName());
        }
    }
}

// 续期任务的数据结构
class ExpirationEntry {
    private final Map<Long, Integer> threadIds = new LinkedHashMap<>();
    private volatile Timeout timeout;

    public synchronized void addThreadId(long threadId) {
        Integer counter = threadIds.get(threadId);
        if (counter == null) {
            counter = 1;
        } else {
            counter++;
        }
        threadIds.put(threadId, counter);
    }

    public synchronized boolean hasNoThreads() {
        return threadIds.isEmpty();
    }

    public synchronized Long getFirstThreadId() {
        if (threadIds.isEmpty()) {
            return null;
        }
        return threadIds.keySet().iterator().next();
    }

    public synchronized void removeThreadId(long threadId) {
        Integer counter = threadIds.get(threadId);
        if (counter == null) {
            return;
        }
        counter--;
        if (counter == 0) {
            threadIds.remove(threadId);
        } else {
            threadIds.put(threadId, counter);
        }
    }

    public void setTimeout(Timeout timeout) {
        this.timeout = timeout;
    }

    public Timeout getTimeout() {
        return timeout;
    }
}
```

**看门狗机制的关键特性：**

1. **自动续期**：每隔10秒（默认）自动续期锁的过期时间
2. **智能停止**：锁释放后自动停止续期任务
3. **多线程支持**：支持同一个锁的多个线程（可重入锁）
4. **异常处理**：续期失败时的异常处理和日志记录

**使用示例：**

```java
@Service
public class BusinessService {

    @Autowired
    private RedissonClient redissonClient;

    public void processLongRunningTask(String taskId) {
        RLock lock = redissonClient.getLock("task:lock:" + taskId);

        try {
            // 获取锁，不指定leaseTime，启用看门狗
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                try {
                    // 执行长时间业务逻辑（可能超过30秒）
                    performLongRunningOperation();
                } finally {
                    // 释放锁，自动停止看门狗
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void performLongRunningOperation() {
        // 模拟长时间运行的业务逻辑
        try {
            Thread.sleep(60000); // 60秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
```

### 10. Redisson如何解决Redis主从切换时的锁丢失问题？

**答案：**

Redisson通过多种机制来缓解Redis主从切换时的锁丢失问题，虽然无法完全避免，但可以大大降低影响。

**问题分析：**

```mermaid
flowchart TD
    A["Redis主从架构"] --> B["Master节点"]
    A --> C["Slave节点"]

    D["锁丢失场景"] --> E["客户端A获取锁"]
    E --> F["写入Master成功"]
    F --> G["Master宕机"]
    G --> H["数据未同步到Slave"]
    H --> I["Slave提升为Master"]
    I --> J["客户端B获取同样的锁"]
    J --> K["锁丢失问题"]

    L["Redisson解决方案"] --> M["RedLock算法"]
    L --> N["主从同步等待"]
    L --> O["锁延迟释放"]
    L --> P["故障检测机制"]
```

**1. RedLock算法实现**

```java
public class RedissonRedLock implements RLock {

    private final List<RLock> locks;

    public RedissonRedLock(RLock... locks) {
        if (locks.length == 0) {
            throw new IllegalArgumentException("Lock instances are not defined");
        }
        this.locks = Arrays.asList(locks);
    }

    @Override
    public boolean tryLock(long waitTime, long leaseTime, TimeUnit unit)
            throws InterruptedException {

        long newLeaseTime = -1;
        if (leaseTime != -1) {
            newLeaseTime = unit.toMillis(leaseTime);
        }

        long time = System.currentTimeMillis();
        long remainTime = unit.toMillis(waitTime);
        long lockWaitTime = calcLockWaitTime(remainTime);

        int failedLocksLimit = failedLocksLimit();
        List<RLock> acquiredLocks = new ArrayList<>(locks.size());

        // 尝试在所有Redis实例上获取锁
        for (ListIterator<RLock> iterator = locks.listIterator(); iterator.hasNext();) {
            RLock lock = iterator.next();
            boolean lockAcquired;

            try {
                if (waitTime == -1 && leaseTime == -1) {
                    lockAcquired = lock.tryLock();
                } else {
                    long awaitTime = Math.min(lockWaitTime, remainTime);
                    lockAcquired = lock.tryLock(awaitTime, newLeaseTime, TimeUnit.MILLISECONDS);
                }
            } catch (RedisResponseTimeoutException e) {
                lockAcquired = false;
            } catch (Exception e) {
                lockAcquired = false;
            }

            if (lockAcquired) {
                acquiredLocks.add(lock);
            } else {
                if (locks.size() - acquiredLocks.size() == failedLocksLimit()) {
                    break;
                }

                if (failedLocksLimit == 0) {
                    unlockInner(acquiredLocks);
                    if (waitTime == -1 && leaseTime == -1) {
                        return false;
                    }
                    failedLocksLimit = failedLocksLimit();
                    acquiredLocks.clear();
                    // 重置迭代器
                    while (iterator.hasPrevious()) {
                        iterator.previous();
                    }
                } else {
                    failedLocksLimit--;
                }
            }

            if (remainTime != -1) {
                remainTime -= System.currentTimeMillis() - time;
                time = System.currentTimeMillis();
                if (remainTime <= 0) {
                    unlockInner(acquiredLocks);
                    return false;
                }
            }
        }

        if (leaseTime != -1) {
            List<RFuture<Boolean>> futures = new ArrayList<>(acquiredLocks.size());
            for (RLock rLock : acquiredLocks) {
                RFuture<Boolean> future = ((RedissonLock) rLock).expireAsync(unit.toMillis(leaseTime), TimeUnit.MILLISECONDS);
                futures.add(future);
            }

            for (RFuture<Boolean> rFuture : futures) {
                rFuture.syncUninterruptibly();
            }
        }

        return true;
    }

    private int failedLocksLimit() {
        return locks.size() - minLocksAmount(locks.size());
    }

    protected int minLocksAmount(final int locksAmount) {
        return locksAmount/2 + 1;
    }

    private long calcLockWaitTime(long remainTime) {
        return Math.max(remainTime / locks.size(), 1);
    }

    private void unlockInner(Collection<RLock> locks) {
        List<RFuture<Void>> futures = new ArrayList<>(locks.size());
        for (RLock lock : locks) {
            futures.add(lock.unlockAsync());
        }

        for (RFuture<Void> unlockFuture : futures) {
            unlockFuture.awaitUninterruptibly();
        }
    }
}
```

**2. 主从同步等待机制**

```java
public class MasterSlaveAwareLock {

    private RedissonClient masterClient;
    private List<RedissonClient> slaveClients;

    public boolean tryLockWithSyncWait(String lockKey, String requestId,
                                      long leaseTime, long syncWaitTime) {

        // 1. 在Master上获取锁
        RLock masterLock = masterClient.getLock(lockKey);
        boolean acquired = false;

        try {
            acquired = masterLock.tryLock(10, leaseTime, TimeUnit.SECONDS);
            if (!acquired) {
                return false;
            }

            // 2. 等待主从同步
            Thread.sleep(syncWaitTime);

            // 3. 验证Slave上的锁状态
            boolean syncSuccess = verifySlaveLockState(lockKey, requestId);
            if (!syncSuccess) {
                // 同步失败，释放锁
                masterLock.unlock();
                return false;
            }

            return true;

        } catch (InterruptedException e) {
            if (acquired) {
                masterLock.unlock();
            }
            Thread.currentThread().interrupt();
            return false;
        }
    }

    private boolean verifySlaveLockState(String lockKey, String requestId) {
        int successCount = 0;
        int requiredCount = slaveClients.size() / 2 + 1;

        for (RedissonClient slaveClient : slaveClients) {
            try {
                RLock slaveLock = slaveClient.getLock(lockKey);
                if (slaveLock.isLocked()) {
                    successCount++;
                }
            } catch (Exception e) {
                // 忽略Slave异常
            }
        }

        return successCount >= requiredCount;
    }
}
```

**3. 故障检测和自动恢复**

```java
public class FaultTolerantLock {

    private RedissonClient redissonClient;
    private ScheduledExecutorService scheduler;

    public void lockWithFaultDetection(String lockKey, Runnable business) {
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                // 启动故障检测
                ScheduledFuture<?> detector = startFaultDetection(lock);

                try {
                    business.run();
                } finally {
                    detector.cancel(true);
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private ScheduledFuture<?> startFaultDetection(RLock lock) {
        return scheduler.scheduleAtFixedRate(() -> {
            try {
                // 检查锁状态
                if (!lock.isLocked()) {
                    // 锁丢失，记录日志并触发告警
                    log.error("Lock lost detected: {}", lock.getName());
                    // 可以在这里实现补偿逻辑
                }

                // 检查Redis连接状态
                checkRedisConnection();

            } catch (Exception e) {
                log.error("Fault detection error", e);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }

    private void checkRedisConnection() {
        try {
            redissonClient.getBucket("health-check").get();
        } catch (Exception e) {
            log.warn("Redis connection issue detected", e);
            // 可以触发主从切换逻辑
        }
    }
}
```

**最佳实践建议：**

1. **使用RedLock**：在对一致性要求极高的场景下使用RedLock
2. **合理设置超时**：根据业务特点设置合适的锁超时时间
3. **监控告警**：建立完善的监控和告警机制
4. **业务幂等**：确保业务逻辑的幂等性，即使出现锁丢失也不会造成严重后果
5. **降级方案**：准备降级方案，在分布式锁不可用时的处理策略

## 实战篇

### 12. 分布式锁在高并发场景下的性能优化策略？

**答案：**

在高并发场景下，分布式锁的性能优化至关重要，需要从多个维度进行优化。

**性能优化策略：**

```mermaid
flowchart TD
    A["性能优化策略"] --> B["锁粒度优化"]
    A --> C["锁竞争减少"]
    A --> D["连接池优化"]
    A --> E["批量操作"]
    A --> F["异步处理"]

    B --> G["细粒度锁"]
    B --> H["分段锁"]
    B --> I["读写分离"]

    C --> J["锁分片"]
    C --> K["随机退避"]
    C --> L["公平锁"]

    D --> M["连接复用"]
    D --> N["连接预热"]
    D --> O["连接监控"]

    E --> P["批量获取"]
    E --> Q["批量释放"]

    F --> R["异步获取锁"]
    F --> S["异步释放锁"]
```

**1. 锁粒度优化**

```java
// 细粒度锁：避免大范围锁竞争
@Service
public class OrderService {

    @Autowired
    private RedissonClient redissonClient;

    // 错误做法：使用全局锁
    public void processOrderWrong(String orderId) {
        RLock globalLock = redissonClient.getLock("global:order:lock");
        // 所有订单处理都会竞争这一个锁
    }

    // 正确做法：使用订单级别的锁
    public void processOrderCorrect(String orderId) {
        RLock orderLock = redissonClient.getLock("order:lock:" + orderId);
        // 只有相同订单ID的请求才会竞争
    }

    // 分段锁：进一步减少竞争
    public void processOrderWithSharding(String orderId) {
        // 根据订单ID计算分片
        int shard = Math.abs(orderId.hashCode()) % 16;
        RLock shardLock = redissonClient.getLock("order:lock:shard:" + shard);
    }
}

// 读写锁：读多写少场景优化
@Service
public class CacheService {

    @Autowired
    private RedissonClient redissonClient;

    public String readCache(String key) {
        RReadWriteLock rwLock = redissonClient.getReadWriteLock("cache:lock:" + key);
        RLock readLock = rwLock.readLock();

        try {
            readLock.lock();
            // 多个读操作可以并发执行
            return getCacheValue(key);
        } finally {
            readLock.unlock();
        }
    }

    public void updateCache(String key, String value) {
        RReadWriteLock rwLock = redissonClient.getReadWriteLock("cache:lock:" + key);
        RLock writeLock = rwLock.writeLock();

        try {
            writeLock.lock();
            // 写操作独占执行
            setCacheValue(key, value);
        } finally {
            writeLock.unlock();
        }
    }
}
```

**2. 锁竞争优化**

```java
// 随机退避策略
public class BackoffLockStrategy {

    private Random random = new Random();

    public boolean tryLockWithBackoff(RLock lock, int maxRetries) {
        for (int i = 0; i < maxRetries; i++) {
            try {
                if (lock.tryLock(100, TimeUnit.MILLISECONDS)) {
                    return true;
                }

                // 随机退避，避免惊群效应
                int backoffTime = random.nextInt(100) + 50; // 50-150ms
                Thread.sleep(backoffTime);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }
}

// 公平锁：按请求顺序获取锁
@Service
public class FairLockService {

    @Autowired
    private RedissonClient redissonClient;

    public void processWithFairLock(String resource) {
        RLock fairLock = redissonClient.getFairLock("fair:lock:" + resource);

        try {
            // 公平锁保证FIFO顺序
            if (fairLock.tryLock(10, 30, TimeUnit.SECONDS)) {
                try {
                    processResource(resource);
                } finally {
                    fairLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
```

**3. 连接池优化**

```java
// Redis连接池配置优化
@Configuration
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 单机模式配置
        SingleServerConfig serverConfig = config.useSingleServer()
            .setAddress("redis://localhost:6379")
            .setDatabase(0);

        // 连接池优化
        serverConfig.setConnectionPoolSize(64)        // 连接池大小
                   .setConnectionMinimumIdleSize(16)  // 最小空闲连接
                   .setConnectTimeout(3000)           // 连接超时
                   .setTimeout(3000)                  // 响应超时
                   .setRetryAttempts(3)               // 重试次数
                   .setRetryInterval(1500);           // 重试间隔

        // 集群模式配置
        ClusterServersConfig clusterConfig = config.useClusterServers()
            .addNodeAddress("redis://127.0.0.1:7000", "redis://127.0.0.1:7001")
            .setMasterConnectionPoolSize(64)
            .setSlaveConnectionPoolSize(64)
            .setMasterConnectionMinimumIdleSize(16)
            .setSlaveConnectionMinimumIdleSize(16);

        return Redisson.create(config);
    }
}
```

**4. 批量操作优化**

```java
// 批量锁操作
public class BatchLockService {

    @Autowired
    private RedissonClient redissonClient;

    public boolean tryLockBatch(List<String> lockKeys, long waitTime, long leaseTime) {
        List<RLock> locks = lockKeys.stream()
            .map(key -> redissonClient.getLock(key))
            .collect(Collectors.toList());

        // 使用MultiLock批量获取
        RLock multiLock = redissonClient.getMultiLock(locks.toArray(new RLock[0]));

        try {
            return multiLock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    // 批量释放
    public void unlockBatch(List<String> lockKeys) {
        List<RLock> locks = lockKeys.stream()
            .map(key -> redissonClient.getLock(key))
            .collect(Collectors.toList());

        RLock multiLock = redissonClient.getMultiLock(locks.toArray(new RLock[0]));
        multiLock.unlock();
    }
}
```

**5. 异步处理优化**

```java
// 异步锁操作
@Service
public class AsyncLockService {

    @Autowired
    private RedissonClient redissonClient;

    public CompletableFuture<Void> processAsync(String lockKey, Supplier<Void> business) {
        RLock lock = redissonClient.getLock(lockKey);

        return lock.tryLockAsync(10, 30, TimeUnit.SECONDS)
            .thenCompose(acquired -> {
                if (acquired) {
                    return CompletableFuture.supplyAsync(business)
                        .whenComplete((result, ex) -> {
                            // 异步释放锁
                            lock.unlockAsync();
                        });
                } else {
                    return CompletableFuture.failedFuture(
                        new RuntimeException("Failed to acquire lock"));
                }
            });
    }
}
```

**6. 性能监控和调优**

```java
// 锁性能监控
@Component
public class LockPerformanceMonitor {

    private final MeterRegistry meterRegistry;
    private final Timer lockAcquisitionTimer;
    private final Counter lockFailureCounter;

    public LockPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.lockAcquisitionTimer = Timer.builder("lock.acquisition.time")
            .description("Time taken to acquire lock")
            .register(meterRegistry);
        this.lockFailureCounter = Counter.builder("lock.acquisition.failures")
            .description("Number of lock acquisition failures")
            .register(meterRegistry);
    }

    public <T> T executeWithMonitoring(String lockKey, Supplier<T> business) {
        RLock lock = redissonClient.getLock(lockKey);
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                sample.stop(lockAcquisitionTimer);
                try {
                    return business.get();
                } finally {
                    lock.unlock();
                }
            } else {
                lockFailureCounter.increment();
                throw new RuntimeException("Failed to acquire lock: " + lockKey);
            }
        } catch (InterruptedException e) {
            lockFailureCounter.increment();
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted while acquiring lock", e);
        }
    }
}
```

**性能优化效果对比：**

| 优化策略 | 优化前QPS | 优化后QPS | 提升比例 |
|----------|-----------|-----------|----------|
| 锁粒度优化 | 1000 | 5000 | 400% |
| 连接池优化 | 1000 | 1500 | 50% |
| 批量操作 | 1000 | 2000 | 100% |
| 异步处理 | 1000 | 3000 | 200% |
| 综合优化 | 1000 | 8000 | 700% |

### 13. 分布式锁的最佳实践和常见陷阱？

**答案：**

分布式锁在实际应用中有很多最佳实践和需要避免的陷阱。

**最佳实践：**

```mermaid
flowchart TD
    A["分布式锁最佳实践"] --> B["设计原则"]
    A --> C["使用规范"]
    A --> D["异常处理"]
    A --> E["监控告警"]

    B --> F["最小锁范围"]
    B --> G["合理超时时间"]
    B --> H["幂等性设计"]

    C --> I["try-finally模式"]
    C --> J["避免嵌套锁"]
    C --> K["锁key设计"]

    D --> L["获取锁失败"]
    D --> M["业务异常处理"]
    D --> N["锁释放异常"]

    E --> O["锁竞争监控"]
    E --> P["性能指标"]
    E --> Q["异常告警"]
```

**1. 标准使用模式**

```java
// 正确的锁使用模式
@Service
public class StandardLockUsage {

    @Autowired
    private RedissonClient redissonClient;

    public void processBusinessLogic(String businessId) {
        String lockKey = "business:lock:" + businessId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 1. 设置合理的等待时间和锁过期时间
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                try {
                    // 2. 执行业务逻辑
                    executeBusinessLogic(businessId);
                } finally {
                    // 3. 确保在finally中释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                // 4. 获取锁失败的处理
                throw new BusinessException("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            // 5. 中断异常处理
            Thread.currentThread().interrupt();
            throw new BusinessException("操作被中断");
        }
    }

    private void executeBusinessLogic(String businessId) {
        // 业务逻辑实现
        log.info("Processing business: {}", businessId);
    }
}
```

**2. 锁Key设计规范**

```java
// 锁Key设计最佳实践
public class LockKeyDesign {

    // 使用有意义的前缀和层次结构
    private static final String LOCK_PREFIX = "distributed:lock:";

    // 订单锁
    public String getOrderLockKey(String orderId) {
        return LOCK_PREFIX + "order:" + orderId;
    }

    // 用户锁
    public String getUserLockKey(String userId) {
        return LOCK_PREFIX + "user:" + userId;
    }

    // 库存锁
    public String getInventoryLockKey(String productId) {
        return LOCK_PREFIX + "inventory:" + productId;
    }

    // 分片锁（减少竞争）
    public String getShardedLockKey(String businessId, int shardCount) {
        int shard = Math.abs(businessId.hashCode()) % shardCount;
        return LOCK_PREFIX + "shard:" + shard + ":" + businessId;
    }
}
```

**3. 异常处理最佳实践**

```java
// 完善的异常处理
@Service
public class RobustLockService {

    @Autowired
    private RedissonClient redissonClient;

    public <T> T executeWithLock(String lockKey, Supplier<T> business, T defaultValue) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean lockAcquired = false;

        try {
            // 尝试获取锁
            lockAcquired = lock.tryLock(10, 30, TimeUnit.SECONDS);

            if (lockAcquired) {
                return business.get();
            } else {
                log.warn("Failed to acquire lock: {}", lockKey);
                return defaultValue;
            }

        } catch (InterruptedException e) {
            log.error("Interrupted while acquiring lock: {}", lockKey, e);
            Thread.currentThread().interrupt();
            return defaultValue;

        } catch (Exception e) {
            log.error("Error executing business logic with lock: {}", lockKey, e);
            return defaultValue;

        } finally {
            // 安全释放锁
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("Error releasing lock: {}", lockKey, e);
                }
            }
        }
    }
}
```

**4. 常见陷阱和解决方案**

```java
// 陷阱1：锁粒度过大
public class LockGranularityTraps {

    // 错误：使用全局锁
    public void badExample() {
        RLock globalLock = redissonClient.getLock("global-lock");
        // 所有操作都竞争同一个锁，性能极差
    }

    // 正确：使用细粒度锁
    public void goodExample(String userId, String orderId) {
        RLock userLock = redissonClient.getLock("user:lock:" + userId);
        RLock orderLock = redissonClient.getLock("order:lock:" + orderId);
        // 不同用户和订单的操作不会相互影响
    }
}

// 陷阱2：死锁问题
public class DeadlockTraps {

    // 错误：可能导致死锁的嵌套锁
    public void badExample(String lockA, String lockB) {
        RLock lock1 = redissonClient.getLock(lockA);
        RLock lock2 = redissonClient.getLock(lockB);

        try {
            lock1.lock();
            try {
                lock2.lock(); // 可能导致死锁
                // 业务逻辑
            } finally {
                lock2.unlock();
            }
        } finally {
            lock1.unlock();
        }
    }

    // 正确：使用MultiLock避免死锁
    public void goodExample(String lockA, String lockB) {
        RLock lock1 = redissonClient.getLock(lockA);
        RLock lock2 = redissonClient.getLock(lockB);
        RLock multiLock = redissonClient.getMultiLock(lock1, lock2);

        try {
            if (multiLock.tryLock(10, 30, TimeUnit.SECONDS)) {
                // 业务逻辑
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            multiLock.unlock();
        }
    }
}

// 陷阱3：锁超时时间设置不当
public class TimeoutTraps {

    // 错误：超时时间过短
    public void badExample() {
        RLock lock = redissonClient.getLock("business-lock");
        try {
            if (lock.tryLock(1, 5, TimeUnit.SECONDS)) { // 5秒太短
                // 复杂业务逻辑可能需要更长时间
                complexBusinessLogic(); // 可能需要10秒
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }

    // 正确：合理设置超时时间或使用看门狗
    public void goodExample() {
        RLock lock = redissonClient.getLock("business-lock");
        try {
            // 方案1：设置足够的超时时间
            if (lock.tryLock(10, 60, TimeUnit.SECONDS)) {
                complexBusinessLogic();
            }

            // 方案2：使用看门狗（不指定leaseTime）
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                complexBusinessLogic(); // 看门狗会自动续期
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }
}
```

**5. 监控和告警**

```java
// 分布式锁监控
@Component
public class LockMonitoring {

    private final MeterRegistry meterRegistry;

    public LockMonitoring(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public <T> T executeWithMonitoring(String lockKey, String operation,
                                      Supplier<T> business) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            RLock lock = redissonClient.getLock(lockKey);

            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                sample.stop(Timer.builder("lock.acquisition.success")
                    .tag("operation", operation)
                    .register(meterRegistry));

                try {
                    return business.get();
                } finally {
                    lock.unlock();
                }
            } else {
                // 记录获取锁失败
                Counter.builder("lock.acquisition.failure")
                    .tag("operation", operation)
                    .register(meterRegistry)
                    .increment();

                throw new RuntimeException("Failed to acquire lock: " + lockKey);
            }
        } catch (InterruptedException e) {
            Counter.builder("lock.acquisition.interrupted")
                .tag("operation", operation)
                .register(meterRegistry)
                .increment();

            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted", e);
        }
    }
}
```

**6. 性能测试和调优**

```java
// 分布式锁性能测试
@Component
public class LockPerformanceTest {

    @Autowired
    private RedissonClient redissonClient;

    public void performanceTest(int threadCount, int operationCount) {
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failureCount = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationCount; j++) {
                        String lockKey = "perf:test:lock:" + (j % 100); // 100个不同的锁
                        RLock lock = redissonClient.getLock(lockKey);

                        try {
                            if (lock.tryLock(100, 1000, TimeUnit.MILLISECONDS)) {
                                try {
                                    // 模拟业务处理
                                    Thread.sleep(10);
                                    successCount.incrementAndGet();
                                } finally {
                                    lock.unlock();
                                }
                            } else {
                                failureCount.incrementAndGet();
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            failureCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
            long endTime = System.currentTimeMillis();

            log.info("Performance Test Results:");
            log.info("Thread Count: {}", threadCount);
            log.info("Operation Count per Thread: {}", operationCount);
            log.info("Total Operations: {}", threadCount * operationCount);
            log.info("Success Count: {}", successCount.get());
            log.info("Failure Count: {}", failureCount.get());
            log.info("Total Time: {} ms", endTime - startTime);
            log.info("TPS: {}", (successCount.get() * 1000.0) / (endTime - startTime));

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
    }
}
```

**总结要点：**

1. **设计原则**：最小锁范围、合理超时、幂等性
2. **使用规范**：try-finally模式、避免嵌套锁、规范Key设计
3. **异常处理**：完善的异常捕获和处理机制
4. **性能优化**：锁粒度、连接池、批量操作、异步处理
5. **监控告警**：关键指标监控、异常告警机制
6. **测试验证**：性能测试、压力测试、故障测试

通过遵循这些最佳实践和避免常见陷阱，可以构建高性能、高可靠的分布式锁系统。
```
