# 面试答案详解 - 第2批 (问题19-33)

> 项目深度问题详解 - 基于6年Java后端开发经验
> 
> 涵盖：智能语音质检平台、呼叫中心平台、号码隐藏保护平台的核心技术实现

---

## 智能语音质检平台核心技术

### 19. ASR和NLP技术集成：ASR和NLP技术如何集成？准确率如何提升？

**答案**：

**技术架构设计**：

我们的智能语音质检平台采用了**ASR(语音识别) + NLP(自然语言处理)**的双引擎架构，处理日均100万+通话录音。

**ASR集成方案**：
```java
@Service
public class AsrIntegrationService {
    
    @Autowired
    private AsrClientFactory asrClientFactory;
    
    @Autowired
    private AudioPreprocessor audioPreprocessor;
    
    // 多厂商ASR集成
    public AsrResult processAudio(AudioFile audioFile) {
        try {
            // 1. 音频预处理
            ProcessedAudio processedAudio = audioPreprocessor.preprocess(audioFile);
            
            // 2. 选择最优ASR厂商
            AsrProvider provider = selectOptimalProvider(audioFile);
            AsrClient client = asrClientFactory.getClient(provider);
            
            // 3. 执行语音识别
            AsrResult primaryResult = client.recognize(processedAudio);
            
            // 4. 质量检查和备用方案
            if (primaryResult.getConfidence() < 0.8) {
                AsrResult backupResult = getBackupResult(processedAudio, provider);
                return mergeResults(primaryResult, backupResult);
            }
            
            return primaryResult;
            
        } catch (Exception e) {
            log.error("ASR处理失败: audioId={}", audioFile.getId(), e);
            throw new ServiceException("语音识别失败", e);
        }
    }
    
    private AsrProvider selectOptimalProvider(AudioFile audioFile) {
        // 根据音频特征选择最优厂商
        AudioFeature feature = audioAnalyzer.analyze(audioFile);
        
        if (feature.getLanguage() == Language.CHINESE) {
            return feature.getDialect() == Dialect.CANTONESE ? 
                AsrProvider.TENCENT : AsrProvider.ALIBABA;
        } else {
            return AsrProvider.GOOGLE;
        }
    }
}
```

**音频预处理优化**：
```java
@Component
public class AudioPreprocessor {
    
    public ProcessedAudio preprocess(AudioFile audioFile) {
        AudioData audioData = audioFile.getData();
        
        // 1. 降噪处理
        audioData = noiseReducer.reduce(audioData);
        
        // 2. 音量标准化
        audioData = volumeNormalizer.normalize(audioData);
        
        // 3. 静音检测和分段
        List<AudioSegment> segments = silenceDetector.segment(audioData);
        
        // 4. 说话人分离
        Map<Speaker, List<AudioSegment>> speakerSegments = 
            speakerDiarization.separate(segments);
        
        return ProcessedAudio.builder()
            .originalFile(audioFile)
            .processedData(audioData)
            .speakerSegments(speakerSegments)
            .build();
    }
}
```

**NLP处理流水线**：
```java
@Service
public class NlpProcessingService {
    
    @Autowired
    private TextPreprocessor textPreprocessor;
    
    @Autowired
    private SentimentAnalyzer sentimentAnalyzer;
    
    @Autowired
    private KeywordExtractor keywordExtractor;
    
    @Autowired
    private IntentClassifier intentClassifier;
    
    public NlpResult processText(AsrResult asrResult) {
        String text = asrResult.getText();
        
        // 1. 文本预处理
        String cleanText = textPreprocessor.clean(text);
        
        // 2. 分词和词性标注
        List<Token> tokens = textPreprocessor.tokenize(cleanText);
        
        // 3. 情感分析
        SentimentResult sentiment = sentimentAnalyzer.analyze(cleanText);
        
        // 4. 关键词提取
        List<Keyword> keywords = keywordExtractor.extract(tokens);
        
        // 5. 意图识别
        IntentResult intent = intentClassifier.classify(cleanText);
        
        // 6. 实体识别
        List<Entity> entities = entityRecognizer.recognize(tokens);
        
        return NlpResult.builder()
            .originalText(text)
            .cleanText(cleanText)
            .tokens(tokens)
            .sentiment(sentiment)
            .keywords(keywords)
            .intent(intent)
            .entities(entities)
            .build();
    }
}
```

**准确率提升策略**：

**1. 多模型融合**：
```java
@Service
public class ModelEnsembleService {
    
    // 集成多个ASR模型
    public AsrResult ensembleAsr(AudioFile audioFile) {
        List<AsrResult> results = Arrays.asList(
            alibabaAsrService.recognize(audioFile),
            tencentAsrService.recognize(audioFile),
            baiduAsrService.recognize(audioFile)
        );
        
        // 投票机制选择最优结果
        return selectBestResult(results);
    }
    
    // 集成多个NLP模型
    public NlpResult ensembleNlp(String text) {
        List<SentimentResult> sentiments = Arrays.asList(
            bertSentimentModel.analyze(text),
            lstmSentimentModel.analyze(text),
            transformerSentimentModel.analyze(text)
        );
        
        // 加权平均
        return weightedAverage(sentiments);
    }
}
```

**2. 领域自适应训练**：
```java
@Service
public class DomainAdaptationService {
    
    // 构建领域词典
    public void buildDomainVocabulary() {
        // 收集呼叫中心专业术语
        List<String> domainTerms = Arrays.asList(
            "投诉", "退款", "换货", "客服", "工单",
            "满意度", "回访", "升级", "转接"
        );
        
        // 更新ASR词典
        asrService.updateVocabulary(domainTerms);
        
        // 训练领域NLP模型
        nlpModelTrainer.trainDomainModel(domainTerms);
    }
    
    // 持续学习机制
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void continuousLearning() {
        // 收集人工标注数据
        List<LabeledData> newData = dataCollector.collectRecentLabels();
        
        // 增量训练模型
        modelTrainer.incrementalTrain(newData);
        
        // 模型效果评估
        ModelMetrics metrics = modelEvaluator.evaluate();
        
        if (metrics.getAccuracy() > currentModel.getAccuracy()) {
            // 模型更新
            modelManager.updateModel(newModel);
        }
    }
}
```

**3. 质量控制机制**：
```java
@Component
public class QualityControlService {
    
    public QualityScore assessQuality(AsrResult asrResult, NlpResult nlpResult) {
        QualityScore score = new QualityScore();
        
        // ASR质量评估
        score.setAsrConfidence(asrResult.getConfidence());
        score.setAudioQuality(assessAudioQuality(asrResult.getAudioFile()));
        
        // NLP质量评估
        score.setTextCoherence(assessTextCoherence(nlpResult.getCleanText()));
        score.setEntityConsistency(assessEntityConsistency(nlpResult.getEntities()));
        
        // 综合质量分数
        double overallScore = calculateOverallScore(score);
        score.setOverallScore(overallScore);
        
        return score;
    }
    
    private double assessAudioQuality(AudioFile audioFile) {
        // 信噪比检测
        double snr = audioAnalyzer.calculateSNR(audioFile);
        
        // 音频完整性检测
        boolean isComplete = audioAnalyzer.checkCompleteness(audioFile);
        
        // 采样率和比特率检测
        AudioFormat format = audioFile.getFormat();
        
        return calculateAudioQualityScore(snr, isComplete, format);
    }
}
```

**效果数据**：
- **ASR准确率**：从85%提升到92%
- **NLP情感分析准确率**：从78%提升到89%
- **关键词提取准确率**：从82%提升到94%
- **端到端处理时延**：从30秒优化到8秒
- **日处理能力**：支持100万+通话录音

**遇到的挑战和解决方案**：

1. **方言识别问题**：通过收集各地方言样本，训练专门的方言识别模型
2. **噪音环境处理**：采用深度学习降噪算法，提升噪音环境下的识别率
3. **实时性要求**：通过流式处理和模型优化，将处理时延降低到秒级

### 20. 质检规则引擎：规则如何配置和执行？支持哪些规则类型？

**答案**：

**规则引擎架构设计**：

我们设计了一套灵活的质检规则引擎，支持**可视化配置**、**实时执行**、**动态更新**，日均执行质检规则1000万+次。

**规则配置系统**：
```java
@Entity
@Table(name = "quality_rule")
public class QualityRule {
    
    @Id
    private Long id;
    
    @Column(name = "rule_name")
    private String ruleName;
    
    @Column(name = "rule_type")
    @Enumerated(EnumType.STRING)
    private RuleType ruleType;
    
    @Column(name = "rule_config", columnDefinition = "JSON")
    private String ruleConfig; // JSON格式的规则配置
    
    @Column(name = "priority")
    private Integer priority; // 规则优先级
    
    @Column(name = "enabled")
    private Boolean enabled;
    
    @Column(name = "business_line")
    private String businessLine; // 业务线
    
    // 规则生效条件
    @Column(name = "conditions", columnDefinition = "JSON")
    private String conditions;
    
    // 规则执行动作
    @Column(name = "actions", columnDefinition = "JSON")
    private String actions;
}

// 规则类型枚举
public enum RuleType {
    KEYWORD_DETECTION,      // 关键词检测
    SENTIMENT_ANALYSIS,     // 情感分析
    SPEECH_SPEED,          // 语速检测
    SILENCE_DETECTION,     // 静音检测
    INTERRUPTION_ANALYSIS, // 打断分析
    COMPLIANCE_CHECK,      // 合规检查
    CUSTOM_SCRIPT         // 自定义脚本
}
```

**规则执行引擎**：
```java
@Service
public class QualityRuleEngine {

    @Autowired
    private RuleConfigService ruleConfigService;

    @Autowired
    private RuleExecutorFactory executorFactory;

    // 执行质检规则
    public QualityCheckResult executeRules(CallRecord callRecord) {
        QualityCheckResult result = new QualityCheckResult();
        result.setCallId(callRecord.getCallId());
        result.setCheckTime(System.currentTimeMillis());

        // 获取适用的规则
        List<QualityRule> applicableRules = getApplicableRules(callRecord);

        // 按优先级排序执行
        applicableRules.sort(Comparator.comparing(QualityRule::getPriority));

        for (QualityRule rule : applicableRules) {
            try {
                RuleExecutor executor = executorFactory.getExecutor(rule.getRuleType());
                RuleExecutionResult ruleResult = executor.execute(rule, callRecord);

                result.addRuleResult(ruleResult);

                // 如果是阻断性规则且触发，停止后续规则执行
                if (ruleResult.isBlocking() && ruleResult.isTriggered()) {
                    break;
                }

            } catch (Exception e) {
                log.error("规则执行失败: ruleId={}, callId={}",
                    rule.getId(), callRecord.getCallId(), e);

                // 记录规则执行异常
                result.addError(rule.getId(), e.getMessage());
            }
        }

        // 计算综合质检分数
        result.setOverallScore(calculateOverallScore(result));

        return result;
    }

    private List<QualityRule> getApplicableRules(CallRecord callRecord) {
        return ruleConfigService.getRulesByBusinessLine(callRecord.getBusinessLine())
            .stream()
            .filter(rule -> rule.getEnabled())
            .filter(rule -> matchesConditions(rule, callRecord))
            .collect(Collectors.toList());
    }
}
```

**具体规则执行器实现**：

**1. 关键词检测规则**：
```java
@Component
public class KeywordDetectionExecutor implements RuleExecutor {

    @Override
    public RuleExecutionResult execute(QualityRule rule, CallRecord callRecord) {
        KeywordRuleConfig config = JsonUtils.fromJsonString(
            rule.getRuleConfig(), KeywordRuleConfig.class);

        String transcript = callRecord.getTranscript();
        List<String> keywords = config.getKeywords();

        List<KeywordMatch> matches = new ArrayList<>();

        for (String keyword : keywords) {
            List<Integer> positions = findKeywordPositions(transcript, keyword);
            if (!positions.isEmpty()) {
                matches.add(new KeywordMatch(keyword, positions));
            }
        }

        boolean triggered = matches.size() >= config.getMinMatchCount();

        return RuleExecutionResult.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getRuleName())
            .triggered(triggered)
            .score(calculateKeywordScore(matches, config))
            .details(matches)
            .build();
    }

    private List<Integer> findKeywordPositions(String text, String keyword) {
        List<Integer> positions = new ArrayList<>();
        int index = 0;

        while ((index = text.indexOf(keyword, index)) != -1) {
            positions.add(index);
            index += keyword.length();
        }

        return positions;
    }
}
```

**2. 情感分析规则**：
```java
@Component
public class SentimentAnalysisExecutor implements RuleExecutor {

    @Autowired
    private SentimentAnalyzer sentimentAnalyzer;

    @Override
    public RuleExecutionResult execute(QualityRule rule, CallRecord callRecord) {
        SentimentRuleConfig config = JsonUtils.fromJsonString(
            rule.getRuleConfig(), SentimentRuleConfig.class);

        String transcript = callRecord.getTranscript();

        // 分段情感分析
        List<String> segments = splitIntoSegments(transcript);
        List<SentimentResult> sentimentResults = new ArrayList<>();

        for (String segment : segments) {
            SentimentResult sentiment = sentimentAnalyzer.analyze(segment);
            sentimentResults.add(sentiment);
        }

        // 计算整体情感倾向
        double avgPositive = sentimentResults.stream()
            .mapToDouble(SentimentResult::getPositiveScore)
            .average()
            .orElse(0.0);

        double avgNegative = sentimentResults.stream()
            .mapToDouble(SentimentResult::getNegativeScore)
            .average()
            .orElse(0.0);

        // 判断是否触发规则
        boolean triggered = false;
        if (config.getType() == SentimentType.NEGATIVE) {
            triggered = avgNegative > config.getThreshold();
        } else if (config.getType() == SentimentType.POSITIVE) {
            triggered = avgPositive > config.getThreshold();
        }

        return RuleExecutionResult.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getRuleName())
            .triggered(triggered)
            .score(calculateSentimentScore(avgPositive, avgNegative, config))
            .details(Map.of(
                "avgPositive", avgPositive,
                "avgNegative", avgNegative,
                "segments", sentimentResults
            ))
            .build();
    }
}
```

**3. 语速检测规则**：
```java
@Component
public class SpeechSpeedExecutor implements RuleExecutor {

    @Override
    public RuleExecutionResult execute(QualityRule rule, CallRecord callRecord) {
        SpeechSpeedConfig config = JsonUtils.fromJsonString(
            rule.getRuleConfig(), SpeechSpeedConfig.class);

        // 计算语速 (字/分钟)
        String transcript = callRecord.getTranscript();
        long duration = callRecord.getDuration(); // 毫秒

        int wordCount = countWords(transcript);
        double wordsPerMinute = (wordCount * 60.0 * 1000) / duration;

        // 判断语速是否异常
        boolean triggered = false;
        String triggerReason = "";

        if (wordsPerMinute > config.getMaxSpeed()) {
            triggered = true;
            triggerReason = "语速过快: " + wordsPerMinute + " 字/分钟";
        } else if (wordsPerMinute < config.getMinSpeed()) {
            triggered = true;
            triggerReason = "语速过慢: " + wordsPerMinute + " 字/分钟";
        }

        return RuleExecutionResult.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getRuleName())
            .triggered(triggered)
            .score(calculateSpeedScore(wordsPerMinute, config))
            .details(Map.of(
                "wordsPerMinute", wordsPerMinute,
                "wordCount", wordCount,
                "duration", duration,
                "triggerReason", triggerReason
            ))
            .build();
    }
}
```

**规则配置管理界面**：
```java
@RestController
@RequestMapping("/admin/quality-rules")
public class QualityRuleController {

    @PostMapping("/create")
    public CommonResult<Long> createRule(@RequestBody QualityRuleDTO ruleDTO) {
        // 规则配置验证
        validateRuleConfig(ruleDTO);

        QualityRule rule = convertToEntity(ruleDTO);
        Long ruleId = qualityRuleService.createRule(rule);

        // 规则生效通知
        ruleChangeNotifier.notifyRuleCreated(ruleId);

        return CommonResult.success(ruleId);
    }

    @PutMapping("/{id}")
    public CommonResult<Void> updateRule(@PathVariable Long id,
                                       @RequestBody QualityRuleDTO ruleDTO) {
        qualityRuleService.updateRule(id, ruleDTO);

        // 热更新规则配置
        ruleEngine.reloadRule(id);

        return CommonResult.success();
    }

    @PostMapping("/{id}/test")
    public CommonResult<RuleTestResult> testRule(@PathVariable Long id,
                                                @RequestBody CallRecord testData) {
        QualityRule rule = qualityRuleService.getById(id);
        RuleExecutionResult result = ruleEngine.testRule(rule, testData);

        return CommonResult.success(RuleTestResult.from(result));
    }
}
```

**支持的规则类型详解**：

**1. 关键词检测规则**：
- 敏感词检测
- 禁用词检测
- 专业术语使用检查
- 品牌词提及检测

**2. 情感分析规则**：
- 客户满意度评估
- 投诉情绪识别
- 服务态度检查
- 情绪波动分析

**3. 语速检测规则**：
- 语速过快检测
- 语速过慢检测
- 语速变化分析

**4. 静音检测规则**：
- 长时间静音检测
- 频繁静音检测
- 异常静音模式识别

**5. 合规检查规则**：
- 开场白标准检查
- 结束语规范检查
- 必要信息确认检查
- 法律条款告知检查

**效果数据**：
- **规则执行效率**：单条规则平均执行时间50ms
- **规则准确率**：平均准确率达到91%
- **规则覆盖率**：覆盖95%的质检场景
- **配置灵活性**：支持可视化配置，业务人员可独立操作

### 21. 实时质检：如何实现通话过程中的实时质检？

**答案**：

**实时质检架构设计**：

实时质检是我们系统的核心功能之一，需要在通话进行过程中实时分析语音内容并给出质检结果，要求**低延迟**（<3秒）、**高准确率**（>85%）、**高并发**（5000+并发通话）。

**实时音频流处理**：
```java
@Component
public class RealTimeAudioProcessor {

    @Autowired
    private StreamingAsrService streamingAsrService;

    @Autowired
    private RealTimeRuleEngine realTimeRuleEngine;

    @Autowired
    private WebSocketNotificationService notificationService;

    // 处理实时音频流
    @EventListener
    public void handleAudioStream(AudioStreamEvent event) {
        String callId = event.getCallId();
        byte[] audioData = event.getAudioData();

        try {
            // 1. 流式ASR处理
            StreamingAsrResult asrResult = streamingAsrService.processChunk(
                callId, audioData);

            if (asrResult.hasNewText()) {
                // 2. 实时文本分析
                RealTimeAnalysisResult analysisResult =
                    realTimeRuleEngine.analyzeText(callId, asrResult.getNewText());

                // 3. 触发告警检查
                checkAndTriggerAlerts(callId, analysisResult);

                // 4. 更新实时质检状态
                updateRealTimeStatus(callId, analysisResult);
            }

        } catch (Exception e) {
            log.error("实时音频处理失败: callId={}", callId, e);
        }
    }

    private void checkAndTriggerAlerts(String callId, RealTimeAnalysisResult result) {
        for (RuleViolation violation : result.getViolations()) {
            if (violation.getSeverity() == Severity.HIGH) {
                // 高优先级违规，立即告警
                AlertMessage alert = AlertMessage.builder()
                    .callId(callId)
                    .ruleId(violation.getRuleId())
                    .ruleName(violation.getRuleName())
                    .severity(violation.getSeverity())
                    .timestamp(System.currentTimeMillis())
                    .build();

                // 发送实时告警
                notificationService.sendRealTimeAlert(callId, alert);

                // 记录告警日志
                alertLogService.recordAlert(alert);
            }
        }
    }
}
```

**流式ASR服务**：
```java
@Service
public class StreamingAsrService {

    private final Map<String, AsrSession> activeSessions = new ConcurrentHashMap<>();

    public StreamingAsrResult processChunk(String callId, byte[] audioData) {
        AsrSession session = activeSessions.computeIfAbsent(callId,
            k -> createNewSession(callId));

        // 音频数据缓冲
        session.appendAudioData(audioData);

        // 检查是否有足够数据进行识别
        if (session.hasEnoughDataForRecognition()) {
            return performStreamingRecognition(session);
        }

        return StreamingAsrResult.empty();
    }

    private StreamingAsrResult performStreamingRecognition(AsrSession session) {
        try {
            // 获取待识别的音频片段
            byte[] audioChunk = session.getNextChunk();

            // 调用流式ASR API
            AsrResponse response = asrClient.streamingRecognize(
                session.getCallId(), audioChunk, session.getContext());

            // 更新会话上下文
            session.updateContext(response.getContext());

            // 处理识别结果
            String newText = extractNewText(response, session.getLastResult());
            session.setLastResult(response);

            return StreamingAsrResult.builder()
                .callId(session.getCallId())
                .newText(newText)
                .confidence(response.getConfidence())
                .timestamp(System.currentTimeMillis())
                .build();

        } catch (Exception e) {
            log.error("流式ASR识别失败: callId={}", session.getCallId(), e);
            return StreamingAsrResult.error(e.getMessage());
        }
    }
}
```

**实时规则引擎**：
```java
@Service
public class RealTimeRuleEngine {

    @Autowired
    private RuleConfigService ruleConfigService;

    @Autowired
    private TextAnalyzer textAnalyzer;

    // 实时文本分析
    public RealTimeAnalysisResult analyzeText(String callId, String newText) {
        RealTimeAnalysisResult result = new RealTimeAnalysisResult();
        result.setCallId(callId);
        result.setAnalysisTime(System.currentTimeMillis());

        // 获取实时规则（优化过的轻量级规则）
        List<QualityRule> realTimeRules = ruleConfigService.getRealTimeRules();

        for (QualityRule rule : realTimeRules) {
            try {
                RuleViolation violation = checkRuleViolation(rule, callId, newText);
                if (violation != null) {
                    result.addViolation(violation);
                }
            } catch (Exception e) {
                log.error("实时规则检查失败: ruleId={}, callId={}",
                    rule.getId(), callId, e);
            }
        }

        return result;
    }

    private RuleViolation checkRuleViolation(QualityRule rule, String callId, String text) {
        switch (rule.getRuleType()) {
            case KEYWORD_DETECTION:
                return checkKeywordViolation(rule, text);
            case SENTIMENT_ANALYSIS:
                return checkSentimentViolation(rule, text);
            case FORBIDDEN_WORDS:
                return checkForbiddenWords(rule, text);
            default:
                return null;
        }
    }

    private RuleViolation checkKeywordViolation(QualityRule rule, String text) {
        KeywordRuleConfig config = JsonUtils.fromJsonString(
            rule.getRuleConfig(), KeywordRuleConfig.class);

        for (String keyword : config.getKeywords()) {
            if (text.contains(keyword)) {
                return RuleViolation.builder()
                    .ruleId(rule.getId())
                    .ruleName(rule.getRuleName())
                    .violationType(ViolationType.KEYWORD_DETECTED)
                    .severity(config.getSeverity())
                    .details("检测到关键词: " + keyword)
                    .timestamp(System.currentTimeMillis())
                    .build();
            }
        }

        return null;
    }
}
```

**实时通知服务**：
```java
@Service
public class WebSocketNotificationService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    // 发送实时告警
    public void sendRealTimeAlert(String callId, AlertMessage alert) {
        // 发送给质检员
        String destination = "/topic/quality-alerts/" + callId;
        messagingTemplate.convertAndSend(destination, alert);

        // 发送给座席管理员
        if (alert.getSeverity() == Severity.HIGH) {
            messagingTemplate.convertAndSend("/topic/supervisor-alerts", alert);
        }

        log.info("发送实时告警: callId={}, ruleId={}", callId, alert.getRuleId());
    }

    // 更新实时质检状态
    public void updateRealTimeStatus(String callId, QualityStatus status) {
        RealTimeStatusUpdate update = RealTimeStatusUpdate.builder()
            .callId(callId)
            .status(status)
            .updateTime(System.currentTimeMillis())
            .build();

        messagingTemplate.convertAndSend("/topic/quality-status/" + callId, update);
    }
}
```

**实时质检前端展示**：
```java
@Controller
public class RealTimeQualityController {

    @MessageMapping("/quality/subscribe/{callId}")
    @SendTo("/topic/quality-updates/{callId}")
    public QualitySubscriptionResponse subscribeToQuality(@DestinationVariable String callId) {
        // 获取当前通话的实时质检状态
        RealTimeQualityStatus status = realTimeQualityService.getCurrentStatus(callId);

        return QualitySubscriptionResponse.builder()
            .callId(callId)
            .currentStatus(status)
            .subscriptionTime(System.currentTimeMillis())
            .build();
    }
}

// 前端WebSocket连接示例
@Component
public class QualityWebSocketClient {

    private StompSession stompSession;

    public void subscribeToQualityUpdates(String callId) {
        String destination = "/topic/quality-updates/" + callId;

        stompSession.subscribe(destination, new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return QualityUpdateMessage.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                QualityUpdateMessage update = (QualityUpdateMessage) payload;
                // 更新前端界面
                updateQualityDisplay(update);
            }
        });
    }
}
```

**性能优化策略**：

**1. 音频数据缓冲优化**：
```java
@Component
public class AudioBufferManager {

    private static final int BUFFER_SIZE = 4096; // 4KB缓冲区
    private static final int MIN_CHUNK_SIZE = 1024; // 最小处理块

    public class AudioBuffer {
        private final CircularBuffer buffer;
        private volatile boolean hasNewData = false;

        public void append(byte[] data) {
            buffer.write(data);
            hasNewData = true;
        }

        public byte[] getNextChunk() {
            if (buffer.available() >= MIN_CHUNK_SIZE) {
                hasNewData = false;
                return buffer.read(MIN_CHUNK_SIZE);
            }
            return null;
        }
    }
}
```

**2. 规则执行优化**：
```java
@Service
public class OptimizedRuleEngine {

    // 规则预编译
    private final Map<Long, CompiledRule> compiledRules = new ConcurrentHashMap<>();

    public void precompileRules() {
        List<QualityRule> rules = ruleConfigService.getAllRealTimeRules();

        rules.parallelStream().forEach(rule -> {
            CompiledRule compiled = compileRule(rule);
            compiledRules.put(rule.getId(), compiled);
        });
    }

    // 快速规则匹配
    public List<RuleViolation> fastMatch(String text) {
        return compiledRules.values().parallelStream()
            .map(rule -> rule.match(text))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
}
```

**实时质检效果数据**：
- **响应延迟**：平均2.1秒，95%请求<3秒
- **准确率**：实时检测准确率87%
- **并发能力**：支持5000+并发通话实时质检
- **资源消耗**：单通话CPU占用<2%，内存占用<50MB
- **告警及时性**：高优先级违规1秒内告警

**遇到的技术挑战**：

1. **音频流同步问题**：通过时间戳对齐和缓冲区管理解决
2. **ASR延迟优化**：采用流式识别+本地缓存策略
3. **规则执行性能**：通过规则预编译和并行处理优化
4. **WebSocket连接稳定性**：实现断线重连和心跳检测机制

### 22. 质检报告：质检结果如何生成和展示？支持哪些维度分析？

**答案**：

**质检报告生成架构**：

我们的质检报告系统支持**多维度分析**、**可视化展示**、**自定义报告**，日均生成质检报告10万+份，支持实时查询和历史数据分析。

**报告数据模型**：
```java
@Entity
@Table(name = "quality_report")
public class QualityReport {

    @Id
    private Long id;

    @Column(name = "call_id")
    private String callId;

    @Column(name = "agent_id")
    private String agentId;

    @Column(name = "customer_id")
    private String customerId;

    @Column(name = "business_line")
    private String businessLine;

    @Column(name = "call_start_time")
    private Long callStartTime;

    @Column(name = "call_duration")
    private Integer callDuration;

    @Column(name = "overall_score")
    private Double overallScore;

    @Column(name = "rule_results", columnDefinition = "JSON")
    private String ruleResults; // 规则执行结果

    @Column(name = "quality_dimensions", columnDefinition = "JSON")
    private String qualityDimensions; // 质检维度得分

    @Column(name = "violations", columnDefinition = "JSON")
    private String violations; // 违规记录

    @Column(name = "recommendations", columnDefinition = "JSON")
    private String recommendations; // 改进建议

    @Column(name = "report_status")
    @Enumerated(EnumType.STRING)
    private ReportStatus reportStatus;

    @Column(name = "generate_time")
    private Long generateTime;
}
```

**报告生成服务**：
```java
@Service
public class QualityReportService {

    @Autowired
    private QualityRuleEngine ruleEngine;

    @Autowired
    private ReportTemplateService templateService;

    @Autowired
    private ScoreCalculator scoreCalculator;

    // 生成质检报告
    public QualityReport generateReport(CallRecord callRecord) {
        try {
            // 1. 执行质检规则
            QualityCheckResult checkResult = ruleEngine.executeRules(callRecord);

            // 2. 计算各维度得分
            QualityDimensions dimensions = calculateDimensions(checkResult);

            // 3. 生成改进建议
            List<Recommendation> recommendations = generateRecommendations(checkResult);

            // 4. 构建报告
            QualityReport report = QualityReport.builder()
                .callId(callRecord.getCallId())
                .agentId(callRecord.getAgentId())
                .customerId(callRecord.getCustomerId())
                .businessLine(callRecord.getBusinessLine())
                .callStartTime(callRecord.getStartTime())
                .callDuration(callRecord.getDuration())
                .overallScore(checkResult.getOverallScore())
                .ruleResults(JsonUtils.toJsonString(checkResult.getRuleResults()))
                .qualityDimensions(JsonUtils.toJsonString(dimensions))
                .violations(JsonUtils.toJsonString(checkResult.getViolations()))
                .recommendations(JsonUtils.toJsonString(recommendations))
                .reportStatus(ReportStatus.COMPLETED)
                .generateTime(System.currentTimeMillis())
                .build();

            // 5. 保存报告
            qualityReportMapper.insert(report);

            // 6. 发送报告通知
            notificationService.sendReportNotification(report);

            return report;

        } catch (Exception e) {
            log.error("质检报告生成失败: callId={}", callRecord.getCallId(), e);
            throw new ServiceException("质检报告生成失败", e);
        }
    }

    // 计算质检维度得分
    private QualityDimensions calculateDimensions(QualityCheckResult checkResult) {
        QualityDimensions dimensions = new QualityDimensions();

        // 服务态度维度
        dimensions.setServiceAttitude(
            scoreCalculator.calculateServiceAttitudeScore(checkResult));

        // 专业能力维度
        dimensions.setProfessionalSkill(
            scoreCalculator.calculateProfessionalSkillScore(checkResult));

        // 沟通技巧维度
        dimensions.setCommunicationSkill(
            scoreCalculator.calculateCommunicationSkillScore(checkResult));

        // 合规性维度
        dimensions.setCompliance(
            scoreCalculator.calculateComplianceScore(checkResult));

        // 问题解决维度
        dimensions.setProblemSolving(
            scoreCalculator.calculateProblemSolvingScore(checkResult));

        return dimensions;
    }
}
```

**多维度分析实现**：
```java
@Service
public class QualityAnalysisService {

    // 座席维度分析
    public AgentQualityAnalysis analyzeByAgent(String agentId, DateRange dateRange) {
        List<QualityReport> reports = qualityReportMapper.selectByAgentAndDateRange(
            agentId, dateRange.getStartTime(), dateRange.getEndTime());

        AgentQualityAnalysis analysis = new AgentQualityAnalysis();
        analysis.setAgentId(agentId);
        analysis.setAnalysisPeriod(dateRange);

        // 计算平均得分
        double avgScore = reports.stream()
            .mapToDouble(QualityReport::getOverallScore)
            .average()
            .orElse(0.0);
        analysis.setAverageScore(avgScore);

        // 得分趋势分析
        List<ScoreTrend> trends = calculateScoreTrends(reports);
        analysis.setScoreTrends(trends);

        // 维度得分分析
        DimensionScores dimensionScores = calculateDimensionScores(reports);
        analysis.setDimensionScores(dimensionScores);

        // 常见问题分析
        List<CommonIssue> commonIssues = analyzeCommonIssues(reports);
        analysis.setCommonIssues(commonIssues);

        // 改进建议
        List<ImprovementSuggestion> suggestions = generateImprovementSuggestions(analysis);
        analysis.setImprovementSuggestions(suggestions);

        return analysis;
    }

    // 业务线维度分析
    public BusinessLineQualityAnalysis analyzeByBusinessLine(String businessLine, DateRange dateRange) {
        List<QualityReport> reports = qualityReportMapper.selectByBusinessLineAndDateRange(
            businessLine, dateRange.getStartTime(), dateRange.getEndTime());

        BusinessLineQualityAnalysis analysis = new BusinessLineQualityAnalysis();

        // 整体质量指标
        QualityMetrics metrics = calculateQualityMetrics(reports);
        analysis.setQualityMetrics(metrics);

        // 座席排名
        List<AgentRanking> rankings = calculateAgentRankings(reports);
        analysis.setAgentRankings(rankings);

        // 问题分布
        Map<String, Integer> issueDistribution = analyzeIssueDistribution(reports);
        analysis.setIssueDistribution(issueDistribution);

        // 时间段分析
        Map<Integer, Double> hourlyScores = analyzeHourlyScores(reports);
        analysis.setHourlyScores(hourlyScores);

        return analysis;
    }

    // 时间维度分析
    public TimeBasedQualityAnalysis analyzeByTime(DateRange dateRange, TimeGranularity granularity) {
        List<QualityReport> reports = qualityReportMapper.selectByDateRange(
            dateRange.getStartTime(), dateRange.getEndTime());

        TimeBasedQualityAnalysis analysis = new TimeBasedQualityAnalysis();

        // 按时间粒度分组
        Map<String, List<QualityReport>> groupedReports = groupByTimeGranularity(reports, granularity);

        // 计算每个时间段的质量指标
        Map<String, QualityMetrics> timeMetrics = new LinkedHashMap<>();
        for (Map.Entry<String, List<QualityReport>> entry : groupedReports.entrySet()) {
            QualityMetrics metrics = calculateQualityMetrics(entry.getValue());
            timeMetrics.put(entry.getKey(), metrics);
        }

        analysis.setTimeMetrics(timeMetrics);

        // 趋势分析
        TrendAnalysis trendAnalysis = analyzeTrends(timeMetrics);
        analysis.setTrendAnalysis(trendAnalysis);

        return analysis;
    }
}
```

**报告可视化展示**：
```java
@RestController
@RequestMapping("/api/quality-reports")
public class QualityReportController {

    @GetMapping("/{callId}")
    public CommonResult<QualityReportVO> getReport(@PathVariable String callId) {
        QualityReport report = qualityReportService.getByCallId(callId);
        QualityReportVO reportVO = convertToVO(report);
        return CommonResult.success(reportVO);
    }

    @GetMapping("/agent/{agentId}/analysis")
    public CommonResult<AgentQualityAnalysisVO> getAgentAnalysis(
            @PathVariable String agentId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        DateRange dateRange = new DateRange(startDate, endDate);
        AgentQualityAnalysis analysis = qualityAnalysisService.analyzeByAgent(agentId, dateRange);

        return CommonResult.success(convertToVO(analysis));
    }

    @GetMapping("/dashboard")
    public CommonResult<QualityDashboardVO> getDashboard(
            @RequestParam(required = false) String businessLine,
            @RequestParam(defaultValue = "7") int days) {

        Date endDate = new Date();
        Date startDate = DateUtils.addDays(endDate, -days);
        DateRange dateRange = new DateRange(startDate, endDate);

        QualityDashboard dashboard = qualityDashboardService.generateDashboard(businessLine, dateRange);

        return CommonResult.success(convertToVO(dashboard));
    }

    @PostMapping("/export")
    public void exportReports(@RequestBody ReportExportRequest request, HttpServletResponse response) {
        try {
            // 查询报告数据
            List<QualityReport> reports = qualityReportService.queryReports(request);

            // 生成Excel文件
            Workbook workbook = reportExportService.generateExcel(reports, request.getTemplate());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=quality_reports.xlsx");

            // 输出文件
            workbook.write(response.getOutputStream());

        } catch (Exception e) {
            log.error("报告导出失败", e);
            throw new ServiceException("报告导出失败", e);
        }
    }
}
```

**自定义报告模板**：
```java
@Service
public class ReportTemplateService {

    // 创建自定义报告模板
    public ReportTemplate createTemplate(ReportTemplateDTO templateDTO) {
        ReportTemplate template = new ReportTemplate();
        template.setTemplateName(templateDTO.getTemplateName());
        template.setBusinessLine(templateDTO.getBusinessLine());
        template.setTemplateType(templateDTO.getTemplateType());

        // 配置报告字段
        List<ReportField> fields = templateDTO.getFields().stream()
            .map(this::convertToReportField)
            .collect(Collectors.toList());
        template.setFields(JsonUtils.toJsonString(fields));

        // 配置图表设置
        List<ChartConfig> charts = templateDTO.getCharts().stream()
            .map(this::convertToChartConfig)
            .collect(Collectors.toList());
        template.setCharts(JsonUtils.toJsonString(charts));

        // 配置过滤条件
        FilterConfig filterConfig = convertToFilterConfig(templateDTO.getFilters());
        template.setFilters(JsonUtils.toJsonString(filterConfig));

        return reportTemplateMapper.insert(template);
    }

    // 根据模板生成报告
    public CustomReport generateCustomReport(Long templateId, ReportParams params) {
        ReportTemplate template = reportTemplateMapper.selectById(templateId);

        // 解析模板配置
        List<ReportField> fields = JsonUtils.fromJsonString(template.getFields(),
            new TypeReference<List<ReportField>>() {});
        List<ChartConfig> charts = JsonUtils.fromJsonString(template.getCharts(),
            new TypeReference<List<ChartConfig>>() {});
        FilterConfig filters = JsonUtils.fromJsonString(template.getFilters(), FilterConfig.class);

        // 查询数据
        List<QualityReport> reports = queryReportsByTemplate(template, params, filters);

        // 生成报告内容
        CustomReport customReport = new CustomReport();
        customReport.setTemplateId(templateId);
        customReport.setTemplateName(template.getTemplateName());
        customReport.setGenerateTime(System.currentTimeMillis());

        // 生成表格数据
        TableData tableData = generateTableData(reports, fields);
        customReport.setTableData(tableData);

        // 生成图表数据
        List<ChartData> chartDataList = generateChartData(reports, charts);
        customReport.setChartData(chartDataList);

        // 生成统计摘要
        StatisticsSummary summary = generateStatisticsSummary(reports);
        customReport.setSummary(summary);

        return customReport;
    }
}
```

**支持的分析维度**：

**1. 座席维度**：
- 个人质检得分趋势
- 各维度能力雷达图
- 常见问题分析
- 改进建议推荐

**2. 业务线维度**：
- 整体质量指标对比
- 座席质量排名
- 问题类型分布
- 时段质量分析

**3. 时间维度**：
- 日/周/月质量趋势
- 同比环比分析
- 季节性规律发现
- 异常时段识别

**4. 规则维度**：
- 规则触发频率统计
- 规则效果评估
- 规则优化建议
- 新规则推荐

**5. 客户维度**：
- 客户满意度分析
- 投诉原因分析
- 客户类型质量差异
- 服务改进建议

**报告系统效果数据**：
- **报告生成速度**：单份报告平均生成时间2秒
- **数据查询性能**：复杂分析查询响应时间<5秒
- **报告准确率**：数据准确率99.5%
- **用户满意度**：报告系统用户满意度4.6/5.0
- **使用频率**：日均报告查看量5万+次

---

## 呼叫中心平台核心技术

### 23. 呼叫中心架构：5000+并发座席如何支撑？架构如何设计？

**答案**：

**高并发呼叫中心架构设计**：

我们的呼叫中心平台支撑**5000+并发座席**、**日均通话量500万+**、**峰值QPS 10万+**，采用**微服务+分布式**架构，确保高可用、高性能、可扩展。

**整体架构图**：
```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                              │
│  Nginx + Keepalived (主备)  │  F5/LVS (硬件负载均衡)          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        网关层                                │
│  Spring Cloud Gateway (集群) │ 限流 │ 鉴权 │ 路由 │ 监控      │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      微服务层                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │座席管理服务  │ │通话路由服务  │ │录音管理服务  │ │质检服务     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │实时通信服务  │ │监控告警服务  │ │报表统计服务  │ │配置管理服务  │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                  │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │MySQL集群    │ │Redis集群    │ │MongoDB集群  │ │Elasticsearch│ │
│ │(分库分表)   │ │(缓存+会话)  │ │(录音元数据) │ │(日志+搜索)  │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │消息队列     │ │文件存储     │ │监控系统     │ │日志系统     │ │
│ │RocketMQ     │ │MinIO/OSS    │ │Prometheus   │ │ELK Stack    │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**核心服务架构实现**：

**1. 座席管理服务**：
```java
@Service
public class AgentManagementService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AgentStateManager agentStateManager;

    // 座席状态管理
    public void updateAgentState(String agentId, AgentState newState) {
        AgentInfo agentInfo = getAgentInfo(agentId);
        AgentState oldState = agentInfo.getState();

        // 状态变更校验
        if (!isValidStateTransition(oldState, newState)) {
            throw new ServiceException("无效的状态变更: " + oldState + " -> " + newState);
        }

        // 更新状态
        agentInfo.setState(newState);
        agentInfo.setStateChangeTime(System.currentTimeMillis());

        // 缓存更新
        String cacheKey = "agent:state:" + agentId;
        redisTemplate.opsForValue().set(cacheKey, agentInfo, Duration.ofHours(24));

        // 发布状态变更事件
        AgentStateChangeEvent event = new AgentStateChangeEvent(agentId, oldState, newState);
        applicationEventPublisher.publishEvent(event);

        // 实时通知
        notificationService.notifyAgentStateChange(agentId, newState);
    }

    // 座席负载均衡
    public String selectOptimalAgent(CallRequest callRequest) {
        // 获取可用座席列表
        List<AgentInfo> availableAgents = getAvailableAgents(callRequest.getSkillGroup());

        if (availableAgents.isEmpty()) {
            throw new ServiceException("暂无可用座席");
        }

        // 负载均衡策略
        AgentInfo selectedAgent = loadBalancer.select(availableAgents, callRequest);

        // 预占座席
        reserveAgent(selectedAgent.getAgentId(), callRequest.getCallId());

        return selectedAgent.getAgentId();
    }

    // 座席技能组管理
    public void assignSkillGroup(String agentId, List<String> skillGroups) {
        AgentSkillMapping mapping = new AgentSkillMapping();
        mapping.setAgentId(agentId);
        mapping.setSkillGroups(skillGroups);
        mapping.setUpdateTime(System.currentTimeMillis());

        agentSkillMapper.insertOrUpdate(mapping);

        // 更新缓存
        String cacheKey = "agent:skills:" + agentId;
        redisTemplate.opsForValue().set(cacheKey, skillGroups, Duration.ofDays(1));
    }
}
```

**2. 通话路由服务**：
```java
@Service
public class CallRoutingService {

    @Autowired
    private RoutingRuleEngine routingRuleEngine;

    @Autowired
    private AgentManagementService agentManagementService;

    // 智能路由
    public RoutingResult routeCall(IncomingCall incomingCall) {
        try {
            // 1. 路由规则匹配
            RoutingRule matchedRule = routingRuleEngine.matchRule(incomingCall);

            // 2. 技能组确定
            String skillGroup = determineSkillGroup(incomingCall, matchedRule);

            // 3. 座席选择
            String selectedAgent = agentManagementService.selectOptimalAgent(
                CallRequest.builder()
                    .callId(incomingCall.getCallId())
                    .skillGroup(skillGroup)
                    .priority(incomingCall.getPriority())
                    .customerLevel(incomingCall.getCustomerLevel())
                    .build()
            );

            // 4. 路由结果
            RoutingResult result = RoutingResult.builder()
                .callId(incomingCall.getCallId())
                .targetAgent(selectedAgent)
                .skillGroup(skillGroup)
                .routingRule(matchedRule.getRuleName())
                .routingTime(System.currentTimeMillis())
                .build();

            // 5. 记录路由日志
            routingLogService.recordRouting(result);

            return result;

        } catch (Exception e) {
            log.error("通话路由失败: callId={}", incomingCall.getCallId(), e);

            // 降级处理：随机分配
            return fallbackRouting(incomingCall);
        }
    }

    // 排队管理
    public void enqueueCall(String callId, String skillGroup, int priority) {
        QueuedCall queuedCall = QueuedCall.builder()
            .callId(callId)
            .skillGroup(skillGroup)
            .priority(priority)
            .enqueueTime(System.currentTimeMillis())
            .build();

        // 加入优先级队列
        String queueKey = "call:queue:" + skillGroup;
        redisTemplate.opsForZSet().add(queueKey, queuedCall, priority);

        // 通知排队状态
        notificationService.notifyCallQueued(callId, getQueuePosition(callId, skillGroup));
    }

    // 队列监控
    @Scheduled(fixedDelay = 5000) // 每5秒检查一次
    public void monitorQueues() {
        Set<String> skillGroups = getActiveSkillGroups();

        for (String skillGroup : skillGroups) {
            String queueKey = "call:queue:" + skillGroup;
            Long queueSize = redisTemplate.opsForZSet().zCard(queueKey);

            if (queueSize > 0) {
                // 检查是否有可用座席
                List<AgentInfo> availableAgents = agentManagementService.getAvailableAgents(skillGroup);

                if (!availableAgents.isEmpty()) {
                    // 分配排队通话
                    assignQueuedCalls(skillGroup, availableAgents);
                }

                // 队列告警
                if (queueSize > QUEUE_ALERT_THRESHOLD) {
                    alertService.sendQueueAlert(skillGroup, queueSize);
                }
            }
        }
    }
}
```

**3. 实时通信服务**：
```java
@Service
public class RealTimeCommunicationService {

    @Autowired
    private WebSocketSessionManager sessionManager;

    @Autowired
    private MessageBroadcaster messageBroadcaster;

    // WebSocket连接管理
    @EventListener
    public void handleWebSocketConnect(WebSocketConnectEvent event) {
        String sessionId = event.getSessionId();
        String agentId = extractAgentId(event);

        // 注册会话
        sessionManager.registerSession(agentId, sessionId);

        // 发送初始化数据
        AgentInitData initData = buildAgentInitData(agentId);
        sendToAgent(agentId, "INIT", initData);

        log.info("座席连接成功: agentId={}, sessionId={}", agentId, sessionId);
    }

    // 实时消息推送
    public void sendToAgent(String agentId, String messageType, Object data) {
        String sessionId = sessionManager.getSessionId(agentId);

        if (sessionId != null) {
            RealTimeMessage message = RealTimeMessage.builder()
                .type(messageType)
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();

            messageBroadcaster.sendToSession(sessionId, message);
        } else {
            log.warn("座席未在线，消息发送失败: agentId={}", agentId);
        }
    }

    // 广播消息
    public void broadcastToSkillGroup(String skillGroup, String messageType, Object data) {
        List<String> agentIds = agentManagementService.getAgentsBySkillGroup(skillGroup);

        agentIds.parallelStream().forEach(agentId -> {
            sendToAgent(agentId, messageType, data);
        });
    }

    // 心跳检测
    @Scheduled(fixedDelay = 30000) // 每30秒
    public void heartbeatCheck() {
        Set<String> activeSessions = sessionManager.getActiveSessions();

        for (String sessionId : activeSessions) {
            try {
                // 发送心跳包
                messageBroadcaster.sendHeartbeat(sessionId);

            } catch (Exception e) {
                // 连接异常，清理会话
                String agentId = sessionManager.getAgentBySession(sessionId);
                sessionManager.removeSession(sessionId);

                // 更新座席状态为离线
                if (agentId != null) {
                    agentManagementService.updateAgentState(agentId, AgentState.OFFLINE);
                }

                log.warn("座席连接异常，已清理: agentId={}, sessionId={}", agentId, sessionId);
            }
        }
    }
}
```

**高并发优化策略**：

**1. 数据库分库分表**：
```java
@Configuration
public class ShardingConfiguration {

    @Bean
    public DataSource dataSource() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        // 通话记录表分片
        TableRuleConfiguration callRecordTableRule = new TableRuleConfiguration("call_record",
            "ds${0..3}.call_record_${0..15}");
        callRecordTableRule.setDatabaseShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("agent_id", "ds${agent_id.hashCode() % 4}"));
        callRecordTableRule.setTableShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("call_id", "call_record_${call_id.hashCode() % 16}"));

        // 座席状态表分片
        TableRuleConfiguration agentStateTableRule = new TableRuleConfiguration("agent_state",
            "ds${0..3}.agent_state_${0..7}");
        agentStateTableRule.setDatabaseShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("agent_id", "ds${agent_id.hashCode() % 4}"));
        agentStateTableRule.setTableShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("agent_id", "agent_state_${agent_id.hashCode() % 8}"));

        shardingRuleConfig.getTableRuleConfigs().add(callRecordTableRule);
        shardingRuleConfig.getTableRuleConfigs().add(agentStateTableRule);

        return ShardingDataSourceFactory.createDataSource(createDataSourceMap(), shardingRuleConfig, new Properties());
    }
}
```

**2. Redis集群缓存**：
```java
@Configuration
public class RedisClusterConfiguration {

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.clusterNode("redis-node1", 6379);
        clusterConfig.clusterNode("redis-node2", 6379);
        clusterConfig.clusterNode("redis-node3", 6379);
        clusterConfig.clusterNode("redis-node4", 6379);
        clusterConfig.clusterNode("redis-node5", 6379);
        clusterConfig.clusterNode("redis-node6", 6379);

        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .commandTimeout(Duration.ofSeconds(2))
            .poolConfig(connectionPoolConfig())
            .build();

        return new LettuceConnectionFactory(clusterConfig, clientConfig);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());

        // 序列化配置
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        return template;
    }
}
```

**3. 消息队列异步处理**：
```java
@Component
public class CallEventProcessor {

    @RocketMQMessageListener(topic = "call-events", consumerGroup = "call-processor")
    public class CallEventConsumer implements RocketMQListener<CallEvent> {

        @Override
        public void onMessage(CallEvent callEvent) {
            try {
                switch (callEvent.getEventType()) {
                    case CALL_START:
                        handleCallStart(callEvent);
                        break;
                    case CALL_END:
                        handleCallEnd(callEvent);
                        break;
                    case AGENT_STATE_CHANGE:
                        handleAgentStateChange(callEvent);
                        break;
                    default:
                        log.warn("未知事件类型: {}", callEvent.getEventType());
                }
            } catch (Exception e) {
                log.error("处理通话事件失败: {}", callEvent, e);
            }
        }
    }

    private void handleCallStart(CallEvent event) {
        // 异步处理通话开始事件
        CompletableFuture.runAsync(() -> {
            // 更新统计数据
            statisticsService.incrementCallCount(event.getAgentId());

            // 记录通话日志
            callLogService.recordCallStart(event);

            // 发送实时通知
            notificationService.notifyCallStart(event);
        });
    }
}
```

**性能数据**：
- **并发座席数**：支持5000+并发座席
- **系统响应时间**：平均响应时间<100ms，95%请求<200ms
- **通话接通率**：99.8%
- **系统可用性**：99.95%
- **数据库QPS**：单库支持1万QPS，集群支持10万+QPS
- **缓存命中率**：Redis缓存命中率>95%

**遇到的技术挑战**：

1. **WebSocket连接管理**：通过连接池+心跳检测+自动重连解决
2. **数据库性能瓶颈**：采用分库分表+读写分离+缓存优化
3. **消息推送延迟**：使用Netty+WebSocket+消息队列优化
4. **系统监控告警**：集成Prometheus+Grafana+自定义告警规则

### 24. WebSocket+Netty：实时通信如何实现？如何保证连接稳定性？

**答案**：

**WebSocket+Netty实时通信架构**：

我们采用**Netty作为底层通信框架**，**WebSocket作为应用层协议**，实现座席端与服务端的实时双向通信，支持**5000+并发连接**，**消息延迟<50ms**。

**Netty服务端实现**：
```java
@Component
public class WebSocketServer {

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;

    @PostConstruct
    public void start() {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup(Runtime.getRuntime().availableProcessors() * 2);

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .option(ChannelOption.SO_BACKLOG, 1024)
                .option(ChannelOption.SO_REUSEADDR, true)
                .childOption(ChannelOption.TCP_NODELAY, true)
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                .childOption(ChannelOption.WRITE_BUFFER_WATER_MARK,
                    new WriteBufferWaterMark(32 * 1024, 64 * 1024))
                .childHandler(new WebSocketChannelInitializer());

            ChannelFuture future = bootstrap.bind(8080).sync();
            serverChannel = future.channel();

            log.info("WebSocket服务启动成功，端口: 8080");

        } catch (Exception e) {
            log.error("WebSocket服务启动失败", e);
            shutdown();
        }
    }

    @PreDestroy
    public void shutdown() {
        if (serverChannel != null) {
            serverChannel.close();
        }
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
    }
}
```

**WebSocket处理器**：
```java
@Component
@ChannelHandler.Sharable
public class WebSocketHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

    @Autowired
    private ConnectionManager connectionManager;

    @Autowired
    private MessageProcessor messageProcessor;

    @Autowired
    private AuthenticationService authService;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        String channelId = ctx.channel().id().asShortText();
        log.info("新连接建立: channelId={}", channelId);

        // 连接预注册
        connectionManager.preRegister(channelId, ctx.channel());
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) {
        String channelId = ctx.channel().id().asShortText();

        if (frame instanceof TextWebSocketFrame) {
            String message = ((TextWebSocketFrame) frame).text();
            handleTextMessage(ctx, channelId, message);

        } else if (frame instanceof BinaryWebSocketFrame) {
            ByteBuf content = ((BinaryWebSocketFrame) frame).content();
            handleBinaryMessage(ctx, channelId, content);

        } else if (frame instanceof PingWebSocketFrame) {
            // 响应Ping帧
            ctx.writeAndFlush(new PongWebSocketFrame(frame.content().retain()));

        } else if (frame instanceof CloseWebSocketFrame) {
            handleConnectionClose(ctx, channelId);
        }
    }

    private void handleTextMessage(ChannelHandlerContext ctx, String channelId, String message) {
        try {
            WebSocketMessage wsMessage = JsonUtils.fromJsonString(message, WebSocketMessage.class);

            // 消息类型处理
            switch (wsMessage.getType()) {
                case "AUTH":
                    handleAuthentication(ctx, channelId, wsMessage);
                    break;
                case "HEARTBEAT":
                    handleHeartbeat(ctx, channelId);
                    break;
                case "BUSINESS":
                    handleBusinessMessage(ctx, channelId, wsMessage);
                    break;
                default:
                    log.warn("未知消息类型: type={}, channelId={}", wsMessage.getType(), channelId);
            }

        } catch (Exception e) {
            log.error("处理文本消息失败: channelId={}, message={}", channelId, message, e);
            sendErrorResponse(ctx, "消息处理失败: " + e.getMessage());
        }
    }

    private void handleAuthentication(ChannelHandlerContext ctx, String channelId, WebSocketMessage message) {
        AuthRequest authRequest = JsonUtils.fromJsonString(
            message.getData().toString(), AuthRequest.class);

        try {
            // 身份验证
            AuthResult authResult = authService.authenticate(authRequest.getToken());

            if (authResult.isSuccess()) {
                // 注册连接
                connectionManager.register(channelId, authResult.getAgentId(), ctx.channel());

                // 发送认证成功响应
                WebSocketMessage response = WebSocketMessage.builder()
                    .type("AUTH_SUCCESS")
                    .data(Map.of("agentId", authResult.getAgentId()))
                    .timestamp(System.currentTimeMillis())
                    .build();

                sendMessage(ctx, response);

                log.info("座席认证成功: agentId={}, channelId={}", authResult.getAgentId(), channelId);

            } else {
                sendErrorResponse(ctx, "认证失败: " + authResult.getErrorMessage());
                ctx.close();
            }

        } catch (Exception e) {
            log.error("认证处理失败: channelId={}", channelId, e);
            sendErrorResponse(ctx, "认证处理异常");
            ctx.close();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        String channelId = ctx.channel().id().asShortText();

        // 清理连接
        connectionManager.unregister(channelId);

        log.info("连接断开: channelId={}", channelId);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        String channelId = ctx.channel().id().asShortText();
        log.error("连接异常: channelId={}", channelId, cause);

        // 清理连接
        connectionManager.unregister(channelId);
        ctx.close();
    }
}
```

**连接管理器**：
```java
@Component
public class ConnectionManager {

    // 连接映射：channelId -> Connection
    private final Map<String, Connection> connections = new ConcurrentHashMap<>();

    // 座席映射：agentId -> channelId
    private final Map<String, String> agentChannels = new ConcurrentHashMap<>();

    // 预注册连接
    public void preRegister(String channelId, Channel channel) {
        Connection connection = Connection.builder()
            .channelId(channelId)
            .channel(channel)
            .connectTime(System.currentTimeMillis())
            .status(ConnectionStatus.CONNECTED)
            .build();

        connections.put(channelId, connection);
    }

    // 正式注册（认证后）
    public void register(String channelId, String agentId, Channel channel) {
        Connection connection = connections.get(channelId);
        if (connection != null) {
            connection.setAgentId(agentId);
            connection.setAuthTime(System.currentTimeMillis());
            connection.setStatus(ConnectionStatus.AUTHENTICATED);

            // 建立座席映射
            agentChannels.put(agentId, channelId);

            // 更新座席在线状态
            agentStateService.updateOnlineStatus(agentId, true);

            log.info("连接注册成功: agentId={}, channelId={}", agentId, channelId);
        }
    }

    // 注销连接
    public void unregister(String channelId) {
        Connection connection = connections.remove(channelId);
        if (connection != null && connection.getAgentId() != null) {
            // 清理座席映射
            agentChannels.remove(connection.getAgentId());

            // 更新座席离线状态
            agentStateService.updateOnlineStatus(connection.getAgentId(), false);

            log.info("连接注销: agentId={}, channelId={}", connection.getAgentId(), channelId);
        }
    }

    // 发送消息给指定座席
    public boolean sendToAgent(String agentId, WebSocketMessage message) {
        String channelId = agentChannels.get(agentId);
        if (channelId != null) {
            Connection connection = connections.get(channelId);
            if (connection != null && connection.getChannel().isActive()) {
                try {
                    String messageText = JsonUtils.toJsonString(message);
                    TextWebSocketFrame frame = new TextWebSocketFrame(messageText);

                    connection.getChannel().writeAndFlush(frame).addListener(future -> {
                        if (future.isSuccess()) {
                            connection.updateLastSendTime();
                        } else {
                            log.error("消息发送失败: agentId={}, channelId={}", agentId, channelId, future.cause());
                        }
                    });

                    return true;

                } catch (Exception e) {
                    log.error("发送消息异常: agentId={}, channelId={}", agentId, channelId, e);
                }
            }
        }

        return false;
    }

    // 广播消息
    public void broadcast(WebSocketMessage message, Predicate<Connection> filter) {
        String messageText = JsonUtils.toJsonString(message);
        TextWebSocketFrame frame = new TextWebSocketFrame(messageText);

        connections.values().parallelStream()
            .filter(filter)
            .filter(conn -> conn.getChannel().isActive())
            .forEach(conn -> {
                conn.getChannel().writeAndFlush(frame.retainedDuplicate())
                    .addListener(future -> {
                        if (future.isSuccess()) {
                            conn.updateLastSendTime();
                        }
                    });
            });

        frame.release();
    }

    // 获取在线座席数量
    public int getOnlineAgentCount() {
        return agentChannels.size();
    }

    // 获取连接统计
    public ConnectionStatistics getStatistics() {
        int totalConnections = connections.size();
        int authenticatedConnections = (int) connections.values().stream()
            .filter(conn -> conn.getStatus() == ConnectionStatus.AUTHENTICATED)
            .count();

        return ConnectionStatistics.builder()
            .totalConnections(totalConnections)
            .authenticatedConnections(authenticatedConnections)
            .onlineAgents(agentChannels.size())
            .build();
    }
}
```

**连接稳定性保障**：

**1. 心跳检测机制**：
```java
@Component
public class HeartbeatManager {

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    @PostConstruct
    public void startHeartbeat() {
        // 服务端主动心跳
        scheduler.scheduleWithFixedDelay(this::sendHeartbeat, 30, 30, TimeUnit.SECONDS);

        // 连接健康检查
        scheduler.scheduleWithFixedDelay(this::checkConnectionHealth, 60, 60, TimeUnit.SECONDS);
    }

    private void sendHeartbeat() {
        WebSocketMessage heartbeat = WebSocketMessage.builder()
            .type("HEARTBEAT")
            .data(Map.of("timestamp", System.currentTimeMillis()))
            .timestamp(System.currentTimeMillis())
            .build();

        // 向所有认证连接发送心跳
        connectionManager.broadcast(heartbeat,
            conn -> conn.getStatus() == ConnectionStatus.AUTHENTICATED);
    }

    private void checkConnectionHealth() {
        long currentTime = System.currentTimeMillis();
        long timeout = 90 * 1000; // 90秒超时

        List<String> timeoutChannels = connectionManager.getConnections().entrySet().stream()
            .filter(entry -> {
                Connection conn = entry.getValue();
                return currentTime - conn.getLastReceiveTime() > timeout;
            })
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        // 清理超时连接
        timeoutChannels.forEach(channelId -> {
            Connection conn = connectionManager.getConnection(channelId);
            if (conn != null) {
                log.warn("连接超时，主动断开: agentId={}, channelId={}",
                    conn.getAgentId(), channelId);
                conn.getChannel().close();
            }
        });
    }
}
```

**2. 断线重连机制**：
```javascript
// 前端WebSocket客户端
class WebSocketClient {
    constructor(url, agentId, token) {
        this.url = url;
        this.agentId = agentId;
        this.token = token;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000; // 5秒
        this.heartbeatInterval = 30000; // 30秒
        this.heartbeatTimer = null;
        this.isManualClose = false;
    }

    connect() {
        try {
            this.ws = new WebSocket(this.url);

            this.ws.onopen = (event) => {
                console.log('WebSocket连接成功');
                this.reconnectAttempts = 0;
                this.authenticate();
                this.startHeartbeat();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };

            this.ws.onclose = (event) => {
                console.log('WebSocket连接关闭:', event.code, event.reason);
                this.stopHeartbeat();

                if (!this.isManualClose) {
                    this.reconnect();
                }
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
            };

        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.reconnect();
        }
    }

    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

            setTimeout(() => {
                this.connect();
            }, this.reconnectInterval * this.reconnectAttempts);
        } else {
            console.error('重连失败，已达到最大重连次数');
            this.onReconnectFailed();
        }
    }

    authenticate() {
        const authMessage = {
            type: 'AUTH',
            data: {
                agentId: this.agentId,
                token: this.token
            },
            timestamp: Date.now()
        };

        this.send(authMessage);
    }

    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const heartbeat = {
                    type: 'HEARTBEAT',
                    data: { timestamp: Date.now() },
                    timestamp: Date.now()
                };
                this.send(heartbeat);
            }
        }, this.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket未连接，消息发送失败');
        }
    }
}
```

**3. 连接池管理**：
```java
@Configuration
public class NettyConfiguration {

    @Bean
    public EventLoopGroup bossGroup() {
        return new NioEventLoopGroup(1, new DefaultThreadFactory("netty-boss"));
    }

    @Bean
    public EventLoopGroup workerGroup() {
        int workerThreads = Runtime.getRuntime().availableProcessors() * 2;
        return new NioEventLoopGroup(workerThreads, new DefaultThreadFactory("netty-worker"));
    }

    @Bean
    public ChannelOption<?>[] channelOptions() {
        return new ChannelOption[]{
            ChannelOption.SO_BACKLOG, 1024,
            ChannelOption.SO_REUSEADDR, true,
            ChannelOption.TCP_NODELAY, true,
            ChannelOption.SO_KEEPALIVE, true,
            ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(32 * 1024, 64 * 1024),
            ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT
        };
    }
}
```

**性能优化数据**：
- **并发连接数**：支持5000+并发WebSocket连接
- **消息延迟**：平均延迟<50ms，95%消息<100ms
- **连接稳定性**：连接存活率>99.5%
- **重连成功率**：断线重连成功率>98%
- **内存使用**：单连接内存占用<2KB
- **CPU使用率**：5000连接下CPU使用率<30%

### 25. 通话录音：录音文件如何存储和管理？支持哪些格式？

**答案**：

**录音存储架构设计**：

我们的录音系统需要处理**日均500万+通话录音**，**总存储容量100TB+**，支持**多种音频格式**、**分布式存储**、**智能压缩**、**快速检索**。

**录音文件管理服务**：
```java
@Service
public class RecordingManagementService {

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private AudioCompressionService compressionService;

    @Autowired
    private RecordingMetadataMapper recordingMetadataMapper;

    // 录音文件上传
    public RecordingUploadResult uploadRecording(RecordingUploadRequest request) {
        try {
            // 1. 文件格式验证
            validateAudioFormat(request.getAudioFile());

            // 2. 音频质量检测
            AudioQuality quality = audioQualityDetector.detect(request.getAudioFile());

            // 3. 音频压缩处理
            CompressedAudio compressed = compressionService.compress(
                request.getAudioFile(), quality);

            // 4. 生成存储路径
            String storagePath = generateStoragePath(request.getCallId(),
                request.getRecordingType());

            // 5. 分布式存储
            StorageResult storageResult = fileStorageService.store(
                storagePath, compressed.getData());

            // 6. 保存元数据
            RecordingMetadata metadata = RecordingMetadata.builder()
                .callId(request.getCallId())
                .agentId(request.getAgentId())
                .customerId(request.getCustomerId())
                .recordingType(request.getRecordingType())
                .originalFormat(request.getAudioFile().getFormat())
                .compressedFormat(compressed.getFormat())
                .originalSize(request.getAudioFile().getSize())
                .compressedSize(compressed.getSize())
                .duration(compressed.getDuration())
                .storagePath(storagePath)
                .storageNode(storageResult.getNodeId())
                .uploadTime(System.currentTimeMillis())
                .build();

            recordingMetadataMapper.insert(metadata);

            return RecordingUploadResult.builder()
                .recordingId(metadata.getId())
                .storagePath(storagePath)
                .compressedSize(compressed.getSize())
                .compressionRatio(calculateCompressionRatio(
                    request.getAudioFile().getSize(), compressed.getSize()))
                .build();

        } catch (Exception e) {
            log.error("录音上传失败: callId={}", request.getCallId(), e);
            throw new ServiceException("录音上传失败", e);
        }
    }

    // 录音文件下载
    public RecordingDownloadResult downloadRecording(String recordingId, String requesterId) {
        // 1. 权限验证
        validateDownloadPermission(recordingId, requesterId);

        // 2. 获取录音元数据
        RecordingMetadata metadata = recordingMetadataMapper.selectById(recordingId);
        if (metadata == null) {
            throw new ServiceException("录音文件不存在");
        }

        // 3. 从存储节点获取文件
        byte[] audioData = fileStorageService.retrieve(metadata.getStoragePath());

        // 4. 记录下载日志
        recordingAccessLogService.recordDownload(recordingId, requesterId);

        return RecordingDownloadResult.builder()
            .recordingId(recordingId)
            .audioData(audioData)
            .format(metadata.getCompressedFormat())
            .duration(metadata.getDuration())
            .build();
    }
}
```

**支持的音频格式**：
```java
public enum AudioFormat {
    WAV("wav", "audio/wav", false, 1.0),
    MP3("mp3", "audio/mpeg", true, 0.1),
    AAC("aac", "audio/aac", true, 0.12),
    OPUS("opus", "audio/opus", true, 0.08),
    G711_ALAW("g711a", "audio/g711", true, 0.5),
    G711_ULAW("g711u", "audio/g711", true, 0.5),
    G729("g729", "audio/g729", true, 0.1);

    private final String extension;
    private final String mimeType;
    private final boolean compressed;
    private final double compressionRatio;
}

@Service
public class AudioFormatConverter {

    // 格式转换
    public ConvertedAudio convert(AudioFile source, AudioFormat targetFormat) {
        if (source.getFormat() == targetFormat) {
            return ConvertedAudio.from(source);
        }

        try {
            // 使用FFmpeg进行格式转换
            FFmpegCommand command = FFmpegCommand.builder()
                .input(source.getFilePath())
                .audioCodec(targetFormat.getCodec())
                .audioBitrate(getOptimalBitrate(targetFormat))
                .audioSampleRate(16000) // 标准化采样率
                .output(generateTempPath(targetFormat))
                .build();

            FFmpegResult result = ffmpegExecutor.execute(command);

            if (result.isSuccess()) {
                return ConvertedAudio.builder()
                    .format(targetFormat)
                    .filePath(result.getOutputPath())
                    .size(result.getFileSize())
                    .duration(result.getDuration())
                    .build();
            } else {
                throw new ServiceException("音频格式转换失败: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("音频格式转换异常: source={}, target={}",
                source.getFormat(), targetFormat, e);
            throw new ServiceException("音频格式转换异常", e);
        }
    }
}
```

**分布式存储实现**：
```java
@Service
public class DistributedFileStorageService {

    @Autowired
    private StorageNodeManager nodeManager;

    @Autowired
    private ConsistentHashRouter hashRouter;

    // 文件存储
    public StorageResult store(String filePath, byte[] data) {
        // 1. 选择存储节点
        List<StorageNode> candidateNodes = selectStorageNodes(filePath, 3); // 3副本

        List<StorageResult> results = new ArrayList<>();

        // 2. 并行存储到多个节点
        candidateNodes.parallelStream().forEach(node -> {
            try {
                StorageResult result = storeToNode(node, filePath, data);
                synchronized (results) {
                    results.add(result);
                }
            } catch (Exception e) {
                log.error("存储到节点失败: nodeId={}, filePath={}",
                    node.getNodeId(), filePath, e);
            }
        });

        // 3. 检查存储结果
        if (results.size() < 2) { // 至少2个副本成功
            throw new ServiceException("存储失败，成功副本数不足");
        }

        // 4. 返回主存储结果
        return results.get(0);
    }

    private List<StorageNode> selectStorageNodes(String filePath, int replicaCount) {
        // 使用一致性哈希选择存储节点
        List<StorageNode> allNodes = nodeManager.getAvailableNodes();
        List<StorageNode> selectedNodes = new ArrayList<>();

        String hash = hashRouter.hash(filePath);
        StorageNode primaryNode = hashRouter.getNode(hash);
        selectedNodes.add(primaryNode);

        // 选择后续副本节点
        for (int i = 1; i < replicaCount && selectedNodes.size() < allNodes.size(); i++) {
            StorageNode nextNode = hashRouter.getNextNode(hash, selectedNodes);
            if (nextNode != null) {
                selectedNodes.add(nextNode);
            }
        }

        return selectedNodes;
    }
}
```

---

## 号码隐藏保护平台核心技术

### 27. X号码分配：号码池如何管理？如何保证号码不重复分配？

**答案**：

**号码池管理架构**：

我们的号码隐藏保护平台管理**1000万+虚拟号码**，支持**日均分配500万次**，通过**分布式锁**、**号码预分配**、**智能回收**确保号码分配的**唯一性**和**高效性**。

**号码池数据模型**：
```java
@Entity
@Table(name = "number_pool")
public class NumberPool {

    @Id
    private Long id;

    @Column(name = "virtual_number")
    private String virtualNumber; // X号码

    @Column(name = "number_type")
    @Enumerated(EnumType.STRING)
    private NumberType numberType; // 号码类型：手机/固话

    @Column(name = "area_code")
    private String areaCode; // 地区编码

    @Column(name = "operator")
    @Enumerated(EnumType.STRING)
    private Operator operator; // 运营商

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private NumberStatus status; // 状态：可用/已分配/冻结/回收中

    @Column(name = "allocated_time")
    private Long allocatedTime; // 分配时间

    @Column(name = "release_time")
    private Long releaseTime; // 释放时间

    @Column(name = "binding_id")
    private String bindingId; // 绑定关系ID

    @Column(name = "last_used_time")
    private Long lastUsedTime; // 最后使用时间

    @Column(name = "usage_count")
    private Integer usageCount; // 使用次数
}

public enum NumberStatus {
    AVAILABLE,    // 可用
    ALLOCATED,    // 已分配
    FROZEN,       // 冻结
    RECYCLING,    // 回收中
    MAINTENANCE   // 维护中
}
```

**号码分配服务**：
```java
@Service
public class NumberAllocationService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private NumberPoolMapper numberPoolMapper;

    @Autowired
    private NumberBindingService bindingService;

    // 分配X号码
    public NumberAllocationResult allocateNumber(NumberAllocationRequest request) {
        String lockKey = "number:allocation:" + request.getAreaCode() + ":" + request.getNumberType();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 获取分布式锁，防止重复分配
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {

                // 1. 查找可用号码
                NumberPool availableNumber = findAvailableNumber(request);

                if (availableNumber == null) {
                    // 触发号码补充
                    numberSupplementService.supplementNumbers(request.getAreaCode(),
                        request.getNumberType());

                    // 重新查找
                    availableNumber = findAvailableNumber(request);

                    if (availableNumber == null) {
                        throw new ServiceException("暂无可用号码");
                    }
                }

                // 2. 更新号码状态
                updateNumberStatus(availableNumber.getId(), NumberStatus.ALLOCATED,
                    request.getBindingId());

                // 3. 创建绑定关系
                NumberBinding binding = bindingService.createBinding(
                    availableNumber.getVirtualNumber(),
                    request.getRealNumberA(),
                    request.getRealNumberB(),
                    request.getExpireTime()
                );

                // 4. 缓存分配结果
                cacheAllocationResult(availableNumber, binding);

                return NumberAllocationResult.builder()
                    .virtualNumber(availableNumber.getVirtualNumber())
                    .bindingId(binding.getBindingId())
                    .expireTime(binding.getExpireTime())
                    .build();

            } else {
                throw new ServiceException("获取分配锁超时");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("号码分配被中断", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private NumberPool findAvailableNumber(NumberAllocationRequest request) {
        // 优先从预分配池获取
        NumberPool preAllocated = getFromPreAllocationPool(request);
        if (preAllocated != null) {
            return preAllocated;
        }

        // 从主号码池查找
        LambdaQueryWrapper<NumberPool> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NumberPool::getStatus, NumberStatus.AVAILABLE)
                   .eq(NumberPool::getAreaCode, request.getAreaCode())
                   .eq(NumberPool::getNumberType, request.getNumberType())
                   .orderByAsc(NumberPool::getLastUsedTime) // 优先分配最久未使用的号码
                   .last("LIMIT 1");

        return numberPoolMapper.selectOne(queryWrapper);
    }

    // 号码释放
    public void releaseNumber(String virtualNumber, String bindingId) {
        String lockKey = "number:release:" + virtualNumber;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {

                // 1. 验证绑定关系
                NumberBinding binding = bindingService.getBinding(bindingId);
                if (binding == null || !binding.getVirtualNumber().equals(virtualNumber)) {
                    throw new ServiceException("绑定关系不存在或不匹配");
                }

                // 2. 删除绑定关系
                bindingService.deleteBinding(bindingId);

                // 3. 更新号码状态
                NumberPool numberPool = numberPoolMapper.selectByVirtualNumber(virtualNumber);
                if (numberPool != null) {
                    numberPool.setStatus(NumberStatus.RECYCLING);
                    numberPool.setReleaseTime(System.currentTimeMillis());
                    numberPool.setBindingId(null);
                    numberPoolMapper.updateById(numberPool);

                    // 4. 加入回收队列
                    addToRecycleQueue(numberPool);
                }

                // 5. 清理缓存
                clearAllocationCache(virtualNumber);

            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("号码释放被中断", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

**号码预分配池**：
```java
@Service
public class NumberPreAllocationService {

    private static final String PRE_ALLOCATION_KEY = "number:pre_allocation:";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 预分配号码到缓存
    @Scheduled(fixedDelay = 60000) // 每分钟执行一次
    public void preAllocateNumbers() {
        Map<String, Integer> demandForecast = numberDemandAnalyzer.forecastDemand();

        for (Map.Entry<String, Integer> entry : demandForecast.entrySet()) {
            String key = entry.getKey(); // areaCode:numberType
            Integer demandCount = entry.getValue();

            String cacheKey = PRE_ALLOCATION_KEY + key;
            Long currentCount = redisTemplate.opsForList().size(cacheKey);

            if (currentCount < demandCount) {
                int needCount = demandCount - currentCount.intValue();
                preAllocateForKey(key, needCount);
            }
        }
    }

    private void preAllocateForKey(String key, int count) {
        String[] parts = key.split(":");
        String areaCode = parts[0];
        NumberType numberType = NumberType.valueOf(parts[1]);

        // 查询可用号码
        LambdaQueryWrapper<NumberPool> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NumberPool::getStatus, NumberStatus.AVAILABLE)
                   .eq(NumberPool::getAreaCode, areaCode)
                   .eq(NumberPool::getNumberType, numberType)
                   .orderByAsc(NumberPool::getLastUsedTime)
                   .last("LIMIT " + count);

        List<NumberPool> availableNumbers = numberPoolMapper.selectList(queryWrapper);

        if (!availableNumbers.isEmpty()) {
            String cacheKey = PRE_ALLOCATION_KEY + key;

            // 批量加入预分配池
            List<String> numberIds = availableNumbers.stream()
                .map(number -> number.getId().toString())
                .collect(Collectors.toList());

            redisTemplate.opsForList().rightPushAll(cacheKey, numberIds.toArray());

            // 设置过期时间
            redisTemplate.expire(cacheKey, Duration.ofHours(2));

            log.info("预分配号码完成: key={}, count={}", key, availableNumbers.size());
        }
    }

    // 从预分配池获取号码
    public NumberPool getFromPreAllocationPool(NumberAllocationRequest request) {
        String key = request.getAreaCode() + ":" + request.getNumberType();
        String cacheKey = PRE_ALLOCATION_KEY + key;

        String numberIdStr = (String) redisTemplate.opsForList().leftPop(cacheKey);
        if (numberIdStr != null) {
            Long numberId = Long.valueOf(numberIdStr);
            return numberPoolMapper.selectById(numberId);
        }

        return null;
    }
}
```

**号码回收机制**：
```java
@Service
public class NumberRecycleService {

    // 号码回收处理
    @Scheduled(fixedDelay = 300000) // 每5分钟执行一次
    public void processRecycleQueue() {
        String queueKey = "number:recycle:queue";

        while (true) {
            String numberData = (String) redisTemplate.opsForList().leftPop(queueKey);
            if (numberData == null) {
                break;
            }

            try {
                NumberRecycleData recycleData = JsonUtils.fromJsonString(numberData, NumberRecycleData.class);
                processNumberRecycle(recycleData);

            } catch (Exception e) {
                log.error("号码回收处理失败: data={}", numberData, e);
                // 重新加入队列末尾
                redisTemplate.opsForList().rightPush(queueKey, numberData);
            }
        }
    }

    private void processNumberRecycle(NumberRecycleData recycleData) {
        String virtualNumber = recycleData.getVirtualNumber();

        // 1. 冷却期检查
        if (!isCoolingPeriodPassed(recycleData)) {
            // 重新加入队列，延后处理
            scheduleRecycleRetry(recycleData);
            return;
        }

        // 2. 号码清理
        cleanupNumberHistory(virtualNumber);

        // 3. 更新状态为可用
        NumberPool numberPool = numberPoolMapper.selectByVirtualNumber(virtualNumber);
        if (numberPool != null) {
            numberPool.setStatus(NumberStatus.AVAILABLE);
            numberPool.setReleaseTime(null);
            numberPool.setLastUsedTime(System.currentTimeMillis());
            numberPool.setUsageCount(numberPool.getUsageCount() + 1);

            numberPoolMapper.updateById(numberPool);

            log.info("号码回收完成: virtualNumber={}", virtualNumber);
        }
    }

    private boolean isCoolingPeriodPassed(NumberRecycleData recycleData) {
        long coolingPeriod = getCoolingPeriod(recycleData.getNumberType());
        long releaseTime = recycleData.getReleaseTime();

        return System.currentTimeMillis() - releaseTime >= coolingPeriod;
    }

    private long getCoolingPeriod(NumberType numberType) {
        // 不同类型号码的冷却期不同
        switch (numberType) {
            case MOBILE:
                return 24 * 60 * 60 * 1000L; // 手机号24小时冷却期
            case LANDLINE:
                return 12 * 60 * 60 * 1000L; // 固话12小时冷却期
            default:
                return 6 * 60 * 60 * 1000L;  // 默认6小时
        }
    }
}
```

**号码分配效果数据**：
- **分配成功率**：99.8%
- **分配响应时间**：平均50ms，95%请求<100ms
- **号码利用率**：85%
- **重复分配率**：0（通过分布式锁保证）
- **号码回收率**：95%
- **系统并发能力**：支持1万+并发分配请求

---

## 总结

第2批面试问题主要围绕三个核心项目的深度技术实现：

**智能语音质检平台**：
- ASR/NLP技术集成与准确率提升
- 可配置规则引擎设计与执行
- 实时质检架构与性能优化
- 多维度质检报告生成与分析

**呼叫中心平台**：
- 5000+并发座席架构设计
- WebSocket+Netty实时通信实现
- 通话录音存储与管理
- 座席管理与智能路由

**号码隐藏保护平台**：
- 分布式号码池管理
- 号码分配唯一性保证
- 智能回收与预分配机制

这些技术实现体现了在**高并发**、**大数据量**、**实时性**场景下的架构设计能力和工程实践经验。
```
```
```
