<!DOCTYPE html>
<!-- saved from url=(0042)http://localhost:8680/#/editor?type=create -->
<html lang="en" class=" "><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style>.ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #444;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused .cm-cursor {display: block;}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; left: 0; z-index: 200;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border-right: 1px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top;}
.ͼ1 .cm-highlightSpace:before {content: attr(data-display); position: absolute; pointer-events: none; color: #888;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ1 .cm-panel.cm-search [name=close] {position: absolute; top: 0; right: 4px; background-color: inherit; border: none; font: inherit; padding: 0; margin: 0;}
.ͼ1 .cm-panel.cm-search input, .ͼ1 .cm-panel.cm-search button, .ͼ1 .cm-panel.cm-search label {margin: .2em .6em .2em 0;}
.ͼ1 .cm-panel.cm-search input[type=checkbox] {margin-right: .2em;}
.ͼ1 .cm-panel.cm-search label {font-size: 80%; white-space: pre;}
.ͼ1 .cm-panel.cm-search {padding: 2px 6px 4px; position: relative;}
.ͼ2 .cm-searchMatch {background-color: #ffff0054;}
.ͼ3 .cm-searchMatch {background-color: #00ffff8a;}
.ͼ2 .cm-searchMatch-selected {background-color: #ff6a0054;}
.ͼ3 .cm-searchMatch-selected {background-color: #ff00ff8a;}
.ͼ1 .cm-selectionMatch {background-color: #99ff7780;}
.ͼ1 .cm-searchMatch .cm-selectionMatch {background-color: transparent;}
.ͼ1 .cm-tooltip.cm-tooltip-autocomplete > ul > li, .ͼ1 .cm-tooltip.cm-tooltip-autocomplete > ul > completion-section {padding: 1px 3px; line-height: 1.2;}
.ͼ1 .cm-tooltip.cm-tooltip-autocomplete > ul > li {overflow-x: hidden; text-overflow: ellipsis; cursor: pointer;}
.ͼ1 .cm-tooltip.cm-tooltip-autocomplete > ul > completion-section {display: list-item; border-bottom: 1px solid silver; padding-left: 0.5em; opacity: 0.7;}
.ͼ1 .cm-tooltip.cm-tooltip-autocomplete > ul {font-family: monospace; white-space: nowrap; overflow: hidden auto; max-width: 700px; max-width: min(700px, 95vw); min-width: 250px; max-height: 10em; height: 100%; list-style: none; margin: 0; padding: 0;}
.ͼ2 .cm-tooltip-autocomplete ul li[aria-selected] {background: #17c; color: white;}
.ͼ2 .cm-tooltip-autocomplete-disabled ul li[aria-selected] {background: #777;}
.ͼ3 .cm-tooltip-autocomplete ul li[aria-selected] {background: #347; color: white;}
.ͼ3 .cm-tooltip-autocomplete-disabled ul li[aria-selected] {background: #444;}
.ͼ1 .cm-completionListIncompleteTop:before, .ͼ1 .cm-completionListIncompleteBottom:after {content: "···"; opacity: 0.5; display: block; text-align: center;}
.ͼ1 .cm-tooltip.cm-completionInfo {position: absolute; padding: 3px 9px; width: max-content; max-width: 400px; box-sizing: border-box;}
.ͼ1 .cm-completionInfo.cm-completionInfo-left {right: 100%;}
.ͼ1 .cm-completionInfo.cm-completionInfo-right {left: 100%;}
.ͼ1 .cm-completionInfo.cm-completionInfo-left-narrow {right: 30px;}
.ͼ1 .cm-completionInfo.cm-completionInfo-right-narrow {left: 30px;}
.ͼ2 .cm-snippetField {background-color: #00000022;}
.ͼ3 .cm-snippetField {background-color: #ffffff22;}
.ͼ1 .cm-snippetFieldPosition {vertical-align: text-top; width: 0; height: 1.15em; display: inline-block; margin: 0 -0.7px -.7em; border-left: 1.4px dotted #888;}
.ͼ1 .cm-completionMatchedText {text-decoration: underline;}
.ͼ1 .cm-completionDetail {margin-left: 0.5em; font-style: italic;}
.ͼ1 .cm-completionIcon {font-size: 90%; width: .8em; display: inline-block; text-align: center; padding-right: .6em; opacity: 0.6; box-sizing: content-box;}
.ͼ1 .cm-completionIcon-function:after, .ͼ1 .cm-completionIcon-method:after {content: 'ƒ';}
.ͼ1 .cm-completionIcon-class:after {content: '○';}
.ͼ1 .cm-completionIcon-interface:after {content: '◌';}
.ͼ1 .cm-completionIcon-variable:after {content: '𝑥';}
.ͼ1 .cm-completionIcon-constant:after {content: '𝐶';}
.ͼ1 .cm-completionIcon-type:after {content: '𝑡';}
.ͼ1 .cm-completionIcon-enum:after {content: '∪';}
.ͼ1 .cm-completionIcon-property:after {content: '□';}
.ͼ1 .cm-completionIcon-keyword:after {content: '🔑︎';}
.ͼ1 .cm-completionIcon-namespace:after {content: '▢';}
.ͼ1 .cm-completionIcon-text:after {content: 'abc'; font-size: 50%; vertical-align: middle;}
.ͼ1 .cm-tooltip {z-index: 100; box-sizing: border-box;}
.ͼ2 .cm-tooltip {border: 1px solid #bbb; background-color: #f5f5f5;}
.ͼ2 .cm-tooltip-section:not(:first-child) {border-top: 1px solid #bbb;}
.ͼ3 .cm-tooltip {background-color: #333338; color: white;}
.ͼ1 .cm-tooltip-arrow:before, .ͼ1 .cm-tooltip-arrow:after {content: ''; position: absolute; width: 0; height: 0; border-left: 7px solid transparent; border-right: 7px solid transparent;}
.ͼ1 .cm-tooltip-above .cm-tooltip-arrow:before {border-top: 7px solid #bbb;}
.ͼ1 .cm-tooltip-above .cm-tooltip-arrow:after {border-top: 7px solid #f5f5f5; bottom: 1px;}
.ͼ1 .cm-tooltip-above .cm-tooltip-arrow {bottom: -7px;}
.ͼ1 .cm-tooltip-below .cm-tooltip-arrow:before {border-bottom: 7px solid #bbb;}
.ͼ1 .cm-tooltip-below .cm-tooltip-arrow:after {border-bottom: 7px solid #f5f5f5; top: 1px;}
.ͼ1 .cm-tooltip-below .cm-tooltip-arrow {top: -7px;}
.ͼ1 .cm-tooltip-arrow {height: 7px; width: 14px; position: absolute; z-index: -1; overflow: hidden;}
.ͼ3 .cm-tooltip .cm-tooltip-arrow:before {border-top-color: #333338; border-bottom-color: #333338;}
.ͼ3 .cm-tooltip .cm-tooltip-arrow:after {border-top-color: transparent; border-bottom-color: transparent;}
.ͼ1.cm-focused .cm-matchingBracket {background-color: #328c8252;}
.ͼ1.cm-focused .cm-nonmatchingBracket {background-color: #bb555544;}
.ͼ1 .cm-foldPlaceholder {background-color: #eee; border: 1px solid #ddd; color: #888; border-radius: .2em; margin: 0 1px; padding: 0 1px; cursor: pointer;}
.ͼ1 .cm-foldGutter span {padding: 0 1px; cursor: pointer;}
.ͼbz {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1329px; background: #fff;}
.ͼby {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1330px; background: #fff;}
.ͼbx {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1329px; background: #fff;}
.ͼbw {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1328px; background: #fff;}
.ͼbv {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1326px; background: #fff;}
.ͼbu {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1325px; background: #fff;}
.ͼbt {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1320px; background: #fff;}
.ͼbs {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1316px; background: #fff;}
.ͼbr {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1309px; background: #fff;}
.ͼbq {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1306px; background: #fff;}
.ͼbp {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1302px; background: #fff;}
.ͼbo {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1300px; background: #fff;}
.ͼbn {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1297px; background: #fff;}
.ͼbm {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1293px; background: #fff;}
.ͼbl {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1284px; background: #fff;}
.ͼbk {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1279px; background: #fff;}
.ͼbj {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1270px; background: #fff;}
.ͼbi {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1265px; background: #fff;}
.ͼbh {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1250px; background: #fff;}
.ͼbg {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1240px; background: #fff;}
.ͼbf {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1220px; background: #fff;}
.ͼbe {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1211px; background: #fff;}
.ͼbd {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1202px; background: #fff;}
.ͼbc {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1187px; background: #fff;}
.ͼbb {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1180px; background: #fff;}
.ͼba {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1167px; background: #fff;}
.ͼb9 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1160px; background: #fff;}
.ͼb8 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1150px; background: #fff;}
.ͼb7 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1145px; background: #fff;}
.ͼb6 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1136px; background: #fff;}
.ͼb5 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1132px; background: #fff;}
.ͼb4 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1122px; background: #fff;}
.ͼb3 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1115px; background: #fff;}
.ͼb2 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1096px; background: #fff;}
.ͼb1 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1070px; background: #fff;}
.ͼb0 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1055px; background: #fff;}
.ͼaz {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1039px; background: #fff;}
.ͼay {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 999px; background: #fff;}
.ͼax {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 974px; background: #fff;}
.ͼaw {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 927px; background: #fff;}
.ͼav {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 902px; background: #fff;}
.ͼau {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 852px; background: #fff;}
.ͼat {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 805px; background: #fff;}
.ͼas {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 791px; background: #fff;}
.ͼar {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 778px; background: #fff;}
.ͼaq {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 754px; background: #fff;}
.ͼap {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 745px; background: #fff;}
.ͼao {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 737px; background: #fff;}
.ͼan {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 723px; background: #fff;}
.ͼam {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 715px; background: #fff;}
.ͼal {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 701px; background: #fff;}
.ͼak {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 691px; background: #fff;}
.ͼaj {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 668px; background: #fff;}
.ͼai {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 657px; background: #fff;}
.ͼah {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 631px; background: #fff;}
.ͼag {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 616px; background: #fff;}
.ͼaf {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 590px; background: #fff;}
.ͼae {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 578px; background: #fff;}
.ͼad {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 561px; background: #fff;}
.ͼac {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 553px; background: #fff;}
.ͼab {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 552px; background: #fff;}
.ͼaa {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼa9 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa8 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa7 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa6 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa5 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa4 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa3 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa2 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa1 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼa0 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ9a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ99 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ98 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ97 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ96 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ95 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ94 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ93 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ92 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ91 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ90 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ8a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ89 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ88 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ87 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ86 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ85 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ84 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ83 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ82 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ81 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ80 {min-height: 300px; max-height: 500px;}
.ͼ7z {min-height: 300px; max-height: 500px;}
.ͼ7y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ7a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ79 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ78 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ77 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ76 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ75 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ74 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ73 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ72 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ71 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ70 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ6a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ69 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ68 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ67 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ66 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ65 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ64 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ63 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ62 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ61 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ60 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ5a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ59 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ58 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ57 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ56 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ55 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ54 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ53 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ52 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ51 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ50 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ4a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ49 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ48 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ47 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ46 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ45 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ44 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ43 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ42 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ41 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ40 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ3a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ39 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ38 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ37 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ36 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ35 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ34 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ33 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ32 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ31 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ30 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ2z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ2y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ2x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ2w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1295px; background: #fff;}
.ͼ2v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1294px; background: #fff;}
.ͼ2u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1293px; background: #fff;}
.ͼ2t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1291px; background: #fff;}
.ͼ2s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1290px; background: #fff;}
.ͼ2r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1289px; background: #fff;}
.ͼ2q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1287px; background: #fff;}
.ͼ2p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1286px; background: #fff;}
.ͼ2o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1285px; background: #fff;}
.ͼ2n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1284px; background: #fff;}
.ͼ2m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1283px; background: #fff;}
.ͼ2l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1281px; background: #fff;}
.ͼ2k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1280px; background: #fff;}
.ͼ2j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1279px; background: #fff;}
.ͼ2i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1278px; background: #fff;}
.ͼ2h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1276px; background: #fff;}
.ͼ2g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1275px; background: #fff;}
.ͼ2f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1273px; background: #fff;}
.ͼ2e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1267px; background: #fff;}
.ͼ2d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1263px; background: #fff;}
.ͼ2c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1249px; background: #fff;}
.ͼ2b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1242px; background: #fff;}
.ͼ2a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1228px; background: #fff;}
.ͼ29 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1220px; background: #fff;}
.ͼ28 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1200px; background: #fff;}
.ͼ27 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1188px; background: #fff;}
.ͼ26 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1166px; background: #fff;}
.ͼ25 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1157px; background: #fff;}
.ͼ24 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1141px; background: #fff;}
.ͼ23 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1132px; background: #fff;}
.ͼ22 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1112px; background: #fff;}
.ͼ21 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1107px; background: #fff;}
.ͼ20 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1099px; background: #fff;}
.ͼ1z {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1094px; background: #fff;}
.ͼ1y {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1081px; background: #fff;}
.ͼ1x {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1074px; background: #fff;}
.ͼ1w {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1059px; background: #fff;}
.ͼ1v {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1052px; background: #fff;}
.ͼ1u {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1034px; background: #fff;}
.ͼ1t {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 1023px; background: #fff;}
.ͼ1s {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 997px; background: #fff;}
.ͼ1r {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 983px; background: #fff;}
.ͼ1q {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 956px; background: #fff;}
.ͼ1p {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 944px; background: #fff;}
.ͼ1o {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 924px; background: #fff;}
.ͼ1n {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 914px; background: #fff;}
.ͼ1m {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 906px; background: #fff;}
.ͼ1l {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 885px; background: #fff;}
.ͼ1k {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 873px; background: #fff;}
.ͼ1j {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 845px; background: #fff;}
.ͼ1i {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 829px; background: #fff;}
.ͼ1h {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 787px; background: #fff;}
.ͼ1g {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 728px; background: #fff;}
.ͼ1f {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 695px; background: #fff;}
.ͼ1e {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 662px; background: #fff;}
.ͼ1d {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 602px; background: #fff;}
.ͼ1c {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 580px; background: #fff;}
.ͼ1b {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 557px; background: #fff;}
.ͼ1a {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 553px; background: #fff;}
.ͼ19 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼ18 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼ17 {height: calc(100vh - 40px); border-left: none; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼ16 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼp {color: #c678dd;}
.ͼq {color: #e06c75;}
.ͼr {color: #61afef;}
.ͼs {color: #d19a66;}
.ͼt {color: #abb2bf;}
.ͼu {color: #e5c07b;}
.ͼv {color: #56b6c2;}
.ͼw {color: #7d8799;}
.ͼx {font-weight: bold;}
.ͼy {font-style: italic;}
.ͼz {text-decoration: line-through;}
.ͼ10 {color: #7d8799; text-decoration: underline;}
.ͼ11 {font-weight: bold; color: #e06c75;}
.ͼ12 {color: #d19a66;}
.ͼ13 {color: #98c379;}
.ͼ14 {color: #ffffff;}
.ͼo {color: #abb2bf; background-color: #282c34;}
.ͼo .cm-content {caret-color: #528bff;}
.ͼo .cm-cursor, .ͼo .cm-dropCursor {border-left-color: #528bff;}
.ͼo.cm-focused .cm-selectionBackground, .ͼo .cm-selectionBackground, .ͼo .cm-content ::selection {background-color: #3E4451;}
.ͼo .cm-panels {background-color: #21252b; color: #abb2bf;}
.ͼo .cm-panels.cm-panels-top {border-bottom: 2px solid black;}
.ͼo .cm-panels.cm-panels-bottom {border-top: 2px solid black;}
.ͼo .cm-searchMatch {background-color: #72a1ff59; outline: 1px solid #457dff;}
.ͼo .cm-searchMatch.cm-searchMatch-selected {background-color: #6199ff2f;}
.ͼo .cm-activeLine {background-color: #6699ff0b;}
.ͼo .cm-selectionMatch {background-color: #aafe661a;}
.ͼo.cm-focused .cm-matchingBracket, .ͼo.cm-focused .cm-nonmatchingBracket {background-color: #bad0f847;}
.ͼo .cm-gutters {background-color: #282c34; color: #7d8799; border: none;}
.ͼo .cm-activeLineGutter {background-color: #2c313a;}
.ͼo .cm-foldPlaceholder {background-color: transparent; border: none; color: #ddd;}
.ͼo .cm-tooltip {border: none; background-color: #353a42;}
.ͼo .cm-tooltip .cm-tooltip-arrow:before {border-top-color: transparent; border-bottom-color: transparent;}
.ͼo .cm-tooltip .cm-tooltip-arrow:after {border-top-color: #353a42; border-bottom-color: #353a42;}
.ͼo .cm-tooltip-autocomplete > ul > li[aria-selected] {background-color: #2c313a; color: #abb2bf;}
.ͼ15 {height: calc(100vh - 40px); border-left: 1px solid #ddd; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; min-width: 550px; width: 550px; background: #fff;}
.ͼ5 {color: #404740;}
.ͼ6 {text-decoration: underline;}
.ͼ7 {text-decoration: underline; font-weight: bold;}
.ͼ8 {font-style: italic;}
.ͼ9 {font-weight: bold;}
.ͼa {text-decoration: line-through;}
.ͼb {color: #708;}
.ͼc {color: #219;}
.ͼd {color: #164;}
.ͼe {color: #a11;}
.ͼf {color: #e40;}
.ͼg {color: #00f;}
.ͼh {color: #30a;}
.ͼi {color: #085;}
.ͼj {color: #167;}
.ͼk {color: #256;}
.ͼl {color: #00c;}
.ͼm {color: #940;}
.ͼn {color: #f00;}
.ͼ4 .cm-line ::selection {background-color: transparent !important;}
.ͼ4 .cm-line::selection {background-color: transparent !important;}
.ͼ4 .cm-line {caret-color: transparent !important;}
</style>
    
    <title>CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历</title>
    <meta name="keywords" content="在线简历,个人简历,简历模板,免费简历,Markdown简历,简历制作软件">
    <meta name="description" content="CodeCV简历支持你使用Markdown和富文本双编辑模式来免费编写你的简历,简历模板丰富,20+模板全部免费。">
    <meta name="author" content="coderlei">
    <meta name="og:title" content="免费在线简历工具-海量简历模板任你挑选-CodeCV简历">
    <meta name="og:keywords" content="在线简历,个人简历,简历模板,免费简历,Markdown简历,简历制作软件">
    <meta name="og:description" content="CodeCV简历支持你使用Markdown和富文本双编辑模式来免费编写你的简历,简历模板丰富,20+模板全部免费。">
    <meta name="360-site-verification" content="7954c6681a6995cef37da0770b0c738a">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/svg+xml" href="http://localhost:8680/favicon.svg">
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/font_3737803_dfosfnkopt7.css">
    <link href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/css2" rel="stylesheet">
    <!-- <link rel="stylesheet" href="https://textbus.io/lib/textbus.min.css" /> -->
    <!-- <script defer src="https://textbus.io/lib/textbus.min.js"></script> -->
    <!-- <script defer src="https://codeleilei.gitee.io/blog/axios.min.js"></script> -->
    <!-- <script defer src="https://codeleilei.gitee.io/blog/jspdf.umd.min.js"></script>
    <script defer src="https://codeleilei.gitee.io/blog/html2canvas.js"></script> -->
    <script type="module" crossorigin="" src="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/index-cd18bd06.js"></script>
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/@vue-c6fcbc26.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/aos-80360ef4.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/@vueuse-63034ea9.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/lodash-es-9d35530d.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/async-validator-604317c1.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/@element-plus-a7a51df2.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/dayjs-d3824421.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/@popperjs-535f1f87.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/@ctrl-aa1b1e70.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/element-plus-37c3e502.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/pinia-c946f11f.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/picture-verification-code-77c40e50.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/axios-93ecc7d0.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/vue-router-5174534a.js">
    <link rel="modulepreload" crossorigin="" href="http://localhost:8680/js/nprogress-6c9d9548.js">
    <link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/aos-73168167.css">
    <link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/element-plus-1386abe5.css">
    <link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/nprogress-8b89e2e0.css">
    <link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/index-14e21415.css">
  <link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/editor-d570dcbe.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/driver.js-dc4c3536.js"><link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/driver-4b398481.css"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/index-425c219f.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/config-9b322ae0.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/vue-codemirror-d2f2654b.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/codemirror-5e4ccb31.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/@codemirror-4e89dd6b.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/@lezer-868eaac8.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/crelt-8a41958c.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/style-mod-b0eaf9b1.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/w3c-keyname-2007ad7e.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/editor-0cf8669d.js"><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/markdown-transform-html-a1f02b0a.js"><link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/editor-d0089e30.css"><style line_height-create="true">.markdown-transform-html * { line-height: 26px; }</style><style custom_markdown_primary_bg_color-create="true">:root { --markdown-primary-bg-color: #333; }</style><style markdown-font-create="true">.jufe * { font-family: Noto Serif SC, 'Noto Sans SC', 'Noto Serif SC', 'Nunito', sans-serif, serif; }</style><style custom-markdown-primary-color-create="true">:root { --markdown-primary-color: #333 }</style><link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/style-84d3d4ba.css"><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  word-break: break-all;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/template-a6b3292e.js"><link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/template-5747c408.css"><style adjust_resume_margin_top-create="true">.jufe h1 {margin-top: 0px!important; margin-bottom: 3px!important;}.jufe .flex-layout {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe .flex-layout-item {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe p {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe .iconfont {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe strong {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe .resume-module {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe h2 {margin-top: 10px!important; margin-bottom: 5px!important;}.jufe ol {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe li {margin-top: 5px!important; margin-bottom: 0px!important;}.jufe ul {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe .single-code {margin-top: 0px!important; margin-bottom: 0px!important;}.jufe blockquote {margin-top: 12px!important; margin-bottom: 12px!important;}</style><link rel="modulepreload" as="script" crossorigin="" href="http://localhost:8680/js/index-1cefd440.js"><link rel="stylesheet" href="./CodeCV简历 - 免费在线简历工具,5分钟打造你的金牌简历_files/index-fb7690cc.css"></head>
  <body data-aos-easing="ease" data-aos-duration="400" data-aos-delay="0" style="--theme: #ff7449; --background: #ffffff; --font-color: #1e293b; --strong-color: #f24672; --toolbar-bg: #222222; --body-background: #f3f5f7; --el-color-primary: #ff7449; --writable-font-color: #545a69; --linear-background: #fbe9db;" class="">
    <div id="app" data-v-app=""><!----><div data-v-e397dbe9="" id="main"><!--v-if--><div data-v-a9816785="" id="header" class="noto-serif-sc"><i data-v-a9816785="" class="iconfont icon-back font-20 hover el-tooltip__trigger el-tooltip__trigger"></i><label data-v-a9816785="" for="resume-name-input">简历名称：</label><input data-v-a9816785="" id="resume-name-input" type="text"><ul data-v-da2d6782="" data-v-a9816785="" class="nav"><li data-v-da2d6782=""><div data-v-da2d6782="" class="el-dropdown"><div data-v-da2d6782="" class="el-dropdown-link el-tooltip__trigger el-tooltip__trigger" id="el-id-4830-225" role="button" tabindex="0" aria-controls="el-id-4830-226" aria-expanded="false" aria-haspopup="menu">导入/导出</div><!--v-if--></div></li><li data-v-da2d6782=""><a data-v-da2d6782="" href="http://localhost:8680/#/template" class="">简历模板</a></li><li data-v-da2d6782=""><a data-v-da2d6782="" href="http://localhost:8680/#/syntax/helper" class="">语法助手</a></li><li data-v-da2d6782="" class="use-guide">开启引导</li></ul><div data-v-b051f1ef="" data-v-a9816785="" class="export-total" style=""> 累计导出 <strong data-v-b051f1ef="">23415</strong> 份 </div><button data-v-4859ce8c="" class="reward btn"><i data-v-4859ce8c="" class="iconfont icon-moneybagfill"></i>打赏</button><!----><button data-v-a9816785="" class="exporter server-export btn">导出PDF</button><button data-v-a9816785="" class="exporter local-export btn">备用导出</button><div data-v-a9816785="" class="operator"><i data-v-a9816785="" class="iconfont icon-github github font-25 el-tooltip__trigger el-tooltip__trigger"></i><i data-v-a9816785="" class="iconfont icon-comment problem font-25 el-tooltip__trigger el-tooltip__trigger"></i><i class="bold iconfont icon-shine mr-20 font-25 pointer hover el-tooltip__trigger el-tooltip__trigger" style="color: rgb(255, 116, 73);"></i></div></div><!----><div data-v-37f2732b="" id="editor"><div data-v-82009773="" data-v-37f2732b="" class="markdown-edit noto-serif-sc"><!----><div data-v-ff2daa66="" class="editor-toolbar markdown-mode light-mode"><button data-v-ff2daa66="" data-command="insertBold"><i data-v-ff2daa66="" class="iconfont icon-bold font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertItalic"><i data-v-ff2daa66="" class="iconfont icon-italic font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertUnorderedlist"><i data-v-ff2daa66="" class="iconfont icon-unorderedlist font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertOrderedlist"><i data-v-ff2daa66="" class="iconfont icon-orderedlist font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertLink"><i data-v-ff2daa66="" class="iconfont icon-link font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertAvatar"><i data-v-ff2daa66="" class="iconfont icon-image font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertIcon"><i data-v-ff2daa66="" class="iconfont icon-emoji font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertHeadLayout"><i data-v-ff2daa66="" class="iconfont icon-info font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertMainLayout"><i data-v-ff2daa66="" class="iconfont icon-practice font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertMultiColumns"><i data-v-ff2daa66="" class="iconfont icon-columns font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="insertTable"><i data-v-ff2daa66="" class="iconfont icon-table font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button><button data-v-ff2daa66="" data-command="toContentMode"><i data-v-ff2daa66="" class="iconfont icon-write font-20 mr-10 ml-10 pointer hover el-tooltip__trigger el-tooltip__trigger"></i></button></div><!----><!----><!----><div class="v-codemirror" style="display: contents;"><div class="cm-editor cm-focused ͼ1 ͼ2 ͼ4 ͼbz"><div aria-live="polite" style="position: fixed; top: -10000px;"></div><div tabindex="-1" class="cm-scroller"><div class="cm-gutters" aria-hidden="true" style="min-height: 2788px; position: sticky;"><div class="cm-gutter cm-lineNumbers"><div class="cm-gutterElement" style="height: 0px; visibility: hidden; pointer-events: none;">999</div><div class="cm-gutterElement" style="height: 20px; margin-top: 764px;">39</div><div class="cm-gutterElement" style="height: 20px;">40</div><div class="cm-gutterElement" style="height: 20px;">41</div><div class="cm-gutterElement" style="height: 20px;">42</div><div class="cm-gutterElement" style="height: 20px;">43</div><div class="cm-gutterElement" style="height: 20px;">44</div><div class="cm-gutterElement" style="height: 20px;">45</div><div class="cm-gutterElement" style="height: 20px;">46</div><div class="cm-gutterElement" style="height: 20px;">47</div><div class="cm-gutterElement" style="height: 20px;">48</div><div class="cm-gutterElement" style="height: 20px;">49</div><div class="cm-gutterElement" style="height: 20px;">50</div><div class="cm-gutterElement" style="height: 20px;">51</div><div class="cm-gutterElement" style="height: 20px;">52</div><div class="cm-gutterElement" style="height: 20px;">53</div><div class="cm-gutterElement" style="height: 20px;">54</div><div class="cm-gutterElement" style="height: 20px;">55</div><div class="cm-gutterElement" style="height: 20px;">56</div><div class="cm-gutterElement" style="height: 20px;">57</div><div class="cm-gutterElement" style="height: 20px;">58</div><div class="cm-gutterElement" style="height: 20px;">59</div><div class="cm-gutterElement" style="height: 20px;">60</div><div class="cm-gutterElement" style="height: 20px;">61</div><div class="cm-gutterElement" style="height: 20px;">62</div><div class="cm-gutterElement" style="height: 20px;">63</div><div class="cm-gutterElement" style="height: 20px;">64</div><div class="cm-gutterElement" style="height: 20px;">65</div><div class="cm-gutterElement" style="height: 20px;">66</div><div class="cm-gutterElement" style="height: 20px;">67</div><div class="cm-gutterElement" style="height: 20px;">68</div><div class="cm-gutterElement" style="height: 20px;">69</div><div class="cm-gutterElement" style="height: 20px;">70</div><div class="cm-gutterElement" style="height: 20px;">71</div><div class="cm-gutterElement" style="height: 20px;">72</div><div class="cm-gutterElement" style="height: 20px;">73</div><div class="cm-gutterElement" style="height: 20px;">74</div><div class="cm-gutterElement" style="height: 20px;">75</div><div class="cm-gutterElement" style="height: 20px;">76</div><div class="cm-gutterElement" style="height: 20px;">77</div><div class="cm-gutterElement" style="height: 20px;">78</div><div class="cm-gutterElement" style="height: 20px;">79</div><div class="cm-gutterElement" style="height: 20px;">80</div><div class="cm-gutterElement" style="height: 20px;">81</div><div class="cm-gutterElement" style="height: 20px;">82</div><div class="cm-gutterElement" style="height: 20px;">83</div><div class="cm-gutterElement" style="height: 20px;">84</div><div class="cm-gutterElement" style="height: 20px;">85</div><div class="cm-gutterElement" style="height: 20px;">86</div><div class="cm-gutterElement cm-activeLineGutter" style="height: 20px;">87</div><div class="cm-gutterElement" style="height: 20px;">88</div><div class="cm-gutterElement" style="height: 20px;">89</div><div class="cm-gutterElement" style="height: 20px;">90</div><div class="cm-gutterElement" style="height: 20px;">91</div><div class="cm-gutterElement" style="height: 20px;">92</div><div class="cm-gutterElement" style="height: 20px;">93</div><div class="cm-gutterElement" style="height: 20px;">94</div><div class="cm-gutterElement" style="height: 20px;">95</div><div class="cm-gutterElement" style="height: 20px;">96</div><div class="cm-gutterElement" style="height: 20px;">97</div><div class="cm-gutterElement" style="height: 20px;">98</div><div class="cm-gutterElement" style="height: 20px;">99</div><div class="cm-gutterElement" style="height: 20px;">100</div><div class="cm-gutterElement" style="height: 20px;">101</div><div class="cm-gutterElement" style="height: 20px;">102</div><div class="cm-gutterElement" style="height: 20px;">103</div><div class="cm-gutterElement" style="height: 20px;">104</div><div class="cm-gutterElement" style="height: 20px;">105</div><div class="cm-gutterElement" style="height: 20px;">106</div><div class="cm-gutterElement" style="height: 20px;">107</div><div class="cm-gutterElement" style="height: 20px;">108</div><div class="cm-gutterElement" style="height: 20px;">109</div><div class="cm-gutterElement" style="height: 20px;">110</div><div class="cm-gutterElement" style="height: 20px;">111</div><div class="cm-gutterElement" style="height: 20px;">112</div><div class="cm-gutterElement" style="height: 20px;">113</div><div class="cm-gutterElement" style="height: 20px;">114</div><div class="cm-gutterElement" style="height: 20px;">115</div><div class="cm-gutterElement" style="height: 20px;">116</div><div class="cm-gutterElement" style="height: 20px;">117</div><div class="cm-gutterElement" style="height: 20px;">118</div><div class="cm-gutterElement" style="height: 20px;">119</div><div class="cm-gutterElement" style="height: 20px;">120</div><div class="cm-gutterElement" style="height: 20px;">121</div><div class="cm-gutterElement" style="height: 20px;">122</div><div class="cm-gutterElement" style="height: 20px;">123</div><div class="cm-gutterElement" style="height: 20px;">124</div><div class="cm-gutterElement" style="height: 20px;">125</div><div class="cm-gutterElement" style="height: 20px;">126</div><div class="cm-gutterElement" style="height: 20px;">127</div><div class="cm-gutterElement" style="height: 20px;">128</div><div class="cm-gutterElement" style="height: 20px;">129</div><div class="cm-gutterElement" style="height: 20px;">130</div><div class="cm-gutterElement" style="height: 20px;">131</div><div class="cm-gutterElement" style="height: 20px;">132</div><div class="cm-gutterElement" style="height: 20px;">133</div><div class="cm-gutterElement" style="height: 20px;">134</div><div class="cm-gutterElement" style="height: 20px;">135</div><div class="cm-gutterElement" style="height: 20px;">136</div><div class="cm-gutterElement" style="height: 20px;">137</div><div class="cm-gutterElement" style="height: 20px;">138</div><div class="cm-gutterElement" style="height: 20px;">139</div></div><div class="cm-gutter cm-foldGutter"><div class="cm-gutterElement" style="height: 0px; visibility: hidden; pointer-events: none;"><span title="Unfold line">›</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 764px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 20px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 120px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 100px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 20px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 80px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 60px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 80px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 60px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement cm-activeLineGutter" style="height: 20px; margin-top: 60px;"></div><div class="cm-gutterElement" style="height: 20px; margin-top: 20px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 80px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 20px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 100px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 80px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 100px;"><span title="Fold line">⌄</span></div><div class="cm-gutterElement" style="height: 20px; margin-top: 40px;"><span title="Fold line">⌄</span></div></div></div><div spellcheck="false" autocorrect="off" autocapitalize="off" translate="no" contenteditable="true" class="cm-content" style="tab-size: 2; flex-basis: 1943px;" role="textbox" aria-multiline="true" aria-autocomplete="list" data-language="markdown"><div contenteditable="false" style="height: 760px;"></div><div class="cm-line"><span class="ͼ7 ͼ5">##</span><span class="ͼ7"> 工作经历</span></div><div class="cm-line"><br></div><div class="cm-line">::: start</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">北京承启通科技有限公司福州分公司</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">:::</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">Java开发工程师（2021.03 - 至今）</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">::: end</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">-</span> 作为核心开发, 负责企业级通信平台的核心业务开发，参与AI质检、呼叫中心、号码隐藏平台等大型项目的设计与实现</div><div class="cm-line"><br></div><div class="cm-line">::: start</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">南威软件</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">:::</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">Java开发工程师（2019.06 - 2021.03）</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">::: end</div><div class="cm-line"><span class="ͼ5">-</span> 作为核心开发, 负责公安数据服务平台的设计与实现</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ7 ͼ5">##</span><span class="ͼ7"> 项目经历</span></div><div class="cm-line"><br></div><div class="cm-line">::: start</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">智能语音质检平台</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">:::</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">Java后端开发（2024.03 - 2024.10）</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">::: end</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">技术栈</span><span class="ͼ9 ͼ5">**</span>：<span class="ͼ5">`</span>SpringCloud<span class="ͼ5">`</span> <span class="ͼ5">`</span>SpringBoot<span class="ͼ5">`</span> <span class="ͼ5">`</span>Redis<span class="ͼ5">`</span> <span class="ͼ5">`</span>MySQL<span class="ͼ5">`</span> <span class="ͼ5">`</span>RocketMQ<span class="ͼ5">`</span> <span class="ͼ5">`</span>Docker<span class="ͼ5">`</span></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目简介</span><span class="ͼ9 ͼ5">**</span>：企业级智能语音质检系统，基于ASR语音识别和NLP自然语言处理技术，实现通话内容的自动转写与智能质检。系统支持质检规则可视化配置、多维度评分、质检报告生成等功能，日均处理通话录音<span class="ͼ9 ͼ5">**</span><span class="ͼ9">100万+</span><span class="ͼ9 ͼ5">**</span>，质检准确率达95%以上。</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">主要内容</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">1.</span> 负责系统核心架构设计与开发，实现与ASR/NLP平台的接口集成，构建高效的语音转文本处理流程</div><div class="cm-line"><span class="ͼ5">2.</span> 设计基于RocketMQ的异步处理流水线，实现录音文件上传、转写结果接收、质检规则应用的全流程自动化</div><div class="cm-line"><span class="ͼ5">3.</span> 开发可视化质检规则配置平台，支持企业自定义质检标准与评分体系</div><div class="cm-line"><span class="ͼ5">4.</span> 实现质检结果回调通知机制，确保质检结果及时反馈给业务系统</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目难点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line">5. 设计高性能文本处理架构，支持每日百万级对话文本的接收、解析、存储与分析，确保系统在高并发场景下的稳定性与响应速度</div><div class="cm-line">6. 基于规则引擎设计可配置的质检评分系统，支持关键词识别、禁用语检测、话术规范检查、情感分析等多维度规则组合，实现复杂业务逻辑的灵活配置</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目亮点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line">7. 自研轻量级规则引擎，支持30+种质检规则类型和复杂条件组合，如"问候语+禁用语+业务话术+情绪检测"的多维度评分，准确率达95%以上</div><div class="cm-line">8. 设计基于RocketMQ的消息驱动架构，实现ASR结果接收→质检任务创建→规则应用→结果通知的完整流程，系统吞吐量提升300%，支持每日100万+通话的实时质检</div><div class="cm-line">9. 开发智能回调通知机制，结合RocketMQ实现可靠消息投递，支持多种通知策略和失败重试机制，通知成功率达99.99%</div><div class="cm-activeLine cm-line">10. 设计离线+实时双模式质检架构，支持HTTP/SFTP多种录音接入方式，通过MinIO对象存储实现录音文件和ASR结果的分离存储，数据库仅存储URL引用，存储成本降低70%，查询性能提升5倍</div><div class="cm-line"><br></div><div class="cm-line">::: start</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">呼叫中心+云总机平台</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">:::</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">Java后端开发（2023.07 - 2023.12）</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">::: end</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">技术栈</span><span class="ͼ9 ͼ5">**</span>：<span class="ͼ5">`</span>SpringCloud<span class="ͼ5">`</span> <span class="ͼ5">`</span>SpringBoot<span class="ͼ5">`</span> <span class="ͼ5">`</span>Redis<span class="ͼ5">`</span> <span class="ͼ5">`</span>MySQL<span class="ͼ5">`</span> <span class="ͼ5">`</span>RocketMQ<span class="ͼ5">`</span> <span class="ͼ5">`</span>Netty<span class="ͼ5">`</span> <span class="ͼ5">`</span>Docker<span class="ͼ5">`</span></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目简介</span><span class="ͼ9 ͼ5">**</span>：企业级呼叫中心与云总机系统，整合电话、短信、在线客服等多渠道通信能力，为企业提供统一的客户沟通平台。系统支持智能IVR、呼叫排队、通话录音、实时监控、数据分析等功能，日均处理通话量<span class="ͼ9 ͼ5">**</span><span class="ͼ9">100万+</span><span class="ͼ9 ͼ5">**</span>。</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">主要内容</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">1.</span> 负责呼叫路由、呼入排队分配、坐席通话状态管理等核心业务模块的架构设计与开发，独立完成路由引擎和状态管理系统，协同团队完成整体系统集成</div><div class="cm-line"><span class="ͼ5">2.</span> 设计并实现基于RocketMQ的呼叫事件处理系统，保障高并发场景下的系统稳定性</div><div class="cm-line"><span class="ͼ5">3.</span> 设计并实现高并发通话处理引擎，支持多租户、多坐席并发通话管理</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目难点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><span class="ͼ5">4.</span> 基于责任链+策略模式设计可插拔路由引擎，实现呼入排队规则的动态配置与热更新，支持"VIP优先→技能匹配→等待时长"等复杂业务规则组合，路由决策时间控制在100ms内，坐席分配准确率达99.9%</div><div class="cm-line"><span class="ͼ5">5.</span> 基于RocketMQ顺序消息构建呼叫事件处理系统，实现坐席发起呼叫后的桥接、三方通话、转接、咨询等复杂业务流程的事件驱动处理，保障呼叫状态一致性和实时通知，事件处理延迟&lt;100ms</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目亮点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><span class="ͼ5">6.</span> 基于Netty+WebSocket构建坐席实时状态管理系统，支持5000+坐席的通话中、响铃、空闲、小休、忙碌等多种状态毫秒级同步，状态变更延迟&lt;50ms</div><div class="cm-line"><span class="ͼ5">7.</span> 基于责任链模式实现可视化呼叫路由规则配置平台，支持企业自定义多级路由策略组合，如"技能组筛选→VIP优先→等待时间最长"的复合规则，实现动态规则热更新，平均等待时间降低45%，客户满意度提升30%</div><div class="cm-line"><span class="ͼ5">8.</span> 基于Redisson分布式锁解决高并发场景下的坐席资源竞争和呼叫分配冲突问题，资源冲突率从5%降至0.01%，呼叫分配准确率达到99.99%</div><div class="cm-line"><br></div><div class="cm-line">::: start</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">号码隐藏保护平台</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">:::</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">Java后端开发（2021.03 - 2021.11）</span><span class="ͼ9 ͼ5">**</span></div><div class="cm-line"><br></div><div class="cm-line">::: end</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">技术栈</span><span class="ͼ9 ͼ5">**</span>：<span class="ͼ5">`</span>SpringCloud<span class="ͼ5">`</span> <span class="ͼ5">`</span>SpringBoot<span class="ͼ5">`</span> <span class="ͼ5">`</span>Redis<span class="ͼ5">`</span> <span class="ͼ5">`</span>MySQL<span class="ͼ5">`</span> <span class="ͼ5">`</span>RabbitMQ<span class="ͼ5">`</span></div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目简介</span><span class="ͼ9 ͼ5">**</span>：企业级号码隐藏保护平台，为电商、外卖、快递、网约车等行业提供通话号码保护服务。系统支持AXB、AXE、AXYB等多种号码绑定模式，实现用户隐私保护与通话安全，日均处理绑定请求<span class="ͼ9 ͼ5">**</span><span class="ͼ9">1000万+</span><span class="ͼ9 ͼ5">**</span>，支持并发通话5万路。</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">主要内容</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">1.</span> 设计基于Redis的高性能号码池管理系统，实现号码资源的智能分配与回收</div><div class="cm-line"><span class="ͼ5">2.</span> 设计并实现多种号码绑定模式：AXB（双向保护）、AXE（单向保护, 分机号）、AXYB（多方保护）等业务场景</div><div class="cm-line"><span class="ͼ5">3.</span> 开发号码池动态分配算法，支持按地区、运营商、号段等维度智能分配中间号码</div><div class="cm-line"><span class="ͼ5">4.</span> 使用SpringBoot开发RESTful API，为客户提供号码绑定、解绑、查询等接口服务</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目难点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><span class="ͼ5">5.</span> 面对每秒数千次绑定请求，如何保证号码资源不重复分配且数据强一致性，采用Redis分布式锁解决并发竞争问题，绑定成功率达99.99%</div><div class="cm-line"><span class="ͼ5">6.</span> 实现多种绑定模式（AXB/AXE/AXYB）的数据模型设计与业务逻辑，支持绑定关系的动态调整、号码过期回收管理与冲突处理</div><div class="cm-line"><span class="ͼ9 ͼ5">**</span><span class="ͼ9">项目亮点</span><span class="ͼ9 ͼ5">**</span>：</div><div class="cm-line"><span class="ͼ5">7.</span> 开发基于RabbitMQ的绑定关系统，通过死信队列+延迟队列实现绑定关系的定时过期与X号码自动回收，号码复用率提高60%</div><div class="cm-line"><span class="ͼ5">8.</span> 通过Redis分布式缓存和MySQL分库分表，绑定关系查询响应时间从200ms优化至20ms，支撑日均500万+绑定查询</div><div class="cm-line"><span class="ͼ5">9.</span> 设计并实现基于HMAC-SHA256的接口签名验证体系，通过appKey+appSecret+timestamp+nonce的组合生成签名，有效防止接口被恶意调用和重放攻击，接口安全性提升99%</div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ7 ͼ5">##</span><span class="ͼ7"> 自我评价</span></div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">&gt;</span> 6年Java后端开发经验，专注企业级高并发系统架构设计与性能优化。深度掌握SpringCloud微服务生态，主导开发多个日均百万级处理量的核心业务系统，通过分布式架构设计和性能调优，系统整体性能提升200%以上。在微服务架构、消息驱动设计、分布式系统优化等方面具有丰富的实战经验和解决方案。</div><div class="cm-line"><span class="ͼ7 ͼ5">##</span><span class="ͼ7"> 致谢</span></div><div class="cm-line"><br></div><div class="cm-line"><span class="ͼ5">&gt;</span> 感谢您阅读我的简历，期待与您一起共事。</div></div><div class="cm-layer cm-layer-above cm-cursorLayer" aria-hidden="true" style="z-index: 150; animation-duration: 1200ms; animation-name: cm-blink;"><div class="cm-cursor cm-cursor-primary" style="left: 672.756px; top: 1725.82px; height: 16.3636px;"></div></div><div class="cm-layer cm-selectionLayer" aria-hidden="true" style="z-index: -2;"></div></div></div></div><div data-v-82009773="" class="move absolute"><span data-v-82009773="">.</span><span data-v-82009773="">.</span><span data-v-82009773="">.</span></div></div><div data-v-8e77df9e="" data-v-37f2732b="" class="outer markdown-render" style="background: var(--bg-theme);"><div data-v-39bf65b5="" class="operator resume-tools"><div data-v-39bf65b5="" class="el-slider el-slider--small slider"><div class="el-slider__runway"><div class="el-slider__bar" style="width: 100%; left: 0%;"></div><div class="el-slider__button-wrapper" tabindex="0" role="slider" aria-label="slider between 0 and 100" aria-valuemin="0" aria-valuemax="100" aria-valuenow="100" aria-valuetext="100" aria-orientation="horizontal" aria-disabled="false" id="el-id-4830-258" style="left: 100%;"><div class="el-slider__button el-tooltip__trigger el-tooltip__trigger"></div></div><!--v-if--><div></div><div><div class="el-slider__stop el-slider__marks-stop" style="left: 0%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 10%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 20%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 30%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 40%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 50%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 60%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 70%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 80%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 90%;"></div><div class="el-slider__stop el-slider__marks-stop" style="left: 100%;"></div></div><div class="el-slider__marks"><div class="el-slider__marks-text" style="left: 0%;">0%</div><div class="el-slider__marks-text" style="left: 10%;">10%</div><div class="el-slider__marks-text" style="left: 20%;">20%</div><div class="el-slider__marks-text" style="left: 30%;">30%</div><div class="el-slider__marks-text" style="left: 40%;">40%</div><div class="el-slider__marks-text" style="left: 50%;">50%</div><div class="el-slider__marks-text" style="left: 60%;">60%</div><div class="el-slider__marks-text" style="left: 70%;">70%</div><div class="el-slider__marks-text" style="left: 80%;">80%</div><div class="el-slider__marks-text" style="left: 90%;">90%</div><div class="el-slider__marks-text" style="left: 100%;">100%</div></div></div><!--v-if--></div><div data-v-39bf65b5="" class="operator-level2"><i data-v-39bf65b5="" class="iconfont icon-adjust operator-item el-tooltip__trigger el-tooltip__trigger"></i><label data-v-39bf65b5="" for="upload-avatar" class="operator-item card el-tooltip__trigger el-tooltip__trigger"><i data-v-39bf65b5="" class="iconfont icon-zhengjian"></i></label><input data-v-39bf65b5="" type="file" id="upload-avatar" accept=".png,.jpg,.jpeg"><i data-v-39bf65b5="" class="operator-item iconfont icon-diy el-tooltip__trigger el-tooltip__trigger"></i><div data-v-39bf65b5="" class="operator-item font-color-picker"><div class="el-color-picker el-color-picker--small el-tooltip__trigger el-tooltip__trigger" role="button" aria-label="color picker" aria-description="current color is #333. press enter to select a new color." tabindex="0" id="el-id-4830-260"><!--v-if--><div class="el-color-picker__trigger"><span class="el-color-picker__color"><span class="el-color-picker__color-inner" style="background-color: rgb(51, 51, 51);"><i class="el-icon el-color-picker__icon is-icon-arrow-down"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--></span></span></div></div></div><div data-v-39bf65b5="" class="operator-item main-color-picker"><div class="el-color-picker el-color-picker--small el-tooltip__trigger el-tooltip__trigger" role="button" aria-label="color picker" aria-description="current color is #333. press enter to select a new color." tabindex="0" id="el-id-4830-262"><!--v-if--><div class="el-color-picker__trigger"><span class="el-color-picker__color"><span class="el-color-picker__color-inner" style="background-color: rgb(51, 51, 51);"><i class="el-icon el-color-picker__icon is-icon-arrow-down"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--></span></span></div></div></div><i data-v-39bf65b5="" class="operator-item iconfont icon-refresh ml-20 el-tooltip__trigger el-tooltip__trigger"></i><div data-v-39bf65b5="" class="el-switch el-switch--small operator-item auto-one-page el-tooltip__trigger el-tooltip__trigger"><input class="el-switch__input" type="checkbox" role="switch" aria-checked="false" aria-disabled="false" name="" true-value="true" false-value="false" id="el-id-4830-263"><!--v-if--><span class="el-switch__core"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><div data-v-39bf65b5="" class="el-switch el-switch--small operator-item follow-roll el-tooltip__trigger el-tooltip__trigger"><input class="el-switch__input" type="checkbox" role="switch" aria-checked="false" aria-disabled="false" name="" true-value="true" false-value="false" id="el-id-4830-264"><!--v-if--><span class="el-switch__core"><!--v-if--><div class="el-switch__action"><!--v-if--></div></span><!--v-if--></div><div data-v-39bf65b5="" class="el-select el-select--small operator-item lh-select el-tooltip__trigger el-tooltip__trigger"><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--small el-input--suffix"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly="" autocomplete="off" tabindex="0" placeholder="Select" id="el-id-4830-265"><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-icon el-select__caret el-select__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--></div></div></div><div data-v-39bf65b5="" class="el-select el-select--small operator-item font-select el-tooltip__trigger el-tooltip__trigger"><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--small el-input--suffix"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly="" autocomplete="off" tabindex="0" placeholder="Select" id="el-id-4830-266"><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-icon el-select__caret el-select__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--></div></div></div></div><br data-v-39bf65b5=""></div><!----><!----><div data-v-8e77df9e="" class="markdown-transform-html jufe reference-dom"><h1>林石墙 - Java开发工程师</h1><div class="flex-layout"><div class="flex-layout-item"><p><i class="iconfont icon-user"></i><strong>姓名</strong>: 林石墙</p><p><i class="iconfont icon-phone"></i><strong>电话号码</strong>：13023828639</p></div><div class="flex-layout-item"><p><i class="iconfont icon-product"></i> <strong>性别</strong>: 男</p><p><i class="iconfont icon-email"></i><strong>邮箱</strong>：<EMAIL></p></div><div class="flex-layout-item"><p><i class="iconfont icon-work"></i><strong>工作经验</strong>：6 年</p></div></div><div class="resume-module"><h2>教育背景</h2><div class="flex-layout"><div class="flex-layout-item"><p>福建理工大学</p></div><div class="flex-layout-item"><p>网络工程</p></div><div class="flex-layout-item"><p><strong>2015.09 - 2019.06</strong></p></div></div></div><div class="resume-module"><h2>专业技能</h2><ol><li>熟练掌握Java核心技术及并发编程，深入理解JVM内存模型与GC机制，具备线上问题排查能力</li><li>熟练应用设计模式(模板方法/责任链/策略/观察者等)解决复杂业务问题，具备系统架构设计能力</li><li>熟练掌握Spring生态(SpringBoot/SpringCloud)和MyBatis Plus，对框架核心原理有深入研究</li><li>熟练掌握SpringCloud微服务生态(Nacos/OpenFeign/Sentinel/Gateway)，具备微服务架构设计经验</li><li>熟练掌握MySQL和Redis，具备分库分表、多级缓存架构设计和性能优化能力</li><li>熟练掌握ShardingSphere-JDBC，具备分库分表设计和实现能力，解决海量数据存储和查询性能问题</li><li>熟练掌握RocketMQ/RabbitMQ等消息中间件，能解决消息幂等性、顺序性与事务性问题</li><li>熟练使用Docker/Jenkins实现CI/CD，搭建ELK/Prometheus监控体系和SkyWalking分布式链路追踪</li><li>熟练使用AI辅助编程工具(Cursor/Augment等)，具备代码审查和重构能力，开发效率提升30%以上，同时保证代码质量和安全性</li></ol></div><div class="resume-module"><h2>工作经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>北京承启通科技有限公司福州分公司</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2021.03 - 至今）</strong></p></div></div><ul><li>作为核心开发, 负责企业级通信平台的核心业务开发，参与AI质检、呼叫中心、号码隐藏平台等大型项目的设计与实现</li></ul><div class="flex-layout"><div class="flex-layout-item"><p><strong>南威软件</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2019.06 - 2021.03）</strong></p></div></div><ul><li>作为核心开发, 负责公安数据服务平台的设计与实现</li></ul></div><div class="resume-module"><h2>项目经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>智能语音质检平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2024.03 - 2024.10）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级智能语音质检系统，基于ASR语音识别和NLP自然语言处理技术，实现通话内容的自动转写与智能质检。系统支持质检规则可视化配置、多维度评分、质检报告生成等功能，日均处理通话录音<strong>100万+</strong>，质检准确率达95%以上。</p><p><strong>主要内容</strong>：</p><ol><li>负责系统核心架构设计与开发，实现与ASR/NLP平台的接口集成，构建高效的语音转文本处理流程</li><div white-space="true" style="height: 90px;"></div><li>设计基于RocketMQ的异步处理流水线，实现录音文件上传、转写结果接收、质检规则应用的全流程自动化</li><li>开发可视化质检规则配置平台，支持企业自定义质检标准与评分体系</li><li>实现质检结果回调通知机制，确保质检结果及时反馈给业务系统</li></ol><p><strong>项目难点</strong>：</p><ol><li>设计高性能文本处理架构，支持每日百万级对话文本的接收、解析、存储与分析，确保系统在高并发场景下的稳定性与响应速度</li><li>基于规则引擎设计可配置的质检评分系统，支持关键词识别、禁用语检测、话术规范检查、情感分析等多维度规则组合，实现复杂业务逻辑的灵活配置</li></ol><p><strong>项目亮点</strong>：</p><ol><li>自研轻量级规则引擎，支持30+种质检规则类型和复杂条件组合，如"问候语+禁用语+业务话术+情绪检测"的多维度评分，准确率达95%以上</li><li>设计基于RocketMQ的消息驱动架构，实现ASR结果接收→质检任务创建→规则应用→结果通知的完整流程，系统吞吐量提升300%，支持每日100万+通话的实时质检</li><li>开发智能回调通知机制，结合RocketMQ实现可靠消息投递，支持多种通知策略和失败重试机制，通知成功率达99.99%</li><li>设计离线+实时双模式质检架构，支持HTTP/SFTP多种录音接入方式，通过MinIO对象存储实现录音文件和ASR结果的分离存储，数据库仅存储URL引用，存储成本降低70%，查询性能提升5倍</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>呼叫中心+云总机平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2023.07 - 2023.12）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Netty</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级呼叫中心与云总机系统，整合电话、短信、在线客服等多渠道通信能力，为企业提供统一的客户沟通平台。系统支持智能IVR、呼叫排队、通话录音、实时监控、数据分析等功能，日均处理通话量<strong>100万+</strong>。</p><p><strong>主要内容</strong>：</p><ol><li>负责呼叫路由、呼入排队分配、坐席通话状态管理等核心业务模块的架构设计与开发，独立完成路由引擎和状态管理系统，协同团队完成整体系统集成</li><li>设计并实现基于RocketMQ的呼叫事件处理系统，保障高并发场景下的系统稳定性</li><li>设计并实现高并发通话处理引擎，支持多租户、多坐席并发通话管理</li></ol><p><strong>项目难点</strong>：</p><ol><li>基于责任链+策略模式设计可插拔路由引擎，实现呼入排队规则的动态配置与热更新，支持"VIP优先→技能匹配→等待时长"等复杂业务规则组合，路由决策时间控制在100ms内，坐席分配准确率达99.9%</li><li>基于RocketMQ顺序消息构建呼叫事件处理系统，实现坐席发起呼叫后的桥接、三方通话、转接、咨询等复杂业务流程的事件驱动处理，保障呼叫状态一致性和实时通知，事件处理延迟&lt;100ms</li></ol><p><strong>项目亮点</strong>：</p><ol><li>基于Netty+WebSocket构建坐席实时状态管理系统，支持5000+坐席的通话中、响铃、空闲、小休、忙碌等多种状态毫秒级同步，状态变更延迟&lt;50ms</li><div white-space="true" style="height: 81px;"></div><li>基于责任链模式实现可视化呼叫路由规则配置平台，支持企业自定义多级路由策略组合，如"技能组筛选→VIP优先→等待时间最长"的复合规则，实现动态规则热更新，平均等待时间降低45%，客户满意度提升30%</li><li>基于Redisson分布式锁解决高并发场景下的坐席资源竞争和呼叫分配冲突问题，资源冲突率从5%降至0.01%，呼叫分配准确率达到99.99%</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>号码隐藏保护平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2021.03 - 2021.11）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RabbitMQ</code></p><p><strong>项目简介</strong>：企业级号码隐藏保护平台，为电商、外卖、快递、网约车等行业提供通话号码保护服务。系统支持AXB、AXE、AXYB等多种号码绑定模式，实现用户隐私保护与通话安全，日均处理绑定请求<strong>1000万+</strong>，支持并发通话5万路。</p><p><strong>主要内容</strong>：</p><ol><li>设计基于Redis的高性能号码池管理系统，实现号码资源的智能分配与回收</li><li>设计并实现多种号码绑定模式：AXB（双向保护）、AXE（单向保护, 分机号）、AXYB（多方保护）等业务场景</li><li>开发号码池动态分配算法，支持按地区、运营商、号段等维度智能分配中间号码</li><li>使用SpringBoot开发RESTful API，为客户提供号码绑定、解绑、查询等接口服务</li></ol><p><strong>项目难点</strong>：</p><ol><li>面对每秒数千次绑定请求，如何保证号码资源不重复分配且数据强一致性，采用Redis分布式锁解决并发竞争问题，绑定成功率达99.99%</li><li>实现多种绑定模式（AXB/AXE/AXYB）的数据模型设计与业务逻辑，支持绑定关系的动态调整、号码过期回收管理与冲突处理</li></ol><p><strong>项目亮点</strong>：</p><ol><li>开发基于RabbitMQ的绑定关系统，通过死信队列+延迟队列实现绑定关系的定时过期与X号码自动回收，号码复用率提高60%</li><li>通过Redis分布式缓存和MySQL分库分表，绑定关系查询响应时间从200ms优化至20ms，支撑日均500万+绑定查询</li><li>设计并实现基于HMAC-SHA256的接口签名验证体系，通过appKey+appSecret+timestamp+nonce的组合生成签名，有效防止接口被恶意调用和重放攻击，接口安全性提升99%</li></ol></div><div class="resume-module"><h2>自我评价</h2><blockquote> 6年Java后端开发经验，专注企业级高并发系统架构设计与性能优化。深度掌握SpringCloud微服务生态，主导开发多个日均百万级处理量的核心业务系统，通过分布式架构设计和性能调优，系统整体性能提升200%以上。在微服务架构、消息驱动设计、分布式系统优化等方面具有丰富的实战经验和解决方案。</blockquote></div><div class="resume-module"><h2>致谢</h2><blockquote> 感谢您阅读我的简历，期待与您一起共事。</blockquote></div></div><div data-v-8e77df9e="" class="re-render" style="transform: translateY(0px) scale(1);"><div class="jufe-wrapper-page"><div class="jufe-wrapper-page-item" style="height: 1123px;"><div data-v-8e77df9e="" class="markdown-transform-html jufe reference-dom" style="position: absolute; top: 0px; left: 0px;"><h1>林石墙 - Java开发工程师</h1><div class="flex-layout"><div class="flex-layout-item"><p><i class="iconfont icon-user"></i><strong>姓名</strong>: 林石墙</p><p><i class="iconfont icon-phone"></i><strong>电话号码</strong>：13023828639</p></div><div class="flex-layout-item"><p><i class="iconfont icon-product"></i> <strong>性别</strong>: 男</p><p><i class="iconfont icon-email"></i><strong>邮箱</strong>：<EMAIL></p></div><div class="flex-layout-item"><p><i class="iconfont icon-work"></i><strong>工作经验</strong>：6 年</p></div></div><div class="resume-module"><h2>教育背景</h2><div class="flex-layout"><div class="flex-layout-item"><p>福建理工大学</p></div><div class="flex-layout-item"><p>网络工程</p></div><div class="flex-layout-item"><p><strong>2015.09 - 2019.06</strong></p></div></div></div><div class="resume-module"><h2>专业技能</h2><ol><li>熟练掌握Java核心技术及并发编程，深入理解JVM内存模型与GC机制，具备线上问题排查能力</li><li>熟练应用设计模式(模板方法/责任链/策略/观察者等)解决复杂业务问题，具备系统架构设计能力</li><li>熟练掌握Spring生态(SpringBoot/SpringCloud)和MyBatis Plus，对框架核心原理有深入研究</li><li>熟练掌握SpringCloud微服务生态(Nacos/OpenFeign/Sentinel/Gateway)，具备微服务架构设计经验</li><li>熟练掌握MySQL和Redis，具备分库分表、多级缓存架构设计和性能优化能力</li><li>熟练掌握ShardingSphere-JDBC，具备分库分表设计和实现能力，解决海量数据存储和查询性能问题</li><li>熟练掌握RocketMQ/RabbitMQ等消息中间件，能解决消息幂等性、顺序性与事务性问题</li><li>熟练使用Docker/Jenkins实现CI/CD，搭建ELK/Prometheus监控体系和SkyWalking分布式链路追踪</li><li>熟练使用AI辅助编程工具(Cursor/Augment等)，具备代码审查和重构能力，开发效率提升30%以上，同时保证代码质量和安全性</li></ol></div><div class="resume-module"><h2>工作经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>北京承启通科技有限公司福州分公司</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2021.03 - 至今）</strong></p></div></div><ul><li>作为核心开发, 负责企业级通信平台的核心业务开发，参与AI质检、呼叫中心、号码隐藏平台等大型项目的设计与实现</li></ul><div class="flex-layout"><div class="flex-layout-item"><p><strong>南威软件</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2019.06 - 2021.03）</strong></p></div></div><ul><li>作为核心开发, 负责公安数据服务平台的设计与实现</li></ul></div><div class="resume-module"><h2>项目经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>智能语音质检平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2024.03 - 2024.10）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级智能语音质检系统，基于ASR语音识别和NLP自然语言处理技术，实现通话内容的自动转写与智能质检。系统支持质检规则可视化配置、多维度评分、质检报告生成等功能，日均处理通话录音<strong>100万+</strong>，质检准确率达95%以上。</p><p><strong>主要内容</strong>：</p><ol><li>负责系统核心架构设计与开发，实现与ASR/NLP平台的接口集成，构建高效的语音转文本处理流程</li><div white-space="true" style="height: 90px;"></div><li>设计基于RocketMQ的异步处理流水线，实现录音文件上传、转写结果接收、质检规则应用的全流程自动化</li><li>开发可视化质检规则配置平台，支持企业自定义质检标准与评分体系</li><li>实现质检结果回调通知机制，确保质检结果及时反馈给业务系统</li></ol><p><strong>项目难点</strong>：</p><ol><li>设计高性能文本处理架构，支持每日百万级对话文本的接收、解析、存储与分析，确保系统在高并发场景下的稳定性与响应速度</li><li>基于规则引擎设计可配置的质检评分系统，支持关键词识别、禁用语检测、话术规范检查、情感分析等多维度规则组合，实现复杂业务逻辑的灵活配置</li></ol><p><strong>项目亮点</strong>：</p><ol><li>自研轻量级规则引擎，支持30+种质检规则类型和复杂条件组合，如"问候语+禁用语+业务话术+情绪检测"的多维度评分，准确率达95%以上</li><li>设计基于RocketMQ的消息驱动架构，实现ASR结果接收→质检任务创建→规则应用→结果通知的完整流程，系统吞吐量提升300%，支持每日100万+通话的实时质检</li><li>开发智能回调通知机制，结合RocketMQ实现可靠消息投递，支持多种通知策略和失败重试机制，通知成功率达99.99%</li><li>设计离线+实时双模式质检架构，支持HTTP/SFTP多种录音接入方式，通过MinIO对象存储实现录音文件和ASR结果的分离存储，数据库仅存储URL引用，存储成本降低70%，查询性能提升5倍</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>呼叫中心+云总机平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2023.07 - 2023.12）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Netty</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级呼叫中心与云总机系统，整合电话、短信、在线客服等多渠道通信能力，为企业提供统一的客户沟通平台。系统支持智能IVR、呼叫排队、通话录音、实时监控、数据分析等功能，日均处理通话量<strong>100万+</strong>。</p><p><strong>主要内容</strong>：</p><ol><li>负责呼叫路由、呼入排队分配、坐席通话状态管理等核心业务模块的架构设计与开发，独立完成路由引擎和状态管理系统，协同团队完成整体系统集成</li><li>设计并实现基于RocketMQ的呼叫事件处理系统，保障高并发场景下的系统稳定性</li><li>设计并实现高并发通话处理引擎，支持多租户、多坐席并发通话管理</li></ol><p><strong>项目难点</strong>：</p><ol><li>基于责任链+策略模式设计可插拔路由引擎，实现呼入排队规则的动态配置与热更新，支持"VIP优先→技能匹配→等待时长"等复杂业务规则组合，路由决策时间控制在100ms内，坐席分配准确率达99.9%</li><li>基于RocketMQ顺序消息构建呼叫事件处理系统，实现坐席发起呼叫后的桥接、三方通话、转接、咨询等复杂业务流程的事件驱动处理，保障呼叫状态一致性和实时通知，事件处理延迟&lt;100ms</li></ol><p><strong>项目亮点</strong>：</p><ol><li>基于Netty+WebSocket构建坐席实时状态管理系统，支持5000+坐席的通话中、响铃、空闲、小休、忙碌等多种状态毫秒级同步，状态变更延迟&lt;50ms</li><div white-space="true" style="height: 81px;"></div><li>基于责任链模式实现可视化呼叫路由规则配置平台，支持企业自定义多级路由策略组合，如"技能组筛选→VIP优先→等待时间最长"的复合规则，实现动态规则热更新，平均等待时间降低45%，客户满意度提升30%</li><li>基于Redisson分布式锁解决高并发场景下的坐席资源竞争和呼叫分配冲突问题，资源冲突率从5%降至0.01%，呼叫分配准确率达到99.99%</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>号码隐藏保护平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2021.03 - 2021.11）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RabbitMQ</code></p><p><strong>项目简介</strong>：企业级号码隐藏保护平台，为电商、外卖、快递、网约车等行业提供通话号码保护服务。系统支持AXB、AXE、AXYB等多种号码绑定模式，实现用户隐私保护与通话安全，日均处理绑定请求<strong>1000万+</strong>，支持并发通话5万路。</p><p><strong>主要内容</strong>：</p><ol><li>设计基于Redis的高性能号码池管理系统，实现号码资源的智能分配与回收</li><li>设计并实现多种号码绑定模式：AXB（双向保护）、AXE（单向保护, 分机号）、AXYB（多方保护）等业务场景</li><li>开发号码池动态分配算法，支持按地区、运营商、号段等维度智能分配中间号码</li><li>使用SpringBoot开发RESTful API，为客户提供号码绑定、解绑、查询等接口服务</li></ol><p><strong>项目难点</strong>：</p><ol><li>面对每秒数千次绑定请求，如何保证号码资源不重复分配且数据强一致性，采用Redis分布式锁解决并发竞争问题，绑定成功率达99.99%</li><li>实现多种绑定模式（AXB/AXE/AXYB）的数据模型设计与业务逻辑，支持绑定关系的动态调整、号码过期回收管理与冲突处理</li></ol><p><strong>项目亮点</strong>：</p><ol><li>开发基于RabbitMQ的绑定关系统，通过死信队列+延迟队列实现绑定关系的定时过期与X号码自动回收，号码复用率提高60%</li><li>通过Redis分布式缓存和MySQL分库分表，绑定关系查询响应时间从200ms优化至20ms，支撑日均500万+绑定查询</li><li>设计并实现基于HMAC-SHA256的接口签名验证体系，通过appKey+appSecret+timestamp+nonce的组合生成签名，有效防止接口被恶意调用和重放攻击，接口安全性提升99%</li></ol></div><div class="resume-module"><h2>自我评价</h2><blockquote> 6年Java后端开发经验，专注企业级高并发系统架构设计与性能优化。深度掌握SpringCloud微服务生态，主导开发多个日均百万级处理量的核心业务系统，通过分布式架构设计和性能调优，系统整体性能提升200%以上。在微服务架构、消息驱动设计、分布式系统优化等方面具有丰富的实战经验和解决方案。</blockquote></div><div class="resume-module"><h2>致谢</h2><blockquote> 感谢您阅读我的简历，期待与您一起共事。</blockquote></div></div></div></div><div class="jufe-wrapper-page"><div class="jufe-wrapper-page-item" style="height: 1123px;"><div data-v-8e77df9e="" class="markdown-transform-html jufe reference-dom" style="position: absolute; top: -1123px; left: 0px;"><h1>林石墙 - Java开发工程师</h1><div class="flex-layout"><div class="flex-layout-item"><p><i class="iconfont icon-user"></i><strong>姓名</strong>: 林石墙</p><p><i class="iconfont icon-phone"></i><strong>电话号码</strong>：13023828639</p></div><div class="flex-layout-item"><p><i class="iconfont icon-product"></i> <strong>性别</strong>: 男</p><p><i class="iconfont icon-email"></i><strong>邮箱</strong>：<EMAIL></p></div><div class="flex-layout-item"><p><i class="iconfont icon-work"></i><strong>工作经验</strong>：6 年</p></div></div><div class="resume-module"><h2>教育背景</h2><div class="flex-layout"><div class="flex-layout-item"><p>福建理工大学</p></div><div class="flex-layout-item"><p>网络工程</p></div><div class="flex-layout-item"><p><strong>2015.09 - 2019.06</strong></p></div></div></div><div class="resume-module"><h2>专业技能</h2><ol><li>熟练掌握Java核心技术及并发编程，深入理解JVM内存模型与GC机制，具备线上问题排查能力</li><li>熟练应用设计模式(模板方法/责任链/策略/观察者等)解决复杂业务问题，具备系统架构设计能力</li><li>熟练掌握Spring生态(SpringBoot/SpringCloud)和MyBatis Plus，对框架核心原理有深入研究</li><li>熟练掌握SpringCloud微服务生态(Nacos/OpenFeign/Sentinel/Gateway)，具备微服务架构设计经验</li><li>熟练掌握MySQL和Redis，具备分库分表、多级缓存架构设计和性能优化能力</li><li>熟练掌握ShardingSphere-JDBC，具备分库分表设计和实现能力，解决海量数据存储和查询性能问题</li><li>熟练掌握RocketMQ/RabbitMQ等消息中间件，能解决消息幂等性、顺序性与事务性问题</li><li>熟练使用Docker/Jenkins实现CI/CD，搭建ELK/Prometheus监控体系和SkyWalking分布式链路追踪</li><li>熟练使用AI辅助编程工具(Cursor/Augment等)，具备代码审查和重构能力，开发效率提升30%以上，同时保证代码质量和安全性</li></ol></div><div class="resume-module"><h2>工作经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>北京承启通科技有限公司福州分公司</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2021.03 - 至今）</strong></p></div></div><ul><li>作为核心开发, 负责企业级通信平台的核心业务开发，参与AI质检、呼叫中心、号码隐藏平台等大型项目的设计与实现</li></ul><div class="flex-layout"><div class="flex-layout-item"><p><strong>南威软件</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2019.06 - 2021.03）</strong></p></div></div><ul><li>作为核心开发, 负责公安数据服务平台的设计与实现</li></ul></div><div class="resume-module"><h2>项目经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>智能语音质检平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2024.03 - 2024.10）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级智能语音质检系统，基于ASR语音识别和NLP自然语言处理技术，实现通话内容的自动转写与智能质检。系统支持质检规则可视化配置、多维度评分、质检报告生成等功能，日均处理通话录音<strong>100万+</strong>，质检准确率达95%以上。</p><p><strong>主要内容</strong>：</p><ol><li>负责系统核心架构设计与开发，实现与ASR/NLP平台的接口集成，构建高效的语音转文本处理流程</li><div white-space="true" style="height: 90px;"></div><li>设计基于RocketMQ的异步处理流水线，实现录音文件上传、转写结果接收、质检规则应用的全流程自动化</li><li>开发可视化质检规则配置平台，支持企业自定义质检标准与评分体系</li><li>实现质检结果回调通知机制，确保质检结果及时反馈给业务系统</li></ol><p><strong>项目难点</strong>：</p><ol><li>设计高性能文本处理架构，支持每日百万级对话文本的接收、解析、存储与分析，确保系统在高并发场景下的稳定性与响应速度</li><li>基于规则引擎设计可配置的质检评分系统，支持关键词识别、禁用语检测、话术规范检查、情感分析等多维度规则组合，实现复杂业务逻辑的灵活配置</li></ol><p><strong>项目亮点</strong>：</p><ol><li>自研轻量级规则引擎，支持30+种质检规则类型和复杂条件组合，如"问候语+禁用语+业务话术+情绪检测"的多维度评分，准确率达95%以上</li><li>设计基于RocketMQ的消息驱动架构，实现ASR结果接收→质检任务创建→规则应用→结果通知的完整流程，系统吞吐量提升300%，支持每日100万+通话的实时质检</li><li>开发智能回调通知机制，结合RocketMQ实现可靠消息投递，支持多种通知策略和失败重试机制，通知成功率达99.99%</li><li>设计离线+实时双模式质检架构，支持HTTP/SFTP多种录音接入方式，通过MinIO对象存储实现录音文件和ASR结果的分离存储，数据库仅存储URL引用，存储成本降低70%，查询性能提升5倍</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>呼叫中心+云总机平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2023.07 - 2023.12）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Netty</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级呼叫中心与云总机系统，整合电话、短信、在线客服等多渠道通信能力，为企业提供统一的客户沟通平台。系统支持智能IVR、呼叫排队、通话录音、实时监控、数据分析等功能，日均处理通话量<strong>100万+</strong>。</p><p><strong>主要内容</strong>：</p><ol><li>负责呼叫路由、呼入排队分配、坐席通话状态管理等核心业务模块的架构设计与开发，独立完成路由引擎和状态管理系统，协同团队完成整体系统集成</li><li>设计并实现基于RocketMQ的呼叫事件处理系统，保障高并发场景下的系统稳定性</li><li>设计并实现高并发通话处理引擎，支持多租户、多坐席并发通话管理</li></ol><p><strong>项目难点</strong>：</p><ol><li>基于责任链+策略模式设计可插拔路由引擎，实现呼入排队规则的动态配置与热更新，支持"VIP优先→技能匹配→等待时长"等复杂业务规则组合，路由决策时间控制在100ms内，坐席分配准确率达99.9%</li><li>基于RocketMQ顺序消息构建呼叫事件处理系统，实现坐席发起呼叫后的桥接、三方通话、转接、咨询等复杂业务流程的事件驱动处理，保障呼叫状态一致性和实时通知，事件处理延迟&lt;100ms</li></ol><p><strong>项目亮点</strong>：</p><ol><li>基于Netty+WebSocket构建坐席实时状态管理系统，支持5000+坐席的通话中、响铃、空闲、小休、忙碌等多种状态毫秒级同步，状态变更延迟&lt;50ms</li><div white-space="true" style="height: 81px;"></div><li>基于责任链模式实现可视化呼叫路由规则配置平台，支持企业自定义多级路由策略组合，如"技能组筛选→VIP优先→等待时间最长"的复合规则，实现动态规则热更新，平均等待时间降低45%，客户满意度提升30%</li><li>基于Redisson分布式锁解决高并发场景下的坐席资源竞争和呼叫分配冲突问题，资源冲突率从5%降至0.01%，呼叫分配准确率达到99.99%</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>号码隐藏保护平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2021.03 - 2021.11）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RabbitMQ</code></p><p><strong>项目简介</strong>：企业级号码隐藏保护平台，为电商、外卖、快递、网约车等行业提供通话号码保护服务。系统支持AXB、AXE、AXYB等多种号码绑定模式，实现用户隐私保护与通话安全，日均处理绑定请求<strong>1000万+</strong>，支持并发通话5万路。</p><p><strong>主要内容</strong>：</p><ol><li>设计基于Redis的高性能号码池管理系统，实现号码资源的智能分配与回收</li><li>设计并实现多种号码绑定模式：AXB（双向保护）、AXE（单向保护, 分机号）、AXYB（多方保护）等业务场景</li><li>开发号码池动态分配算法，支持按地区、运营商、号段等维度智能分配中间号码</li><li>使用SpringBoot开发RESTful API，为客户提供号码绑定、解绑、查询等接口服务</li></ol><p><strong>项目难点</strong>：</p><ol><li>面对每秒数千次绑定请求，如何保证号码资源不重复分配且数据强一致性，采用Redis分布式锁解决并发竞争问题，绑定成功率达99.99%</li><li>实现多种绑定模式（AXB/AXE/AXYB）的数据模型设计与业务逻辑，支持绑定关系的动态调整、号码过期回收管理与冲突处理</li></ol><p><strong>项目亮点</strong>：</p><ol><li>开发基于RabbitMQ的绑定关系统，通过死信队列+延迟队列实现绑定关系的定时过期与X号码自动回收，号码复用率提高60%</li><li>通过Redis分布式缓存和MySQL分库分表，绑定关系查询响应时间从200ms优化至20ms，支撑日均500万+绑定查询</li><li>设计并实现基于HMAC-SHA256的接口签名验证体系，通过appKey+appSecret+timestamp+nonce的组合生成签名，有效防止接口被恶意调用和重放攻击，接口安全性提升99%</li></ol></div><div class="resume-module"><h2>自我评价</h2><blockquote> 6年Java后端开发经验，专注企业级高并发系统架构设计与性能优化。深度掌握SpringCloud微服务生态，主导开发多个日均百万级处理量的核心业务系统，通过分布式架构设计和性能调优，系统整体性能提升200%以上。在微服务架构、消息驱动设计、分布式系统优化等方面具有丰富的实战经验和解决方案。</blockquote></div><div class="resume-module"><h2>致谢</h2><blockquote> 感谢您阅读我的简历，期待与您一起共事。</blockquote></div></div></div></div><div class="jufe-wrapper-page"><div class="jufe-wrapper-page-item" style="height: 1087px;"><div data-v-8e77df9e="" class="markdown-transform-html jufe reference-dom" style="position: absolute; top: -2246px; left: 0px;"><h1>林石墙 - Java开发工程师</h1><div class="flex-layout"><div class="flex-layout-item"><p><i class="iconfont icon-user"></i><strong>姓名</strong>: 林石墙</p><p><i class="iconfont icon-phone"></i><strong>电话号码</strong>：13023828639</p></div><div class="flex-layout-item"><p><i class="iconfont icon-product"></i> <strong>性别</strong>: 男</p><p><i class="iconfont icon-email"></i><strong>邮箱</strong>：<EMAIL></p></div><div class="flex-layout-item"><p><i class="iconfont icon-work"></i><strong>工作经验</strong>：6 年</p></div></div><div class="resume-module"><h2>教育背景</h2><div class="flex-layout"><div class="flex-layout-item"><p>福建理工大学</p></div><div class="flex-layout-item"><p>网络工程</p></div><div class="flex-layout-item"><p><strong>2015.09 - 2019.06</strong></p></div></div></div><div class="resume-module"><h2>专业技能</h2><ol><li>熟练掌握Java核心技术及并发编程，深入理解JVM内存模型与GC机制，具备线上问题排查能力</li><li>熟练应用设计模式(模板方法/责任链/策略/观察者等)解决复杂业务问题，具备系统架构设计能力</li><li>熟练掌握Spring生态(SpringBoot/SpringCloud)和MyBatis Plus，对框架核心原理有深入研究</li><li>熟练掌握SpringCloud微服务生态(Nacos/OpenFeign/Sentinel/Gateway)，具备微服务架构设计经验</li><li>熟练掌握MySQL和Redis，具备分库分表、多级缓存架构设计和性能优化能力</li><li>熟练掌握ShardingSphere-JDBC，具备分库分表设计和实现能力，解决海量数据存储和查询性能问题</li><li>熟练掌握RocketMQ/RabbitMQ等消息中间件，能解决消息幂等性、顺序性与事务性问题</li><li>熟练使用Docker/Jenkins实现CI/CD，搭建ELK/Prometheus监控体系和SkyWalking分布式链路追踪</li><li>熟练使用AI辅助编程工具(Cursor/Augment等)，具备代码审查和重构能力，开发效率提升30%以上，同时保证代码质量和安全性</li></ol></div><div class="resume-module"><h2>工作经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>北京承启通科技有限公司福州分公司</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2021.03 - 至今）</strong></p></div></div><ul><li>作为核心开发, 负责企业级通信平台的核心业务开发，参与AI质检、呼叫中心、号码隐藏平台等大型项目的设计与实现</li></ul><div class="flex-layout"><div class="flex-layout-item"><p><strong>南威软件</strong></p></div><div class="flex-layout-item"><p><strong>Java开发工程师（2019.06 - 2021.03）</strong></p></div></div><ul><li>作为核心开发, 负责公安数据服务平台的设计与实现</li></ul></div><div class="resume-module"><h2>项目经历</h2><div class="flex-layout"><div class="flex-layout-item"><p><strong>智能语音质检平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2024.03 - 2024.10）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级智能语音质检系统，基于ASR语音识别和NLP自然语言处理技术，实现通话内容的自动转写与智能质检。系统支持质检规则可视化配置、多维度评分、质检报告生成等功能，日均处理通话录音<strong>100万+</strong>，质检准确率达95%以上。</p><p><strong>主要内容</strong>：</p><ol><li>负责系统核心架构设计与开发，实现与ASR/NLP平台的接口集成，构建高效的语音转文本处理流程</li><div white-space="true" style="height: 90px;"></div><li>设计基于RocketMQ的异步处理流水线，实现录音文件上传、转写结果接收、质检规则应用的全流程自动化</li><li>开发可视化质检规则配置平台，支持企业自定义质检标准与评分体系</li><li>实现质检结果回调通知机制，确保质检结果及时反馈给业务系统</li></ol><p><strong>项目难点</strong>：</p><ol><li>设计高性能文本处理架构，支持每日百万级对话文本的接收、解析、存储与分析，确保系统在高并发场景下的稳定性与响应速度</li><li>基于规则引擎设计可配置的质检评分系统，支持关键词识别、禁用语检测、话术规范检查、情感分析等多维度规则组合，实现复杂业务逻辑的灵活配置</li></ol><p><strong>项目亮点</strong>：</p><ol><li>自研轻量级规则引擎，支持30+种质检规则类型和复杂条件组合，如"问候语+禁用语+业务话术+情绪检测"的多维度评分，准确率达95%以上</li><li>设计基于RocketMQ的消息驱动架构，实现ASR结果接收→质检任务创建→规则应用→结果通知的完整流程，系统吞吐量提升300%，支持每日100万+通话的实时质检</li><li>开发智能回调通知机制，结合RocketMQ实现可靠消息投递，支持多种通知策略和失败重试机制，通知成功率达99.99%</li><li>设计离线+实时双模式质检架构，支持HTTP/SFTP多种录音接入方式，通过MinIO对象存储实现录音文件和ASR结果的分离存储，数据库仅存储URL引用，存储成本降低70%，查询性能提升5倍</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>呼叫中心+云总机平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2023.07 - 2023.12）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RocketMQ</code> <code class="single-code">Netty</code> <code class="single-code">Docker</code></p><p><strong>项目简介</strong>：企业级呼叫中心与云总机系统，整合电话、短信、在线客服等多渠道通信能力，为企业提供统一的客户沟通平台。系统支持智能IVR、呼叫排队、通话录音、实时监控、数据分析等功能，日均处理通话量<strong>100万+</strong>。</p><p><strong>主要内容</strong>：</p><ol><li>负责呼叫路由、呼入排队分配、坐席通话状态管理等核心业务模块的架构设计与开发，独立完成路由引擎和状态管理系统，协同团队完成整体系统集成</li><li>设计并实现基于RocketMQ的呼叫事件处理系统，保障高并发场景下的系统稳定性</li><li>设计并实现高并发通话处理引擎，支持多租户、多坐席并发通话管理</li></ol><p><strong>项目难点</strong>：</p><ol><li>基于责任链+策略模式设计可插拔路由引擎，实现呼入排队规则的动态配置与热更新，支持"VIP优先→技能匹配→等待时长"等复杂业务规则组合，路由决策时间控制在100ms内，坐席分配准确率达99.9%</li><li>基于RocketMQ顺序消息构建呼叫事件处理系统，实现坐席发起呼叫后的桥接、三方通话、转接、咨询等复杂业务流程的事件驱动处理，保障呼叫状态一致性和实时通知，事件处理延迟&lt;100ms</li></ol><p><strong>项目亮点</strong>：</p><ol><li>基于Netty+WebSocket构建坐席实时状态管理系统，支持5000+坐席的通话中、响铃、空闲、小休、忙碌等多种状态毫秒级同步，状态变更延迟&lt;50ms</li><div white-space="true" style="height: 81px;"></div><li>基于责任链模式实现可视化呼叫路由规则配置平台，支持企业自定义多级路由策略组合，如"技能组筛选→VIP优先→等待时间最长"的复合规则，实现动态规则热更新，平均等待时间降低45%，客户满意度提升30%</li><li>基于Redisson分布式锁解决高并发场景下的坐席资源竞争和呼叫分配冲突问题，资源冲突率从5%降至0.01%，呼叫分配准确率达到99.99%</li></ol><div class="flex-layout"><div class="flex-layout-item"><p><strong>号码隐藏保护平台</strong></p></div><div class="flex-layout-item"><p><strong>Java后端开发（2021.03 - 2021.11）</strong></p></div></div><p><strong>技术栈</strong>：<code class="single-code">SpringCloud</code> <code class="single-code">SpringBoot</code> <code class="single-code">Redis</code> <code class="single-code">MySQL</code> <code class="single-code">RabbitMQ</code></p><p><strong>项目简介</strong>：企业级号码隐藏保护平台，为电商、外卖、快递、网约车等行业提供通话号码保护服务。系统支持AXB、AXE、AXYB等多种号码绑定模式，实现用户隐私保护与通话安全，日均处理绑定请求<strong>1000万+</strong>，支持并发通话5万路。</p><p><strong>主要内容</strong>：</p><ol><li>设计基于Redis的高性能号码池管理系统，实现号码资源的智能分配与回收</li><li>设计并实现多种号码绑定模式：AXB（双向保护）、AXE（单向保护, 分机号）、AXYB（多方保护）等业务场景</li><li>开发号码池动态分配算法，支持按地区、运营商、号段等维度智能分配中间号码</li><li>使用SpringBoot开发RESTful API，为客户提供号码绑定、解绑、查询等接口服务</li></ol><p><strong>项目难点</strong>：</p><ol><li>面对每秒数千次绑定请求，如何保证号码资源不重复分配且数据强一致性，采用Redis分布式锁解决并发竞争问题，绑定成功率达99.99%</li><li>实现多种绑定模式（AXB/AXE/AXYB）的数据模型设计与业务逻辑，支持绑定关系的动态调整、号码过期回收管理与冲突处理</li></ol><p><strong>项目亮点</strong>：</p><ol><li>开发基于RabbitMQ的绑定关系统，通过死信队列+延迟队列实现绑定关系的定时过期与X号码自动回收，号码复用率提高60%</li><li>通过Redis分布式缓存和MySQL分库分表，绑定关系查询响应时间从200ms优化至20ms，支撑日均500万+绑定查询</li><li>设计并实现基于HMAC-SHA256的接口签名验证体系，通过appKey+appSecret+timestamp+nonce的组合生成签名，有效防止接口被恶意调用和重放攻击，接口安全性提升99%</li></ol></div><div class="resume-module"><h2>自我评价</h2><blockquote> 6年Java后端开发经验，专注企业级高并发系统架构设计与性能优化。深度掌握SpringCloud微服务生态，主导开发多个日均百万级处理量的核心业务系统，通过分布式架构设计和性能调优，系统整体性能提升200%以上。在微服务架构、消息驱动设计、分布式系统优化等方面具有丰富的实战经验和解决方案。</blockquote></div><div class="resume-module"><h2>致谢</h2><blockquote> 感谢您阅读我的简历，期待与您一起共事。</blockquote></div></div></div></div></div></div><i data-v-37f2732b="" data-aos="fade-in" data-aos-duration="800" data-aos-offset="50" class="iconfont icon-export hover pointer standby-export el-tooltip__trigger aos-init aos-animate"></i></div></div><!----></div>
    
  

<div id="el-popper-container-4830"><!--v-if--><!--v-if--><div class="el-popper is-pure is-light el-dropdown__popper" tabindex="-1" aria-hidden="true" style="z-index: 2129; position: absolute; inset: 71.8182px auto auto 470.909px; display: none;" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom"><div class="el-scrollbar"><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default"><div class="el-scrollbar__view el-dropdown__list" style=""><ul data-v-da2d6782="" class="el-dropdown-menu" tabindex="-1" role="menu" aria-labelledby="el-id-4830-225" id="el-id-4830-226" style="outline: none;"><!--v-if--><li data-el-collection-item="" aria-disabled="false" class="el-dropdown-menu__item" tabindex="-1" role="menuitem"><!--v-if--><label data-v-da2d6782="" for="import_md"> 导入MD <input data-v-da2d6782="" accept=".md" id="import_md" type="file"></label></li><!--v-if--><li data-el-collection-item="" aria-disabled="false" class="el-dropdown-menu__item" tabindex="-1" role="menuitem"><!--v-if--><span data-v-da2d6782="">导出MD</span></li><!--v-if--><li data-el-collection-item="" aria-disabled="false" class="el-dropdown-menu__item" tabindex="-1" role="menuitem"><!--v-if--><span data-v-da2d6782="">导出图片</span></li></ul></div></div><div class="el-scrollbar__bar is-horizontal" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span></div><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><div class="el-popper is-dark" tabindex="-1" aria-hidden="true" role="tooltip" id="el-id-4830-245" style="z-index: 2130; position: absolute; inset: auto auto 0px 0px; display: none; transform: translate3d(1795.91px, -1051.82px, 0px);" data-popper-reference-hidden="false" data-popper-escaped="true" data-popper-placement="top"><span>100</span><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px; transform: translate3d(0px, 0px, 0px);"></span></div><!--v-if--><!--v-if--><!--v-if--><div class="el-popper is-light el-color-picker__panel el-color-dropdown" tabindex="-1" aria-hidden="true" style="z-index: 2131; position: absolute; inset: 145.455px auto auto 1222.73px; display: none;" role="tooltip" id="el-id-4830-249" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom"><div><div class="el-color-dropdown__main-wrapper"><div class="el-color-hue-slider is-vertical hue-slider"><div class="el-color-hue-slider__bar"></div><div class="el-color-hue-slider__thumb" style="left: 0px; top: 0px;"></div></div><div class="el-color-svpanel" style="background-color: rgb(255, 0, 0);"><div class="el-color-svpanel__white"></div><div class="el-color-svpanel__black"></div><div class="el-color-svpanel__cursor" style="top: 0px; left: 0px;"><div></div></div></div></div><!--v-if--><!--v-if--><div class="el-color-dropdown__btns"><span class="el-color-dropdown__value"><div class="el-input el-input--small"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" id="el-id-4830-259"><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--></div></span><button class="el-button el-button--small is-text el-color-dropdown__link-btn" aria-disabled="false" type="button"><!--v-if--><span class="">Clear</span></button><button class="el-button el-button--small is-plain el-color-dropdown__btn" aria-disabled="false" type="button"><!--v-if--><span class="">OK</span></button></div></div><!--v-if--></div><div class="el-popper is-light el-color-picker__panel el-color-dropdown" tabindex="-1" aria-hidden="true" style="z-index: 2132; position: absolute; inset: 145.455px auto auto 1268.64px; display: none;" role="tooltip" id="el-id-4830-250" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom"><div><div class="el-color-dropdown__main-wrapper"><div class="el-color-hue-slider is-vertical hue-slider"><div class="el-color-hue-slider__bar"></div><div class="el-color-hue-slider__thumb" style="left: 0px; top: 0px;"></div></div><div class="el-color-svpanel" style="background-color: rgb(255, 0, 0);"><div class="el-color-svpanel__white"></div><div class="el-color-svpanel__black"></div><div class="el-color-svpanel__cursor" style="top: 0px; left: 0px;"><div></div></div></div></div><!--v-if--><!--v-if--><div class="el-color-dropdown__btns"><span class="el-color-dropdown__value"><div class="el-input el-input--small"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" id="el-id-4830-261"><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--></div></span><button class="el-button el-button--small is-text el-color-dropdown__link-btn" aria-disabled="false" type="button"><!--v-if--><span class="">Clear</span></button><button class="el-button el-button--small is-plain el-color-dropdown__btn" aria-disabled="false" type="button"><!--v-if--><span class="">OK</span></button></div></div><!--v-if--></div><!--v-if--><!--v-if--><!--v-if--><div class="el-popper is-pure is-light el-select__popper" tabindex="-1" aria-hidden="true" style="z-index: 2133; position: absolute; inset: 157.273px auto auto 1507.27px; display: none;" role="tooltip" id="el-id-4830-255" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom"><div class="el-select-dropdown" style="min-width: 161px;"><div class="el-scrollbar" style=""><div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default"><ul class="el-scrollbar__view el-select-dropdown__list" style=""><!--v-if--><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>10px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>11px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>12px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>13px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>14px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>15px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>16px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>17px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>18px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>19px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>20px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>21px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>22px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>23px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>24px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>25px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item selected"><span>26px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>27px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>28px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>29px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>30px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>31px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>32px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>33px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>34px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>35px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>36px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>37px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>38px</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>39px</span></li></ul></div><div class="el-scrollbar__bar is-horizontal" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><!--v-if--></div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span></div><!--v-if--><div class="el-popper is-pure is-light el-select__popper" tabindex="-1" aria-hidden="true" style="z-index: 2134; position: absolute; inset: 157.273px auto auto 1682.27px; display: none;" role="tooltip" id="el-id-4830-257" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom"><div class="el-select-dropdown" style="min-width: 161px;"><div class="el-scrollbar" style=""><div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default"><ul class="el-scrollbar__view el-select-dropdown__list" style=""><!--v-if--><li data-v-39bf65b5="" class="el-select-dropdown__item selected"><span>Noto Serif SC</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>Noto Sans SC</span></li><li data-v-39bf65b5="" class="el-select-dropdown__item"><span>Nunito(英文)</span></li></ul></div><div class="el-scrollbar__bar is-horizontal" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><!--v-if--></div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span></div><!--v-if--><!--v-if--></div><a href="http://localhost:8680/#" id="__TIMER_INJECTION_FLAG__dkdhhcbjijekmneelocdllcldcpmekmm" style="visibility: hidden;"></a></body><div id="immersive-translate-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 2px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --background-light-green: #f5f7f9;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  --float-ball-more-button-border-color: #f6f6f6;
  --float-ball-more-button-background-color: #ffffff;
  --float-ball-more-button-svg-color: #6c6f73;
  color-scheme: light;
  --service-bg-hover: #f7faff;
  --service-bg: #fafbfb;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --float-ball-more-button-background-color: #ffffff;
    --background-light-green: #141e26;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
    color-scheme: dark;
    --service-bg-hover: #22292f;
    --service-bg: rgba(0, 0, 0, 0.1);
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --float-ball-more-button-background-color: #ffffff;
  --background-light-green: #141e26;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  color-scheme: dark;
  --service-bg: rgba(0, 0, 0, 0.1);
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 2px !important;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 12px !important;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 24px !important;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}
/* 
@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
} */
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 1280px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
  --switch-checked-background-color: var(--primary);
}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 13px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #f9fbfc;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.2);
  --service-select-border-color: #fafafa;
  --service-select-selected-background-color: #f3f5f6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
    --service-select-border-color: #2c2c2c;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
  --service-select-border-color: #2c2c2c;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

#mount {
  min-width: 268px;
}

.main-button {
  font-size: 15px;
  vertical-align: middle;
  border-radius: 12px;
  padding: unset;
  height: 44px;
  line-height: 44px;
}

.pt-4 {
  padding-top: 16px;
}

.p-2 {
  padding: 8px;
}

.pl-5 {
  padding-left: 48px;
}

.p-0 {
  padding: 0;
}

.pl-2 {
  padding-left: 8px;
}

.pl-4 {
  padding-left: 24px;
}

.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pr-5 {
  padding-right: 48px;
}

.text-sm {
  font-size: 13px;
}

.text-base {
  font-size: 16px;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.justify-between {
  justify-content: space-between;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 24px;
}

.ml-3 {
  margin-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.pl-3 {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.p-3 {
  padding: 12px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.px-6 {
  padding-left: 18px;
  padding-right: 18px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.pt-6 {
  padding-top: 20px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-30 {
  max-height: 120px;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-2 {
  flex: 2;
}

.items-center {
  align-items: center;
}

.max-content {
  width: max-content;
}

.justify-center {
  justify-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {
  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

select.min-select-secondary {
  color: var(--color);
}

select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}

select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.popup-footer {
  background-color: var(--popup-footer-background-color);
  height: 40px;
}

.text-right {
  text-align: right;
}

.clickable {
  cursor: pointer;
}

.close {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}

.text-label {
  color: #666;
}

.display-none {
  display: none;
}

/* dark use #18232c */
@media (prefers-color-scheme: dark) {
  .text-label {
    color: #9ca3af;
  }
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --text-decoration: none !important;
  background-color: transparent !important;
}

.language-select-container {
  position: relative;
  width: 100%;
  background-color: var(--popup-item-background-color);
  height: 55px;
  border-radius: 12px;
}

select.language-select {
  color: var(--text-black-2);
  font-size: 14px;
  padding: 8px 24px 24px 16px;
  position: absolute;
  border-radius: 12px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

select.text-gray-6 {
  color: var(--text-gray-6);
}

.language-select-container label {
  position: absolute;
  bottom: 10px;
  left: 16px;
  font-size: 12px;
  color: var(--text-gray-9);
  line-height: 12px;
  margin: 0;
}

.translation-service-container {
  background-color: var(--popup-item-background-color);
  border-radius: 12px;
}

.min-select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  background-color: var(--popup-item-background-color);
  padding-left: 16px;
}

.min-select-container:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.min-select-container:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.min-select-container:only-child {
  border-radius: 10px;
}

.translate-mode {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: var(--popup-item-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.translate-mode svg {
  fill: var(--text-gray-2);
}

.widgets-container {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  gap: 9px;
}

.widget-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--popup-item-background-color);
  font-size: 12px;
  min-height: 59px;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
  padding: 8px 4px;
  text-align: center;
}

.widget-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 100%;
  margin-bottom: 4px;
}

.widget-title-container {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 24px;
  width: 100%;
  padding-bottom: 4px;
}

.widget-icon {
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
}

.widget-title {
  color: var(--text-gray-6);
  font-size: 12px;
  text-align: center;
  width: 100%;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 2px 2px;
}

.widget-item svg {
  fill: var(--text-gray-2);
}

.share-button-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 3px 0 8px;
}

.share-button-container svg {
  fill: var(--text-gray-9);
}

.min-select-container:hover,
.language-select-container:hover,
.widget-item:hover,
.translate-mode:hover {
  background-color: var(--popup-item-hover-background-color);
}

.main-button:hover {
  background-color: #f5508f;
}

.share-button-container:hover {
  background-color: var(--popup-item-background-color);
  border-radius: 6px;
}

.error-boundary {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  display: flex;
  padding: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  word-break: break-all;
  margin: 12px;
  border-radius: 12px;
  flex-direction: column;
}

.upgrade-pro {
  border-radius: 11px;
  background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
  padding: 2px 8px;
  transform: scale(0.85);
}

.upgrade-pro span {
  background: linear-gradient(180deg, #ffeab4 17.65%, #f8c235 85.29%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  margin-left: 4px;
}

.upgrade-pro svg {
  margin-top: -2px;
}

.upgrade-pro:hover {
  background: linear-gradient(57deg, #3d3d3d 19.8%, #949494 82.2%);
}

.border-bottom-radius-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.trial-pro-container {
  border-radius: 0px 0px 12px 12px;
  background: var(--popup-trial-pro-background-color);
  display: flex;
  align-items: center;
  height: 44px;
  padding-left: 16px;
  padding-right: 12px;
  font-size: 12px;
}

.trial-pro-container label {
  line-height: 13px;
  color: var(--text-black-2);
}

.trial-pro-container img {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.upgrade-pro-discount-act {
  height: 25px;
  display: flex;
  padding: 0 4px;
  align-items: center;
  border-radius: 15px;
  background: linear-gradient(
    90deg,
    #cefbfa 11.33%,
    #d7f56f 63.75%,
    #fccd5e 100%
  );
  transform: scale(0.9);
  box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.upgrade-pro-discount-act span {
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  color: #222222;
}

.upgrade-pro-discount-act:hover {
  text-decoration: unset;
  background: linear-gradient(
    90deg,
    #e2fffe 11.33%,
    #e6ff91 63.75%,
    #ffdf93 100%
  );
}

.custom-select-container {
  width: 200px;
  position: relative;
  flex: 1;
}

#translation-service-select {
  padding-right: 12px;
  padding-left: 6px;
}

.custom-select-content {
  border-radius: 12px;
  background: var(--popup-content-background-color);
  box-shadow: var(--service-select-content-shadow);
  border: 1px solid var(--service-select-border-color);
  padding: 4px 5px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  overflow-y: auto;
}

.custom-select-item.default {
  width: 100%;
  padding: 0;
}

.custom-select-item {
  font-size: 13px;
  padding: 5px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-black-2);
  width: auto;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}

.custom-select-content .custom-select-item.selected,
.custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin: 0 3px;
  width: 20px;
}

.custom-select-group-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-gray-9);
  padding: 6px 8px 4px;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.more-container {
  position: relative;
}

.new-menu-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #ef3434;
  border-radius: 50%;
  right: 18px;
  top: 4px;
}

html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  z-index: 2147483647;
  top: 335px;
  width: fit-content;
  display: flex;
  flex-direction: column;
  display: none;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 54px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-fb-logo-img-big-bg {
  width: 28px;
  height: 28px;
  margin: 0;
  padding: 4px;
  background-color: #ed6d8f;
  border-radius: 50%;
  margin: 0 5px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-button {
  pointer-events: all;
  cursor: pointer;
  position: absolute;
  margin-top: -10px;
}

.imt-fb-close-content {
  padding: 22px;
  width: 320px;
  pointer-events: all;
}

.imt-fb-close-title {
  font-weight: 500;
  color: var(--h2-color);
}

.imt-fb-close-radio-content {
  background-color: var(--background-light-green);
  padding: 8px 20px;
}

.imt-fb-radio-sel,
.imt-fb-radio-nor {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  flex-shrink: 0;
}

.imt-fb-radio-sel {
  border: 2px solid var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-radio-sel div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: var(--primary);
}

.imt-fb-radio-nor {
  border: 2px solid #d3d4d6;
}

.imt-fb-primary-btn {
  background-color: var(--primary);
  width: 72px;
  height: 32px;
  color: white;
  border-radius: 8px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-default-btn {
  border: 1px solid var(--primary);
  width: 72px;
  height: 32px;
  border-radius: 8px;
  color: var(--primary);
  line-height: 32px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-guide-container {
  width: 312px;
  transform: translateY(-50%);
}

.imt-fb-guide-bg {
  position: absolute;
  left: 30px;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  height: 100%;
  width: 90%;
}

.imt-fb-guide-bg.left {
  transform: scaleX(-1);
}

.imt-fb-guide-content {
  margin: 16px -30px 80px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imt-fb-guide-content.left {
  margin: 16px 21px 60px 32px;
}

.imt-fb-guide-img {
  width: 220px;
  height: 112px;
}

.imt-fb-guide-message {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  white-space: pre-wrap;
  text-align: center;
  font-weight: 700;
  margin-bottom: 20px;
}

.imt-fb-guide-button {
  margin-top: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 20px;
  width: unset;
}

.imt-fb-more-buttons {
  box-shadow: 0px 2px 10px 0px #00000014;
  border: none;
  background: var(--float-ball-more-button-background-color);
  width: 36px;
  display: flex;
  flex-direction: column;
  border-radius: 18px;
  margin-top: 0px;
  padding: 7px 0 7px 0;
}

.imt-fb-more-buttons > div {
  margin: auto;
}

.imt-fb-side {
  border-radius: 50%;
  margin-bottom: 10px;
  cursor: pointer;
  pointer-events: all;
  position: relative;
}

.imt-fb-new-badge {
  width: 26px;
  height: 14px;
  padding: 3px;
  background-color: #F53F3F;
  border-radius: 4px;
  position: absolute;
  top: -5px;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-side * {
  pointer-events: all;
}

.imt-fb-more-button {
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
/* Sheet.css */
.immersive-translate-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Start off screen */
  left: 0;
  right: 0;
  background-color: white;
  transition: transform 0.3s ease-out;
  /* Smooth slide transition */
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it's above other content */
  bottom: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.immersive-translate-sheet.visible {
  transform: translateY(0);
}

.immersive-translate-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.immersive-translate-sheet-backdrop.visible {
  opacity: 1;
}

.popup-container-sheet {
  max-width: 100vw;
  width: 100vw;
}

.imt-no-events svg * {
  pointer-events: none !important;
}

.imt-manga-button {
  width: 36px;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: all;
  margin: 0 0 10px 0;
  background-color: var(--float-ball-more-button-background-color);
  border-radius: 18px;
  filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
  opacity: 0.5;
  right: 8px;
  padding: 10px 0 4px 0;
}

.imt-manga-feedback {
  cursor: pointer;
  margin-bottom: 10px;
}

.imt-fb-feedback {
  cursor: pointer;
  margin-top: 10px;
}

.imt-fb-upgrade-button {
  cursor: pointer;
  margin-top: 10px;
}

.imt-manga-button:hover {
  opacity: 1;
}

.imt-manga-translated {
  position: absolute;
  left: 24px;
  top: 20px;
}

.imt-float-ball-loading {
  animation: imt-loading-animation 0.6s infinite linear !important;
}

.imt-manga-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  width: 372px;
  transform: translateY(-50%);
}
.imt-manga-guide-content {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 40px 0;
}

.img-manga-guide-button {
  width: fit-content;
  margin: 16px auto;
}

.img-manga-close {
  position: absolute;
  bottom: -200px;
  width: 32px;
  height: 32px;
  left: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

.imt-fb-container.dragging .imt-fb-more-buttons,
.imt-fb-container.dragging .imt-manga-button,
.imt-fb-container.dragging .btn-animate:not(.imt-fb-btn) {
  display: none !important;
}

.imt-fb-container.dragging .imt-fb-btn {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: move !important;
}

.imt-fb-container.dragging .imt-fb-btn div {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.imt-fb-container.dragging .imt-fb-btn.left,
.imt-fb-container.dragging .imt-fb-btn.right {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-btn.left div,
.imt-fb-container.dragging .imt-fb-btn.right div {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-logo-img {
  margin: 0 !important;
  padding: 4px !important;
}

.imt-fb-container.dragging .imt-float-ball-translated {
  right: 2px !important;
  bottom: 2px !important;
}

@-webkit-keyframes imt-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes imt-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
</style><div id="mount" style="display: block;"><div class="imt-fb-container right notranslate " dir="ltr" style="z-index: 2147483637; pointer-events: none; right: 0px; top: 795px; display: flex;"><div class="imt-fb-btn imt-fb-more-button imt-fb-side" style="transform: translateX(-4px); opacity: 0.7;"><svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.60547 12.9228C8.84029 12.9228 9.03755 13.0022 9.19629 13.161C9.3551 13.3198 9.43457 13.5171 9.43457 13.7519V18.5107C9.43457 18.7453 9.35513 18.9426 9.19629 19.1015C9.03755 19.2602 8.84029 19.3398 8.60547 19.3398H3.8457C3.61127 19.3397 3.41464 19.26 3.25586 19.1015C3.09712 18.9426 3.01758 18.7453 3.01758 18.5107V13.7519C3.01758 13.517 3.09712 13.3198 3.25586 13.161C3.41465 13.0023 3.61125 12.9229 3.8457 12.9228H8.60547ZM17.208 12.9228C17.4427 12.9228 17.6399 13.0022 17.7988 13.161C17.9575 13.3198 18.0371 13.5171 18.0371 13.7519V18.5107C18.0371 18.7453 17.9576 18.9426 17.7988 19.1015C17.6399 19.2602 17.4427 19.3398 17.208 19.3398H12.4492C12.2144 19.3398 12.0171 19.2602 11.8584 19.1015C11.6995 18.9426 11.6201 18.7453 11.6201 18.5107V13.7519C11.6201 13.517 11.6995 13.3198 11.8584 13.161C12.0171 13.0022 12.2144 12.9228 12.4492 12.9228H17.208ZM4.39258 17.9648H8.05957V14.2978H4.39258V17.9648ZM12.9951 17.9648H16.6621V14.2978H12.9951V17.9648ZM14.7598 2.92179C14.8641 2.57295 15.3576 2.57295 15.4619 2.92179L15.9561 4.57511C16.1376 5.18219 16.5965 5.66815 17.1924 5.8837L18.7412 6.44327C19.0635 6.56002 19.0633 7.01583 18.7412 7.13273L17.1924 7.69327C16.5966 7.90881 16.1376 8.39389 15.9561 9.00089L15.4619 10.6552C15.3575 11.0038 14.8642 11.0037 14.7598 10.6552L14.2646 9.00089C14.0831 8.39401 13.625 7.90881 13.0293 7.69327L11.4805 7.13273C11.158 7.01598 11.1579 6.55996 11.4805 6.44327L13.0293 5.8837C13.6251 5.66814 14.0831 5.18219 14.2646 4.57511L14.7598 2.92179ZM8.60547 4.32023C8.84029 4.32023 9.03755 4.39977 9.19629 4.55851C9.35496 4.71727 9.43448 4.91396 9.43457 5.14835V9.90812C9.43457 10.1429 9.35518 10.3402 9.19629 10.4989C9.03755 10.6578 8.84029 10.7372 8.60547 10.7372H3.8457C3.61131 10.7371 3.41463 10.6576 3.25586 10.4989C3.09712 10.3402 3.01758 10.1429 3.01758 9.90812V5.14835C3.01767 4.91386 3.09721 4.71731 3.25586 4.55851C3.41466 4.39986 3.61121 4.32032 3.8457 4.32023H8.60547ZM4.39258 9.36222H8.05957V5.69523H4.39258V9.36222Z" fill="#666666"></path></svg></div><div hidden="" class="imt-manga-button imt-no-events btn-animate " id="manga-button" style="transform: translateX(2px);"><div class=" " style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><svg class="imt-manga-feedback" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.0003 14.2749C11.213 14.2749 11.3895 14.2047 11.5299 14.0643C11.6705 13.9239 11.7408 13.7473 11.7408 13.5345C11.7408 13.3218 11.6705 13.1453 11.5299 13.0049C11.3895 12.8645 11.213 12.7943 11.0003 12.7943C10.7877 12.7943 10.6111 12.8645 10.4707 13.0049C10.3302 13.1453 10.2599 13.3218 10.2599 13.5345C10.2599 13.7473 10.3302 13.9239 10.4707 14.0643C10.6111 14.2047 10.7877 14.2749 11.0003 14.2749ZM11.0003 11.0842C11.1954 11.0842 11.3587 11.0185 11.4903 10.8869C11.622 10.7552 11.6878 10.5918 11.6878 10.3967V6.23645C11.6878 6.04135 11.622 5.87803 11.4903 5.74649C11.3587 5.6148 11.1954 5.54895 11.0003 5.54895C10.8052 5.54895 10.6419 5.6148 10.5104 5.74649C10.3787 5.87803 10.3128 6.04135 10.3128 6.23645V10.3967C10.3128 10.5918 10.3787 10.7552 10.5104 10.8869C10.6419 11.0185 10.8052 11.0842 11.0003 11.0842ZM5.53562 16.8311L3.70045 18.666C3.43966 18.9269 3.13968 18.9861 2.80051 18.8434C2.4615 18.7005 2.29199 18.4434 2.29199 18.072V4.73816C2.29199 4.27509 2.45241 3.88314 2.77324 3.5623C3.09408 3.24147 3.48603 3.08105 3.9491 3.08105H18.0516C18.5146 3.08105 18.9066 3.24147 19.2274 3.5623C19.5482 3.88314 19.7087 4.27509 19.7087 4.73816V15.174C19.7087 15.637 19.5482 16.029 19.2274 16.3498C18.9066 16.6706 18.5146 16.8311 18.0516 16.8311H5.53562ZM4.95033 15.4561H18.0516C18.1221 15.4561 18.1868 15.4266 18.2454 15.3678C18.3042 15.3092 18.3337 15.2445 18.3337 15.174V4.73816C18.3337 4.66758 18.3042 4.60295 18.2454 4.54428C18.1868 4.48546 18.1221 4.45605 18.0516 4.45605H3.9491C3.87851 4.45605 3.81389 4.48546 3.75522 4.54428C3.6964 4.60295 3.66699 4.66758 3.66699 4.73816V16.7254L4.95033 15.4561Z" fill="#666666"></path></svg></div></div><div style="position: relative;"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="manhua"><path id="Vector" d="M14.8853 4.92364C14.8853 4.92364 16.3905 10.4362 22.6668 4C22.6668 4 20.3381 10.8907 25.3364 10.0843C25.3364 10.0843 22.0563 15.6994 29 18.0599C29 18.0599 22.9934 19.306 21.1617 28C21.1617 28 17.7679 24.54 14.8853 27.3549C14.8853 27.3549 13.3233 23.5724 7.33097 26.27C7.33097 26.27 10.1141 20.6549 4.83179 21.0507C4.83179 21.0507 7.16057 18.8955 3 15.9047C3 15.9047 7.50137 16.1833 6.33697 11.7117C6.33697 11.7117 10.0005 12.3421 8.66576 6.82957C8.65156 6.81491 12.4855 9.80574 14.8853 4.92364Z" fill="#ED6D8F"></path><path id="Vector_2" d="M20.8599 13.7022C20.885 13.1361 20.9543 12.5713 20.9959 12.0052C21.0337 11.568 20.8107 11.2794 20.3876 11.18C20.0759 11.1013 19.7508 11.0867 19.433 11.137C19.1951 11.1945 18.9542 11.2396 18.7113 11.2721C18.2403 11.3028 17.9973 11.5275 17.9796 11.988C17.977 12.0833 17.9596 12.1777 17.928 12.268C17.3034 13.9102 16.6774 15.5499 16.0503 17.1873C16.0301 17.2401 16.0062 17.2904 15.9671 17.3776C15.7291 16.8975 15.4281 16.4898 15.2745 15.9986C14.8073 14.5152 14.3186 13.033 13.8312 11.5594C13.6826 11.1112 13.3489 10.9344 12.8754 11.0216C12.7889 11.0365 12.7008 11.0398 12.6134 11.0314C12.2241 10.9938 11.8311 11.0404 11.4623 11.1677C11.0946 11.2991 10.9498 11.557 11.0152 11.9254C11.0428 12.0371 11.0643 12.1503 11.0795 12.2643C11.1223 13.1902 11.1777 14.1087 11.2054 15.0321C11.257 16.7992 11.2117 18.5651 11.0858 20.3284C11.0644 20.6354 11.0304 20.9424 11.0228 21.2494C11.0115 21.6092 11.1613 21.7811 11.5266 21.8143C11.9976 21.8573 12.4711 21.8708 12.9421 21.9088C13.0309 21.9201 13.121 21.9003 13.1962 21.8528C13.2714 21.8053 13.3268 21.7334 13.3527 21.6497C13.3996 21.5394 13.4252 21.4216 13.4282 21.3022C13.4295 20.8258 13.4207 20.3493 13.4081 19.8741C13.393 19.3264 13.3917 18.7763 13.3438 18.231C13.2857 17.5839 13.266 16.934 13.2847 16.2847C13.2847 16.2466 13.291 16.2073 13.2985 16.1312C13.3338 16.2024 13.3514 16.2356 13.3665 16.2712C13.9017 17.5228 14.3617 18.8037 14.7443 20.1074C14.7928 20.2421 14.7928 20.3889 14.7443 20.5237C14.6322 20.8196 14.7141 21.037 14.9659 21.1377C15.4445 21.3268 15.9331 21.4926 16.4155 21.6731C16.4865 21.7033 16.566 21.7091 16.6408 21.6895C16.7157 21.6698 16.7815 21.6259 16.8273 21.565C16.9085 21.4643 16.9743 21.3526 17.0225 21.2335C17.0537 21.1374 17.0798 21.0399 17.1006 20.9412C17.3185 20.2425 17.5653 19.5499 17.7517 18.8438C17.9785 17.9723 18.2624 17.1158 18.6018 16.2798C18.6201 16.2439 18.6411 16.2094 18.6647 16.1766C18.6761 16.2319 18.6761 16.254 18.6761 16.2761C18.6345 17.59 18.5955 18.8978 18.5501 20.2056C18.5363 20.5949 18.491 20.9829 18.4809 21.3722C18.4721 21.705 18.6207 21.8708 18.9557 21.9002C19.4355 21.9432 19.9191 21.9592 20.4002 21.9973C20.4888 22.0079 20.5784 21.9875 20.653 21.9399C20.7277 21.8922 20.7827 21.8203 20.8082 21.7369C20.8531 21.6305 20.8766 21.5167 20.8775 21.4017C20.88 20.7668 20.8674 20.132 20.8674 19.4971C20.8662 19.2846 20.8687 19.0722 20.8523 18.8622C20.8158 18.3968 20.7264 17.9314 20.7339 17.4685C20.7515 16.2122 20.8044 14.9572 20.8599 13.7022Z" fill="white"></path></g></svg><svg hidden="true" class="imt-manga-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg></div><svg class="imt-float-ball-loading" hidden="true" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 9px;"><path d="M9.42859 0C9.84288 0 10.1929 0.387143 10.1929 0.847143V3.99429C10.1929 4.45429 9.84431 4.84143 9.42859 4.84143C9.01431 4.84143 8.66431 4.45571 8.66431 3.99429V0.847143C8.66431 0.387143 9.01288 0 9.42859 0Z" fill="#E9E9E9"></path><path d="M14.1301 1.38877C14.5158 1.62591 14.6301 2.12163 14.4258 2.52305L12.9515 5.19448C12.901 5.28714 12.8325 5.36876 12.75 5.43455C12.6675 5.50035 12.5727 5.54898 12.4712 5.5776C12.3696 5.60621 12.2634 5.61424 12.1586 5.60119C12.0539 5.58814 11.9529 5.55429 11.8615 5.50163C11.6787 5.38432 11.5468 5.20237 11.4923 4.9921C11.4377 4.78184 11.4645 4.55874 11.5672 4.36734L13.0415 1.69591C13.2686 1.29448 13.7443 1.15305 14.1301 1.38877Z" fill="#989697"></path><path d="M17.4685 4.75707C17.5813 4.95451 17.6123 5.18824 17.5549 5.40825C17.4975 5.62826 17.3563 5.81705 17.1614 5.93422L14.4971 7.52564C14.0971 7.76993 13.6014 7.62422 13.3657 7.20707C13.2532 7.00994 13.2222 6.77667 13.2793 6.55702C13.3365 6.33737 13.4771 6.14874 13.6714 6.03136L16.3357 4.43993C16.7371 4.21993 17.2557 4.34136 17.4685 4.7585V4.75707Z" fill="#9B999A"></path><path d="M18.8572 9.42835C18.8572 9.84263 18.47 10.1926 18.01 10.1926H14.8629C14.4029 10.1926 14.0157 9.84406 14.0157 9.42835C14.0157 9.01406 14.4029 8.66406 14.8629 8.66406H18.01C18.47 8.66406 18.8572 9.01263 18.8572 9.42835Z" fill="#A3A1A2"></path><path d="M17.4686 14.1303C17.3515 14.3134 17.1697 14.4455 16.9594 14.5003C16.7491 14.5552 16.5259 14.5286 16.3343 14.426L13.6629 12.9517C13.5702 12.9012 13.4886 12.8327 13.4228 12.7503C13.357 12.6678 13.3084 12.573 13.2798 12.4714C13.2512 12.3698 13.2431 12.2636 13.2562 12.1589C13.2692 12.0542 13.3031 11.9532 13.3558 11.8617C13.4731 11.6789 13.655 11.547 13.8653 11.4925C14.0755 11.4379 14.2986 11.4647 14.49 11.5674L17.1615 13.0417C17.5629 13.2689 17.7043 13.7446 17.4686 14.1303Z" fill="#ABA9AA"></path><path opacity="0.7" d="M14.1 17.4686C13.9026 17.5814 13.6689 17.6124 13.4489 17.555C13.2288 17.4976 13.04 17.3564 12.9229 17.1615L11.3315 14.4972C11.0872 14.0972 11.2329 13.6015 11.65 13.3658C11.8472 13.2533 12.0804 13.2224 12.3001 13.2795C12.5197 13.3366 12.7084 13.4773 12.8257 13.6715L14.4172 16.3358C14.6372 16.7372 14.5157 17.2558 14.0986 17.4686H14.1Z" fill="#B2B2B2"></path><path opacity="0.6" d="M9.42859 18.8571C9.01431 18.8571 8.66431 18.4699 8.66431 18.0099V14.8628C8.66431 14.4028 9.01288 14.0156 9.42859 14.0156C9.84288 14.0156 10.1929 14.4028 10.1929 14.8628V18.0099C10.1929 18.4699 9.84431 18.8571 9.42859 18.8571Z" fill="#BAB8B9"></path><path opacity="0.5" d="M4.72717 17.4685C4.5441 17.3514 4.41195 17.1696 4.35713 16.9593C4.30231 16.749 4.32885 16.5258 4.43145 16.3342L5.90574 13.6628C5.95622 13.5701 6.02472 13.4885 6.1072 13.4227C6.18969 13.3569 6.2845 13.3083 6.38606 13.2797C6.48762 13.251 6.59387 13.243 6.69857 13.2561C6.80327 13.2691 6.90431 13.303 6.99574 13.3556C7.38145 13.5914 7.49431 14.0885 7.29002 14.4899L5.81574 17.1614C5.5886 17.5628 5.11288 17.7042 4.72717 17.4685Z" fill="#C2C0C1"></path><path opacity="0.4" d="M1.38862 14.1002C1.27584 13.9027 1.24483 13.669 1.30223 13.449C1.35964 13.229 1.50089 13.0402 1.69576 12.923L4.36004 11.3316C4.76004 11.0873 5.25576 11.233 5.49147 11.6502C5.60393 11.8473 5.63491 12.0806 5.5778 12.3002C5.52069 12.5199 5.38 12.7085 5.18576 12.8259L2.52004 14.4173C2.12004 14.6373 1.60004 14.5159 1.38862 14.0987V14.1002Z" fill="#CBCBCB"></path><path d="M0 9.42835C0 9.01406 0.387143 8.66406 0.847143 8.66406H3.99429C4.45429 8.66406 4.84143 9.01263 4.84143 9.42835C4.84143 9.84263 4.45571 10.1926 3.99429 10.1926H0.847143C0.387143 10.1926 0 9.84406 0 9.42835Z" fill="#D2D2D2"></path><path opacity="0.2" d="M1.38852 4.72705C1.50561 4.54398 1.68746 4.41183 1.89774 4.35701C2.10803 4.30219 2.33125 4.32873 2.52281 4.43133L5.19424 5.90562C5.28689 5.9561 5.36851 6.0246 5.43431 6.10708C5.5001 6.18957 5.54874 6.28438 5.57735 6.38594C5.60597 6.48749 5.61399 6.59375 5.60094 6.69845C5.5879 6.80315 5.55405 6.90419 5.50138 6.99562C5.38407 7.17844 5.20212 7.31029 4.99186 7.36484C4.78159 7.4194 4.55849 7.39263 4.3671 7.2899L1.69567 5.81562C1.29424 5.58847 1.15281 5.11276 1.38852 4.72705Z" fill="#DADADA"></path><path d="M4.75719 1.38849C4.95463 1.27571 5.18837 1.24471 5.40838 1.30211C5.62838 1.35952 5.81718 1.50077 5.93434 1.69564L7.52577 4.35992C7.77005 4.75992 7.62434 5.25564 7.20719 5.49135C7.01006 5.60381 6.77679 5.63479 6.55714 5.57768C6.33749 5.52056 6.14886 5.37988 6.03148 5.18564L4.44005 2.51992C4.22005 2.11992 4.34148 1.59992 4.75862 1.38849H4.75719Z" fill="#E2E2E2"></path></svg></div><div class=" " style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div style="display: flex; align-items: center; flex-direction: row;"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: block; opacity: 0;"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg><div class="imt-fb-btn  right btn-animate " dir="ltr" style="transform: translateX(15px); opacity: 0.7;"><div><svg class="imt-fb-logo-img imt-fb-logo-img-big-bg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 15v2a2 2 0 0 0 1.85 1.995L7 19h3v2H7a4 4 0 0 1-4-4v-2h2zm13-5l4.4 11h-2.155l-1.201-3h-4.09l-1.199 3h-2.154L16 10h2zm-1 2.885L15.753 16h2.492L17 12.885zM8 2v2h4v7H8v3H6v-3H2V4h4V2h2zm9 1a4 4 0 0 1 4 4v2h-2V7a2 2 0 0 0-2-2h-3V3h3zM6 6H4v3h2V6zm4 0H8v3h2V6z" fill="rgba(255,255,255,1)"></path></svg><svg hidden="true" class="imt-float-ball-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg></div></div><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none; opacity: 0;"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div></div><div style="position: relative; width: 100%; opacity: 0;"><div title="关闭悬浮球" class="imt-fb-close-button" style="transform: translateX(100%);"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div><div class="imt-fb-more-buttons btn-animate" style="margin-top: 10px; transform: translateX(60px);"><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div class="imt-fb-more-button"><svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 22px; height: 22px;"><path d="M16 7.66699H10.375" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11.625 14.333L6 14.333" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.125 16C15.1605 16 16 15.1605 16 14.125C16 13.0895 15.1605 12.25 14.125 12.25C13.0895 12.25 12.25 13.0895 12.25 14.125C12.25 15.1605 13.0895 16 14.125 16Z" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.875 9.75C8.91053 9.75 9.75 8.91053 9.75 7.875C9.75 6.83947 8.91053 6 7.875 6C6.83947 6 6 6.83947 6 7.875C6 8.91053 6.83947 9.75 7.875 9.75Z" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><rect x="3" y="3" width="16" height="16" rx="1.66667" stroke="#666666" stroke-width="1.4"></rect></svg></div></div></div><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><svg class="imt-fb-feedback" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.0003 14.2749C11.213 14.2749 11.3895 14.2047 11.5299 14.0643C11.6705 13.9239 11.7408 13.7473 11.7408 13.5345C11.7408 13.3218 11.6705 13.1453 11.5299 13.0049C11.3895 12.8645 11.213 12.7943 11.0003 12.7943C10.7877 12.7943 10.6111 12.8645 10.4707 13.0049C10.3302 13.1453 10.2599 13.3218 10.2599 13.5345C10.2599 13.7473 10.3302 13.9239 10.4707 14.0643C10.6111 14.2047 10.7877 14.2749 11.0003 14.2749ZM11.0003 11.0842C11.1954 11.0842 11.3587 11.0185 11.4903 10.8869C11.622 10.7552 11.6878 10.5918 11.6878 10.3967V6.23645C11.6878 6.04135 11.622 5.87803 11.4903 5.74649C11.3587 5.6148 11.1954 5.54895 11.0003 5.54895C10.8052 5.54895 10.6419 5.6148 10.5104 5.74649C10.3787 5.87803 10.3128 6.04135 10.3128 6.23645V10.3967C10.3128 10.5918 10.3787 10.7552 10.5104 10.8869C10.6419 11.0185 10.8052 11.0842 11.0003 11.0842ZM5.53562 16.8311L3.70045 18.666C3.43966 18.9269 3.13968 18.9861 2.80051 18.8434C2.4615 18.7005 2.29199 18.4434 2.29199 18.072V4.73816C2.29199 4.27509 2.45241 3.88314 2.77324 3.5623C3.09408 3.24147 3.48603 3.08105 3.9491 3.08105H18.0516C18.5146 3.08105 18.9066 3.24147 19.2274 3.5623C19.5482 3.88314 19.7087 4.27509 19.7087 4.73816V15.174C19.7087 15.637 19.5482 16.029 19.2274 16.3498C18.9066 16.6706 18.5146 16.8311 18.0516 16.8311H5.53562ZM4.95033 15.4561H18.0516C18.1221 15.4561 18.1868 15.4266 18.2454 15.3678C18.3042 15.3092 18.3337 15.2445 18.3337 15.174V4.73816C18.3337 4.66758 18.3042 4.60295 18.2454 4.54428C18.1868 4.48546 18.1221 4.45605 18.0516 4.45605H3.9491C3.87851 4.45605 3.81389 4.48546 3.75522 4.54428C3.6964 4.60295 3.66699 4.66758 3.66699 4.73816V16.7254L4.95033 15.4561Z" fill="#666666"></path></svg></div></div></div></div></div></template></div></html>