# 面试答案详解 - 第3批 (问题34-48)

> 架构设计与性能优化问题详解 - 基于6年Java后端开发经验
>
> 涵盖：架构设计、性能优化、场景设计等高级技术问题

---

## 架构设计问题

### 34. 微服务拆分：如何进行微服务拆分？拆分原则是什么？

**答案**：

**微服务拆分策略与实践**：

在我们的项目中，我从**单体应用**逐步演进到**微服务架构**，管理**15个微服务**，支撑**日均500万+请求**。微服务拆分需要遵循**业务边界**、**数据一致性**、**团队组织**等多个维度的原则。

**拆分原则与方法**：

**1. 业务领域驱动拆分（DDD）**：
```java
// 呼叫中心系统的领域划分
@Component
public class DomainBoundaryAnalyzer {

    // 核心业务领域识别
    public List<BusinessDomain> identifyDomains() {
        return Arrays.asList(
            // 用户管理域
            BusinessDomain.builder()
                .name("用户管理")
                .boundedContext("UserManagement")
                .aggregateRoots(Arrays.asList("User", "Agent", "Customer"))
                .businessCapabilities(Arrays.asList("用户注册", "权限管理", "座席管理"))
                .dataEntities(Arrays.asList("user", "agent", "role", "permission"))
                .build(),

            // 通话管理域
            BusinessDomain.builder()
                .name("通话管理")
                .boundedContext("CallManagement")
                .aggregateRoots(Arrays.asList("Call", "CallRecord", "CallRoute"))
                .businessCapabilities(Arrays.asList("通话路由", "通话记录", "通话监控"))
                .dataEntities(Arrays.asList("call_record", "call_route", "call_queue"))
                .build(),

            // 质检管理域
            BusinessDomain.builder()
                .name("质检管理")
                .boundedContext("QualityManagement")
                .aggregateRoots(Arrays.asList("QualityRule", "QualityReport", "QualityTask"))
                .businessCapabilities(Arrays.asList("规则配置", "质检执行", "报告生成"))
                .dataEntities(Arrays.asList("quality_rule", "quality_report", "quality_task"))
                .build(),

            // 录音管理域
            BusinessDomain.builder()
                .name("录音管理")
                .boundedContext("RecordingManagement")
                .aggregateRoots(Arrays.asList("Recording", "RecordingMetadata"))
                .businessCapabilities(Arrays.asList("录音存储", "录音检索", "录音播放"))
                .dataEntities(Arrays.asList("recording_metadata", "recording_storage"))
                .build()
        );
    }

    // 服务边界分析
    public ServiceBoundaryAnalysis analyzeServiceBoundary(BusinessDomain domain) {
        ServiceBoundaryAnalysis analysis = new ServiceBoundaryAnalysis();

        // 1. 数据内聚性分析
        DataCohesionScore cohesionScore = calculateDataCohesion(domain.getDataEntities());
        analysis.setDataCohesion(cohesionScore);

        // 2. 业务功能内聚性
        BusinessCohesionScore businessCohesion = calculateBusinessCohesion(domain.getBusinessCapabilities());
        analysis.setBusinessCohesion(businessCohesion);

        // 3. 团队组织匹配度
        TeamAlignmentScore teamAlignment = calculateTeamAlignment(domain);
        analysis.setTeamAlignment(teamAlignment);

        // 4. 技术栈一致性
        TechStackConsistency techConsistency = analyzeTechStackConsistency(domain);
        analysis.setTechConsistency(techConsistency);

        return analysis;
    }
}
```

**2. 数据一致性边界**：
```java
@Service
public class DataConsistencyAnalyzer {

    // 事务边界分析
    public TransactionBoundaryAnalysis analyzeTransactionBoundary() {
        Map<String, List<String>> transactionScopes = new HashMap<>();

        // 强一致性事务范围
        transactionScopes.put("用户注册", Arrays.asList(
            "创建用户账户", "分配默认角色", "初始化用户配置"
        ));

        transactionScopes.put("通话建立", Arrays.asList(
            "座席状态更新", "通话记录创建", "路由信息记录"
        ));

        transactionScopes.put("质检任务", Arrays.asList(
            "任务状态更新", "质检结果保存", "评分计算"
        ));

        // 最终一致性场景
        Map<String, ConsistencyStrategy> eventualConsistency = new HashMap<>();
        eventualConsistency.put("统计数据更新", ConsistencyStrategy.EVENTUAL);
        eventualConsistency.put("报表数据同步", ConsistencyStrategy.EVENTUAL);
        eventualConsistency.put("日志数据写入", ConsistencyStrategy.EVENTUAL);

        return TransactionBoundaryAnalysis.builder()
            .strongConsistencyScopes(transactionScopes)
            .eventualConsistencyScopes(eventualConsistency)
            .build();
    }

    // 数据依赖分析
    public DataDependencyGraph analyzeDataDependencies() {
        DataDependencyGraph graph = new DataDependencyGraph();

        // 添加实体依赖关系
        graph.addDependency("User", "Agent", DependencyType.ONE_TO_ONE);
        graph.addDependency("Agent", "CallRecord", DependencyType.ONE_TO_MANY);
        graph.addDependency("CallRecord", "QualityReport", DependencyType.ONE_TO_ONE);
        graph.addDependency("CallRecord", "Recording", DependencyType.ONE_TO_ONE);

        // 分析循环依赖
        List<CircularDependency> circularDeps = graph.findCircularDependencies();
        if (!circularDeps.isEmpty()) {
            log.warn("发现循环依赖: {}", circularDeps);
        }

        return graph;
    }
}
```

**3. 团队康威定律应用**：
```java
@Component
public class TeamOrganizationMapper {

    // 团队与服务映射
    public ServiceTeamMapping mapTeamToServices() {
        return ServiceTeamMapping.builder()
            .teamServiceMap(Map.of(
                "用户管理团队", Arrays.asList("user-service", "auth-service"),
                "通话业务团队", Arrays.asList("call-service", "routing-service"),
                "质检算法团队", Arrays.asList("quality-service", "asr-service"),
                "基础设施团队", Arrays.asList("gateway-service", "config-service", "monitor-service"),
                "数据平台团队", Arrays.asList("report-service", "analytics-service")
            ))
            .teamSize(Map.of(
                "用户管理团队", 4,
                "通话业务团队", 6,
                "质检算法团队", 5,
                "基础设施团队", 3,
                "数据平台团队", 4
            ))
            .build();
    }

    // 团队协作复杂度评估
    public TeamCollaborationComplexity assessCollaborationComplexity() {
        // 计算团队间接口数量
        int crossTeamInterfaces = calculateCrossTeamInterfaces();

        // 计算沟通成本
        double communicationCost = calculateCommunicationCost();

        // 计算发布依赖度
        double releaseDependency = calculateReleaseDependency();

        return TeamCollaborationComplexity.builder()
            .crossTeamInterfaces(crossTeamInterfaces)
            .communicationCost(communicationCost)
            .releaseDependency(releaseDependency)
            .complexityScore(crossTeamInterfaces * communicationCost * releaseDependency)
            .build();
    }
}
```

**4. 技术栈与性能考量**：
```java
@Service
public class TechnicalFactorAnalyzer {

    // 性能要求分析
    public PerformanceRequirementAnalysis analyzePerformanceRequirements() {
        Map<String, PerformanceRequirement> serviceRequirements = new HashMap<>();

        // 高性能要求服务
        serviceRequirements.put("call-service", PerformanceRequirement.builder()
            .responseTime("< 100ms")
            .throughput("10000 QPS")
            .availability("99.99%")
            .consistency("强一致性")
            .build());

        serviceRequirements.put("routing-service", PerformanceRequirement.builder()
            .responseTime("< 50ms")
            .throughput("5000 QPS")
            .availability("99.99%")
            .consistency("强一致性")
            .build());

        // 中等性能要求服务
        serviceRequirements.put("quality-service", PerformanceRequirement.builder()
            .responseTime("< 500ms")
            .throughput("1000 QPS")
            .availability("99.9%")
            .consistency("最终一致性")
            .build());

        // 低性能要求服务
        serviceRequirements.put("report-service", PerformanceRequirement.builder()
            .responseTime("< 2s")
            .throughput("100 QPS")
            .availability("99.5%")
            .consistency("最终一致性")
            .build());

        return PerformanceRequirementAnalysis.builder()
            .serviceRequirements(serviceRequirements)
            .build();
    }

    // 技术栈选择建议
    public TechStackRecommendation recommendTechStack(String serviceName, PerformanceRequirement requirement) {
        TechStackRecommendation recommendation = new TechStackRecommendation();

        if (requirement.getThroughput().contains("10000")) {
            // 高并发服务
            recommendation.setFramework("Spring WebFlux + Netty");
            recommendation.setDatabase("Redis + MySQL读写分离");
            recommendation.setCache("Redis Cluster");
            recommendation.setMq("RocketMQ");
        } else if (requirement.getThroughput().contains("1000")) {
            // 中等并发服务
            recommendation.setFramework("Spring Boot + Tomcat");
            recommendation.setDatabase("MySQL + MyBatis Plus");
            recommendation.setCache("Redis");
            recommendation.setMq("RabbitMQ");
        } else {
            // 低并发服务
            recommendation.setFramework("Spring Boot");
            recommendation.setDatabase("MySQL");
            recommendation.setCache("本地缓存");
            recommendation.setMq("异步处理");
        }

        return recommendation;
    }
}
```

**实际拆分案例**：

**拆分前单体架构问题**：
- 代码库庞大（50万+行代码）
- 部署困难（全量部署需要30分钟）
- 技术栈绑定（无法使用新技术）
- 团队协作冲突（代码冲突频繁）
- 扩展性差（无法独立扩展模块）

**拆分后微服务架构**：
```yaml
# 微服务架构图
services:
  # 网关层
  api-gateway:
    instances: 3
    resources: "2C4G"

  # 核心业务服务
  user-service:
    instances: 2
    resources: "2C4G"
    database: "user_db"

  call-service:
    instances: 5
    resources: "4C8G"
    database: "call_db"

  quality-service:
    instances: 3
    resources: "4C8G"
    database: "quality_db"

  recording-service:
    instances: 2
    resources: "2C4G"
    database: "recording_db"

  # 支撑服务
  config-service:
    instances: 2
    resources: "1C2G"

  monitor-service:
    instances: 2
    resources: "2C4G"
```

**拆分效果数据**：
- **部署效率**：单服务部署时间从30分钟降低到3分钟
- **开发效率**：团队并行开发，代码冲突减少80%
- **系统可用性**：从99.5%提升到99.9%
- **扩展性**：可独立扩展，资源利用率提升40%
- **技术栈灵活性**：不同服务可选择最适合的技术栈

**拆分过程中的挑战与解决方案**：

1. **数据一致性问题**：采用Saga模式和事件驱动架构
2. **服务间通信**：使用Feign + Hystrix实现服务调用和熔断
3. **分布式事务**：使用Seata实现分布式事务管理
4. **配置管理**：使用Nacos实现配置中心
5. **监控告警**：使用Skywalking + Prometheus实现全链路监控

### 35. 分布式事务：如何处理分布式事务？用过哪些方案？

**答案**：

**分布式事务解决方案实践**：

在微服务架构中，我们面临**跨服务数据一致性**挑战，处理**日均100万+分布式事务**，采用**多种分布式事务方案**确保数据一致性。

**1. Seata分布式事务框架**：
```java
@Configuration
public class SeataConfiguration {

    @Bean
    public GlobalTransactionScanner globalTransactionScanner() {
        return new GlobalTransactionScanner("call-center-tx-group", "my_test_tx_group");
    }
}

@Service
public class CallProcessService {

    @Autowired
    private AgentService agentService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private BillingService billingService;

    // AT模式：自动补偿事务
    @GlobalTransactional(name = "call-process-tx", rollbackFor = Exception.class)
    public CallProcessResult processCall(CallRequest request) {
        try {
            // 1. 更新座席状态
            AgentStateResult agentResult = agentService.updateAgentState(
                request.getAgentId(), AgentState.BUSY);

            // 2. 创建通话记录
            CallRecord callRecord = callRecordService.createCallRecord(request);

            // 3. 计费处理
            BillingResult billingResult = billingService.createBilling(
                callRecord.getCallId(), request.getCustomerId());

            return CallProcessResult.builder()
                .callId(callRecord.getCallId())
                .agentResult(agentResult)
                .billingResult(billingResult)
                .build();

        } catch (Exception e) {
            log.error("通话处理失败，事务回滚: request={}", request, e);
            throw e; // 触发全局事务回滚
        }
    }

    // TCC模式：手动补偿事务
    @TwoPhaseBusinessAction(name = "callProcessTcc", commitMethod = "commitCallProcess",
                           rollbackMethod = "rollbackCallProcess")
    public boolean prepareCallProcess(BusinessActionContext context, CallRequest request) {
        try {
            // Try阶段：预留资源
            String callId = request.getCallId();

            // 1. 预留座席资源
            boolean agentReserved = agentService.reserveAgent(request.getAgentId(), callId);
            context.setActionContext("agentReserved", agentReserved);

            // 2. 预创建通话记录
            boolean recordPrepared = callRecordService.prepareCallRecord(request);
            context.setActionContext("recordPrepared", recordPrepared);

            // 3. 预留计费资源
            boolean billingPrepared = billingService.prepareBilling(request.getCustomerId(), callId);
            context.setActionContext("billingPrepared", billingPrepared);

            return agentReserved && recordPrepared && billingPrepared;

        } catch (Exception e) {
            log.error("TCC Try阶段失败: request={}", request, e);
            return false;
        }
    }

    public boolean commitCallProcess(BusinessActionContext context) {
        try {
            String callId = context.getActionContext("callId").toString();

            // Confirm阶段：确认资源使用
            agentService.confirmAgentReservation(callId);
            callRecordService.confirmCallRecord(callId);
            billingService.confirmBilling(callId);

            log.info("TCC事务提交成功: callId={}", callId);
            return true;

        } catch (Exception e) {
            log.error("TCC Confirm阶段失败: context={}", context, e);
            return false;
        }
    }

    public boolean rollbackCallProcess(BusinessActionContext context) {
        try {
            String callId = context.getActionContext("callId").toString();

            // Cancel阶段：释放预留资源
            if ((Boolean) context.getActionContext("agentReserved")) {
                agentService.cancelAgentReservation(callId);
            }

            if ((Boolean) context.getActionContext("recordPrepared")) {
                callRecordService.cancelCallRecord(callId);
            }

            if ((Boolean) context.getActionContext("billingPrepared")) {
                billingService.cancelBilling(callId);
            }

            log.info("TCC事务回滚成功: callId={}", callId);
            return true;

        } catch (Exception e) {
            log.error("TCC Cancel阶段失败: context={}", context, e);
            return false;
        }
    }
}
```

**2. Saga模式：事件驱动补偿**：
```java
@Component
public class CallProcessSaga {

    @Autowired
    private SagaOrchestrator sagaOrchestrator;

    // Saga编排器
    public void executeCallProcessSaga(CallRequest request) {
        SagaDefinition saga = SagaDefinition.builder()
            .sagaId(request.getCallId())
            .addStep(SagaStep.builder()
                .stepName("updateAgentState")
                .action(() -> agentService.updateAgentState(request.getAgentId(), AgentState.BUSY))
                .compensation(() -> agentService.updateAgentState(request.getAgentId(), AgentState.AVAILABLE))
                .build())
            .addStep(SagaStep.builder()
                .stepName("createCallRecord")
                .action(() -> callRecordService.createCallRecord(request))
                .compensation(() -> callRecordService.deleteCallRecord(request.getCallId()))
                .build())
            .addStep(SagaStep.builder()
                .stepName("createBilling")
                .action(() -> billingService.createBilling(request.getCallId(), request.getCustomerId()))
                .compensation(() -> billingService.deleteBilling(request.getCallId()))
                .build())
            .build();

        sagaOrchestrator.execute(saga);
    }
}

@Service
public class SagaOrchestrator {

    @Autowired
    private SagaStateManager stateManager;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void execute(SagaDefinition saga) {
        SagaExecution execution = SagaExecution.builder()
            .sagaId(saga.getSagaId())
            .status(SagaStatus.STARTED)
            .currentStep(0)
            .steps(saga.getSteps())
            .startTime(System.currentTimeMillis())
            .build();

        stateManager.saveExecution(execution);

        try {
            executeSteps(execution);
        } catch (Exception e) {
            log.error("Saga执行失败，开始补偿: sagaId={}", saga.getSagaId(), e);
            compensate(execution);
        }
    }

    private void executeSteps(SagaExecution execution) {
        List<SagaStep> steps = execution.getSteps();

        for (int i = execution.getCurrentStep(); i < steps.size(); i++) {
            SagaStep step = steps.get(i);

            try {
                // 执行步骤
                Object result = step.getAction().call();

                // 更新执行状态
                execution.setCurrentStep(i + 1);
                execution.addStepResult(i, result);
                stateManager.updateExecution(execution);

                // 发布步骤完成事件
                eventPublisher.publishEvent(new SagaStepCompletedEvent(
                    execution.getSagaId(), step.getStepName(), result));

            } catch (Exception e) {
                execution.setStatus(SagaStatus.FAILED);
                execution.setFailedStep(i);
                execution.setErrorMessage(e.getMessage());
                stateManager.updateExecution(execution);

                throw new SagaExecutionException("步骤执行失败: " + step.getStepName(), e);
            }
        }

        execution.setStatus(SagaStatus.COMPLETED);
        execution.setEndTime(System.currentTimeMillis());
        stateManager.updateExecution(execution);
    }

    private void compensate(SagaExecution execution) {
        List<SagaStep> steps = execution.getSteps();
        int failedStep = execution.getFailedStep();

        // 从失败步骤开始，逆序执行补偿
        for (int i = failedStep - 1; i >= 0; i--) {
            SagaStep step = steps.get(i);

            try {
                step.getCompensation().call();

                // 发布补偿完成事件
                eventPublisher.publishEvent(new SagaCompensationCompletedEvent(
                    execution.getSagaId(), step.getStepName()));

            } catch (Exception e) {
                log.error("补偿步骤失败: sagaId={}, stepName={}",
                    execution.getSagaId(), step.getStepName(), e);

                // 补偿失败，需要人工介入
                alertService.sendCompensationFailureAlert(execution, step, e);
            }
        }

        execution.setStatus(SagaStatus.COMPENSATED);
        stateManager.updateExecution(execution);
    }
}
```

**3. 本地消息表模式**：
```java
@Service
@Transactional
public class LocalMessageTransactionService {

    @Autowired
    private CallRecordMapper callRecordMapper;

    @Autowired
    private LocalMessageMapper localMessageMapper;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    // 本地事务 + 消息表
    public void processCallWithMessage(CallRequest request) {
        try {
            // 1. 本地事务：业务操作 + 消息记录
            CallRecord callRecord = new CallRecord();
            callRecord.setCallId(request.getCallId());
            callRecord.setAgentId(request.getAgentId());
            callRecord.setCustomerId(request.getCustomerId());
            callRecord.setStatus(CallStatus.PROCESSING);
            callRecord.setCreateTime(System.currentTimeMillis());

            callRecordMapper.insert(callRecord);

            // 2. 记录待发送消息
            LocalMessage message = LocalMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .topic("call-events")
                .tag("call-created")
                .body(JsonUtils.toJsonString(callRecord))
                .status(MessageStatus.PENDING)
                .createTime(System.currentTimeMillis())
                .retryCount(0)
                .build();

            localMessageMapper.insert(message);

            // 本地事务提交后，异步发送消息

        } catch (Exception e) {
            log.error("处理通话记录失败: request={}", request, e);
            throw e;
        }
    }

    // 定时任务：发送待发送消息
    @Scheduled(fixedDelay = 5000) // 每5秒执行一次
    public void sendPendingMessages() {
        List<LocalMessage> pendingMessages = localMessageMapper.selectPendingMessages(100);

        for (LocalMessage message : pendingMessages) {
            try {
                // 发送消息
                SendResult sendResult = rocketMQTemplate.syncSend(
                    message.getTopic() + ":" + message.getTag(),
                    message.getBody()
                );

                if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
                    // 更新消息状态为已发送
                    message.setStatus(MessageStatus.SENT);
                    message.setSendTime(System.currentTimeMillis());
                    localMessageMapper.updateById(message);

                } else {
                    // 发送失败，增加重试次数
                    handleMessageSendFailure(message);
                }

            } catch (Exception e) {
                log.error("发送消息失败: messageId={}", message.getMessageId(), e);
                handleMessageSendFailure(message);
            }
        }
    }

    private void handleMessageSendFailure(LocalMessage message) {
        message.setRetryCount(message.getRetryCount() + 1);
        message.setLastRetryTime(System.currentTimeMillis());

        if (message.getRetryCount() >= 5) {
            // 超过最大重试次数，标记为失败
            message.setStatus(MessageStatus.FAILED);

            // 发送告警
            alertService.sendMessageFailureAlert(message);
        }

        localMessageMapper.updateById(message);
    }
}
```

**4. 最大努力通知模式**：
```java
@Service
public class BestEffortNotificationService {

    @Autowired
    private NotificationRecordMapper notificationRecordMapper;

    @Autowired
    private ExternalApiClient externalApiClient;

    // 最大努力通知
    public void notifyExternalSystem(NotificationRequest request) {
        NotificationRecord record = NotificationRecord.builder()
            .notificationId(UUID.randomUUID().toString())
            .targetSystem(request.getTargetSystem())
            .notificationType(request.getType())
            .content(request.getContent())
            .status(NotificationStatus.PENDING)
            .createTime(System.currentTimeMillis())
            .retryCount(0)
            .maxRetryCount(5)
            .build();

        notificationRecordMapper.insert(record);

        // 异步发送通知
        CompletableFuture.runAsync(() -> sendNotification(record));
    }

    private void sendNotification(NotificationRecord record) {
        try {
            // 调用外部系统API
            ApiResponse response = externalApiClient.sendNotification(
                record.getTargetSystem(),
                record.getContent()
            );

            if (response.isSuccess()) {
                // 通知成功
                record.setStatus(NotificationStatus.SUCCESS);
                record.setResponseTime(System.currentTimeMillis());
                record.setResponse(response.getData());

            } else {
                // 通知失败，准备重试
                scheduleRetry(record, response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("发送通知异常: notificationId={}", record.getNotificationId(), e);
            scheduleRetry(record, e.getMessage());
        }

        notificationRecordMapper.updateById(record);
    }

    private void scheduleRetry(NotificationRecord record, String errorMessage) {
        record.setRetryCount(record.getRetryCount() + 1);
        record.setLastRetryTime(System.currentTimeMillis());
        record.setLastError(errorMessage);

        if (record.getRetryCount() >= record.getMaxRetryCount()) {
            // 超过最大重试次数
            record.setStatus(NotificationStatus.FAILED);

            // 发送人工处理告警
            alertService.sendNotificationFailureAlert(record);

        } else {
            // 计算下次重试时间（指数退避）
            long retryDelay = calculateRetryDelay(record.getRetryCount());

            // 延迟重试
            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(retryDelay);
                    sendNotification(record);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
    }

    private long calculateRetryDelay(int retryCount) {
        // 指数退避：1s, 2s, 4s, 8s, 16s
        return (long) Math.pow(2, retryCount) * 1000;
    }
}
```

**分布式事务方案对比**：

| 方案 | 一致性 | 性能 | 复杂度 | 适用场景 |
|------|--------|------|--------|----------|
| Seata AT | 强一致性 | 中等 | 低 | 通用业务场景 |
| Seata TCC | 强一致性 | 高 | 高 | 高性能要求场景 |
| Saga | 最终一致性 | 高 | 中等 | 长流程业务 |
| 本地消息表 | 最终一致性 | 高 | 中等 | 异步处理场景 |
| 最大努力通知 | 最终一致性 | 高 | 低 | 外部系统集成 |

**实际应用效果**：
- **事务成功率**：99.5%
- **平均响应时间**：AT模式200ms，TCC模式100ms
- **补偿成功率**：98%
- **系统可用性**：从99.5%提升到99.9%

---

## 性能优化问题

### 36. 缓存策略：如何设计多级缓存？缓存一致性如何保证？

**答案**：

**多级缓存架构设计**：

我们的系统采用**四级缓存架构**，处理**日均10亿+缓存请求**，缓存命中率达到**95%+**，响应时间从**200ms降低到20ms**。

**缓存架构层次**：
```java
@Configuration
public class MultiLevelCacheConfiguration {

    // L1: 本地缓存 (Caffeine)
    @Bean
    public Cache<String, Object> localCache() {
        return Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(Duration.ofMinutes(5))
            .expireAfterAccess(Duration.ofMinutes(2))
            .recordStats()
            .build();
    }

    // L2: 分布式缓存 (Redis)
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(jedisConnectionFactory());
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setDefaultSerializer(new GenericJackson2JsonRedisSerializer());
        return template;
    }

    // L3: 数据库查询缓存 (MyBatis二级缓存)
    @Bean
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource());

        org.apache.ibatis.session.Configuration configuration =
            new org.apache.ibatis.session.Configuration();
        configuration.setCacheEnabled(true); // 启用二级缓存
        configuration.setLocalCacheScope(LocalCacheScope.STATEMENT);

        factoryBean.setConfiguration(configuration);
        return factoryBean.getObject();
    }
}

@Service
public class MultiLevelCacheService {

    @Autowired
    private Cache<String, Object> localCache;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AgentMapper agentMapper;

    // 多级缓存查询
    public Agent getAgent(String agentId) {
        String cacheKey = "agent:" + agentId;

        // L1: 本地缓存查询
        Agent agent = (Agent) localCache.getIfPresent(cacheKey);
        if (agent != null) {
            cacheMetrics.incrementL1Hit();
            return agent;
        }

        // L2: Redis缓存查询
        agent = (Agent) redisTemplate.opsForValue().get(cacheKey);
        if (agent != null) {
            // 回填本地缓存
            localCache.put(cacheKey, agent);
            cacheMetrics.incrementL2Hit();
            return agent;
        }

        // L3: 数据库查询
        agent = agentMapper.selectById(agentId);
        if (agent != null) {
            // 回填多级缓存
            redisTemplate.opsForValue().set(cacheKey, agent, Duration.ofHours(1));
            localCache.put(cacheKey, agent);
            cacheMetrics.incrementDbHit();
        }

        return agent;
    }

    // 缓存更新
    public void updateAgent(Agent agent) {
        String cacheKey = "agent:" + agent.getAgentId();

        // 1. 更新数据库
        agentMapper.updateById(agent);

        // 2. 更新缓存
        updateMultiLevelCache(cacheKey, agent);

        // 3. 发送缓存失效消息
        publishCacheInvalidationEvent(cacheKey);
    }

    private void updateMultiLevelCache(String cacheKey, Agent agent) {
        // 更新本地缓存
        localCache.put(cacheKey, agent);

        // 更新Redis缓存
        redisTemplate.opsForValue().set(cacheKey, agent, Duration.ofHours(1));
    }
}
```

**缓存一致性保证策略**：

**1. Cache-Aside模式 + 事件驱动**：
```java
@Service
public class CacheConsistencyService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    // 缓存更新事件
    @EventListener
    public void handleCacheUpdateEvent(CacheUpdateEvent event) {
        String cacheKey = event.getCacheKey();
        Object newValue = event.getNewValue();

        // 本地缓存更新
        localCache.put(cacheKey, newValue);

        // 发送分布式缓存更新消息
        CacheUpdateMessage message = CacheUpdateMessage.builder()
            .cacheKey(cacheKey)
            .value(newValue)
            .operation(CacheOperation.UPDATE)
            .timestamp(System.currentTimeMillis())
            .sourceNode(getLocalNodeId())
            .build();

        rocketMQTemplate.asyncSend("cache-update-topic", message);
    }

    // 缓存失效事件
    @EventListener
    public void handleCacheInvalidationEvent(CacheInvalidationEvent event) {
        String cacheKey = event.getCacheKey();

        // 本地缓存失效
        localCache.invalidate(cacheKey);

        // 发送分布式缓存失效消息
        CacheInvalidationMessage message = CacheInvalidationMessage.builder()
            .cacheKey(cacheKey)
            .operation(CacheOperation.INVALIDATE)
            .timestamp(System.currentTimeMillis())
            .sourceNode(getLocalNodeId())
            .build();

        rocketMQTemplate.asyncSend("cache-invalidation-topic", message);
    }
}

@RocketMQMessageListener(topic = "cache-update-topic", consumerGroup = "cache-consumer")
@Component
public class CacheUpdateMessageListener implements RocketMQListener<CacheUpdateMessage> {

    @Override
    public void onMessage(CacheUpdateMessage message) {
        // 避免处理自己发送的消息
        if (message.getSourceNode().equals(getLocalNodeId())) {
            return;
        }

        try {
            String cacheKey = message.getCacheKey();
            Object value = message.getValue();

            // 更新本地缓存
            localCache.put(cacheKey, value);

            // 更新Redis缓存
            redisTemplate.opsForValue().set(cacheKey, value, Duration.ofHours(1));

            log.info("缓存更新完成: cacheKey={}", cacheKey);

        } catch (Exception e) {
            log.error("处理缓存更新消息失败: message={}", message, e);
        }
    }
}
```

**2. 分布式锁保证原子性**：
```java
@Service
public class DistributedCacheService {

    @Autowired
    private RedissonClient redissonClient;

    // 分布式锁保证缓存更新原子性
    public void updateCacheWithLock(String cacheKey, Supplier<Object> dataLoader) {
        String lockKey = "cache:lock:" + cacheKey;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {

                // 双重检查，避免重复加载
                Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
                if (cachedValue != null) {
                    return;
                }

                // 加载数据
                Object newValue = dataLoader.get();

                if (newValue != null) {
                    // 原子性更新缓存
                    redisTemplate.opsForValue().set(cacheKey, newValue, Duration.ofHours(1));
                    localCache.put(cacheKey, newValue);
                }

            } else {
                log.warn("获取缓存锁超时: cacheKey={}", cacheKey);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("缓存更新被中断: cacheKey={}", cacheKey, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

**3. 缓存版本控制**：
```java
@Entity
public class CacheableEntity {
    private String id;
    private Object data;
    private Long version; // 版本号
    private Long updateTime;

    // getters and setters
}

@Service
public class VersionedCacheService {

    // 版本化缓存更新
    public boolean updateVersionedCache(String cacheKey, Object newValue, Long expectedVersion) {
        String versionKey = cacheKey + ":version";

        // 获取当前版本
        Long currentVersion = (Long) redisTemplate.opsForValue().get(versionKey);

        if (currentVersion != null && !currentVersion.equals(expectedVersion)) {
            // 版本冲突，更新失败
            log.warn("缓存版本冲突: cacheKey={}, expected={}, current={}",
                cacheKey, expectedVersion, currentVersion);
            return false;
        }

        // 原子性更新缓存和版本
        Long newVersion = expectedVersion + 1;

        RedisScript<Boolean> script = RedisScript.of(
            "redis.call('set', KEYS[1], ARGV[1]) " +
            "redis.call('set', KEYS[2], ARGV[2]) " +
            "return true",
            Boolean.class
        );

        Boolean result = redisTemplate.execute(script,
            Arrays.asList(cacheKey, versionKey),
            JsonUtils.toJsonString(newValue), newVersion.toString());

        if (result) {
            // 更新本地缓存
            localCache.put(cacheKey, newValue);
            localCache.put(versionKey, newVersion);
        }

        return result;
    }
}
```

**缓存预热策略**：
```java
@Service
public class CacheWarmupService {

    @PostConstruct
    public void warmupCache() {
        log.info("开始缓存预热...");

        // 预热热点数据
        warmupHotData();

        // 预热基础配置数据
        warmupConfigData();

        log.info("缓存预热完成");
    }

    private void warmupHotData() {
        // 预热活跃座席数据
        List<String> activeAgentIds = agentService.getActiveAgentIds();
        activeAgentIds.parallelStream().forEach(agentId -> {
            try {
                Agent agent = agentMapper.selectById(agentId);
                if (agent != null) {
                    String cacheKey = "agent:" + agentId;
                    redisTemplate.opsForValue().set(cacheKey, agent, Duration.ofHours(2));
                    localCache.put(cacheKey, agent);
                }
            } catch (Exception e) {
                log.error("预热座席数据失败: agentId={}", agentId, e);
            }
        });

        // 预热热点质检规则
        List<QualityRule> hotRules = qualityRuleService.getHotRules();
        hotRules.forEach(rule -> {
            String cacheKey = "quality:rule:" + rule.getRuleId();
            redisTemplate.opsForValue().set(cacheKey, rule, Duration.ofHours(4));
            localCache.put(cacheKey, rule);
        });
    }

    private void warmupConfigData() {
        // 预热系统配置
        Map<String, Object> systemConfigs = configService.getAllConfigs();
        systemConfigs.forEach((key, value) -> {
            String cacheKey = "config:" + key;
            redisTemplate.opsForValue().set(cacheKey, value, Duration.ofDays(1));
            localCache.put(cacheKey, value);
        });
    }
}
```

**缓存监控与指标**：
```java
@Component
public class CacheMetrics {

    private final AtomicLong l1Hits = new AtomicLong(0);
    private final AtomicLong l2Hits = new AtomicLong(0);
    private final AtomicLong dbHits = new AtomicLong(0);
    private final AtomicLong totalRequests = new AtomicLong(0);

    public void incrementL1Hit() {
        l1Hits.incrementAndGet();
        totalRequests.incrementAndGet();
    }

    public void incrementL2Hit() {
        l2Hits.incrementAndGet();
        totalRequests.incrementAndGet();
    }

    public void incrementDbHit() {
        dbHits.incrementAndGet();
        totalRequests.incrementAndGet();
    }

    @Scheduled(fixedDelay = 60000) // 每分钟输出一次指标
    public void reportMetrics() {
        long total = totalRequests.get();
        if (total > 0) {
            double l1HitRate = (double) l1Hits.get() / total * 100;
            double l2HitRate = (double) l2Hits.get() / total * 100;
            double dbHitRate = (double) dbHits.get() / total * 100;

            log.info("缓存命中率统计 - L1: {:.2f}%, L2: {:.2f}%, DB: {:.2f}%, 总请求: {}",
                l1HitRate, l2HitRate, dbHitRate, total);

            // 重置计数器
            l1Hits.set(0);
            l2Hits.set(0);
            dbHits.set(0);
            totalRequests.set(0);
        }
    }
}
```

**缓存优化效果**：
- **缓存命中率**：L1缓存80%，L2缓存15%，数据库5%
- **响应时间**：从200ms降低到20ms
- **数据库压力**：减少95%的数据库查询
- **系统吞吐量**：提升5倍
- **缓存一致性**：99.9%的数据一致性保证

---

## 场景设计问题

### 37. 秒杀系统：如何设计一个高并发秒杀系统？

**答案**：

**秒杀系统架构设计**：

基于我们呼叫中心的高并发经验，设计支持**100万+并发**、**99.99%可用性**的秒杀系统，采用**多层防护**、**异步处理**、**限流熔断**等策略。

**整体架构设计**：
```java
@RestController
@RequestMapping("/seckill")
public class SeckillController {

    @Autowired
    private SeckillService seckillService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 秒杀接口
    @PostMapping("/kill/{productId}")
    @RateLimiter(key = "seckill", permitsPerSecond = 10000) // 限流
    @CircuitBreaker(name = "seckill", fallbackMethod = "seckillFallback") // 熔断
    public CommonResult<SeckillResult> seckill(
            @PathVariable Long productId,
            @RequestParam String userId,
            HttpServletRequest request) {

        try {
            // 1. 参数校验
            if (productId == null || StringUtils.isEmpty(userId)) {
                return CommonResult.error("参数错误");
            }

            // 2. 用户验证（防刷）
            if (!validateUser(userId, request)) {
                return CommonResult.error("用户验证失败");
            }

            // 3. 活动时间校验
            if (!isActivityTime(productId)) {
                return CommonResult.error("不在活动时间内");
            }

            // 4. 库存预检查
            if (!hasStock(productId)) {
                return CommonResult.error("商品已售罄");
            }

            // 5. 重复购买检查
            if (hasPurchased(userId, productId)) {
                return CommonResult.error("您已参与过该活动");
            }

            // 6. 执行秒杀
            SeckillResult result = seckillService.doSeckill(productId, userId);

            return CommonResult.success(result);

        } catch (SeckillException e) {
            log.warn("秒杀失败: productId={}, userId={}, error={}",
                productId, userId, e.getMessage());
            return CommonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("秒杀异常: productId={}, userId={}", productId, userId, e);
            return CommonResult.error("系统繁忙，请稍后重试");
        }
    }

    // 熔断降级方法
    public CommonResult<SeckillResult> seckillFallback(Long productId, String userId,
                                                      HttpServletRequest request, Exception ex) {
        log.warn("秒杀熔断降级: productId={}, userId={}", productId, userId);
        return CommonResult.error("系统繁忙，请稍后重试");
    }

    // 用户验证（防刷机制）
    private boolean validateUser(String userId, HttpServletRequest request) {
        String clientIp = getClientIp(request);
        String userAgent = request.getHeader("User-Agent");

        // 1. IP限流
        String ipKey = "seckill:ip:" + clientIp;
        Long ipCount = redisTemplate.opsForValue().increment(ipKey);
        if (ipCount == 1) {
            redisTemplate.expire(ipKey, Duration.ofMinutes(1));
        }
        if (ipCount > 100) { // 每分钟最多100次
            return false;
        }

        // 2. 用户限流
        String userKey = "seckill:user:" + userId;
        Long userCount = redisTemplate.opsForValue().increment(userKey);
        if (userCount == 1) {
            redisTemplate.expire(userKey, Duration.ofMinutes(1));
        }
        if (userCount > 10) { // 每分钟最多10次
            return false;
        }

        // 3. User-Agent检查（防机器人）
        if (StringUtils.isEmpty(userAgent) || isBot(userAgent)) {
            return false;
        }

        return true;
    }

    // 库存预检查
    private boolean hasStock(Long productId) {
        String stockKey = "seckill:stock:" + productId;
        String stock = (String) redisTemplate.opsForValue().get(stockKey);
        return stock != null && Integer.parseInt(stock) > 0;
    }

    // 重复购买检查
    private boolean hasPurchased(String userId, Long productId) {
        String purchaseKey = "seckill:purchased:" + productId + ":" + userId;
        return redisTemplate.hasKey(purchaseKey);
    }
}
```

**核心秒杀服务**：
```java
@Service
public class SeckillService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private SeckillOrderMapper seckillOrderMapper;

    // 秒杀核心逻辑
    public SeckillResult doSeckill(Long productId, String userId) {
        String stockKey = "seckill:stock:" + productId;
        String lockKey = "seckill:lock:" + productId;

        // 使用Lua脚本保证原子性
        String luaScript =
            "local stock = redis.call('get', KEYS[1]) " +
            "if not stock or tonumber(stock) <= 0 then " +
            "    return 0 " +
            "end " +
            "redis.call('decr', KEYS[1]) " +
            "return 1";

        RedisScript<Long> script = RedisScript.of(luaScript, Long.class);
        Long result = redisTemplate.execute(script, Collections.singletonList(stockKey));

        if (result == 0) {
            throw new SeckillException("商品已售罄");
        }

        // 生成预订单
        String orderId = generateOrderId();
        SeckillOrder order = SeckillOrder.builder()
            .orderId(orderId)
            .productId(productId)
            .userId(userId)
            .status(OrderStatus.PENDING)
            .createTime(System.currentTimeMillis())
            .build();

        // 异步处理订单
        SeckillOrderMessage message = SeckillOrderMessage.builder()
            .order(order)
            .timestamp(System.currentTimeMillis())
            .build();

        rocketMQTemplate.asyncSend("seckill-order-topic", message);

        // 记录购买标记
        String purchaseKey = "seckill:purchased:" + productId + ":" + userId;
        redisTemplate.opsForValue().set(purchaseKey, "1", Duration.ofDays(1));

        return SeckillResult.builder()
            .orderId(orderId)
            .status("SUCCESS")
            .message("秒杀成功，正在处理订单")
            .build();
    }

    // 库存预热
    @PostConstruct
    public void warmupStock() {
        List<SeckillProduct> products = seckillProductService.getActiveProducts();

        for (SeckillProduct product : products) {
            String stockKey = "seckill:stock:" + product.getProductId();
            redisTemplate.opsForValue().set(stockKey, product.getStock().toString());

            log.info("库存预热完成: productId={}, stock={}",
                product.getProductId(), product.getStock());
        }
    }
}
```

**异步订单处理**：
```java
@RocketMQMessageListener(topic = "seckill-order-topic", consumerGroup = "seckill-order-consumer")
@Component
public class SeckillOrderProcessor implements RocketMQListener<SeckillOrderMessage> {

    @Autowired
    private SeckillOrderService seckillOrderService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private InventoryService inventoryService;

    @Override
    public void onMessage(SeckillOrderMessage message) {
        SeckillOrder order = message.getOrder();

        try {
            // 1. 订单入库
            seckillOrderService.createOrder(order);

            // 2. 扣减真实库存
            boolean stockDeducted = inventoryService.deductStock(
                order.getProductId(), 1);

            if (!stockDeducted) {
                // 库存不足，订单失败
                handleOrderFailure(order, "库存不足");
                return;
            }

            // 3. 创建支付订单
            PaymentOrder paymentOrder = paymentService.createPaymentOrder(order);

            // 4. 更新订单状态
            order.setStatus(OrderStatus.WAIT_PAYMENT);
            order.setPaymentOrderId(paymentOrder.getPaymentOrderId());
            seckillOrderService.updateOrder(order);

            // 5. 发送支付通知
            sendPaymentNotification(order);

            log.info("秒杀订单处理成功: orderId={}", order.getOrderId());

        } catch (Exception e) {
            log.error("秒杀订单处理失败: orderId={}", order.getOrderId(), e);
            handleOrderFailure(order, "系统异常");
        }
    }

    private void handleOrderFailure(SeckillOrder order, String reason) {
        // 1. 更新订单状态
        order.setStatus(OrderStatus.FAILED);
        order.setFailureReason(reason);
        seckillOrderService.updateOrder(order);

        // 2. 恢复Redis库存
        String stockKey = "seckill:stock:" + order.getProductId();
        redisTemplate.opsForValue().increment(stockKey);

        // 3. 清除购买标记
        String purchaseKey = "seckill:purchased:" + order.getProductId() + ":" + order.getUserId();
        redisTemplate.delete(purchaseKey);

        // 4. 发送失败通知
        sendFailureNotification(order, reason);
    }
}
```

**限流熔断实现**：
```java
@Component
public class RateLimiterAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Around("@annotation(rateLimiter)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        String key = rateLimiter.key();
        int permitsPerSecond = rateLimiter.permitsPerSecond();

        // 令牌桶算法实现
        if (!tryAcquire(key, permitsPerSecond)) {
            throw new RateLimitException("请求过于频繁，请稍后重试");
        }

        return joinPoint.proceed();
    }

    private boolean tryAcquire(String key, int permitsPerSecond) {
        String luaScript =
            "local key = KEYS[1] " +
            "local capacity = tonumber(ARGV[1]) " +
            "local tokens = tonumber(ARGV[2]) " +
            "local interval = tonumber(ARGV[3]) " +
            "local current = redis.call('hmget', key, 'tokens', 'timestamp') " +
            "local now = tonumber(ARGV[4]) " +
            "if current[1] == false then " +
            "    redis.call('hmset', key, 'tokens', capacity - 1, 'timestamp', now) " +
            "    redis.call('expire', key, interval) " +
            "    return 1 " +
            "end " +
            "local currentTokens = tonumber(current[1]) " +
            "local lastTime = tonumber(current[2]) " +
            "local elapsed = now - lastTime " +
            "local newTokens = math.min(capacity, currentTokens + elapsed * tokens / 1000) " +
            "if newTokens < 1 then " +
            "    redis.call('hmset', key, 'tokens', newTokens, 'timestamp', now) " +
            "    return 0 " +
            "else " +
            "    redis.call('hmset', key, 'tokens', newTokens - 1, 'timestamp', now) " +
            "    redis.call('expire', key, interval) " +
            "    return 1 " +
            "end";

        RedisScript<Long> script = RedisScript.of(luaScript, Long.class);
        Long result = redisTemplate.execute(script,
            Collections.singletonList("rate_limiter:" + key),
            String.valueOf(permitsPerSecond),
            String.valueOf(permitsPerSecond),
            String.valueOf(60), // 60秒过期
            String.valueOf(System.currentTimeMillis()));

        return result != null && result == 1;
    }
}

@Component
public class CircuitBreakerAspect {

    private final Map<String, CircuitBreakerState> circuitBreakers = new ConcurrentHashMap<>();

    @Around("@annotation(circuitBreaker)")
    public Object around(ProceedingJoinPoint joinPoint, CircuitBreaker circuitBreaker) throws Throwable {
        String name = circuitBreaker.name();
        CircuitBreakerState state = circuitBreakers.computeIfAbsent(name,
            k -> new CircuitBreakerState());

        if (state.isOpen()) {
            // 熔断器打开，执行降级方法
            return executeFallback(joinPoint, circuitBreaker);
        }

        try {
            Object result = joinPoint.proceed();
            state.recordSuccess();
            return result;

        } catch (Exception e) {
            state.recordFailure();

            if (state.shouldOpen()) {
                log.warn("熔断器打开: name={}", name);
            }

            throw e;
        }
    }

    private Object executeFallback(ProceedingJoinPoint joinPoint, CircuitBreaker circuitBreaker) {
        try {
            Method fallbackMethod = joinPoint.getTarget().getClass()
                .getMethod(circuitBreaker.fallbackMethod(),
                    getParameterTypes(joinPoint));

            return fallbackMethod.invoke(joinPoint.getTarget(), joinPoint.getArgs());

        } catch (Exception e) {
            log.error("执行降级方法失败", e);
            throw new RuntimeException("系统繁忙，请稍后重试");
        }
    }
}
```

**秒杀系统优化效果**：
- **并发处理能力**：支持100万+并发请求
- **响应时间**：平均50ms，95%请求<100ms
- **系统可用性**：99.99%
- **库存准确性**：100%（无超卖）
- **用户体验**：秒杀成功率15%，用户满意度90%+

### 38. 消息队列选型：RocketMQ vs Kafka vs RabbitMQ，如何选择？

**答案**：

**消息队列选型对比分析**：

在我们的项目中，我使用过**RocketMQ**、**Kafka**、**RabbitMQ**三种消息队列，根据不同业务场景选择最适合的方案。

**技术特性对比**：

| 特性 | RocketMQ | Kafka | RabbitMQ |
|------|----------|-------|----------|
| **吞吐量** | 10万+/秒 | 100万+/秒 | 1万+/秒 |
| **延迟** | 1-5ms | 2-10ms | 1-10ms |
| **可靠性** | 99.99% | 99.9% | 99.95% |
| **消息顺序** | 支持 | 支持 | 部分支持 |
| **事务消息** | 支持 | 不支持 | 不支持 |
| **延时消息** | 支持 | 不支持 | 插件支持 |
| **运维复杂度** | 中等 | 高 | 低 |

**实际应用场景选择**：

**1. RocketMQ - 业务消息处理**：
```java
// 呼叫中心业务消息处理
@Service
public class CallEventProducer {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    // 事务消息：通话记录创建
    @Transactional
    public void sendCallCreatedEvent(CallRecord callRecord) {
        CallCreatedEvent event = CallCreatedEvent.builder()
            .callId(callRecord.getCallId())
            .agentId(callRecord.getAgentId())
            .customerId(callRecord.getCustomerId())
            .startTime(callRecord.getStartTime())
            .build();

        // 事务消息确保本地事务和消息发送的一致性
        rocketMQTemplate.sendMessageInTransaction(
            "call-events:call-created",
            MessageBuilder.withPayload(event).build(),
            callRecord
        );
    }

    // 延时消息：通话超时检查
    public void scheduleCallTimeoutCheck(String callId, int timeoutMinutes) {
        CallTimeoutCheckEvent event = CallTimeoutCheckEvent.builder()
            .callId(callId)
            .checkTime(System.currentTimeMillis())
            .build();

        // 延时消息，在指定时间后触发
        Message<CallTimeoutCheckEvent> message = MessageBuilder
            .withPayload(event)
            .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL,
                calculateDelayLevel(timeoutMinutes))
            .build();

        rocketMQTemplate.send("call-timeout:check", message);
    }

    // 顺序消息：座席状态变更
    public void sendAgentStateChangeEvent(AgentStateChangeEvent event) {
        // 使用agentId作为分区键，确保同一座席的状态变更消息有序
        rocketMQTemplate.syncSendOrderly(
            "agent-state:change",
            event,
            event.getAgentId() // 分区键
        );
    }
}

// 事务消息监听器
@RocketMQTransactionListener
@Component
public class CallEventTransactionListener implements RocketMQLocalTransactionListener {

    @Autowired
    private CallRecordService callRecordService;

    @Override
    public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        try {
            CallRecord callRecord = (CallRecord) arg;

            // 执行本地事务：保存通话记录
            callRecordService.saveCallRecord(callRecord);

            return RocketMQLocalTransactionState.COMMIT;

        } catch (Exception e) {
            log.error("本地事务执行失败", e);
            return RocketMQLocalTransactionState.ROLLBACK;
        }
    }

    @Override
    public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
        // 事务状态回查
        CallCreatedEvent event = JSON.parseObject(
            new String(msg.getBody()), CallCreatedEvent.class);

        CallRecord callRecord = callRecordService.getByCallId(event.getCallId());

        return callRecord != null ?
            RocketMQLocalTransactionState.COMMIT :
            RocketMQLocalTransactionState.ROLLBACK;
    }
}
```

**2. Kafka - 大数据流处理**：
```java
// 通话数据流处理
@Service
public class CallDataStreamProcessor {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    // 实时通话数据采集
    public void collectCallData(CallDataEvent event) {
        // 高吞吐量数据采集
        kafkaTemplate.send("call-data-stream", event.getCallId(), event);
    }

    // 批量数据处理
    @KafkaListener(topics = "call-data-stream",
                   containerFactory = "batchKafkaListenerContainerFactory")
    public void processBatchCallData(List<CallDataEvent> events) {
        try {
            // 批量处理提高效率
            List<CallAnalytics> analytics = events.stream()
                .map(this::analyzeCallData)
                .collect(Collectors.toList());

            // 批量写入数据仓库
            callAnalyticsService.batchSave(analytics);

            log.info("批量处理通话数据完成: count={}", events.size());

        } catch (Exception e) {
            log.error("批量处理通话数据失败", e);
        }
    }
}

// Kafka配置
@Configuration
public class KafkaConfiguration {

    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, Object>>
            batchKafkaListenerContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, Object> factory =
            new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(consumerFactory());
        factory.setBatchListener(true); // 启用批量监听
        factory.getContainerProperties().setPollTimeout(3000);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);

        return factory;
    }

    @Bean
    public ConsumerFactory<String, Object> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "call-data-consumer");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 500); // 批量大小
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 1024); // 1MB

        return new DefaultKafkaConsumerFactory<>(props);
    }
}
```

**3. RabbitMQ - 轻量级消息处理**：
```java
// 系统通知消息处理
@Service
public class NotificationService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 发送邮件通知
    public void sendEmailNotification(EmailNotificationEvent event) {
        rabbitTemplate.convertAndSend(
            "notification.email.exchange",
            "email.send",
            event
        );
    }

    // 发送短信通知
    public void sendSmsNotification(SmsNotificationEvent event) {
        rabbitTemplate.convertAndSend(
            "notification.sms.exchange",
            "sms.send",
            event
        );
    }

    // 死信队列处理
    @RabbitListener(queues = "notification.email.dlq")
    public void handleEmailDeadLetter(EmailNotificationEvent event,
                                     @Header Map<String, Object> headers) {

        Integer retryCount = (Integer) headers.get("x-retry-count");
        if (retryCount == null) {
            retryCount = 0;
        }

        if (retryCount < 3) {
            // 重试处理
            headers.put("x-retry-count", retryCount + 1);

            rabbitTemplate.convertAndSend(
                "notification.email.retry.exchange",
                "email.retry",
                event,
                message -> {
                    message.getMessageProperties().setDelay(
                        (retryCount + 1) * 60 * 1000); // 延迟重试
                    return message;
                }
            );

        } else {
            // 超过重试次数，记录失败日志
            log.error("邮件通知最终失败: event={}", event);
            notificationFailureService.recordFailure(event, "超过最大重试次数");
        }
    }
}

// RabbitMQ配置
@Configuration
@EnableRabbit
public class RabbitMQConfiguration {

    // 邮件通知交换机和队列
    @Bean
    public TopicExchange emailExchange() {
        return new TopicExchange("notification.email.exchange");
    }

    @Bean
    public Queue emailQueue() {
        return QueueBuilder.durable("notification.email.queue")
            .withArgument("x-dead-letter-exchange", "notification.email.dlx")
            .withArgument("x-dead-letter-routing-key", "email.dead")
            .build();
    }

    @Bean
    public Binding emailBinding() {
        return BindingBuilder.bind(emailQueue())
            .to(emailExchange())
            .with("email.send");
    }

    // 死信交换机和队列
    @Bean
    public DirectExchange emailDeadLetterExchange() {
        return new DirectExchange("notification.email.dlx");
    }

    @Bean
    public Queue emailDeadLetterQueue() {
        return QueueBuilder.durable("notification.email.dlq").build();
    }

    @Bean
    public Binding emailDeadLetterBinding() {
        return BindingBuilder.bind(emailDeadLetterQueue())
            .to(emailDeadLetterExchange())
            .with("email.dead");
    }
}
```

**选型决策矩阵**：

**选择RocketMQ的场景**：
- 需要事务消息保证一致性
- 需要延时消息功能
- 对消息顺序有严格要求
- 业务消息处理（如订单、支付）
- 团队对运维复杂度要求适中

**选择Kafka的场景**：
- 需要极高吞吐量（日志、监控数据）
- 大数据流处理
- 数据管道和ETL
- 实时数据分析
- 团队有较强运维能力

**选择RabbitMQ的场景**：
- 轻量级消息处理
- 复杂路由需求
- 需要丰富的消息模式
- 快速原型开发
- 团队运维能力有限

**实际项目中的应用**：

```yaml
# 我们项目中的消息队列架构
message_queues:
  rocketmq:
    use_cases:
      - "通话事件处理"
      - "座席状态同步"
      - "质检任务分发"
      - "计费数据处理"
    daily_messages: "500万+"

  kafka:
    use_cases:
      - "通话录音数据流"
      - "实时监控数据"
      - "日志数据采集"
      - "大数据分析"
    daily_messages: "1000万+"

  rabbitmq:
    use_cases:
      - "邮件短信通知"
      - "系统告警"
      - "配置变更通知"
    daily_messages: "10万+"
```

**性能对比实测数据**：
- **RocketMQ**：单机8万QPS，集群50万QPS
- **Kafka**：单机20万QPS，集群200万QPS
- **RabbitMQ**：单机1万QPS，集群5万QPS

### 39. 系统监控：如何设计完整的监控告警体系？

**答案**：

**监控告警体系架构**：

我们构建了**四层监控体系**，覆盖**基础设施**、**应用服务**、**业务指标**、**用户体验**，实现**秒级监控**、**分钟级告警**、**99.9%告警准确率**。

**监控架构层次**：
```java
@Configuration
public class MonitoringConfiguration {

    // Prometheus配置
    @Bean
    public MeterRegistry meterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }

    // 自定义指标收集器
    @Bean
    public CustomMetricsCollector customMetricsCollector() {
        return new CustomMetricsCollector();
    }
}

@Component
public class BusinessMetricsCollector {

    private final Counter callTotalCounter;
    private final Timer callDurationTimer;
    private final Gauge activeAgentsGauge;
    private final Counter qualityCheckCounter;

    public BusinessMetricsCollector(MeterRegistry meterRegistry) {
        this.callTotalCounter = Counter.builder("call_total")
            .description("总通话数量")
            .tag("type", "business")
            .register(meterRegistry);

        this.callDurationTimer = Timer.builder("call_duration")
            .description("通话时长")
            .register(meterRegistry);

        this.activeAgentsGauge = Gauge.builder("active_agents")
            .description("在线座席数")
            .register(meterRegistry, this, BusinessMetricsCollector::getActiveAgentCount);

        this.qualityCheckCounter = Counter.builder("quality_check_total")
            .description("质检任务数量")
            .tag("status", "completed")
            .register(meterRegistry);
    }

    // 记录通话指标
    public void recordCall(CallRecord callRecord) {
        callTotalCounter.increment(
            Tags.of(
                "agent_id", callRecord.getAgentId(),
                "call_type", callRecord.getCallType().name(),
                "result", callRecord.getResult().name()
            )
        );

        if (callRecord.getDuration() > 0) {
            callDurationTimer.record(callRecord.getDuration(), TimeUnit.MILLISECONDS);
        }
    }

    // 记录质检指标
    public void recordQualityCheck(QualityCheckResult result) {
        qualityCheckCounter.increment(
            Tags.of(
                "rule_type", result.getRuleType(),
                "result", result.getResult().name(),
                "score_level", getScoreLevel(result.getScore())
            )
        );
    }

    private double getActiveAgentCount() {
        return agentService.getActiveAgentCount();
    }
}
```

**应用性能监控（APM）**：
```java
@Component
public class ApplicationPerformanceMonitor {

    @Autowired
    private MeterRegistry meterRegistry;

    // HTTP请求监控
    @EventListener
    public void handleHttpRequestEvent(HttpRequestEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);

        sample.stop(Timer.builder("http_request_duration")
            .description("HTTP请求耗时")
            .tag("method", event.getMethod())
            .tag("uri", event.getUri())
            .tag("status", String.valueOf(event.getStatus()))
            .register(meterRegistry));

        // 记录请求计数
        Counter.builder("http_request_total")
            .description("HTTP请求总数")
            .tag("method", event.getMethod())
            .tag("status", String.valueOf(event.getStatus()))
            .register(meterRegistry)
            .increment();
    }

    // JVM监控
    @Scheduled(fixedDelay = 30000) // 每30秒采集一次
    public void collectJvmMetrics() {
        Runtime runtime = Runtime.getRuntime();

        // 内存使用情况
        Gauge.builder("jvm_memory_used")
            .description("JVM已使用内存")
            .register(meterRegistry, runtime, r -> r.totalMemory() - r.freeMemory());

        Gauge.builder("jvm_memory_max")
            .description("JVM最大内存")
            .register(meterRegistry, runtime, Runtime::maxMemory);

        // GC监控
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();

        Gauge.builder("jvm_heap_used")
            .description("堆内存使用量")
            .register(meterRegistry, heapUsage, MemoryUsage::getUsed);

        Gauge.builder("jvm_heap_committed")
            .description("堆内存提交量")
            .register(meterRegistry, heapUsage, MemoryUsage::getCommitted);
    }

    // 数据库连接池监控
    @Scheduled(fixedDelay = 60000) // 每分钟采集一次
    public void collectDataSourceMetrics() {
        HikariDataSource dataSource = (HikariDataSource) this.dataSource;
        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();

        Gauge.builder("datasource_active_connections")
            .description("活跃数据库连接数")
            .register(meterRegistry, poolBean, HikariPoolMXBean::getActiveConnections);

        Gauge.builder("datasource_idle_connections")
            .description("空闲数据库连接数")
            .register(meterRegistry, poolBean, HikariPoolMXBean::getIdleConnections);

        Gauge.builder("datasource_total_connections")
            .description("总数据库连接数")
            .register(meterRegistry, poolBean, HikariPoolMXBean::getTotalConnections);
    }
}
```

**告警规则引擎**：
```java
@Service
public class AlertRuleEngine {

    @Autowired
    private AlertRuleRepository alertRuleRepository;

    @Autowired
    private AlertNotificationService notificationService;

    @Autowired
    private PrometheusQueryService prometheusService;

    // 定时执行告警检查
    @Scheduled(fixedDelay = 60000) // 每分钟检查一次
    public void executeAlertRules() {
        List<AlertRule> activeRules = alertRuleRepository.findActiveRules();

        activeRules.parallelStream().forEach(rule -> {
            try {
                checkAlertRule(rule);
            } catch (Exception e) {
                log.error("告警规则检查失败: ruleId={}", rule.getId(), e);
            }
        });
    }

    private void checkAlertRule(AlertRule rule) {
        // 查询指标数据
        PrometheusQueryResult result = prometheusService.query(
            rule.getQuery(), rule.getEvaluationInterval());

        if (result.hasData()) {
            for (MetricSample sample : result.getSamples()) {
                double value = sample.getValue();

                // 评估告警条件
                AlertEvaluationResult evaluation = evaluateCondition(rule, value);

                if (evaluation.shouldAlert()) {
                    // 检查告警抑制
                    if (!isAlertSuppressed(rule, sample)) {
                        triggerAlert(rule, sample, evaluation);
                    }
                } else if (evaluation.shouldResolve()) {
                    // 告警恢复
                    resolveAlert(rule, sample);
                }
            }
        }
    }

    private AlertEvaluationResult evaluateCondition(AlertRule rule, double value) {
        AlertCondition condition = rule.getCondition();
        boolean shouldAlert = false;

        switch (condition.getOperator()) {
            case GREATER_THAN:
                shouldAlert = value > condition.getThreshold();
                break;
            case LESS_THAN:
                shouldAlert = value < condition.getThreshold();
                break;
            case EQUALS:
                shouldAlert = Math.abs(value - condition.getThreshold()) < 0.001;
                break;
        }

        return AlertEvaluationResult.builder()
            .shouldAlert(shouldAlert)
            .value(value)
            .threshold(condition.getThreshold())
            .build();
    }

    private void triggerAlert(AlertRule rule, MetricSample sample, AlertEvaluationResult evaluation) {
        Alert alert = Alert.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getName())
            .severity(rule.getSeverity())
            .message(buildAlertMessage(rule, evaluation))
            .labels(sample.getLabels())
            .value(evaluation.getValue())
            .threshold(evaluation.getThreshold())
            .triggerTime(System.currentTimeMillis())
            .status(AlertStatus.FIRING)
            .build();

        // 保存告警记录
        alertRepository.save(alert);

        // 发送告警通知
        notificationService.sendAlert(alert);

        log.warn("触发告警: rule={}, value={}, threshold={}",
            rule.getName(), evaluation.getValue(), evaluation.getThreshold());
    }
}

@Service
public class AlertNotificationService {

    @Autowired
    private DingTalkNotifier dingTalkNotifier;

    @Autowired
    private EmailNotifier emailNotifier;

    @Autowired
    private SmsNotifier smsNotifier;

    public void sendAlert(Alert alert) {
        AlertRule rule = alertRuleRepository.findById(alert.getRuleId());
        List<NotificationChannel> channels = rule.getNotificationChannels();

        for (NotificationChannel channel : channels) {
            try {
                switch (channel.getType()) {
                    case DINGTALK:
                        sendDingTalkAlert(alert, channel);
                        break;
                    case EMAIL:
                        sendEmailAlert(alert, channel);
                        break;
                    case SMS:
                        sendSmsAlert(alert, channel);
                        break;
                }
            } catch (Exception e) {
                log.error("发送告警通知失败: channel={}, alert={}", channel.getType(), alert.getId(), e);
            }
        }
    }

    private void sendDingTalkAlert(Alert alert, NotificationChannel channel) {
        DingTalkMessage message = DingTalkMessage.builder()
            .msgtype("markdown")
            .markdown(DingTalkMarkdown.builder()
                .title("🚨 系统告警")
                .text(buildDingTalkMessage(alert))
                .build())
            .build();

        dingTalkNotifier.send(channel.getWebhookUrl(), message);
    }

    private String buildDingTalkMessage(Alert alert) {
        return String.format(
            "### 🚨 系统告警\n\n" +
            "**告警规则**: %s\n\n" +
            "**告警级别**: %s\n\n" +
            "**当前值**: %.2f\n\n" +
            "**阈值**: %.2f\n\n" +
            "**告警时间**: %s\n\n" +
            "**告警详情**: %s\n\n",
            alert.getRuleName(),
            alert.getSeverity().getDisplayName(),
            alert.getValue(),
            alert.getThreshold(),
            DateUtils.formatDateTime(alert.getTriggerTime()),
            alert.getMessage()
        );
    }
}
```

**监控大盘配置**：
```yaml
# Grafana Dashboard配置
dashboards:
  business_overview:
    title: "业务监控总览"
    panels:
      - title: "实时通话量"
        type: "stat"
        query: "rate(call_total[5m])"

      - title: "座席在线数"
        type: "gauge"
        query: "active_agents"

      - title: "通话成功率"
        type: "stat"
        query: "rate(call_total{result='success'}[5m]) / rate(call_total[5m]) * 100"

      - title: "平均通话时长"
        type: "stat"
        query: "rate(call_duration_sum[5m]) / rate(call_duration_count[5m])"

  system_performance:
    title: "系统性能监控"
    panels:
      - title: "API响应时间"
        type: "graph"
        query: "histogram_quantile(0.95, rate(http_request_duration_bucket[5m]))"

      - title: "JVM内存使用率"
        type: "graph"
        query: "jvm_memory_used / jvm_memory_max * 100"

      - title: "数据库连接池"
        type: "graph"
        query: "datasource_active_connections"

  alert_overview:
    title: "告警监控"
    panels:
      - title: "告警统计"
        type: "table"
        query: "alert_total by (severity, status)"
```

**监控体系效果**：
- **监控覆盖率**：100%（所有关键指标）
- **告警响应时间**：平均1分钟
- **告警准确率**：99.9%（误报率<0.1%）
- **故障发现时间**：从30分钟缩短到2分钟
- **系统可用性**：从99.5%提升到99.95%

---

## 总结

第3批面试问题主要涵盖了**架构设计**、**性能优化**、**场景设计**等高级技术领域：

**架构设计能力**：
- 微服务拆分的系统性方法论
- 分布式事务的多种解决方案
- 技术选型的决策思维

**性能优化实践**：
- 多级缓存架构设计
- 缓存一致性保证策略
- 高并发系统优化经验

**场景设计思维**：
- 秒杀系统的完整设计
- 消息队列的选型对比
- 监控告警体系构建

这些答案展现了在**大型分布式系统**设计和优化方面的深度技术能力，体现了6年Java后端开发的技术积累和工程实践经验。