# Java基础常见面试题详解

## 数据类型

### 1. Java中有哪8种基本数据类型？它们的默认值和占用的空间大小？⭐⭐⭐⭐

#### 问题分析

这是Java基础中的核心知识点，考查对基本数据类型的掌握程度。

#### 标准答案

**Java 8种基本数据类型详解：**

```mermaid
flowchart TB
    subgraph main ["Java基本数据类型"]
        A["基本数据类型<br/>8种"]

        A --> B["数字类型<br/>6种"]
        A --> C["字符类型<br/>1种"]
        A --> D["布尔类型<br/>1种"]

        B --> B1["整数类型<br/>4种"]
        B --> B2["浮点类型<br/>2种"]

        B1 --> B11["byte<br/>1字节<br/>-128~127<br/>默认值:0"]
        B1 --> B12["short<br/>2字节<br/>-32768~32767<br/>默认值:0"]
        B1 --> B13["int<br/>4字节<br/>-2^31~2^31-1<br/>默认值:0"]
        B1 --> B14["long<br/>8字节<br/>-2^63~2^63-1<br/>默认值:0L"]

        B2 --> B21["float<br/>4字节<br/>IEEE754标准<br/>默认值:0.0f"]
        B2 --> B22["double<br/>8字节<br/>IEEE754标准<br/>默认值:0.0d"]

        C --> C1["char<br/>2字节<br/>0~65535<br/>默认值:'\\u0000'"]

        D --> D1["boolean<br/>1字节<br/>true/false<br/>默认值:false"]
    end

    classDef root fill:#e1f5fe,stroke:#0277bd
    classDef number fill:#fff3e0,stroke:#f57c00
    classDef character fill:#e8f5e8,stroke:#388e3c
    classDef boolean fill:#fce4ec,stroke:#c2185b

    class A root
    class B number
    class C character
    class D boolean
```

#### 详细对比表

| 数据类型          | 字节数 | 位数 | 取值范围                                               | 默认值   | 包装类型  |
| ----------------- | ------ | ---- | ------------------------------------------------------ | -------- | --------- |
| **byte**    | 1      | 8    | -128 ~ 127                                             | 0        | Byte      |
| **short**   | 2      | 16   | -32,768 ~ 32,767                                       | 0        | Short     |
| **int**     | 4      | 32   | -2,147,483,648 ~ 2,147,483,647                         | 0        | Integer   |
| **long**    | 8      | 64   | -9,223,372,036,854,775,808 ~ 9,223,372,036,854,775,807 | 0L       | Long      |
| **float**   | 4      | 32   | IEEE 754标准                                           | 0.0f     | Float     |
| **double**  | 8      | 64   | IEEE 754标准                                           | 0.0d     | Double    |
| **char**    | 2      | 16   | 0 ~ 65,535 (Unicode)                                   | '\u0000' | Character |
| **boolean** | 1      | 8    | true/false                                             | false    | Boolean   |

#### 代码示例

```java
public class BasicDataTypes {
    // 整数类型
    byte byteVar = 127;                    // 1字节
    short shortVar = 32767;                // 2字节
    int intVar = 2147483647;               // 4字节
    long longVar = 9223372036854775807L;   // 8字节，注意L后缀
  
    // 浮点类型
    float floatVar = 3.14f;                // 4字节，注意f后缀
    double doubleVar = 3.141592653589793;  // 8字节
  
    // 字符类型
    char charVar = 'A';                    // 2字节，Unicode编码
    char unicodeChar = '\u0041';           // Unicode表示的'A'
  
    // 布尔类型
    boolean boolVar = true;                // 1字节
  
    public void demonstrateRanges() {
        System.out.println("byte范围: " + Byte.MIN_VALUE + " ~ " + Byte.MAX_VALUE);
        System.out.println("short范围: " + Short.MIN_VALUE + " ~ " + Short.MAX_VALUE);
        System.out.println("int范围: " + Integer.MIN_VALUE + " ~ " + Integer.MAX_VALUE);
        System.out.println("long范围: " + Long.MIN_VALUE + " ~ " + Long.MAX_VALUE);
        System.out.println("float范围: " + Float.MIN_VALUE + " ~ " + Float.MAX_VALUE);
        System.out.println("double范围: " + Double.MIN_VALUE + " ~ " + Double.MAX_VALUE);
        System.out.println("char范围: " + (int)Character.MIN_VALUE + " ~ " + (int)Character.MAX_VALUE);
    }
}
```

### 2. 包装类型的常量池技术了解么？⭐⭐⭐⭐⭐

#### 问题分析

这是Java内存优化的重要机制，考查对对象缓存和内存管理的理解。

#### 标准答案

**包装类常量池机制：**

#### 常量池范围

```java
public class WrapperConstantPool {
    public static void main(String[] args) {
        // Integer常量池：-128 ~ 127
        Integer a1 = 127;
        Integer a2 = 127;
        System.out.println(a1 == a2);  // true，使用常量池

        Integer b1 = 128;
        Integer b2 = 128;
        System.out.println(b1 == b2);  // false，超出常量池范围

        // Character常量池：0 ~ 127
        Character c1 = 'A';  // ASCII 65
        Character c2 = 'A';
        System.out.println(c1 == c2);  // true

        // Boolean常量池：true/false
        Boolean d1 = true;
        Boolean d2 = true;
        System.out.println(d1 == d2);  // true

        // Byte常量池：-128 ~ 127（全范围）
        Byte e1 = 100;
        Byte e2 = 100;
        System.out.println(e1 == e2);  // true

        // Short常量池：-128 ~ 127
        Short f1 = 100;
        Short f2 = 100;
        System.out.println(f1 == f2);  // true

        // Long常量池：-128 ~ 127
        Long g1 = 100L;
        Long g2 = 100L;
        System.out.println(g1 == g2);  // true

        // Float和Double没有常量池
        Float h1 = 1.0f;
        Float h2 = 1.0f;
        System.out.println(h1 == h2);  // false
    }
}
```

#### 常量池实现原理

```java
// Integer.valueOf()源码分析
public static Integer valueOf(int i) {
    if (i >= IntegerCache.low && i <= IntegerCache.high)
        return IntegerCache.cache[i + (-IntegerCache.low)];
    return new Integer(i);
}

// IntegerCache内部类
private static class IntegerCache {
    static final int low = -128;
    static final int high;
    static final Integer cache[];

    static {
        // high value may be configured by property
        int h = 127;
        String integerCacheHighPropValue =
            sun.misc.VM.getSavedProperty("java.lang.Integer.IntegerCache.high");
        if (integerCacheHighPropValue != null) {
            try {
                int i = parseInt(integerCacheHighPropValue);
                i = Math.max(i, 127);
                // Maximum array size is Integer.MAX_VALUE
                h = Math.min(i, Integer.MAX_VALUE - (-low) -1);
            } catch( NumberFormatException nfe) {
                // If the property cannot be parsed into an int, ignore it.
            }
        }
        high = h;

        cache = new Integer[(high - low) + 1];
        int j = low;
        for(int k = 0; k < cache.length; k++)
            cache[k] = new Integer(j++);

        // range [-128, 127] must be interned (JLS7 5.1.7)
        assert IntegerCache.high >= 127;
    }
}
```

#### 最佳实践

```java
public class WrapperBestPractices {
    public void correctComparison() {
        Integer a = 200;
        Integer b = 200;
      
        // ❌ 错误：使用==比较包装类型
        if (a == b) {
            System.out.println("相等");  // 不会执行
        }
      
        // ✅ 正确：使用equals比较包装类型
        if (a.equals(b)) {
            System.out.println("相等");  // 会执行
        }
      
        // ✅ 正确：使用Objects.equals避免NPE
        if (Objects.equals(a, b)) {
            System.out.println("相等");  // 会执行，且null安全
        }
    }
  
    public void avoidAutoboxing() {
        // ❌ 性能较差：频繁装箱拆箱
        Integer sum = 0;
        for (int i = 0; i < 1000; i++) {
            sum += i;  // 每次循环都有装箱拆箱
        }
      
        // ✅ 性能更好：使用基本类型
        int sum2 = 0;
        for (int i = 0; i < 1000; i++) {
            sum2 += i;  // 纯基本类型运算
        }
    }
}
```

### 3. 为什么要有包装类型？⭐⭐⭐

#### 问题分析

考查对Java类型系统设计的理解。

#### 标准答案

**包装类型存在的原因：**

#### 1. 泛型参数限制

```java
// ❌ 泛型不能使用基本类型
// List<int> numbers = new ArrayList<>();  // 编译错误

// ✅ 泛型必须使用包装类型
List<Integer> numbers = new ArrayList<>();
Map<String, Double> scores = new HashMap<>();
```

#### 2. 集合框架需要

```java
public class CollectionExample {
    public void demonstrateCollections() {
        // 集合只能存储对象，不能存储基本类型
        List<Integer> intList = Arrays.asList(1, 2, 3, 4, 5);
        Set<Boolean> boolSet = new HashSet<>(Arrays.asList(true, false));
      
        // 自动装箱使得使用更方便
        intList.add(6);  // 自动装箱：int -> Integer
        int first = intList.get(0);  // 自动拆箱：Integer -> int
    }
}
```

#### 3. 提供有用的方法

```java
public class WrapperMethods {
    public void demonstrateMethods() {
        // 类型转换
        String str = "123";
        int num = Integer.parseInt(str);
        Integer wrapper = Integer.valueOf(str);
      
        // 进制转换
        String binary = Integer.toBinaryString(10);    // "1010"
        String hex = Integer.toHexString(255);         // "ff"
        String octal = Integer.toOctalString(8);       // "10"
      
        // 比较方法
        int result = Integer.compare(10, 20);  // -1
      
        // 最值获取
        int max = Integer.MAX_VALUE;
        int min = Integer.MIN_VALUE;
      
        // 类型判断
        boolean isDigit = Character.isDigit('5');      // true
        boolean isLetter = Character.isLetter('A');    // true
        boolean isWhitespace = Character.isWhitespace(' '); // true
    }
}
```

#### 4. null值支持

```java
public class NullSupport {
    // 基本类型不能为null
    // int value = null;  // 编译错误
  
    // 包装类型可以为null
    Integer value = null;  // 正确
  
    public Integer calculateScore(Integer base, Integer bonus) {
        if (base == null || bonus == null) {
            return null;  // 可以返回null表示无效结果
        }
        return base + bonus;
    }
}
```

### 4. 什么是自动拆装箱？原理？⭐⭐⭐⭐⭐

#### 问题分析

这是Java语法糖的重要体现，考查对编译器优化的理解。

#### 标准答案

**自动装箱拆箱机制：**

```mermaid
flowchart LR
    A[基本类型] -->|"装箱<br/>valueOf"| B[包装类型]
    B -->|"拆箱<br/>xxxValue"| A

    subgraph box ["装箱过程"]
        C["int i = 10"] --> D["Integer.valueOf(i)"]
        D --> E["Integer obj"]
    end

    subgraph unbox ["拆箱过程"]
        F["Integer obj"] --> G["obj.intValue()"]
        G --> H["int i"]
    end

    classDef primitive fill:#e3f2fd,stroke:#2196f3
    classDef wrapper fill:#fff3e0,stroke:#ff9800
    classDef boxing fill:#e8f5e8,stroke:#4caf50
    classDef unboxing fill:#fce4ec,stroke:#e91e63

    class A primitive
    class B wrapper
    class C,E boxing
    class F,H unboxing
```

#### 装箱拆箱示例

```java
public class AutoBoxingUnboxing {
    public void demonstrateAutoBoxing() {
        // 自动装箱：基本类型 -> 包装类型
        Integer a = 10;        // 等价于 Integer.valueOf(10)
        Double b = 3.14;       // 等价于 Double.valueOf(3.14)
        Boolean c = true;      // 等价于 Boolean.valueOf(true)
      
        // 自动拆箱：包装类型 -> 基本类型
        int x = a;             // 等价于 a.intValue()
        double y = b;          // 等价于 b.doubleValue()
        boolean z = c;         // 等价于 c.booleanValue()
      
        // 混合运算中的自动拆装箱
        Integer num1 = 100;
        Integer num2 = 200;
        Integer sum = num1 + num2;  // 拆箱运算后装箱
        // 等价于：Integer.valueOf(num1.intValue() + num2.intValue())
    }

    // 编译器生成的字节码分析
    public void compiledCode() {
        Integer a = 10;
        // 编译后变成：
        // Integer a = Integer.valueOf(10);

        int b = a;
        // 编译后变成：
        // int b = a.intValue();

        Integer c = a + 5;
        // 编译后变成：
        // Integer c = Integer.valueOf(a.intValue() + 5);
    }
}

#### 性能影响分析
```java
public class PerformanceImpact {
    public void performanceTest() {
        long start, end;

        // 测试1：基本类型运算
        start = System.currentTimeMillis();
        int sum1 = 0;
        for (int i = 0; i < 1000000; i++) {
            sum1 += i;
        }
        end = System.currentTimeMillis();
        System.out.println("基本类型耗时: " + (end - start) + "ms");

        // 测试2：包装类型运算（频繁装拆箱）
        start = System.currentTimeMillis();
        Integer sum2 = 0;
        for (int i = 0; i < 1000000; i++) {
            sum2 += i;  // 每次都有装拆箱操作
        }
        end = System.currentTimeMillis();
        System.out.println("包装类型耗时: " + (end - start) + "ms");
    }
}
```

### 5. 遇到过自动拆箱引发的NPE问题吗？⭐⭐⭐⭐

#### 问题分析

这是实际开发中的常见陷阱，考查对NPE异常的预防能力。

#### 标准答案

**NPE产生的常见场景：**

#### 场景1：数据库查询结果为null

```java
public class DatabaseNPE {
    // ❌ 危险：可能NPE
    public void dangerousCode() {
        // 假设数据库查询返回的Integer可能为null
        Integer count = queryCountFromDatabase();  // 可能返回null
        int total = count + 10;  // NPE！count.intValue()时空指针
    }

    // ✅ 安全：null检查
    public void safeCode() {
        Integer count = queryCountFromDatabase();
        int total = (count != null ? count : 0) + 10;

        // 或者使用Optional
        int total2 = Optional.ofNullable(count).orElse(0) + 10;
    }

    private Integer queryCountFromDatabase() {
        // 模拟数据库查询，可能返回null
        return Math.random() > 0.5 ? 100 : null;
    }
}
```

#### 场景2：三目运算符的陷阱

```java
public class TernaryOperatorNPE {
    public void demonstrateTernaryNPE() {
        Boolean flag = null;
        Integer a = 10;
        Integer b = 20;

        // ❌ 危险：NPE陷阱
        // 当条件表达式为包装类型null时，会自动拆箱导致NPE
        try {
            Integer result = flag ? a : b;  // NPE！flag.booleanValue()
        } catch (NullPointerException e) {
            System.out.println("三目运算符NPE: " + e.getMessage());
        }

        // ✅ 安全：明确null检查
        Integer result = (flag != null && flag) ? a : b;

        // 另一个陷阱：类型不匹配时的自动拆箱
        Integer x = 10;
        Double y = 20.0;
        // Number result2 = true ? x : y;  // 会导致自动拆箱再装箱
    }

    public void anotherTernaryTrap() {
        Integer a = null;
        int b = 10;

        // ❌ 危险：当Integer为null时NPE
        try {
            int result = true ? a : b;  // NPE！a.intValue()
        } catch (NullPointerException e) {
            System.out.println("类型混合NPE: " + e.getMessage());
        }

        // ✅ 安全：保持类型一致
        Integer result = true ? a : Integer.valueOf(b);
    }
}
```

#### 场景3：集合操作中的NPE

```java
public class CollectionNPE {
    public void demonstrateCollectionNPE() {
        List<Integer> numbers = Arrays.asList(1, 2, null, 4, 5);

        // ❌ 危险：遍历时可能NPE
        try {
            int sum = 0;
            for (Integer num : numbers) {
                sum += num;  // 当num为null时NPE
            }
        } catch (NullPointerException e) {
            System.out.println("集合遍历NPE: " + e.getMessage());
        }

        // ✅ 安全：null检查
        int sum = 0;
        for (Integer num : numbers) {
            if (num != null) {
                sum += num;
            }
        }

        // ✅ 使用Stream API安全处理
        int sum2 = numbers.stream()
            .filter(Objects::nonNull)
            .mapToInt(Integer::intValue)
            .sum();
    }
}
```

#### 最佳实践

```java
public class NPEPrevention {
    // 1. 使用Optional处理可能为null的值
    public Optional<Integer> safeCalculate(Integer a, Integer b) {
        if (a == null || b == null) {
            return Optional.empty();
        }
        return Optional.of(a + b);
    }

    // 2. 使用Objects.equals避免NPE
    public boolean safeEquals(Integer a, Integer b) {
        return Objects.equals(a, b);  // null安全
    }

    // 3. 使用默认值
    public int safeAdd(Integer a, Integer b) {
        int safeA = a != null ? a : 0;
        int safeB = b != null ? b : 0;
        return safeA + safeB;
    }

    // 4. 使用@Nullable和@NonNull注解
    public int processValue(@Nullable Integer input) {
        if (input == null) {
            throw new IllegalArgumentException("Input cannot be null");
        }
        return input * 2;
    }
}
```

## 面向对象

### 6. String、StringBuffer和StringBuilder的区别？String为什么是不可变的？⭐⭐⭐⭐⭐

#### 问题分析

这是Java中字符串处理的核心知识点，考查对不可变性、线程安全性和性能的理解。

#### 标准答案

**String类不可变性原理：**

```mermaid
flowchart TB
    subgraph immutable ["String不可变性"]
        A[String对象] --> B["final char[] value"]
        A --> C[没有修改方法]
        A --> D[所有方法返回新对象]

        B --> B1[数组引用final]
        B --> B2[数组内容不可修改]
        B --> B3[类本身final]
    end

    subgraph comparison ["字符串操作对比"]
        E["String<br/>不可变<br/>线程安全<br/>性能差"]
        F["StringBuffer<br/>可变<br/>线程安全<br/>synchronized"]
        G["StringBuilder<br/>可变<br/>非线程安全<br/>性能最好"]

        E -->|频繁修改| H["创建大量对象<br/>内存浪费"]
        F -->|同步开销| I["性能中等<br/>多线程安全"]
        G -->|无同步| J["性能最佳<br/>单线程使用"]
    end

    classDef immutableClass fill:#ffcdd2,stroke:#d32f2f
    classDef stringClass fill:#ffcdd2,stroke:#d32f2f
    classDef bufferClass fill:#fff3e0,stroke:#f57c00
    classDef builderClass fill:#c8e6c9,stroke:#388e3c

    class A immutableClass
    class E stringClass
    class F bufferClass
    class G builderClass
```

#### String不可变性源码分析

```java
public final class String implements java.io.Serializable, Comparable<String>, CharSequence {
    // 1. 类被final修饰，不能被继承

    // 2. 字符数组被final修饰，引用不可变
    private final char value[];

    // 3. 没有提供修改value数组的方法

    // 4. 所有"修改"操作都返回新的String对象
    public String substring(int beginIndex) {
        if (beginIndex < 0) {
            throw new StringIndexOutOfBoundsException(beginIndex);
        }
        int subLen = value.length - beginIndex;
        if (subLen < 0) {
            throw new StringIndexOutOfBoundsException(subLen);
        }
        return (beginIndex == 0) ? this : new String(value, beginIndex, subLen);
    }

    public String concat(String str) {
        int otherLen = str.length();
        if (otherLen == 0) {
            return this;
        }
        int len = value.length;
        char buf[] = Arrays.copyOf(value, len + otherLen);
        str.getChars(buf, len);
        return new String(buf, true);  // 返回新对象
    }
}
```

#### 三种字符串类对比

```java
public class StringComparison {
    public void demonstrateDifferences() {
        // 1. String：不可变，每次操作创建新对象
        String str = "Hello";
        str += " World";  // 创建新的String对象
        str += "!";       // 又创建新的String对象

        // 2. StringBuffer：可变，线程安全
        StringBuffer sb = new StringBuffer("Hello");
        sb.append(" World");  // 在原对象上修改
        sb.append("!");       // 在原对象上修改

        // 3. StringBuilder：可变，非线程安全，性能最好
        StringBuilder sbd = new StringBuilder("Hello");
        sbd.append(" World");  // 在原对象上修改
        sbd.append("!");       // 在原对象上修改
    }

    // 性能测试
    public void performanceTest() {
        int iterations = 10000;

        // String拼接性能测试
        long start = System.currentTimeMillis();
        String str = "";
        for (int i = 0; i < iterations; i++) {
            str += "a";  // 每次创建新对象，O(n²)复杂度
        }
        long stringTime = System.currentTimeMillis() - start;

        // StringBuffer性能测试
        start = System.currentTimeMillis();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < iterations; i++) {
            sb.append("a");  // 在原对象修改，O(n)复杂度
        }
        long bufferTime = System.currentTimeMillis() - start;

        // StringBuilder性能测试
        start = System.currentTimeMillis();
        StringBuilder sbd = new StringBuilder();
        for (int i = 0; i < iterations; i++) {
            sbd.append("a");  // 在原对象修改，无同步开销
        }
        long builderTime = System.currentTimeMillis() - start;

        System.out.println("String耗时: " + stringTime + "ms");
        System.out.println("StringBuffer耗时: " + bufferTime + "ms");
        System.out.println("StringBuilder耗时: " + builderTime + "ms");
    }
}
```

#### 线程安全性对比

```java
public class ThreadSafetyComparison {
    private StringBuffer stringBuffer = new StringBuffer();
    private StringBuilder stringBuilder = new StringBuilder();

    // StringBuffer是线程安全的
    public void testStringBuffer() {
        // 多个线程同时操作StringBuffer是安全的
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    stringBuffer.append("a");  // synchronized方法，线程安全
                }
            }).start();
        }
    }

    // StringBuilder不是线程安全的
    public void testStringBuilder() {
        // 多个线程同时操作StringBuilder可能出现问题
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    stringBuilder.append("a");  // 非synchronized，非线程安全
                }
            }).start();
        }
    }
}
```

#### 使用场景建议

```java
public class UsageRecommendations {
    // 1. 字符串不变或少量修改：使用String
    public String getWelcomeMessage(String name) {
        return "Hello, " + name + "!";  // 简单拼接，使用String
    }

    // 2. 频繁修改，多线程环境：使用StringBuffer
    public String buildLogMessage(List<String> events) {
        StringBuffer buffer = new StringBuffer();
        for (String event : events) {
            buffer.append("[").append(new Date()).append("] ")
                  .append(event).append("\n");
        }
        return buffer.toString();
    }

    // 3. 频繁修改，单线程环境：使用StringBuilder（推荐）
    public String buildHtml(List<String> items) {
        StringBuilder html = new StringBuilder();
        html.append("<ul>");
        for (String item : items) {
            html.append("<li>").append(item).append("</li>");
        }
        html.append("</ul>");
        return html.toString();
    }

    // 4. 编译器优化：简单拼接会自动优化为StringBuilder
    public String compilerOptimization() {
        String a = "Hello";
        String b = "World";
        // 编译器会优化为StringBuilder
        return a + " " + b + "!";
    }
}
```

### 7. 重载和重写的区别？⭐⭐⭐⭐⭐

#### 问题分析

这是面向对象编程的核心概念，考查对多态性的理解。

#### 标准答案

**重载(Overload)与重写(Override)对比：**

```mermaid
graph TB
    subgraph "重载 Overload"
        A[同一个类中] --> A1[方法名相同]
        A1 --> A2[参数列表不同]
        A2 --> A3[编译时多态]
        A3 --> A4[静态绑定]

        A5[参数个数不同<br/>参数类型不同<br/>参数顺序不同]
    end

    subgraph "重写 Override"
        B[继承关系中] --> B1[方法签名完全相同]
        B1 --> B2[子类重新实现]
        B2 --> B3[运行时多态]
        B3 --> B4[动态绑定]

        B5[返回值类型相同或协变<br/>访问权限不能更严格<br/>异常范围不能扩大]
    end

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style A5 fill:#c8e6c9
    style B5 fill:#ffcdd2
```

#### 详细对比表

| 特性                 | 重载(Overload) | 重写(Override)       |
| -------------------- | -------------- | -------------------- |
| **发生范围**   | 同一个类中     | 父子类之间           |
| **方法名**     | 必须相同       | 必须相同             |
| **参数列表**   | 必须不同       | 必须相同             |
| **返回值类型** | 可以不同       | 相同或协变返回类型   |
| **访问修饰符** | 可以不同       | 不能比父类更严格     |
| **异常**       | 可以不同       | 不能抛出新的检查异常 |
| **绑定时机**   | 编译时(静态)   | 运行时(动态)         |
| **多态类型**   | 编译时多态     | 运行时多态           |

#### 重载示例

```java
public class OverloadExample {
    // 1. 参数个数不同
    public void print() {
        System.out.println("无参数");
    }

    public void print(String message) {
        System.out.println("一个参数: " + message);
    }

    public void print(String message, int count) {
        System.out.println("两个参数: " + message + ", " + count);
    }

    // 2. 参数类型不同
    public void process(int number) {
        System.out.println("处理整数: " + number);
    }

    public void process(double number) {
        System.out.println("处理小数: " + number);
    }

    public void process(String text) {
        System.out.println("处理字符串: " + text);
    }

    // 3. 参数顺序不同
    public void combine(String text, int number) {
        System.out.println("字符串在前: " + text + number);
    }

    public void combine(int number, String text) {
        System.out.println("数字在前: " + number + text);
    }

    // 4. 返回值类型可以不同（但仅凭返回值不同不能重载）
    public int calculate(int a, int b) {
        return a + b;
    }

    public double calculate(double a, double b) {
        return a + b;
    }

    // ❌ 错误：仅返回值不同不能构成重载
    // public double calculate(int a, int b) {  // 编译错误
    //     return a + b;
    // }
}
```

#### 重写示例

```java
// 父类
class Animal {
    public void makeSound() {
        System.out.println("动物发出声音");
    }

    public Animal getChild() {
        return new Animal();
    }

    protected void sleep() {
        System.out.println("动物睡觉");
    }

    public void eat() throws Exception {
        System.out.println("动物吃东西");
    }
}

// 子类
class Dog extends Animal {
    // 1. 基本重写
    @Override
    public void makeSound() {
        System.out.println("狗汪汪叫");
    }

    // 2. 协变返回类型（返回值类型可以是父类返回类型的子类）
    @Override
    public Dog getChild() {  // Animal -> Dog，协变返回
        return new Dog();
    }

    // 3. 访问权限可以更宽松，不能更严格
    @Override
    public void sleep() {  // protected -> public，更宽松
        System.out.println("狗睡觉");
    }

    // 4. 异常范围不能扩大
    @Override
    public void eat() {  // 不抛异常或抛出更小范围的异常
        System.out.println("狗吃骨头");
    }

    // ❌ 错误示例：
    // @Override
    // private void makeSound() { }  // 访问权限更严格，编译错误

    // @Override
    // public void eat() throws IOException { }  // 抛出新异常，编译错误
}
```

#### 编译时多态 vs 运行时多态

```java
public class PolymorphismDemo {
    public static void main(String[] args) {
        // 编译时多态（重载）
        OverloadExample example = new OverloadExample();
        example.print();           // 编译时确定调用哪个方法
        example.print("Hello");    // 编译时确定调用哪个方法
        example.print("Hello", 5); // 编译时确定调用哪个方法

        // 运行时多态（重写）
        Animal animal1 = new Animal();
        Animal animal2 = new Dog();  // 向上转型

        animal1.makeSound();  // 运行时确定：调用Animal的方法
        animal2.makeSound();  // 运行时确定：调用Dog的方法

        // 动态绑定演示
        Animal[] animals = {new Animal(), new Dog()};
        for (Animal animal : animals) {
            animal.makeSound();  // 运行时根据实际对象类型调用相应方法
        }
    }
}
```

#### 重载解析规则

```java
public class OverloadResolution {
    public void method(int i) {
        System.out.println("int");
    }

    public void method(long l) {
        System.out.println("long");
    }

    public void method(Integer i) {
        System.out.println("Integer");
    }

    public void method(int... args) {
        System.out.println("varargs");
    }

    public static void main(String[] args) {
        OverloadResolution obj = new OverloadResolution();

        obj.method(1);        // 输出: int (精确匹配)
        obj.method(1L);       // 输出: long (精确匹配)
        obj.method(Integer.valueOf(1)); // 输出: Integer (精确匹配)

        // 重载解析优先级：
        // 1. 精确匹配
        // 2. 基本类型自动提升 (byte->short->int->long->float->double)
        // 3. 自动装箱/拆箱
        // 4. 可变参数

        byte b = 1;
        obj.method(b);        // 输出: int (byte提升为int)

        short s = 1;
        obj.method(s);        // 输出: int (short提升为int)
    }
}
```

### 8. ==和equals()的区别？⭐⭐⭐⭐⭐

#### 问题分析

这是Java中对象比较的核心知识点，考查对引用和值比较的理解。

#### 标准答案

**==和equals()比较机制：**

```mermaid
flowchart TB
    subgraph operator ["== 操作符比较机制"]
        A["== 操作符"] --> A1[基本类型比较值]
        A --> A2[引用类型比较地址]

        A1 --> A3["示例: int a=5, b=5<br/>结果: a==b 返回 true"]
        A2 --> A4["示例: String s1=new String('hello')<br/>String s2=new String('hello')<br/>结果: s1==s2 返回 false"]
    end

    subgraph equals ["equals() 方法比较机制"]
        B["equals() 方法"] --> B1[Object默认实现]
        B --> B2[重写后的实现]

        B1 --> B3["等同于==操作符<br/>比较内存地址"]
        B2 --> B4["比较对象内容<br/>如String、Integer等"]
    end

    classDef operatorStyle fill:#e3f2fd,stroke:#2196f3
    classDef equalsStyle fill:#fff3e0,stroke:#ff9800
    classDef correctResult fill:#c8e6c9,stroke:#4caf50
    classDef incorrectResult fill:#ffcdd2,stroke:#f44336

    class A,A1,A2 operatorStyle
    class B,B1,B2 equalsStyle
    class A3,B4 correctResult
    class A4,B3 incorrectResult
```

#### 基本类型vs引用类型比较

```java
public class ComparisonDemo {
    public void demonstrateBasicTypes() {
        // 基本类型：== 比较值
        int a = 100;
        int b = 100;
        System.out.println(a == b);  // true，比较值

        double x = 3.14;
        double y = 3.14;
        System.out.println(x == y);  // true，比较值

        // 注意浮点数精度问题
        double d1 = 0.1 + 0.2;
        double d2 = 0.3;
        System.out.println(d1 == d2);  // false！精度问题
        System.out.println(Math.abs(d1 - d2) < 1e-10);  // true，正确比较方式
    }

    public void demonstrateReferenceTypes() {
        // 引用类型：== 比较内存地址
        String s1 = new String("hello");
        String s2 = new String("hello");
        String s3 = "hello";
        String s4 = "hello";

        System.out.println(s1 == s2);   // false，不同对象，不同地址
        System.out.println(s1 == s3);   // false，堆对象 vs 常量池对象
        System.out.println(s3 == s4);   // true，都指向常量池中同一对象

        System.out.println(s1.equals(s2));  // true，内容相同
        System.out.println(s1.equals(s3));  // true，内容相同

        // Integer包装类型
        Integer i1 = new Integer(100);
        Integer i2 = new Integer(100);
        Integer i3 = 100;  // 自动装箱，使用缓存
        Integer i4 = 100;  // 自动装箱，使用缓存

        System.out.println(i1 == i2);   // false，不同对象
        System.out.println(i1 == i3);   // false，new对象 vs 缓存对象
        System.out.println(i3 == i4);   // true，都使用缓存中同一对象

        System.out.println(i1.equals(i2));  // true，内容相同
    }
}
```

#### Object.equals()默认实现

```java
// Object类中equals()的默认实现
public boolean equals(Object obj) {
    return (this == obj);  // 默认就是比较内存地址
}

// 未重写equals()的类
class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // 没有重写equals()方法
}

public class DefaultEqualsDemo {
    public static void main(String[] args) {
        Person p1 = new Person("张三", 25);
        Person p2 = new Person("张三", 25);

        System.out.println(p1 == p2);        // false，不同对象
        System.out.println(p1.equals(p2));   // false，使用Object默认实现
    }
}
```

#### 正确重写equals()方法

```java
class Student {
    private String name;
    private int age;
    private String studentId;

    public Student(String name, int age, String studentId) {
        this.name = name;
        this.age = age;
        this.studentId = studentId;
    }

    // 正确重写equals()方法
    @Override
    public boolean equals(Object obj) {
        // 1. 检查是否为同一个对象引用
        if (this == obj) {
            return true;
        }

        // 2. 检查是否为null
        if (obj == null) {
            return false;
        }

        // 3. 检查类型是否相同
        if (getClass() != obj.getClass()) {
            return false;
        }

        // 4. 类型转换
        Student student = (Student) obj;

        // 5. 比较关键字段
        return Objects.equals(name, student.name) &&
               age == student.age &&
               Objects.equals(studentId, student.studentId);
    }

    // 重写equals()必须同时重写hashCode()
    @Override
    public int hashCode() {
        return Objects.hash(name, age, studentId);
    }

    // 为了调试方便，重写toString()
    @Override
    public String toString() {
        return "Student{name='" + name + "', age=" + age + ", studentId='" + studentId + "'}";
    }
}
```

#### equals()方法的约定

```java
public class EqualsContract {
    /**
     * equals()方法必须满足的约定：
     *
     * 1. 自反性(Reflexive)：x.equals(x) 必须返回true
     * 2. 对称性(Symmetric)：x.equals(y) 和 y.equals(x) 必须返回相同结果
     * 3. 传递性(Transitive)：如果x.equals(y)和y.equals(z)都返回true，那么x.equals(z)也必须返回true
     * 4. 一致性(Consistent)：多次调用x.equals(y)必须返回相同结果（前提是对象没有被修改）
     * 5. 非空性(Non-null)：x.equals(null) 必须返回false
     */

    public void demonstrateContract() {
        Student s1 = new Student("张三", 20, "001");
        Student s2 = new Student("张三", 20, "001");
        Student s3 = new Student("张三", 20, "001");

        // 1. 自反性
        System.out.println(s1.equals(s1));  // true

        // 2. 对称性
        System.out.println(s1.equals(s2));  // true
        System.out.println(s2.equals(s1));  // true

        // 3. 传递性
        System.out.println(s1.equals(s2));  // true
        System.out.println(s2.equals(s3));  // true
        System.out.println(s1.equals(s3));  // true

        // 4. 一致性
        System.out.println(s1.equals(s2));  // 多次调用结果相同
        System.out.println(s1.equals(s2));  // true

        // 5. 非空性
        System.out.println(s1.equals(null)); // false
    }
}
```

#### 常见错误和最佳实践

```java
public class EqualsBestPractices {
    // ❌ 错误：重写equals()但不重写hashCode()
    class BadExample {
        private String name;

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            BadExample that = (BadExample) obj;
            return Objects.equals(name, that.name);
        }
        // 缺少hashCode()重写，违反了equals-hashCode约定
    }

    // ✅ 正确：同时重写equals()和hashCode()
    class GoodExample {
        private String name;

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            GoodExample that = (GoodExample) obj;
            return Objects.equals(name, that.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name);
        }
    }

    // 使用Objects.equals()避免NPE
    public boolean safeEquals(String s1, String s2) {
        // ❌ 可能NPE
        // return s1.equals(s2);

        // ✅ null安全
        return Objects.equals(s1, s2);
    }
}
```

## 反射&注解&泛型

### 9. Java反射？反射有什么优点/缺点？⭐⭐⭐⭐⭐

#### 问题分析

反射是Java的核心特性，是框架开发的基础，考查对动态编程的理解。

#### 标准答案

**Java反射机制原理：**

```mermaid
graph TB
    subgraph "反射核心类"
        A[Class对象] --> A1[类的元数据]
        A --> A2[获取构造器]
        A --> A3[获取方法]
        A --> A4[获取字段]
        A --> A5[获取注解]

        A2 --> B[Constructor]
        A3 --> C[Method]
        A4 --> D[Field]
        A5 --> E[Annotation]
    end

    subgraph "反射应用场景"
        F[框架开发] --> F1[Spring IoC容器]
        F --> F2[ORM框架]
        F --> F3[序列化框架]

        G[动态代理] --> G1[AOP切面编程]
        G --> G2[RPC远程调用]

        H[配置驱动] --> H1[注解处理]
        H --> H2[XML配置解析]
    end

    subgraph "性能影响"
        I[优点] --> I1[灵活性高<br/>动态性强<br/>框架基础]
        J[缺点] --> J1[性能开销<br/>安全风险<br/>代码复杂]
    end

    style A fill:#e3f2fd
    style F fill:#fff3e0
    style G fill:#e8f5e8
    style I fill:#c8e6c9
    style J fill:#ffcdd2
```

#### 反射基本用法

```java
public class ReflectionBasics {
    public static void main(String[] args) throws Exception {
        // 1. 获取Class对象的三种方式
        Class<?> clazz1 = String.class;                    // 类字面量
        Class<?> clazz2 = "hello".getClass();              // 对象方法
        Class<?> clazz3 = Class.forName("java.lang.String"); // 类名字符串

        System.out.println(clazz1 == clazz2);  // true，同一个Class对象
        System.out.println(clazz2 == clazz3);  // true，同一个Class对象

        // 2. 获取类信息
        demonstrateClassInfo();

        // 3. 操作构造器
        demonstrateConstructors();

        // 4. 操作方法
        demonstrateMethods();

        // 5. 操作字段
        demonstrateFields();
    }

    public static void demonstrateClassInfo() throws Exception {
        Class<?> clazz = ArrayList.class;

        // 类名信息
        System.out.println("简单类名: " + clazz.getSimpleName());     // ArrayList
        System.out.println("完整类名: " + clazz.getName());           // java.util.ArrayList
        System.out.println("规范类名: " + clazz.getCanonicalName()); // java.util.ArrayList

        // 继承关系
        System.out.println("父类: " + clazz.getSuperclass());        // AbstractList
        System.out.println("接口: " + Arrays.toString(clazz.getInterfaces()));

        // 修饰符
        int modifiers = clazz.getModifiers();
        System.out.println("是否public: " + Modifier.isPublic(modifiers));
        System.out.println("是否final: " + Modifier.isFinal(modifiers));
        System.out.println("是否abstract: " + Modifier.isAbstract(modifiers));
    }

    public static void demonstrateConstructors() throws Exception {
        Class<?> clazz = ArrayList.class;

        // 获取所有public构造器
        Constructor<?>[] constructors = clazz.getConstructors();
        for (Constructor<?> constructor : constructors) {
            System.out.println("构造器: " + constructor);
            System.out.println("参数类型: " + Arrays.toString(constructor.getParameterTypes()));
        }

        // 获取特定构造器并创建对象
        Constructor<?> constructor = clazz.getConstructor(int.class);
        Object arrayList = constructor.newInstance(10);
        System.out.println("创建的对象: " + arrayList);
    }

    public static void demonstrateMethods() throws Exception {
        Class<?> clazz = ArrayList.class;
        Object arrayList = clazz.newInstance();

        // 获取特定方法
        Method addMethod = clazz.getMethod("add", Object.class);
        Method sizeMethod = clazz.getMethod("size");

        // 调用方法
        addMethod.invoke(arrayList, "Hello");
        addMethod.invoke(arrayList, "World");

        Object size = sizeMethod.invoke(arrayList);
        System.out.println("列表大小: " + size);  // 2

        // 获取所有public方法
        Method[] methods = clazz.getMethods();
        System.out.println("方法总数: " + methods.length);
    }

    public static void demonstrateFields() throws Exception {
        // 自定义类演示字段操作
        Person person = new Person("张三", 25);
        Class<?> clazz = person.getClass();

        // 获取所有字段（包括private）
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);  // 设置可访问private字段
            System.out.println(field.getName() + " = " + field.get(person));
        }

        // 修改private字段
        Field nameField = clazz.getDeclaredField("name");
        nameField.setAccessible(true);
        nameField.set(person, "李四");
        System.out.println("修改后的姓名: " + nameField.get(person));
    }
}

class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public void sayHello() {
        System.out.println("Hello, I'm " + name);
    }

    private void secretMethod() {
        System.out.println("这是私有方法");
    }
}
```

#### 反射在框架中的应用

```java
// 模拟Spring IoC容器的简单实现
public class SimpleIoCContainer {
    private Map<String, Object> beans = new HashMap<>();

    // 根据类名创建Bean
    public void registerBean(String beanName, String className) throws Exception {
        Class<?> clazz = Class.forName(className);
        Object bean = createBean(clazz);
        beans.put(beanName, bean);
    }

    // 使用反射创建对象并注入依赖
    private Object createBean(Class<?> clazz) throws Exception {
        // 1. 创建对象
        Object bean = clazz.newInstance();

        // 2. 注入依赖（简化版本）
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Autowired.class)) {
                field.setAccessible(true);
                String beanName = field.getType().getSimpleName().toLowerCase();
                Object dependency = beans.get(beanName);
                if (dependency != null) {
                    field.set(bean, dependency);
                }
            }
        }

        return bean;
    }

    public Object getBean(String beanName) {
        return beans.get(beanName);
    }
}

// 自定义注解
@interface Autowired {
}

// 示例服务类
class UserService {
    @Autowired
    private UserRepository userRepository;

    public void saveUser(String name) {
        if (userRepository != null) {
            userRepository.save(name);
        }
    }
}

class UserRepository {
    public void save(String name) {
        System.out.println("保存用户: " + name);
    }
}
```

#### 动态代理示例

```java
// JDK动态代理
public class DynamicProxyExample {
    interface UserService {
        void saveUser(String name);
        String getUser(String id);
    }

    static class UserServiceImpl implements UserService {
        @Override
        public void saveUser(String name) {
            System.out.println("保存用户: " + name);
        }

        @Override
        public String getUser(String id) {
            return "用户" + id;
        }
    }

    // 代理处理器
    static class LoggingInvocationHandler implements InvocationHandler {
        private Object target;

        public LoggingInvocationHandler(Object target) {
            this.target = target;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            System.out.println("方法调用前: " + method.getName());
            long start = System.currentTimeMillis();

            Object result = method.invoke(target, args);

            long end = System.currentTimeMillis();
            System.out.println("方法调用后: " + method.getName() + ", 耗时: " + (end - start) + "ms");

            return result;
        }
    }

    public static void main(String[] args) {
        UserService target = new UserServiceImpl();

        // 创建代理对象
        UserService proxy = (UserService) Proxy.newProxyInstance(
            UserService.class.getClassLoader(),
            new Class[]{UserService.class},
            new LoggingInvocationHandler(target)
        );

        // 使用代理对象
        proxy.saveUser("张三");
        String user = proxy.getUser("001");
        System.out.println(user);
    }
}
```

#### 反射的优缺点

```java
public class ReflectionProsAndCons {
    /**
     * 优点：
     * 1. 灵活性：运行时动态获取类信息和调用方法
     * 2. 通用性：编写通用的框架和工具
     * 3. 可扩展性：支持插件化架构
     * 4. 配置驱动：基于配置文件或注解的编程
     */

    /**
     * 缺点：
     * 1. 性能开销：反射调用比直接调用慢10-100倍
     * 2. 安全风险：可以访问私有成员，破坏封装性
     * 3. 代码复杂：增加代码复杂度，难以维护
     * 4. 编译时检查：失去编译时类型检查，运行时才发现错误
     */

    // 性能对比测试
    public void performanceComparison() throws Exception {
        Person person = new Person("张三", 25);
        Method sayHelloMethod = Person.class.getMethod("sayHello");

        int iterations = 1000000;

        // 直接调用
        long start = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            person.sayHello();
        }
        long directTime = System.currentTimeMillis() - start;

        // 反射调用
        start = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            sayHelloMethod.invoke(person);
        }
        long reflectionTime = System.currentTimeMillis() - start;

        System.out.println("直接调用耗时: " + directTime + "ms");
        System.out.println("反射调用耗时: " + reflectionTime + "ms");
        System.out.println("性能差异: " + (reflectionTime / (double) directTime) + "倍");
    }
}
```

### 10. 谈谈对Java注解的理解，解决了什么问题？⭐⭐⭐

#### 问题分析

注解是Java的重要特性，是现代框架的基础，考查对元编程的理解。

#### 标准答案

**Java注解机制：**

```mermaid
flowchart TB
    subgraph classification ["注解分类"]
        A[Java注解] --> A1[标准注解]
        A --> A2[元注解]
        A --> A3[自定义注解]

        A1 --> A11["@Override<br/>@Deprecated<br/>@SuppressWarnings"]
        A2 --> A21["@Target<br/>@Retention<br/>@Documented<br/>@Inherited"]
        A3 --> A31["业务注解<br/>框架注解"]
    end

    subgraph processing ["注解处理"]
        B[编译时处理] --> B1["注解处理器<br/>APT"]
        B --> B2["代码生成<br/>编译检查"]

        C[运行时处理] --> C1[反射获取注解]
        C --> C2["动态行为<br/>配置驱动"]
    end

    subgraph problems ["解决的问题"]
        D[配置外部化] --> D1["XML配置 → 注解配置"]
        D --> D2[减少样板代码]

        E[元数据描述] --> E1["类/方法/字段描述"]
        E --> E2[编译时验证]

        F[框架集成] --> F1[依赖注入]
        F --> F2[AOP切面]
        F --> F3[ORM映射]
    end

    classDef annotation fill:#e3f2fd,stroke:#2196f3
    classDef compile fill:#fff3e0,stroke:#ff9800
    classDef runtime fill:#e8f5e8,stroke:#4caf50
    classDef solution fill:#c8e6c9,stroke:#388e3c

    class A annotation
    class B compile
    class C runtime
    class D solution
```

#### 注解基础语法

```java
// 1. 标准注解使用
public class StandardAnnotations {
    @Override
    public String toString() {  // 编译器检查是否正确重写
        return "StandardAnnotations";
    }

    @Deprecated
    public void oldMethod() {   // 标记为过时方法
        System.out.println("这是过时的方法");
    }

    @SuppressWarnings("unchecked")
    public void suppressWarnings() {  // 抑制编译器警告
        List list = new ArrayList();  // 原始类型，但抑制警告
        list.add("item");
    }
}

// 2. 自定义注解
@Target(ElementType.METHOD)           // 只能用于方法
@Retention(RetentionPolicy.RUNTIME)   // 运行时保留
@Documented                           // 包含在JavaDoc中
public @interface LogExecution {
    String value() default "";         // 注解参数
    boolean enabled() default true;
    int timeout() default 5000;
}

// 3. 复杂注解示例
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited                            // 子类继承此注解
public @interface Permission {
    String[] roles() default {};       // 数组参数
    PermissionLevel level() default PermissionLevel.READ;
    Class<? extends Validator> validator() default DefaultValidator.class;
}

enum PermissionLevel {
    READ, WRITE, ADMIN
}

interface Validator {
    boolean validate(Object obj);
}

class DefaultValidator implements Validator {
    @Override
    public boolean validate(Object obj) {
        return true;
    }
}
```

#### 注解处理器示例

```java
// 运行时注解处理
public class AnnotationProcessor {

    // 使用注解的类
    @Permission(roles = {"admin", "user"}, level = PermissionLevel.WRITE)
    public static class UserService {

        @LogExecution(value = "保存用户", timeout = 3000)
        public void saveUser(String name) {
            System.out.println("保存用户: " + name);
        }

        @LogExecution(value = "删除用户", enabled = false)
        public void deleteUser(String id) {
            System.out.println("删除用户: " + id);
        }
    }

    // 注解处理逻辑
    public static void processAnnotations(Class<?> clazz) {
        // 处理类级别注解
        if (clazz.isAnnotationPresent(Permission.class)) {
            Permission permission = clazz.getAnnotation(Permission.class);
            System.out.println("类权限: " + Arrays.toString(permission.roles()));
            System.out.println("权限级别: " + permission.level());
        }

        // 处理方法级别注解
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(LogExecution.class)) {
                LogExecution logExecution = method.getAnnotation(LogExecution.class);
                System.out.println("方法: " + method.getName());
                System.out.println("描述: " + logExecution.value());
                System.out.println("启用: " + logExecution.enabled());
                System.out.println("超时: " + logExecution.timeout());
                System.out.println("---");
            }
        }
    }

    public static void main(String[] args) {
        processAnnotations(UserService.class);
    }
}
```

#### 注解解决的问题

```java
// 问题1：配置外部化 - 从XML到注解
public class ConfigurationEvolution {

    // 传统XML配置方式（Spring早期）
    /*
    <bean id="userService" class="com.example.UserService">
        <property name="userRepository" ref="userRepository"/>
        <property name="cacheEnabled" value="true"/>
    </bean>
    */

    // 现代注解配置方式
    @Component
    @Scope("singleton")
    public class UserService {
        @Autowired
        private UserRepository userRepository;

        @Value("${cache.enabled:true}")
        private boolean cacheEnabled;

        @PostConstruct
        public void init() {
            System.out.println("UserService初始化完成");
        }

        @PreDestroy
        public void destroy() {
            System.out.println("UserService销毁");
        }
    }
}

// 问题2：减少样板代码
public class BoilerplateReduction {

    // 传统方式：大量样板代码
    public class TraditionalUser {
        private String name;
        private int age;
        private String email;

        public TraditionalUser() {}

        public TraditionalUser(String name, int age, String email) {
            this.name = name;
            this.age = age;
            this.email = email;
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public int getAge() { return age; }
        public void setAge(int age) { this.age = age; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TraditionalUser that = (TraditionalUser) o;
            return age == that.age &&
                   Objects.equals(name, that.name) &&
                   Objects.equals(email, that.email);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, age, email);
        }

        @Override
        public String toString() {
            return "TraditionalUser{name='" + name + "', age=" + age + ", email='" + email + "'}";
        }
    }

    // 使用Lombok注解：大幅减少代码
    @Data                    // 生成getter/setter/equals/hashCode/toString
    @NoArgsConstructor       // 生成无参构造器
    @AllArgsConstructor      // 生成全参构造器
    @Builder                 // 生成建造者模式
    public class ModernUser {
        private String name;
        private int age;
        private String email;
    }

    // 使用示例
    public void demonstrateUsage() {
        // 建造者模式
        ModernUser user = ModernUser.builder()
            .name("张三")
            .age(25)
            .email("<EMAIL>")
            .build();

        System.out.println(user);  // 自动生成的toString
    }
}

// 问题3：编译时验证和代码生成
public class CompileTimeProcessing {

    // 自定义编译时注解
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.SOURCE)  // 编译时处理，运行时不保留
    public @interface AutoService {
        Class<?> value();
    }

    // 使用注解
    @AutoService(UserService.class)
    public class UserServiceImpl implements UserService {
        @Override
        public void saveUser(String name) {
            System.out.println("保存用户: " + name);
        }
    }

    // 注解处理器会在编译时生成相应的配置文件或代码
}
```

### 11. Java泛型了解么？泛型的作用？什么是类型擦除？⭐⭐⭐⭐

#### 问题分析

泛型是Java类型系统的重要特性，考查对类型安全和类型擦除的理解。

#### 标准答案

**Java泛型机制：**

```mermaid
graph TB
    subgraph "泛型的作用"
        A[类型安全] --> A1[编译时类型检查]
        A --> A2[避免ClassCastException]

        B[代码复用] --> B1[泛型类]
        B --> B2[泛型方法]
        B --> B3[泛型接口]

        C[可读性] --> C1[明确的类型信息]
        C --> C2[自文档化代码]
    end

    subgraph "类型擦除"
        D[编译时] --> D1[泛型信息完整]
        D --> D2[类型检查]
        D --> D3[类型推断]

        E[运行时] --> E1[泛型信息擦除]
        E --> E2[原始类型]
        E --> E3[类型强转]

        D1 --> E1
        D2 --> E2
        D3 --> E3
    end

    subgraph "通配符"
        F[上界通配符] --> F1[? extends T<br/>协变]
        G[下界通配符] --> G1[? super T<br/>逆变]
        H[无界通配符] --> H1[?<br/>不确定类型]
    end

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#c8e6c9
    style E fill:#ffcdd2
```

#### 泛型基础用法

```java
// 1. 泛型类
public class GenericClass<T> {
    private T data;

    public GenericClass(T data) {
        this.data = data;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}

// 2. 泛型接口
public interface GenericInterface<T> {
    void process(T item);
    T get();
}

// 3. 泛型方法
public class GenericMethods {
    // 静态泛型方法
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // 实例泛型方法
    public <T extends Comparable<T>> T max(T a, T b) {
        return a.compareTo(b) > 0 ? a : b;
    }

    // 多个类型参数
    public <K, V> void put(K key, V value) {
        System.out.println("Key: " + key + ", Value: " + value);
    }
}

// 4. 有界类型参数
public class BoundedGenerics {
    // 上界：T必须是Number或其子类
    public static <T extends Number> double sum(List<T> numbers) {
        double sum = 0.0;
        for (T number : numbers) {
            sum += number.doubleValue();
        }
        return sum;
    }

    // 多重界限
    public static <T extends Comparable<T> & Serializable> void process(T item) {
        // T必须同时实现Comparable和Serializable
        System.out.println("处理: " + item);
    }
}
```

#### 类型擦除详解

```java
public class TypeErasureDemo {

    // 编译前的泛型代码
    public static void beforeErasure() {
        List<String> stringList = new ArrayList<String>();
        stringList.add("Hello");
        String str = stringList.get(0);  // 不需要强制转换

        List<Integer> intList = new ArrayList<Integer>();
        intList.add(42);
        Integer num = intList.get(0);    // 不需要强制转换
    }

    // 编译后的代码（类型擦除后）
    public static void afterErasure() {
        List stringList = new ArrayList();  // 原始类型
        stringList.add("Hello");
        String str = (String) stringList.get(0);  // 编译器插入强制转换

        List intList = new ArrayList();     // 原始类型
        intList.add(42);
        Integer num = (Integer) intList.get(0);   // 编译器插入强制转换
    }

    // 类型擦除的证明
    public static void proveTypeErasure() {
        List<String> stringList = new ArrayList<>();
        List<Integer> intList = new ArrayList<>();

        // 运行时类型相同
        System.out.println(stringList.getClass() == intList.getClass());  // true
        System.out.println(stringList.getClass());  // class java.util.ArrayList
        System.out.println(intList.getClass());     // class java.util.ArrayList

        // 无法在运行时获取泛型类型信息
        // System.out.println(stringList instanceof List<String>);  // 编译错误
        System.out.println(stringList instanceof List);  // true，只能检查原始类型
    }

    // 类型擦除的限制
    public static void typeErasureLimitations() {
        // ❌ 不能创建泛型数组
        // List<String>[] arrays = new List<String>[10];  // 编译错误

        // ❌ 不能使用instanceof检查泛型类型
        List<String> list = new ArrayList<>();
        // if (list instanceof List<String>) { }  // 编译错误

        // ❌ 不能在静态上下文中使用类型参数
        // public static T staticField;  // 编译错误
        // public static T staticMethod() { }  // 编译错误

        // ❌ 不能抛出或捕获泛型异常
        // class GenericException<T> extends Exception { }  // 编译错误
    }
}
```

#### 通配符详解

```java
public class WildcardDemo {

    // 1. 上界通配符 (? extends T) - 协变
    public static double sumNumbers(List<? extends Number> numbers) {
        double sum = 0.0;
        for (Number number : numbers) {
            sum += number.doubleValue();  // 只能读取，不能写入
        }
        // numbers.add(1);  // 编译错误：不能添加元素
        return sum;
    }

    // 2. 下界通配符 (? super T) - 逆变
    public static void addNumbers(List<? super Integer> numbers) {
        numbers.add(1);      // 可以添加Integer及其子类
        numbers.add(2);
        // Integer num = numbers.get(0);  // 编译错误：不能确定返回类型
        Object obj = numbers.get(0);     // 只能用Object接收
    }

    // 3. 无界通配符 (?)
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
        // list.add("item");  // 编译错误：不能添加元素（除了null）
        list.add(null);       // 可以添加null
    }

    // PECS原则演示：Producer Extends, Consumer Super
    public static void demonstratePECS() {
        List<Integer> intList = Arrays.asList(1, 2, 3);
        List<Number> numList = new ArrayList<>();
        List<Object> objList = new ArrayList<>();

        // Producer Extends：从集合中读取数据时使用extends
        double sum = sumNumbers(intList);  // List<Integer>可以传给List<? extends Number>

        // Consumer Super：向集合中写入数据时使用super
        addNumbers(numList);   // List<Number>可以传给List<? super Integer>
        addNumbers(objList);   // List<Object>可以传给List<? super Integer>
    }

    // 复杂的通配符使用
    public static <T> void copy(List<? extends T> src, List<? super T> dest) {
        for (T item : src) {
            dest.add(item);
        }
    }
}
```

#### 泛型的实际应用

```java
// 泛型在实际开发中的应用
public class GenericApplications {

    // 1. 泛型DAO模式
    public abstract class BaseDAO<T, ID> {
        protected Class<T> entityClass;

        @SuppressWarnings("unchecked")
        public BaseDAO() {
            // 通过反射获取泛型类型（需要子类化）
            Type superClass = getClass().getGenericSuperclass();
            if (superClass instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) superClass;
                this.entityClass = (Class<T>) parameterizedType.getActualTypeArguments()[0];
            }
        }

        public abstract T findById(ID id);
        public abstract List<T> findAll();
        public abstract void save(T entity);
        public abstract void delete(ID id);

        protected Class<T> getEntityClass() {
            return entityClass;
        }
    }

    // 具体实现
    public class UserDAO extends BaseDAO<User, Long> {
        @Override
        public User findById(Long id) {
            // 实现查找逻辑
            return new User(id, "用户" + id);
        }

        @Override
        public List<User> findAll() {
            return Arrays.asList(new User(1L, "张三"), new User(2L, "李四"));
        }

        @Override
        public void save(User user) {
            System.out.println("保存用户: " + user);
        }

        @Override
        public void delete(Long id) {
            System.out.println("删除用户: " + id);
        }
    }

    // 2. 泛型工具类
    public static class CollectionUtils {
        // 安全的类型转换
        @SuppressWarnings("unchecked")
        public static <T> List<T> castList(Object obj, Class<T> clazz) {
            List<T> result = new ArrayList<>();
            if (obj instanceof List<?>) {
                for (Object o : (List<?>) obj) {
                    result.add(clazz.cast(o));
                }
            }
            return result;
        }

        // 泛型过滤器
        public static <T> List<T> filter(List<T> list, Predicate<T> predicate) {
            List<T> result = new ArrayList<>();
            for (T item : list) {
                if (predicate.test(item)) {
                    result.add(item);
                }
            }
            return result;
        }

        // 泛型映射
        public static <T, R> List<R> map(List<T> list, Function<T, R> mapper) {
            List<R> result = new ArrayList<>();
            for (T item : list) {
                result.add(mapper.apply(item));
            }
            return result;
        }
    }

    // 3. 泛型建造者模式
    public static class GenericBuilder<T> {
        private final T instance;

        public GenericBuilder(Class<T> clazz) throws Exception {
            this.instance = clazz.newInstance();
        }

        public <V> GenericBuilder<T> set(String fieldName, V value) throws Exception {
            Field field = instance.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(instance, value);
            return this;
        }

        public T build() {
            return instance;
        }
    }
}

// 示例实体类
class User {
    private Long id;
    private String name;

    public User() {}

    public User(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    // getter/setter省略
    @Override
    public String toString() {
        return "User{id=" + id + ", name='" + name + "'}";
    }
}
```

## SPI

### 12. 什么是SPI？有什么用？⭐⭐⭐⭐

#### 问题分析

SPI是Java的服务发现机制，是框架扩展的重要基础，考查对插件化架构的理解。

#### 标准答案

**Java SPI机制：**

```mermaid
flowchart TB
    subgraph concept ["SPI核心概念"]
        A[Service Provider Interface] --> A1[服务接口定义]
        A --> A2[服务提供者实现]
        A --> A3[服务发现机制]

        A1 --> B[接口标准化]
        A2 --> C[多种实现]
        A3 --> D[动态加载]
    end

    subgraph comparison ["SPI vs API"]
        E[API调用方式] --> E1["调用者 → 实现者"]
        E --> E2[编译时依赖]
        E --> E3[静态绑定]

        F[SPI调用方式] --> F1["实现者 → 调用者"]
        F --> F2[运行时发现]
        F --> F3[动态绑定]
    end

    subgraph scenarios ["SPI应用场景"]
        G[JDBC驱动] --> G1[数据库厂商提供驱动]
        H[日志框架] --> H1["SLF4J + 具体实现"]
        I[Spring框架] --> I1[自动配置机制]
        J[Dubbo扩展] --> J1["协议/负载均衡扩展"]
    end

    classDef spi fill:#e3f2fd,stroke:#2196f3
    classDef api fill:#fff3e0,stroke:#ff9800
    classDef spiWay fill:#e8f5e8,stroke:#4caf50
    classDef application fill:#c8e6c9,stroke:#388e3c

    class A spi
    class E api
    class F spiWay
    class G application
```

#### SPI基本实现

```java
// 1. 定义服务接口
public interface PaymentService {
    void pay(double amount);
    String getProviderName();
}

// 2. 服务实现1：支付宝
public class AlipayService implements PaymentService {
    @Override
    public void pay(double amount) {
        System.out.println("使用支付宝支付: " + amount + "元");
    }

    @Override
    public String getProviderName() {
        return "Alipay";
    }
}

// 3. 服务实现2：微信支付
public class WechatPayService implements PaymentService {
    @Override
    public void pay(double amount) {
        System.out.println("使用微信支付: " + amount + "元");
    }

    @Override
    public String getProviderName() {
        return "WechatPay";
    }
}

// 4. 服务实现3：银行卡支付
public class BankCardService implements PaymentService {
    @Override
    public void pay(double amount) {
        System.out.println("使用银行卡支付: " + amount + "元");
    }

    @Override
    public String getProviderName() {
        return "BankCard";
    }
}
```

#### SPI配置文件

```java
/**
 * 在resources/META-INF/services/目录下创建文件：
 * 文件名：com.example.PaymentService（接口全限定名）
 * 文件内容：
 * com.example.AlipayService
 * com.example.WechatPayService
 * com.example.BankCardService
 */

// SPI服务加载器
public class PaymentServiceLoader {

    public static void main(String[] args) {
        // 使用ServiceLoader加载所有实现
        ServiceLoader<PaymentService> serviceLoader =
            ServiceLoader.load(PaymentService.class);

        System.out.println("发现的支付服务提供者：");
        for (PaymentService service : serviceLoader) {
            System.out.println("- " + service.getProviderName());
            service.pay(100.0);
            System.out.println();
        }

        // 获取特定实现
        PaymentService alipay = getServiceByName("Alipay");
        if (alipay != null) {
            alipay.pay(200.0);
        }
    }

    // 根据名称获取特定服务实现
    public static PaymentService getServiceByName(String providerName) {
        ServiceLoader<PaymentService> serviceLoader =
            ServiceLoader.load(PaymentService.class);

        for (PaymentService service : serviceLoader) {
            if (providerName.equals(service.getProviderName())) {
                return service;
            }
        }
        return null;
    }

    // 获取所有可用的服务提供者
    public static List<PaymentService> getAllServices() {
        ServiceLoader<PaymentService> serviceLoader =
            ServiceLoader.load(PaymentService.class);

        List<PaymentService> services = new ArrayList<>();
        for (PaymentService service : serviceLoader) {
            services.add(service);
        }
        return services;
    }
}
```

#### SPI实现原理

```java
// 简化版ServiceLoader实现原理
public class SimpleServiceLoader<S> implements Iterable<S> {
    private static final String PREFIX = "META-INF/services/";

    private final Class<S> service;
    private final ClassLoader loader;
    private LinkedHashMap<String, S> providers = new LinkedHashMap<>();
    private LazyIterator lookupIterator;

    public SimpleServiceLoader(Class<S> svc, ClassLoader cl) {
        service = Objects.requireNonNull(svc, "Service interface cannot be null");
        loader = (cl == null) ? ClassLoader.getSystemClassLoader() : cl;
        reload();
    }

    public void reload() {
        providers.clear();
        lookupIterator = new LazyIterator(service, loader);
    }

    @Override
    public Iterator<S> iterator() {
        return new Iterator<S>() {
            Iterator<Map.Entry<String, S>> knownProviders = providers.entrySet().iterator();

            @Override
            public boolean hasNext() {
                if (knownProviders.hasNext()) {
                    return true;
                }
                return lookupIterator.hasNext();
            }

            @Override
            public S next() {
                if (knownProviders.hasNext()) {
                    return knownProviders.next().getValue();
                }
                return lookupIterator.next();
            }
        };
    }

    // 懒加载迭代器
    private class LazyIterator implements Iterator<S> {
        Class<S> service;
        ClassLoader loader;
        Enumeration<URL> configs = null;
        Iterator<String> pending = null;
        String nextName = null;

        private LazyIterator(Class<S> service, ClassLoader loader) {
            this.service = service;
            this.loader = loader;
        }

        @Override
        public boolean hasNext() {
            if (nextName != null) {
                return true;
            }
            if (configs == null) {
                try {
                    String fullName = PREFIX + service.getName();
                    configs = loader.getResources(fullName);
                } catch (IOException x) {
                    throw new ServiceConfigurationError("Error locating configuration files", x);
                }
            }
            while ((pending == null) || !pending.hasNext()) {
                if (!configs.hasMoreElements()) {
                    return false;
                }
                pending = parse(configs.nextElement());
            }
            nextName = pending.next();
            return true;
        }

        @Override
        public S next() {
            if (!hasNext()) {
                throw new NoSuchElementException();
            }
            String cn = nextName;
            nextName = null;
            Class<?> c = null;
            try {
                c = Class.forName(cn, false, loader);
            } catch (ClassNotFoundException x) {
                throw new ServiceConfigurationError("Provider " + cn + " not found");
            }
            if (!service.isAssignableFrom(c)) {
                throw new ServiceConfigurationError("Provider " + cn + " not a subtype");
            }
            try {
                S p = service.cast(c.newInstance());
                providers.put(cn, p);
                return p;
            } catch (Throwable x) {
                throw new ServiceConfigurationError("Provider " + cn + " could not be instantiated", x);
            }
        }

        private Iterator<String> parse(URL u) {
            // 解析配置文件，返回类名列表
            // 简化实现，实际会处理注释、空行等
            List<String> names = new ArrayList<>();
            try (InputStream in = u.openStream();
                 BufferedReader r = new BufferedReader(new InputStreamReader(in, "utf-8"))) {
                String line;
                while ((line = r.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        names.add(line);
                    }
                }
            } catch (IOException x) {
                throw new ServiceConfigurationError("Error reading configuration file", x);
            }
            return names.iterator();
        }
    }

    public static <S> SimpleServiceLoader<S> load(Class<S> service) {
        ClassLoader cl = Thread.currentThread().getContextClassLoader();
        return new SimpleServiceLoader<>(service, cl);
    }
}
```

#### SPI在框架中的应用

```java
// 1. JDBC驱动加载示例
public class JDBCExample {
    public static void demonstrateJDBCSPI() {
        // JDBC 4.0之前需要手动加载驱动
        // Class.forName("com.mysql.cj.jdbc.Driver");

        // JDBC 4.0之后，通过SPI自动发现驱动
        // MySQL驱动在META-INF/services/java.sql.Driver文件中声明：
        // com.mysql.cj.jdbc.Driver

        try {
            Connection conn = DriverManager.getConnection(
                "********************************", "user", "password");
            System.out.println("数据库连接成功");
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}

// 2. 日志框架SPI示例
public class LoggingExample {
    // SLF4J通过SPI机制发现具体的日志实现
    // 如：logback-classic、log4j-slf4j-impl等

    public static void demonstrateLoggingSPI() {
        // SLF4J会在classpath中查找org.slf4j.impl.StaticLoggerBinder
        // 不同的日志实现提供不同的StaticLoggerBinder

        org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(LoggingExample.class);
        logger.info("这条日志会被具体的日志实现处理");

        // 运行时可以切换不同的日志实现，只需要替换jar包
    }
}

// 3. 自定义插件系统
public interface PluginInterface {
    String getName();
    void execute();
    boolean isEnabled();
}

public class PluginManager {
    private List<PluginInterface> plugins;

    public PluginManager() {
        loadPlugins();
    }

    private void loadPlugins() {
        plugins = new ArrayList<>();
        ServiceLoader<PluginInterface> serviceLoader =
            ServiceLoader.load(PluginInterface.class);

        for (PluginInterface plugin : serviceLoader) {
            if (plugin.isEnabled()) {
                plugins.add(plugin);
                System.out.println("加载插件: " + plugin.getName());
            }
        }
    }

    public void executeAllPlugins() {
        for (PluginInterface plugin : plugins) {
            try {
                plugin.execute();
            } catch (Exception e) {
                System.err.println("插件执行失败: " + plugin.getName() + ", " + e.getMessage());
            }
        }
    }

    public PluginInterface getPlugin(String name) {
        return plugins.stream()
            .filter(plugin -> name.equals(plugin.getName()))
            .findFirst()
            .orElse(null);
    }
}
```

#### SPI vs API对比

```java
public class SPIvsAPI {

    /**
     * API (Application Programming Interface)
     * - 调用者依赖实现者
     * - 编译时绑定
     * - 调用者控制
     */
    public void demonstrateAPI() {
        // 直接依赖具体实现
        AlipayService alipay = new AlipayService();
        alipay.pay(100.0);

        // 编译时就确定了使用哪个实现
        // 要更换实现需要修改代码
    }

    /**
     * SPI (Service Provider Interface)
     * - 实现者适配调用者
     * - 运行时绑定
     * - 实现者控制
     */
    public void demonstrateSPI() {
        // 通过SPI动态发现实现
        ServiceLoader<PaymentService> serviceLoader =
            ServiceLoader.load(PaymentService.class);

        // 运行时确定使用哪个实现
        // 可以通过配置文件动态添加新实现
        for (PaymentService service : serviceLoader) {
            service.pay(100.0);
        }
    }

    /**
     * SPI的优势：
     * 1. 解耦：调用者不依赖具体实现
     * 2. 扩展性：可以动态添加新实现
     * 3. 插件化：支持插件架构
     * 4. 标准化：定义统一的服务接口
     *
     * SPI的劣势：
     * 1. 复杂性：增加了系统复杂度
     * 2. 性能：运行时发现有一定开销
     * 3. 调试：问题定位相对困难
     * 4. 依赖：需要正确配置META-INF/services
     */
}
```

## I/O

### 13. BIO、NIO、AIO有什么区别？⭐⭐⭐⭐⭐

#### 问题分析

这是Java I/O模型的核心知识点，考查对同步异步、阻塞非阻塞的理解。

#### 标准答案

**Java I/O模型对比：**

```mermaid
graph TB
    subgraph "BIO - 阻塞I/O"
        A[客户端请求] --> A1[创建线程]
        A1 --> A2[阻塞等待]
        A2 --> A3[数据就绪]
        A3 --> A4[处理数据]
        A4 --> A5[线程结束]

        A6[特点:<br/>同步阻塞<br/>一线程一连接<br/>资源消耗大]
    end

    subgraph "NIO - 非阻塞I/O"
        B[多个客户端] --> B1[Selector轮询]
        B1 --> B2[检查就绪状态]
        B2 --> B3[处理就绪连接]
        B3 --> B1

        B4[特点:<br/>同步非阻塞<br/>一线程多连接<br/>基于事件驱动]
    end

    subgraph "AIO - 异步I/O"
        C[发起异步请求] --> C1[立即返回]
        C1 --> C2[系统处理I/O]
        C2 --> C3[完成回调]
        C3 --> C4[处理结果]

        C5[特点:<br/>异步非阻塞<br/>基于回调<br/>真正异步]
    end

    style A fill:#ffcdd2
    style B fill:#fff3e0
    style C fill:#c8e6c9
    style A6 fill:#ffcdd2
    style B4 fill:#fff3e0
    style C5 fill:#c8e6c9
```

#### BIO示例代码

```java
// BIO服务器实现
public class BIOServer {
    private static final int PORT = 8080;

    public static void main(String[] args) throws IOException {
        ServerSocket serverSocket = new ServerSocket(PORT);
        System.out.println("BIO服务器启动，监听端口: " + PORT);

        while (true) {
            // 阻塞等待客户端连接
            Socket clientSocket = serverSocket.accept();

            // 为每个客户端创建新线程
            new Thread(() -> handleClient(clientSocket)).start();
        }
    }

    private static void handleClient(Socket clientSocket) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter writer = new PrintWriter(
                clientSocket.getOutputStream(), true)) {

            String inputLine;
            // 阻塞读取客户端数据
            while ((inputLine = reader.readLine()) != null) {
                System.out.println("收到消息: " + inputLine);
                writer.println("Echo: " + inputLine);

                if ("bye".equalsIgnoreCase(inputLine)) {
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}

// BIO客户端实现
public class BIOClient {
    public static void main(String[] args) throws IOException {
        Socket socket = new Socket("localhost", 8080);

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(socket.getInputStream()));
             PrintWriter writer = new PrintWriter(
                socket.getOutputStream(), true);
             Scanner scanner = new Scanner(System.in)) {

            String userInput;
            while ((userInput = scanner.nextLine()) != null) {
                writer.println(userInput);
                String response = reader.readLine();
                System.out.println("服务器响应: " + response);

                if ("bye".equalsIgnoreCase(userInput)) {
                    break;
                }
            }
        }
    }
}
```

#### NIO示例代码

```java
// NIO服务器实现
public class NIOServer {
    private static final int PORT = 8080;
    private static final int BUFFER_SIZE = 1024;

    public static void main(String[] args) throws IOException {
        // 创建选择器
        Selector selector = Selector.open();

        // 创建服务器套接字通道
        ServerSocketChannel serverChannel = ServerSocketChannel.open();
        serverChannel.configureBlocking(false);  // 设置为非阻塞
        serverChannel.bind(new InetSocketAddress(PORT));

        // 注册接受连接事件
        serverChannel.register(selector, SelectionKey.OP_ACCEPT);

        System.out.println("NIO服务器启动，监听端口: " + PORT);

        while (true) {
            // 阻塞等待事件发生
            int readyChannels = selector.select();

            if (readyChannels == 0) {
                continue;
            }

            // 获取就绪的选择键
            Set<SelectionKey> selectedKeys = selector.selectedKeys();
            Iterator<SelectionKey> keyIterator = selectedKeys.iterator();

            while (keyIterator.hasNext()) {
                SelectionKey key = keyIterator.next();

                if (key.isAcceptable()) {
                    handleAccept(key, selector);
                } else if (key.isReadable()) {
                    handleRead(key);
                }

                keyIterator.remove();
            }
        }
    }

    private static void handleAccept(SelectionKey key, Selector selector) throws IOException {
        ServerSocketChannel serverChannel = (ServerSocketChannel) key.channel();
        SocketChannel clientChannel = serverChannel.accept();

        if (clientChannel != null) {
            clientChannel.configureBlocking(false);
            clientChannel.register(selector, SelectionKey.OP_READ);
            System.out.println("新客户端连接: " + clientChannel.getRemoteAddress());
        }
    }

    private static void handleRead(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        ByteBuffer buffer = ByteBuffer.allocate(BUFFER_SIZE);

        try {
            int bytesRead = clientChannel.read(buffer);

            if (bytesRead > 0) {
                buffer.flip();
                String message = new String(buffer.array(), 0, buffer.limit());
                System.out.println("收到消息: " + message.trim());

                // 回写数据
                String response = "Echo: " + message;
                ByteBuffer responseBuffer = ByteBuffer.wrap(response.getBytes());
                clientChannel.write(responseBuffer);

            } else if (bytesRead < 0) {
                // 客户端断开连接
                System.out.println("客户端断开连接: " + clientChannel.getRemoteAddress());
                key.cancel();
                clientChannel.close();
            }
        } catch (IOException e) {
            System.out.println("客户端异常断开: " + e.getMessage());
            key.cancel();
            clientChannel.close();
        }
    }
}

// NIO客户端实现
public class NIOClient {
    public static void main(String[] args) throws IOException {
        SocketChannel socketChannel = SocketChannel.open();
        socketChannel.connect(new InetSocketAddress("localhost", 8080));

        Scanner scanner = new Scanner(System.in);
        String userInput;

        while ((userInput = scanner.nextLine()) != null) {
            // 发送数据
            ByteBuffer buffer = ByteBuffer.wrap(userInput.getBytes());
            socketChannel.write(buffer);

            // 读取响应
            ByteBuffer responseBuffer = ByteBuffer.allocate(1024);
            int bytesRead = socketChannel.read(responseBuffer);

            if (bytesRead > 0) {
                responseBuffer.flip();
                String response = new String(responseBuffer.array(), 0, responseBuffer.limit());
                System.out.println("服务器响应: " + response);
            }

            if ("bye".equalsIgnoreCase(userInput)) {
                break;
            }
        }

        socketChannel.close();
    }
}
```

#### AIO示例代码

```java
// AIO服务器实现
public class AIOServer {
    private static final int PORT = 8080;

    public static void main(String[] args) throws IOException, InterruptedException {
        AsynchronousServerSocketChannel serverChannel =
            AsynchronousServerSocketChannel.open();
        serverChannel.bind(new InetSocketAddress(PORT));

        System.out.println("AIO服务器启动，监听端口: " + PORT);

        // 异步接受连接
        serverChannel.accept(null, new CompletionHandler<AsynchronousSocketChannel, Void>() {
            @Override
            public void completed(AsynchronousSocketChannel clientChannel, Void attachment) {
                // 继续接受下一个连接
                serverChannel.accept(null, this);

                // 处理当前连接
                handleClient(clientChannel);
            }

            @Override
            public void failed(Throwable exc, Void attachment) {
                exc.printStackTrace();
            }
        });

        // 保持主线程运行
        Thread.currentThread().join();
    }

    private static void handleClient(AsynchronousSocketChannel clientChannel) {
        ByteBuffer buffer = ByteBuffer.allocate(1024);

        // 异步读取数据
        clientChannel.read(buffer, buffer, new CompletionHandler<Integer, ByteBuffer>() {
            @Override
            public void completed(Integer result, ByteBuffer attachment) {
                if (result > 0) {
                    attachment.flip();
                    String message = new String(attachment.array(), 0, attachment.limit());
                    System.out.println("收到消息: " + message.trim());

                    // 异步写回数据
                    String response = "Echo: " + message;
                    ByteBuffer responseBuffer = ByteBuffer.wrap(response.getBytes());

                    clientChannel.write(responseBuffer, responseBuffer,
                        new CompletionHandler<Integer, ByteBuffer>() {
                            @Override
                            public void completed(Integer result, ByteBuffer attachment) {
                                // 继续读取下一条消息
                                attachment.clear();
                                clientChannel.read(attachment, attachment, this);
                            }

                            @Override
                            public void failed(Throwable exc, ByteBuffer attachment) {
                                try {
                                    clientChannel.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                        });
                } else {
                    // 客户端断开连接
                    try {
                        clientChannel.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void failed(Throwable exc, ByteBuffer attachment) {
                try {
                    clientChannel.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
```

#### I/O模型对比总结

```java
public class IOModelComparison {

    /**
     * BIO (Blocking I/O) - 阻塞I/O
     *
     * 特点：
     * - 同步阻塞模型
     * - 一个连接一个线程
     * - 线程在I/O操作时会阻塞
     *
     * 优点：
     * - 编程模型简单
     * - 适合连接数较少的场景
     *
     * 缺点：
     * - 线程资源消耗大
     * - 线程上下文切换开销大
     * - 不适合高并发场景
     *
     * 适用场景：
     * - 连接数比较小且固定的架构
     * - 对服务器资源要求不高的场景
     */

    /**
     * NIO (Non-blocking I/O) - 非阻塞I/O
     *
     * 特点：
     * - 同步非阻塞模型
     * - 一个线程处理多个连接
     * - 基于事件驱动
     * - 使用Selector进行轮询
     *
     * 核心组件：
     * - Channel：数据传输通道
     * - Buffer：数据缓冲区
     * - Selector：多路复用器
     *
     * 优点：
     * - 资源利用率高
     * - 支持高并发
     * - 减少线程创建开销
     *
     * 缺点：
     * - 编程复杂度高
     * - 需要处理半包、粘包问题
     * - 调试困难
     *
     * 适用场景：
     * - 高并发、高性能的网络应用
     * - 连接数多但每个连接数据量不大
     */

    /**
     * AIO (Asynchronous I/O) - 异步I/O
     *
     * 特点：
     * - 异步非阻塞模型
     * - 基于回调机制
     * - 真正的异步I/O
     * - 操作系统级别支持
     *
     * 优点：
     * - 真正异步，性能最高
     * - 减少线程数量
     * - 适合高并发场景
     *
     * 缺点：
     * - 编程复杂度最高
     * - 调试困难
     * - 操作系统支持有限
     *
     * 适用场景：
     * - 超高并发的网络应用
     * - 对性能要求极高的场景
     */

    // 性能对比示例
    public void performanceComparison() {
        System.out.println("I/O模型性能对比：");
        System.out.println("并发连接数 | BIO线程数 | NIO线程数 | AIO线程数");
        System.out.println("1000      | 1000     | 1-10     | 1-5");
        System.out.println("10000     | 10000    | 1-10     | 1-5");
        System.out.println("100000    | 无法支持  | 1-10     | 1-5");

        System.out.println("\n内存消耗对比：");
        System.out.println("BIO: 每个连接约1MB内存（线程栈）");
        System.out.println("NIO: 总体内存消耗较低，主要是Buffer");
        System.out.println("AIO: 内存消耗最低");

        System.out.println("\n适用场景：");
        System.out.println("BIO: 连接数少、开发简单的场景");
        System.out.println("NIO: 高并发、长连接的场景");
        System.out.println("AIO: 超高并发、对性能要求极高的场景");
    }
}
```

---

## 总结

本文档涵盖了Java基础的核心面试题，包括：

1. **数据类型**：8种基本数据类型、包装类型常量池、自动装箱拆箱、NPE问题
2. **面向对象**：String特性、重载重写、==与equals比较
3. **反射&注解&泛型**：反射机制、注解应用、泛型类型擦除
4. **SPI机制**：服务发现、插件化架构
5. **I/O模型**：BIO、NIO、AIO的区别和应用场景

每个知识点都包含了：

- 问题分析
- 标准答案
- 详细代码示例
- 实际应用场景
- 最佳实践建议

这些内容覆盖了Java基础面试的核心考点，通过理论结合实践的方式，帮助深入理解Java的底层机制和设计思想。
