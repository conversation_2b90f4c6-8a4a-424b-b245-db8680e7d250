# 高并发系统架构设计面试题详解

## 目录
- [基础并发概念](#基础并发概念)
- [缓存架构设计](#缓存架构设计)
- [数据库优化策略](#数据库优化策略)
- [分布式系统设计](#分布式系统设计)
- [微服务架构](#微服务架构)
- [消息队列应用](#消息队列应用)
- [负载均衡与容错](#负载均衡与容错)
- [性能监控与调优](#性能监控与调优)
- [秒杀系统设计](#秒杀系统设计)
- [大型系统架构案例](#大型系统架构案例)

## 基础并发概念

### 1. 什么是高并发？如何衡量系统的并发能力？请详细说明QPS、TPS、RT等关键指标 ⭐⭐⭐

#### 问题分析
考查候选人对高并发基础概念的理解，以及对系统性能指标的掌握程度。

#### 标准答案

**高并发定义与指标体系：**

```mermaid
flowchart TB
    subgraph concurrent_concepts ["并发概念"]
        A["Concurrency<br/>并发性"]
        B["Parallelism<br/>并行性"]
        C["Throughput<br/>吞吐量"]
        D["Latency<br/>延迟"]
    end
    
    subgraph performance_metrics ["性能指标"]
        E["QPS<br/>每秒查询数"]
        F["TPS<br/>每秒事务数"]
        G["RT<br/>响应时间"]
        H["Concurrent Users<br/>并发用户数"]
    end
    
    subgraph measurement_dimensions ["衡量维度"]
        I["CPU Utilization<br/>CPU利用率"]
        J["Memory Usage<br/>内存使用"]
        K["I/O Throughput<br/>I/O吞吐量"]
        L["Network Bandwidth<br/>网络带宽"]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> K
    H --> L
    
    classDef conceptStyle fill:#c8e6c9,stroke:#4caf50
    classDef metricStyle fill:#e3f2fd,stroke:#2196f3
    classDef dimensionStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C,D conceptStyle
    class E,F,G,H metricStyle
    class I,J,K,L dimensionStyle
```

**详细解析：**

**1. 高并发的本质理解：**

高并发是指系统能够同时处理大量请求的能力，主要体现在：
- **时间维度**：单位时间内处理的请求数量
- **空间维度**：同时在线的用户数量
- **资源维度**：系统资源的有效利用率

**2. 核心性能指标详解：**

**QPS (Queries Per Second)：**
```java
// QPS计算示例
public class QPSCalculator {
    private final AtomicLong requestCount = new AtomicLong(0);
    private final long startTime = System.currentTimeMillis();
    
    public void recordRequest() {
        requestCount.incrementAndGet();
    }
    
    public double getCurrentQPS() {
        long currentTime = System.currentTimeMillis();
        long elapsedSeconds = (currentTime - startTime) / 1000;
        return elapsedSeconds > 0 ? (double) requestCount.get() / elapsedSeconds : 0;
    }
    
    // 滑动窗口QPS计算
    public class SlidingWindowQPS {
        private final int windowSize = 60; // 60秒窗口
        private final AtomicLong[] buckets = new AtomicLong[windowSize];
        private volatile int currentBucket = 0;
        
        public SlidingWindowQPS() {
            for (int i = 0; i < windowSize; i++) {
                buckets[i] = new AtomicLong(0);
            }
            
            // 每秒切换bucket
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.scheduleAtFixedRate(() -> {
                currentBucket = (currentBucket + 1) % windowSize;
                buckets[currentBucket].set(0);
            }, 1, 1, TimeUnit.SECONDS);
        }
        
        public void recordRequest() {
            buckets[currentBucket].incrementAndGet();
        }
        
        public long getQPS() {
            return Arrays.stream(buckets)
                        .mapToLong(AtomicLong::get)
                        .sum();
        }
    }
}
```

**TPS (Transactions Per Second)：**
```java
// TPS监控实现
public class TPSMonitor {
    private final AtomicLong successfulTransactions = new AtomicLong(0);
    private final AtomicLong failedTransactions = new AtomicLong(0);
    
    @Transactional
    public void executeTransaction(Runnable transaction) {
        try {
            transaction.run();
            successfulTransactions.incrementAndGet();
        } catch (Exception e) {
            failedTransactions.incrementAndGet();
            throw e;
        }
    }
    
    public TPSMetrics getTPSMetrics() {
        long total = successfulTransactions.get() + failedTransactions.get();
        long successful = successfulTransactions.get();
        
        return TPSMetrics.builder()
            .totalTPS(calculateTPS(total))
            .successfulTPS(calculateTPS(successful))
            .successRate((double) successful / total * 100)
            .build();
    }
}
```

**RT (Response Time) 分析：**
```java
// 响应时间统计
public class ResponseTimeTracker {
    private final Histogram responseTimeHistogram = Histogram.build()
        .name("response_time_seconds")
        .help("Response time in seconds")
        .buckets(0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0)
        .register();
    
    public <T> T trackResponseTime(Supplier<T> operation) {
        Timer.Sample sample = Timer.start();
        try {
            return operation.get();
        } finally {
            sample.stop(responseTimeHistogram);
        }
    }
    
    // 百分位数计算
    public ResponseTimeStats getResponseTimeStats() {
        return ResponseTimeStats.builder()
            .p50(responseTimeHistogram.get().quantile(0.5))
            .p90(responseTimeHistogram.get().quantile(0.9))
            .p95(responseTimeHistogram.get().quantile(0.95))
            .p99(responseTimeHistogram.get().quantile(0.99))
            .avg(responseTimeHistogram.get().mean())
            .build();
    }
}
```

**3. 并发能力评估模型：**

**Little's Law应用：**
```
并发用户数 = 吞吐量 × 平均响应时间
L = λ × W

其中：
L = 系统中的平均用户数（并发数）
λ = 到达率（QPS）
W = 平均等待时间（RT）
```

**系统容量规划：**
```java
public class CapacityPlanner {
    
    public CapacityPlan calculateCapacity(PerformanceRequirements requirements) {
        // 基于Little's Law计算
        double requiredConcurrency = requirements.getTargetQPS() * 
                                   requirements.getMaxAcceptableRT();
        
        // 考虑安全系数
        double safetyFactor = 1.5;
        double actualConcurrency = requiredConcurrency * safetyFactor;
        
        // 计算所需资源
        int requiredCPUCores = (int) Math.ceil(actualConcurrency / 100); // 假设每核心支持100并发
        int requiredMemoryGB = (int) Math.ceil(actualConcurrency * 0.01); // 假设每并发10MB内存
        
        return CapacityPlan.builder()
            .targetQPS(requirements.getTargetQPS())
            .maxRT(requirements.getMaxAcceptableRT())
            .requiredConcurrency((int) actualConcurrency)
            .cpuCores(requiredCPUCores)
            .memoryGB(requiredMemoryGB)
            .build();
    }
}
```

**最佳实践：**
1. 建立完整的性能指标监控体系
2. 使用压力测试验证系统容量
3. 基于业务场景设定合理的性能目标
4. 持续监控和优化系统性能
5. 建立性能基线和告警机制

## 缓存架构设计

### 2. 设计一个多级缓存架构，如何解决缓存穿透、缓存击穿、缓存雪崩问题？ ⭐⭐⭐⭐

#### 问题分析
考查候选人对缓存架构设计的深度理解，以及对常见缓存问题的解决方案掌握。

#### 标准答案

**多级缓存架构图：**

```mermaid
flowchart TB
    subgraph client_layer ["客户端层"]
        A["Browser Cache<br/>浏览器缓存"]
        B["Mobile App Cache<br/>移动端缓存"]
        C["CDN<br/>内容分发网络"]
    end
    
    subgraph application_layer ["应用层"]
        D["Local Cache<br/>本地缓存（Caffeine）"]
        E["Distributed Cache<br/>分布式缓存（Redis）"]
        F["Database Cache<br/>数据库缓存"]
    end
    
    subgraph storage_layer ["存储层"]
        G["Primary Database<br/>主数据库"]
        H["Read Replicas<br/>读副本"]
        I["Search Engine<br/>搜索引擎"]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    E --> F
    
    F --> G
    F --> H
    F --> I
    
    classDef clientStyle fill:#c8e6c9,stroke:#4caf50
    classDef appStyle fill:#e3f2fd,stroke:#2196f3
    classDef storageStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C clientStyle
    class D,E,F appStyle
    class G,H,I storageStyle
```

**详细解析：**

**1. 多级缓存架构实现：**

**本地缓存层（L1 Cache）：**
```java
@Configuration
public class LocalCacheConfig {

    @Bean
    public Cache<String, Object> localCache() {
        return Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterAccess(2, TimeUnit.MINUTES)
            .recordStats()
            .removalListener((key, value, cause) -> {
                log.info("Local cache evicted: key={}, cause={}", key, cause);
            })
            .build();
    }
}

@Service
public class MultiLevelCacheService {

    @Autowired
    private Cache<String, Object> localCache;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public <T> T get(String key, Class<T> type, Supplier<T> dataLoader) {
        // L1: 本地缓存
        T value = (T) localCache.getIfPresent(key);
        if (value != null) {
            return value;
        }

        // L2: 分布式缓存
        value = (T) redisTemplate.opsForValue().get(key);
        if (value != null) {
            localCache.put(key, value);
            return value;
        }

        // L3: 数据源
        value = dataLoader.get();
        if (value != null) {
            // 写入所有缓存层
            redisTemplate.opsForValue().set(key, value, Duration.ofMinutes(30));
            localCache.put(key, value);
        }

        return value;
    }
}
```

**2. 缓存问题解决方案：**

**缓存穿透解决方案：**

```mermaid
flowchart LR
    subgraph cache_penetration ["缓存穿透解决"]
        A["Bloom Filter<br/>布隆过滤器"]
        B["Null Value Cache<br/>空值缓存"]
        C["Parameter Validation<br/>参数校验"]
        D["Rate Limiting<br/>限流控制"]
    end

    subgraph implementation ["实现方式"]
        E["Pre-populate Filter<br/>预填充过滤器"]
        F["Cache Null Results<br/>缓存空结果"]
        G["Input Sanitization<br/>输入清理"]
        H["Request Throttling<br/>请求限流"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50
    classDef implStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C,D solutionStyle
    class E,F,G,H implStyle
```

**布隆过滤器实现：**
```java
@Component
public class BloomFilterCacheGuard {

    private final BloomFilter<String> bloomFilter;

    public BloomFilterCacheGuard() {
        // 预期插入100万条数据，误判率0.01%
        this.bloomFilter = BloomFilter.create(
            Funnels.stringFunnel(Charset.defaultCharset()),
            1000000,
            0.0001
        );

        // 初始化时预加载所有有效key
        initializeBloomFilter();
    }

    private void initializeBloomFilter() {
        // 从数据库加载所有有效ID
        List<String> validIds = dataService.getAllValidIds();
        validIds.forEach(bloomFilter::put);
    }

    public boolean mightExist(String key) {
        return bloomFilter.mightContain(key);
    }

    public void addKey(String key) {
        bloomFilter.put(key);
    }
}

@Service
public class CachePenetrationProtectedService {

    @Autowired
    private BloomFilterCacheGuard bloomFilter;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public Object getData(String id) {
        // 1. 布隆过滤器预检查
        if (!bloomFilter.mightExist(id)) {
            return null; // 确定不存在，直接返回
        }

        // 2. 查询缓存
        Object cached = redisTemplate.opsForValue().get(id);
        if (cached != null) {
            return "NULL".equals(cached) ? null : cached;
        }

        // 3. 查询数据库
        Object data = dataService.getById(id);

        // 4. 缓存结果（包括空值）
        if (data != null) {
            redisTemplate.opsForValue().set(id, data, Duration.ofMinutes(30));
        } else {
            // 缓存空值，较短过期时间
            redisTemplate.opsForValue().set(id, "NULL", Duration.ofMinutes(5));
        }

        return data;
    }
}
```

**缓存击穿解决方案：**
```java
@Service
public class CacheBreakdownProtectedService {

    private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

    public Object getHotData(String key) {
        // 1. 尝试从缓存获取
        Object cached = redisTemplate.opsForValue().get(key);
        if (cached != null) {
            return cached;
        }

        // 2. 获取分布式锁
        String lockKey = "lock:" + key;
        String lockValue = UUID.randomUUID().toString();

        try {
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));

            if (Boolean.TRUE.equals(acquired)) {
                // 获得锁，查询数据库
                Object data = dataService.getById(key);
                if (data != null) {
                    // 设置随机过期时间，避免同时过期
                    int randomExpire = 1800 + new Random().nextInt(600); // 30-40分钟
                    redisTemplate.opsForValue().set(key, data, Duration.ofSeconds(randomExpire));
                }
                return data;
            } else {
                // 未获得锁，等待后重试
                Thread.sleep(50);
                return getHotData(key); // 递归重试
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        } finally {
            // 释放锁
            releaseLock(lockKey, lockValue);
        }
    }

    private void releaseLock(String lockKey, String lockValue) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) else return 0 end";
        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class),
                            Collections.singletonList(lockKey), lockValue);
    }
}
```

**缓存雪崩解决方案：**

```mermaid
flowchart TB
    subgraph avalanche_prevention ["缓存雪崩预防"]
        A["Random Expiration<br/>随机过期时间"]
        B["Cache Warming<br/>缓存预热"]
        C["Circuit Breaker<br/>熔断器"]
        D["Backup Cache<br/>备用缓存"]
    end

    subgraph recovery_strategy ["恢复策略"]
        E["Gradual Recovery<br/>渐进式恢复"]
        F["Load Shedding<br/>负载削减"]
        G["Fallback Mechanism<br/>降级机制"]
        H["Multi-level Cache<br/>多级缓存"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    classDef preventionStyle fill:#c8e6c9,stroke:#4caf50
    classDef recoveryStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C,D preventionStyle
    class E,F,G,H recoveryStyle
```

**缓存雪崩防护实现：**
```java
@Service
public class CacheAvalancheProtectedService {

    @Autowired
    private CircuitBreaker circuitBreaker;

    @Autowired
    private RedisTemplate<String, Object> primaryCache;

    @Autowired
    private RedisTemplate<String, Object> backupCache;

    public Object getDataWithProtection(String key) {
        return circuitBreaker.executeSupplier(() -> {
            // 1. 主缓存查询
            Object data = primaryCache.opsForValue().get(key);
            if (data != null) {
                return data;
            }

            // 2. 备用缓存查询
            data = backupCache.opsForValue().get(key);
            if (data != null) {
                // 异步恢复主缓存
                asyncRestorePrimaryCache(key, data);
                return data;
            }

            // 3. 数据库查询（限流保护）
            return getDataFromDatabaseWithRateLimit(key);
        });
    }

    @Async
    public void asyncRestorePrimaryCache(String key, Object data) {
        try {
            int randomExpire = generateRandomExpireTime();
            primaryCache.opsForValue().set(key, data, Duration.ofSeconds(randomExpire));
        } catch (Exception e) {
            log.error("Failed to restore primary cache for key: {}", key, e);
        }
    }

    private int generateRandomExpireTime() {
        // 基础过期时间 + 随机时间，避免同时过期
        int baseExpire = 1800; // 30分钟
        int randomRange = 600;  // 10分钟随机范围
        return baseExpire + new Random().nextInt(randomRange);
    }

    // 缓存预热
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        log.info("Starting cache warm-up...");

        CompletableFuture.runAsync(() -> {
            List<String> hotKeys = getHotKeys();
            hotKeys.parallelStream().forEach(key -> {
                try {
                    Object data = dataService.getById(key);
                    if (data != null) {
                        int randomExpire = generateRandomExpireTime();
                        primaryCache.opsForValue().set(key, data, Duration.ofSeconds(randomExpire));
                        backupCache.opsForValue().set(key, data, Duration.ofSeconds(randomExpire * 2));
                    }
                } catch (Exception e) {
                    log.error("Failed to warm up cache for key: {}", key, e);
                }
            });
        });

        log.info("Cache warm-up completed");
    }
}
```

**3. 缓存一致性保证：**

**Canal + MQ模式：**
```java
@Component
public class CacheConsistencyManager {

    @RabbitListener(queues = "cache.update.queue")
    public void handleDatabaseChange(DatabaseChangeEvent event) {
        String key = generateCacheKey(event.getTable(), event.getId());

        switch (event.getEventType()) {
            case INSERT:
            case UPDATE:
                // 延迟双删策略
                deleteCache(key);
                CompletableFuture.runAsync(() -> {
                    try {
                        Thread.sleep(1000); // 延迟1秒
                        deleteCache(key);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
                break;
            case DELETE:
                deleteCache(key);
                break;
        }
    }

    private void deleteCache(String key) {
        try {
            primaryCache.delete(key);
            backupCache.delete(key);
            localCache.invalidate(key);
        } catch (Exception e) {
            log.error("Failed to delete cache for key: {}", key, e);
        }
    }
}
```

**最佳实践：**
1. 合理设计缓存层次和过期策略
2. 使用布隆过滤器防止缓存穿透
3. 实现分布式锁防止缓存击穿
4. 通过随机过期时间和多级缓存防止雪崩
5. 建立缓存监控和告警机制

## 数据库优化策略

### 3. 如何设计数据库分库分表方案？请详细说明分片策略、数据迁移、跨分片查询等关键问题 ⭐⭐⭐⭐⭐

#### 问题分析
考查候选人对大规模数据存储架构设计的深度理解，包括分片算法、数据一致性、查询优化等核心技术。

#### 标准答案

**分库分表架构图：**

```mermaid
flowchart TB
    subgraph application_layer ["应用层"]
        A["Application Server<br/>应用服务器"]
        B["Sharding Proxy<br/>分片代理"]
        C["Connection Pool<br/>连接池"]
    end

    subgraph sharding_layer ["分片层"]
        D["Shard Router<br/>分片路由"]
        E["Query Parser<br/>查询解析"]
        F["Result Merger<br/>结果合并"]
        G["Transaction Manager<br/>事务管理"]
    end

    subgraph database_layer ["数据库层"]
        H["DB Shard 1<br/>数据库分片1"]
        I["DB Shard 2<br/>数据库分片2"]
        J["DB Shard 3<br/>数据库分片3"]
        K["DB Shard N<br/>数据库分片N"]
    end

    A --> B
    B --> C
    C --> D

    D --> E
    E --> F
    F --> G

    G --> H
    G --> I
    G --> J
    G --> K

    classDef appStyle fill:#c8e6c9,stroke:#4caf50
    classDef shardStyle fill:#e3f2fd,stroke:#2196f3
    classDef dbStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C appStyle
    class D,E,F,G shardStyle
    class H,I,J,K dbStyle
```

**详细解析：**

**1. 分片策略设计：**

**水平分片算法：**
```java
public interface ShardingStrategy {
    String determineTargetDataSource(String logicTableName, Object shardingValue);
    String determineTargetTable(String logicTableName, Object shardingValue);
}

// 哈希分片策略
@Component
public class HashShardingStrategy implements ShardingStrategy {

    private final int databaseCount = 4;
    private final int tableCount = 16;

    @Override
    public String determineTargetDataSource(String logicTableName, Object shardingValue) {
        int hash = Math.abs(shardingValue.hashCode());
        int dbIndex = hash % databaseCount;
        return "ds" + dbIndex;
    }

    @Override
    public String determineTargetTable(String logicTableName, Object shardingValue) {
        int hash = Math.abs(shardingValue.hashCode());
        int tableIndex = hash % tableCount;
        return logicTableName + "_" + tableIndex;
    }
}

// 范围分片策略
@Component
public class RangeShardingStrategy implements ShardingStrategy {

    private final Map<String, RangeConfig> rangeConfigs = new HashMap<>();

    @PostConstruct
    public void initRangeConfigs() {
        // 按时间范围分片
        rangeConfigs.put("order", RangeConfig.builder()
            .shardingColumn("create_time")
            .ranges(Arrays.asList(
                new DateRange("2023-01-01", "2023-03-31", "ds0", "order_q1"),
                new DateRange("2023-04-01", "2023-06-30", "ds1", "order_q2"),
                new DateRange("2023-07-01", "2023-09-30", "ds2", "order_q3"),
                new DateRange("2023-10-01", "2023-12-31", "ds3", "order_q4")
            ))
            .build());
    }

    @Override
    public String determineTargetDataSource(String logicTableName, Object shardingValue) {
        RangeConfig config = rangeConfigs.get(logicTableName);
        if (config != null && shardingValue instanceof Date) {
            return config.findDataSource((Date) shardingValue);
        }
        throw new IllegalArgumentException("Unsupported sharding value type");
    }
}

// 一致性哈希分片策略
@Component
public class ConsistentHashShardingStrategy implements ShardingStrategy {

    private final TreeMap<Long, String> ring = new TreeMap<>();
    private final int virtualNodes = 150;

    @PostConstruct
    public void initConsistentHashRing() {
        List<String> dataSources = Arrays.asList("ds0", "ds1", "ds2", "ds3");

        for (String dataSource : dataSources) {
            for (int i = 0; i < virtualNodes; i++) {
                String virtualNode = dataSource + "#" + i;
                long hash = hash(virtualNode);
                ring.put(hash, dataSource);
            }
        }
    }

    @Override
    public String determineTargetDataSource(String logicTableName, Object shardingValue) {
        long hash = hash(shardingValue.toString());
        Map.Entry<Long, String> entry = ring.ceilingEntry(hash);
        if (entry == null) {
            entry = ring.firstEntry();
        }
        return entry.getValue();
    }

    private long hash(String key) {
        // 使用MurmurHash算法
        return Hashing.murmur3_128().hashString(key, StandardCharsets.UTF_8).asLong();
    }
}
```

**2. 分片路由实现：**

**智能路由器：**
```java
@Component
public class ShardingRouter {

    @Autowired
    private Map<String, ShardingStrategy> shardingStrategies;

    @Autowired
    private DataSourceManager dataSourceManager;

    public RouteResult route(String sql, Object[] parameters) {
        // 1. 解析SQL
        SQLStatement sqlStatement = SQLParserUtils.parse(sql, DbType.mysql);

        // 2. 提取分片键
        ShardingContext context = extractShardingContext(sqlStatement, parameters);

        // 3. 确定路由目标
        List<RouteTarget> targets = determineRouteTargets(context);

        // 4. 重写SQL
        List<String> rewrittenSQLs = rewriteSQL(sqlStatement, targets);

        return RouteResult.builder()
            .targets(targets)
            .rewrittenSQLs(rewrittenSQLs)
            .needMerge(targets.size() > 1)
            .build();
    }

    private List<RouteTarget> determineRouteTargets(ShardingContext context) {
        List<RouteTarget> targets = new ArrayList<>();

        if (context.getShardingValue() != null) {
            // 精确路由
            String dataSource = getShardingStrategy(context.getTableName())
                .determineTargetDataSource(context.getTableName(), context.getShardingValue());
            String table = getShardingStrategy(context.getTableName())
                .determineTargetTable(context.getTableName(), context.getShardingValue());

            targets.add(new RouteTarget(dataSource, table));
        } else {
            // 全路由（需要查询所有分片）
            targets.addAll(getAllShardTargets(context.getTableName()));
        }

        return targets;
    }
}
```

**3. 跨分片查询优化：**

**分布式查询执行器：**

```mermaid
flowchart LR
    subgraph query_execution ["分布式查询执行"]
        A["Query Parser<br/>查询解析"]
        B["Execution Plan<br/>执行计划"]
        C["Parallel Execution<br/>并行执行"]
        D["Result Merger<br/>结果合并"]
    end

    subgraph optimization_techniques ["优化技术"]
        E["Predicate Pushdown<br/>谓词下推"]
        F["Projection Pushdown<br/>投影下推"]
        G["Limit Pushdown<br/>限制下推"]
        H["Aggregation Pushdown<br/>聚合下推"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    classDef queryStyle fill:#c8e6c9,stroke:#4caf50
    classDef optimizationStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C,D queryStyle
    class E,F,G,H optimizationStyle
```

**查询优化实现：**
```java
@Service
public class DistributedQueryExecutor {

    @Autowired
    private ShardingRouter shardingRouter;

    @Autowired
    private ExecutorService queryExecutorService;

    public <T> List<T> executeQuery(String sql, Object[] parameters, RowMapper<T> rowMapper) {
        // 1. 路由分析
        RouteResult routeResult = shardingRouter.route(sql, parameters);

        if (routeResult.getTargets().size() == 1) {
            // 单分片查询
            return executeSingleShardQuery(routeResult, rowMapper);
        } else {
            // 多分片查询
            return executeMultiShardQuery(routeResult, rowMapper);
        }
    }

    private <T> List<T> executeMultiShardQuery(RouteResult routeResult, RowMapper<T> rowMapper) {
        List<CompletableFuture<List<T>>> futures = new ArrayList<>();

        // 并行执行查询
        for (int i = 0; i < routeResult.getTargets().size(); i++) {
            RouteTarget target = routeResult.getTargets().get(i);
            String rewrittenSQL = routeResult.getRewrittenSQLs().get(i);

            CompletableFuture<List<T>> future = CompletableFuture.supplyAsync(() -> {
                JdbcTemplate jdbcTemplate = dataSourceManager.getJdbcTemplate(target.getDataSource());
                return jdbcTemplate.query(rewrittenSQL, rowMapper);
            }, queryExecutorService);

            futures.add(future);
        }

        // 等待所有查询完成并合并结果
        List<T> mergedResults = new ArrayList<>();
        for (CompletableFuture<List<T>> future : futures) {
            try {
                mergedResults.addAll(future.get(5, TimeUnit.SECONDS));
            } catch (Exception e) {
                throw new RuntimeException("Query execution failed", e);
            }
        }

        // 结果后处理（排序、分页等）
        return postProcessResults(mergedResults, routeResult.getQueryContext());
    }

    private <T> List<T> postProcessResults(List<T> results, QueryContext context) {
        // 排序
        if (context.hasOrderBy()) {
            results.sort(context.getComparator());
        }

        // 分页
        if (context.hasLimit()) {
            int offset = context.getOffset();
            int limit = context.getLimit();
            int endIndex = Math.min(offset + limit, results.size());
            return results.subList(offset, endIndex);
        }

        return results;
    }
}
```

**4. 数据迁移方案：**

**在线数据迁移：**
```java
@Service
public class OnlineDataMigrationService {

    @Autowired
    private DataMigrationConfig migrationConfig;

    public void migrateData(MigrationPlan plan) {
        // 1. 双写阶段
        enableDualWrite(plan);

        // 2. 历史数据迁移
        migrateHistoricalData(plan);

        // 3. 数据校验
        validateDataConsistency(plan);

        // 4. 切换读流量
        switchReadTraffic(plan);

        // 5. 停止双写
        disableDualWrite(plan);
    }

    private void enableDualWrite(MigrationPlan plan) {
        // 配置双写策略
        DualWriteStrategy strategy = DualWriteStrategy.builder()
            .sourceDataSource(plan.getSourceDataSource())
            .targetDataSource(plan.getTargetDataSource())
            .writeMode(WriteMode.SYNC) // 同步双写
            .failureHandling(FailureHandling.LOG_AND_CONTINUE)
            .build();

        dualWriteManager.enableDualWrite(plan.getTableName(), strategy);
    }

    private void migrateHistoricalData(MigrationPlan plan) {
        String sourceTable = plan.getSourceTable();
        String targetTable = plan.getTargetTable();

        // 分批迁移历史数据
        long minId = getMinId(sourceTable);
        long maxId = getMaxId(sourceTable);
        int batchSize = 1000;

        for (long currentId = minId; currentId <= maxId; currentId += batchSize) {
            long endId = Math.min(currentId + batchSize - 1, maxId);

            // 查询源数据
            List<Map<String, Object>> batch = queryBatch(sourceTable, currentId, endId);

            // 写入目标表
            insertBatch(targetTable, batch);

            // 记录迁移进度
            updateMigrationProgress(plan.getId(), currentId, maxId);

            // 限流控制
            rateLimiter.acquire();
        }
    }

    private void validateDataConsistency(MigrationPlan plan) {
        // 数据一致性校验
        ConsistencyChecker checker = new ConsistencyChecker(
            plan.getSourceDataSource(),
            plan.getTargetDataSource()
        );

        ConsistencyReport report = checker.check(plan.getTableName());

        if (!report.isConsistent()) {
            throw new DataInconsistencyException("Data migration validation failed: " + report);
        }
    }
}
```

**5. 分布式事务处理：**

**Seata分布式事务：**
```java
@Service
public class DistributedTransactionService {

    @GlobalTransactional(rollbackFor = Exception.class)
    public void executeDistributedTransaction(OrderRequest request) {
        try {
            // 1. 创建订单（分片1）
            Order order = createOrder(request);

            // 2. 扣减库存（分片2）
            reduceInventory(request.getProductId(), request.getQuantity());

            // 3. 扣减余额（分片3）
            deductBalance(request.getUserId(), request.getAmount());

            // 4. 创建支付记录（分片4）
            createPaymentRecord(order.getId(), request.getAmount());

        } catch (Exception e) {
            // 全局事务回滚
            throw new BusinessException("Distributed transaction failed", e);
        }
    }

    @Transactional
    public Order createOrder(OrderRequest request) {
        // 本地事务：创建订单
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setProductId(request.getProductId());
        order.setQuantity(request.getQuantity());
        order.setAmount(request.getAmount());

        return orderService.save(order);
    }

    @Transactional
    public void reduceInventory(Long productId, Integer quantity) {
        // 本地事务：扣减库存
        inventoryService.reduce(productId, quantity);
    }
}
```

**最佳实践：**
1. 选择合适的分片键，避免热点数据
2. 设计合理的分片数量，考虑扩容需求
3. 优化跨分片查询，减少全表扫描
4. 实现平滑的数据迁移方案
5. 建立完善的监控和运维体系

## 分布式系统设计

### 4. 设计一个分布式ID生成系统，要求全局唯一、高性能、高可用，请对比不同方案的优缺点 ⭐⭐⭐⭐

#### 问题分析
考查候选人对分布式系统核心组件设计的理解，包括一致性、可用性、性能等方面的权衡。

#### 标准答案

**分布式ID生成方案对比：**

```mermaid
flowchart TB
    subgraph id_generation_methods ["ID生成方法"]
        A["UUID<br/>通用唯一标识符"]
        B["Snowflake<br/>雪花算法"]
        C["Database<br/>数据库自增"]
        D["Redis<br/>Redis计数器"]
    end

    subgraph evaluation_criteria ["评估标准"]
        E["Uniqueness<br/>唯一性"]
        F["Performance<br/>性能"]
        G["Availability<br/>可用性"]
        H["Ordering<br/>有序性"]
    end

    subgraph implementation_challenges ["实现挑战"]
        I["Clock Synchronization<br/>时钟同步"]
        J["Machine ID Management<br/>机器ID管理"]
        K["Sequence Overflow<br/>序列号溢出"]
        L["Network Partition<br/>网络分区"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef methodStyle fill:#c8e6c9,stroke:#4caf50
    classDef criteriaStyle fill:#e3f2fd,stroke:#2196f3
    classDef challengeStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D methodStyle
    class E,F,G,H criteriaStyle
    class I,J,K,L challengeStyle
```

**详细解析：**

**1. Snowflake算法实现：**

**标准Snowflake实现：**
```java
@Component
public class SnowflakeIdGenerator {

    // 时间戳位数
    private final long timestampBits = 41L;
    // 机器ID位数
    private final long machineIdBits = 10L;
    // 序列号位数
    private final long sequenceBits = 12L;

    // 最大值计算
    private final long maxMachineId = ~(-1L << machineIdBits);
    private final long maxSequence = ~(-1L << sequenceBits);

    // 位移量
    private final long machineIdShift = sequenceBits;
    private final long timestampShift = sequenceBits + machineIdBits;

    // 起始时间戳 (2023-01-01)
    private final long epoch = 1672531200000L;

    private final long machineId;
    private long lastTimestamp = -1L;
    private long sequence = 0L;

    public SnowflakeIdGenerator() {
        this.machineId = getMachineId();
        if (machineId > maxMachineId || machineId < 0) {
            throw new IllegalArgumentException("Machine ID must be between 0 and " + maxMachineId);
        }
    }

    public synchronized long nextId() {
        long timestamp = getCurrentTimestamp();

        // 时钟回拨检测
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate id");
        }

        if (timestamp == lastTimestamp) {
            // 同一毫秒内，序列号递增
            sequence = (sequence + 1) & maxSequence;
            if (sequence == 0) {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            // 新的毫秒，序列号重置
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        // 组装ID
        return ((timestamp - epoch) << timestampShift) |
               (machineId << machineIdShift) |
               sequence;
    }

    private long waitNextMillis(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }

    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    private long getMachineId() {
        // 从配置中心获取或基于IP生成
        return MachineIdRegistry.getInstance().getMachineId();
    }
}
```

**改进版Snowflake（解决时钟回拨）：**
```java
@Component
public class ImprovedSnowflakeIdGenerator {

    private final long machineId;
    private long lastTimestamp = -1L;
    private long sequence = 0L;

    // 时钟回拨容忍时间（毫秒）
    private final long clockBackwardToleranceMs = 5000L;

    public synchronized long nextId() {
        long timestamp = getCurrentTimestamp();

        // 处理时钟回拨
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;

            if (offset <= clockBackwardToleranceMs) {
                // 小幅回拨，等待时钟追上
                try {
                    Thread.sleep(offset);
                    timestamp = getCurrentTimestamp();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for clock", e);
                }
            } else {
                // 大幅回拨，抛出异常
                throw new RuntimeException("Clock moved backwards by " + offset + "ms");
            }
        }

        // 生成ID逻辑...
        return generateId(timestamp);
    }

    // 使用备用序列号处理时钟回拨
    private long generateIdWithBackupSequence(long timestamp) {
        if (timestamp == lastTimestamp) {
            sequence = (sequence + 1) & maxSequence;
            if (sequence == 0) {
                // 使用备用序列号空间
                return generateBackupId(timestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = timestamp;
        return assembleId(timestamp, machineId, sequence);
    }
}
```

**2. 机器ID管理方案：**

**基于ZooKeeper的机器ID分配：**
```java
@Component
public class ZooKeeperMachineIdRegistry implements MachineIdRegistry {

    private final CuratorFramework zkClient;
    private final String machineIdPath = "/snowflake/machine-ids";
    private final int maxMachineId = 1023; // 10位，最大1023

    @Override
    public long getMachineId() {
        try {
            // 确保路径存在
            zkClient.create()
                   .creatingParentsIfNeeded()
                   .forPath(machineIdPath);

            // 尝试获取可用的机器ID
            for (int i = 0; i <= maxMachineId; i++) {
                String nodePath = machineIdPath + "/" + i;
                try {
                    // 创建临时顺序节点
                    zkClient.create()
                           .withMode(CreateMode.EPHEMERAL)
                           .forPath(nodePath, getLocalHostInfo().getBytes());

                    // 成功创建，返回机器ID
                    return i;
                } catch (KeeperException.NodeExistsException e) {
                    // 节点已存在，尝试下一个ID
                    continue;
                }
            }

            throw new RuntimeException("No available machine ID");

        } catch (Exception e) {
            throw new RuntimeException("Failed to get machine ID from ZooKeeper", e);
        }
    }

    private String getLocalHostInfo() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress() + ":" + ManagementFactory.getRuntimeMXBean().getName();
        } catch (Exception e) {
            return "unknown";
        }
    }
}
```

**3. 数据库方案实现：**

**号段模式：**
```java
@Service
public class SegmentIdGenerator {

    @Autowired
    private IdSegmentMapper segmentMapper;

    private final Map<String, SegmentBuffer> segmentBuffers = new ConcurrentHashMap<>();

    public long nextId(String bizType) {
        SegmentBuffer buffer = segmentBuffers.computeIfAbsent(bizType,
            k -> new SegmentBuffer(k, this::loadSegment));

        return buffer.nextId();
    }

    private Segment loadSegment(String bizType) {
        // 数据库获取号段
        IdSegment segment = segmentMapper.getAndUpdateSegment(bizType);

        return Segment.builder()
            .bizType(bizType)
            .currentId(segment.getCurrentId())
            .maxId(segment.getMaxId())
            .step(segment.getStep())
            .build();
    }

    private static class SegmentBuffer {
        private final String bizType;
        private final Function<String, Segment> segmentLoader;
        private volatile Segment currentSegment;
        private volatile Segment nextSegment;
        private final AtomicBoolean isLoadingNext = new AtomicBoolean(false);

        public long nextId() {
            if (currentSegment == null) {
                synchronized (this) {
                    if (currentSegment == null) {
                        currentSegment = segmentLoader.apply(bizType);
                    }
                }
            }

            long id = currentSegment.nextId();

            // 预加载下一个号段
            if (currentSegment.isHalfUsed() && !isLoadingNext.get()) {
                if (isLoadingNext.compareAndSet(false, true)) {
                    CompletableFuture.runAsync(() -> {
                        try {
                            nextSegment = segmentLoader.apply(bizType);
                        } finally {
                            isLoadingNext.set(false);
                        }
                    });
                }
            }

            // 切换到下一个号段
            if (currentSegment.isExhausted() && nextSegment != null) {
                synchronized (this) {
                    if (currentSegment.isExhausted() && nextSegment != null) {
                        currentSegment = nextSegment;
                        nextSegment = null;
                    }
                }
                return nextId(); // 递归获取新号段的ID
            }

            return id;
        }
    }
}
```

**4. Redis方案实现：**

**Redis Lua脚本保证原子性：**
```java
@Service
public class RedisIdGenerator {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private final String luaScript =
        "local key = KEYS[1] " +
        "local step = tonumber(ARGV[1]) " +
        "local current = redis.call('GET', key) " +
        "if current == false then " +
        "  redis.call('SET', key, step) " +
        "  redis.call('EXPIRE', key, 86400) " +
        "  return step " +
        "else " +
        "  return redis.call('INCRBY', key, step) " +
        "end";

    private final DefaultRedisScript<Long> redisScript;

    public RedisIdGenerator() {
        redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(luaScript);
        redisScript.setResultType(Long.class);
    }

    public long nextId(String bizType) {
        String key = "id_generator:" + bizType;
        Long result = redisTemplate.execute(redisScript,
                                          Collections.singletonList(key),
                                          "1");
        return result != null ? result : 0L;
    }

    // 批量获取ID
    public List<Long> nextIds(String bizType, int count) {
        String key = "id_generator:" + bizType;
        Long maxId = redisTemplate.execute(redisScript,
                                         Collections.singletonList(key),
                                         String.valueOf(count));

        List<Long> ids = new ArrayList<>();
        for (long i = maxId - count + 1; i <= maxId; i++) {
            ids.add(i);
        }
        return ids;
    }
}
```

**5. 方案对比分析：**

| 方案 | 唯一性 | 性能 | 可用性 | 有序性 | 复杂度 |
|------|--------|------|--------|--------|--------|
| **UUID** | ✅ 强 | ✅ 高 | ✅ 高 | ❌ 无序 | ⭐ 简单 |
| **Snowflake** | ✅ 强 | ✅ 高 | ⚠️ 中等 | ✅ 有序 | ⭐⭐⭐ 中等 |
| **数据库** | ✅ 强 | ❌ 低 | ❌ 低 | ✅ 有序 | ⭐⭐ 简单 |
| **Redis** | ✅ 强 | ⭐⭐ 中等 | ⭐⭐ 中等 | ✅ 有序 | ⭐⭐ 简单 |

**6. 混合方案设计：**

**多策略ID生成器：**
```java
@Service
public class HybridIdGenerator {

    @Autowired
    private SnowflakeIdGenerator snowflakeGenerator;

    @Autowired
    private SegmentIdGenerator segmentGenerator;

    @Autowired
    private RedisIdGenerator redisGenerator;

    public long nextId(String bizType, IdGenerationStrategy strategy) {
        switch (strategy) {
            case SNOWFLAKE:
                return snowflakeGenerator.nextId();
            case SEGMENT:
                return segmentGenerator.nextId(bizType);
            case REDIS:
                return redisGenerator.nextId(bizType);
            case AUTO:
                return autoSelectStrategy(bizType);
            default:
                throw new IllegalArgumentException("Unsupported strategy: " + strategy);
        }
    }

    private long autoSelectStrategy(String bizType) {
        // 根据业务类型和系统状态自动选择策略
        if (isHighConcurrency(bizType)) {
            return snowflakeGenerator.nextId();
        } else if (requiresStrictOrdering(bizType)) {
            return segmentGenerator.nextId(bizType);
        } else {
            return redisGenerator.nextId(bizType);
        }
    }
}
```

**最佳实践：**
1. 根据业务需求选择合适的ID生成策略
2. 实现多种方案的降级和切换机制
3. 建立完善的监控和告警体系
4. 考虑时钟同步和机器ID管理
5. 设计合理的容错和恢复机制

## 微服务架构

### 5. 如何设计微服务架构？请详细说明服务拆分原则、服务间通信、数据一致性等关键问题 ⭐⭐⭐⭐⭐

#### 问题分析
考查候选人对微服务架构设计的全面理解，包括服务治理、分布式事务、服务网格等高级概念。

#### 标准答案

**微服务架构全景图：**

```mermaid
flowchart TB
    subgraph client_layer ["客户端层"]
        A["Web Client<br/>Web客户端"]
        B["Mobile Client<br/>移动客户端"]
        C["Third Party<br/>第三方系统"]
    end

    subgraph gateway_layer ["网关层"]
        D["API Gateway<br/>API网关"]
        E["Load Balancer<br/>负载均衡器"]
        F["Rate Limiter<br/>限流器"]
    end

    subgraph service_layer ["服务层"]
        G["User Service<br/>用户服务"]
        H["Order Service<br/>订单服务"]
        I["Payment Service<br/>支付服务"]
        J["Inventory Service<br/>库存服务"]
    end

    subgraph infrastructure_layer ["基础设施层"]
        K["Service Registry<br/>服务注册中心"]
        L["Config Center<br/>配置中心"]
        M["Message Queue<br/>消息队列"]
        N["Distributed Cache<br/>分布式缓存"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J

    G --> K
    H --> K
    I --> K
    J --> K

    G --> L
    H --> L
    I --> L
    J --> L

    G --> M
    H --> M
    I --> M
    J --> M

    G --> N
    H --> N
    I --> N
    J --> N

    classDef clientStyle fill:#c8e6c9,stroke:#4caf50
    classDef gatewayStyle fill:#e3f2fd,stroke:#2196f3
    classDef serviceStyle fill:#fff3e0,stroke:#ff9800
    classDef infraStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C clientStyle
    class D,E,F gatewayStyle
    class G,H,I,J serviceStyle
    class K,L,M,N infraStyle
```

**详细解析：**

**1. 服务拆分原则：**

**领域驱动设计（DDD）拆分：**
```java
// 用户领域服务
@Service
public class UserDomainService {

    // 用户聚合根
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserProfileRepository profileRepository;

    public User createUser(CreateUserCommand command) {
        // 领域逻辑：用户创建
        User user = User.builder()
            .username(command.getUsername())
            .email(command.getEmail())
            .build();

        // 领域事件
        user.addDomainEvent(new UserCreatedEvent(user.getId()));

        return userRepository.save(user);
    }

    public void updateUserProfile(UpdateProfileCommand command) {
        User user = userRepository.findById(command.getUserId())
            .orElseThrow(() -> new UserNotFoundException(command.getUserId()));

        // 领域逻辑验证
        user.validateProfileUpdate(command);

        UserProfile profile = profileRepository.findByUserId(command.getUserId());
        profile.update(command);

        profileRepository.save(profile);
    }
}

// 订单领域服务
@Service
public class OrderDomainService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderItemRepository orderItemRepository;

    @Autowired
    private DomainEventPublisher eventPublisher;

    public Order createOrder(CreateOrderCommand command) {
        // 订单聚合根
        Order order = Order.builder()
            .userId(command.getUserId())
            .totalAmount(command.getTotalAmount())
            .status(OrderStatus.PENDING)
            .build();

        // 订单项
        List<OrderItem> items = command.getItems().stream()
            .map(item -> OrderItem.builder()
                .orderId(order.getId())
                .productId(item.getProductId())
                .quantity(item.getQuantity())
                .price(item.getPrice())
                .build())
            .collect(Collectors.toList());

        order.setItems(items);

        // 领域事件
        order.addDomainEvent(new OrderCreatedEvent(order.getId(), order.getUserId()));

        Order savedOrder = orderRepository.save(order);

        // 发布领域事件
        eventPublisher.publish(order.getDomainEvents());

        return savedOrder;
    }
}
```

**服务拆分策略：**
```java
@Component
public class ServiceDecompositionStrategy {

    // 按业务能力拆分
    public List<ServiceBoundary> decomposeByBusinessCapability() {
        return Arrays.asList(
            ServiceBoundary.builder()
                .serviceName("user-service")
                .capabilities(Arrays.asList("用户注册", "用户认证", "用户资料管理"))
                .dataOwnership(Arrays.asList("user", "user_profile", "user_auth"))
                .build(),

            ServiceBoundary.builder()
                .serviceName("order-service")
                .capabilities(Arrays.asList("订单创建", "订单查询", "订单状态管理"))
                .dataOwnership(Arrays.asList("order", "order_item"))
                .build(),

            ServiceBoundary.builder()
                .serviceName("payment-service")
                .capabilities(Arrays.asList("支付处理", "退款处理", "支付查询"))
                .dataOwnership(Arrays.asList("payment", "refund"))
                .build()
        );
    }

    // 按数据模型拆分
    public List<ServiceBoundary> decomposeByDataModel() {
        return Arrays.asList(
            ServiceBoundary.builder()
                .serviceName("product-catalog-service")
                .dataModel("Product Catalog")
                .operations(Arrays.asList("CRUD", "Search", "Category Management"))
                .build(),

            ServiceBoundary.builder()
                .serviceName("inventory-service")
                .dataModel("Inventory")
                .operations(Arrays.asList("Stock Management", "Reservation", "Allocation"))
                .build()
        );
    }
}
```

**2. 服务间通信设计：**

**同步通信 - OpenFeign：**
```java
// 用户服务客户端
@FeignClient(name = "user-service", fallback = UserServiceFallback.class)
public interface UserServiceClient {

    @GetMapping("/users/{userId}")
    UserDTO getUser(@PathVariable("userId") Long userId);

    @PostMapping("/users/{userId}/validate")
    ValidationResult validateUser(@PathVariable("userId") Long userId,
                                @RequestBody ValidationRequest request);
}

// 降级处理
@Component
public class UserServiceFallback implements UserServiceClient {

    @Override
    public UserDTO getUser(Long userId) {
        return UserDTO.builder()
            .id(userId)
            .username("unknown")
            .status("UNAVAILABLE")
            .build();
    }

    @Override
    public ValidationResult validateUser(Long userId, ValidationRequest request) {
        return ValidationResult.builder()
            .valid(false)
            .reason("User service unavailable")
            .build();
    }
}

// 订单服务中使用
@Service
public class OrderService {

    @Autowired
    private UserServiceClient userServiceClient;

    @Autowired
    private PaymentServiceClient paymentServiceClient;

    public Order createOrder(CreateOrderRequest request) {
        // 1. 验证用户
        UserDTO user = userServiceClient.getUser(request.getUserId());
        if (!"ACTIVE".equals(user.getStatus())) {
            throw new InvalidUserException("User is not active");
        }

        // 2. 创建订单
        Order order = buildOrder(request);
        order = orderRepository.save(order);

        // 3. 异步处理支付
        CompletableFuture.runAsync(() -> {
            try {
                PaymentRequest paymentRequest = PaymentRequest.builder()
                    .orderId(order.getId())
                    .amount(order.getTotalAmount())
                    .userId(request.getUserId())
                    .build();

                paymentServiceClient.processPayment(paymentRequest);
            } catch (Exception e) {
                log.error("Payment processing failed for order: {}", order.getId(), e);
                // 发送补偿事件
                eventPublisher.publish(new PaymentFailedEvent(order.getId()));
            }
        });

        return order;
    }
}
```

**异步通信 - 事件驱动：**
```java
// 事件发布
@Service
public class OrderEventPublisher {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void publishOrderCreated(OrderCreatedEvent event) {
        rabbitTemplate.convertAndSend(
            "order.exchange",
            "order.created",
            event,
            message -> {
                message.getMessageProperties().setHeader("eventType", "ORDER_CREATED");
                message.getMessageProperties().setHeader("version", "1.0");
                return message;
            }
        );
    }

    public void publishOrderPaid(OrderPaidEvent event) {
        rabbitTemplate.convertAndSend(
            "order.exchange",
            "order.paid",
            event
        );
    }
}

// 事件消费
@Component
public class InventoryEventListener {

    @Autowired
    private InventoryService inventoryService;

    @RabbitListener(queues = "inventory.order.created.queue")
    public void handleOrderCreated(OrderCreatedEvent event) {
        try {
            // 预留库存
            inventoryService.reserveInventory(
                event.getOrderId(),
                event.getOrderItems()
            );

            log.info("Inventory reserved for order: {}", event.getOrderId());
        } catch (InsufficientInventoryException e) {
            // 发送库存不足事件
            eventPublisher.publish(new InventoryInsufficientEvent(event.getOrderId()));
        }
    }

    @RabbitListener(queues = "inventory.order.paid.queue")
    public void handleOrderPaid(OrderPaidEvent event) {
        // 确认库存扣减
        inventoryService.confirmInventoryDeduction(event.getOrderId());
        log.info("Inventory confirmed for order: {}", event.getOrderId());
    }
}
```

**3. 分布式事务处理：**

**Saga模式实现：**

```mermaid
flowchart LR
    subgraph saga_pattern ["Saga事务模式"]
        A["Order Created<br/>订单创建"]
        B["Inventory Reserved<br/>库存预留"]
        C["Payment Processed<br/>支付处理"]
        D["Order Confirmed<br/>订单确认"]
    end

    subgraph compensation_flow ["补偿流程"]
        E["Cancel Payment<br/>取消支付"]
        F["Release Inventory<br/>释放库存"]
        G["Cancel Order<br/>取消订单"]
    end

    A --> B
    B --> C
    C --> D

    D -.-> E
    E -.-> F
    F -.-> G

    classDef sagaStyle fill:#c8e6c9,stroke:#4caf50
    classDef compensationStyle fill:#ffcdd2,stroke:#f44336

    class A,B,C,D sagaStyle
    class E,F,G compensationStyle
```

**Saga编排器实现：**
```java
@Service
public class OrderSagaOrchestrator {

    @Autowired
    private OrderService orderService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private SagaManager sagaManager;

    public void executeOrderSaga(CreateOrderRequest request) {
        SagaDefinition saga = SagaDefinition.builder()
            .sagaId(UUID.randomUUID().toString())
            .build();

        // 定义Saga步骤
        saga.addStep(SagaStep.builder()
            .stepName("CREATE_ORDER")
            .action(() -> orderService.createOrder(request))
            .compensation(() -> orderService.cancelOrder(request.getOrderId()))
            .build());

        saga.addStep(SagaStep.builder()
            .stepName("RESERVE_INVENTORY")
            .action(() -> inventoryService.reserveInventory(request.getOrderItems()))
            .compensation(() -> inventoryService.releaseInventory(request.getOrderItems()))
            .build());

        saga.addStep(SagaStep.builder()
            .stepName("PROCESS_PAYMENT")
            .action(() -> paymentService.processPayment(request.getPaymentInfo()))
            .compensation(() -> paymentService.refundPayment(request.getPaymentInfo()))
            .build());

        saga.addStep(SagaStep.builder()
            .stepName("CONFIRM_ORDER")
            .action(() -> orderService.confirmOrder(request.getOrderId()))
            .compensation(() -> orderService.cancelOrder(request.getOrderId()))
            .build());

        // 执行Saga
        sagaManager.execute(saga);
    }
}

@Component
public class SagaManager {

    public void execute(SagaDefinition saga) {
        List<SagaStep> executedSteps = new ArrayList<>();

        try {
            for (SagaStep step : saga.getSteps()) {
                step.getAction().run();
                executedSteps.add(step);

                // 记录执行状态
                sagaStateRepository.updateStepStatus(
                    saga.getSagaId(),
                    step.getStepName(),
                    SagaStepStatus.COMPLETED
                );
            }

            // Saga成功完成
            sagaStateRepository.updateSagaStatus(saga.getSagaId(), SagaStatus.COMPLETED);

        } catch (Exception e) {
            log.error("Saga execution failed: {}", saga.getSagaId(), e);

            // 执行补偿
            compensate(executedSteps);

            sagaStateRepository.updateSagaStatus(saga.getSagaId(), SagaStatus.COMPENSATED);
        }
    }

    private void compensate(List<SagaStep> executedSteps) {
        // 逆序执行补偿
        Collections.reverse(executedSteps);

        for (SagaStep step : executedSteps) {
            try {
                step.getCompensation().run();
                log.info("Compensation completed for step: {}", step.getStepName());
            } catch (Exception e) {
                log.error("Compensation failed for step: {}", step.getStepName(), e);
                // 补偿失败需要人工介入
            }
        }
    }
}
```

**4. 服务治理：**

**服务注册与发现：**
```java
@RestController
@RequestMapping("/health")
public class HealthCheckController {

    @Autowired
    private DatabaseHealthIndicator databaseHealth;

    @Autowired
    private RedisHealthIndicator redisHealth;

    @GetMapping
    public ResponseEntity<HealthStatus> healthCheck() {
        HealthStatus status = HealthStatus.builder()
            .status("UP")
            .timestamp(System.currentTimeMillis())
            .checks(Arrays.asList(
                databaseHealth.check(),
                redisHealth.check()
            ))
            .build();

        boolean allHealthy = status.getChecks().stream()
            .allMatch(check -> "UP".equals(check.getStatus()));

        return ResponseEntity.status(allHealthy ? 200 : 503).body(status);
    }
}

// 服务发现客户端
@Component
public class ServiceDiscoveryClient {

    @Autowired
    private DiscoveryClient discoveryClient;

    public List<ServiceInstance> getAvailableInstances(String serviceName) {
        return discoveryClient.getInstances(serviceName).stream()
            .filter(this::isHealthy)
            .collect(Collectors.toList());
    }

    private boolean isHealthy(ServiceInstance instance) {
        try {
            String healthUrl = "http://" + instance.getHost() + ":" +
                             instance.getPort() + "/health";

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);

            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            return false;
        }
    }
}
```

**最佳实践：**
1. 基于业务领域进行服务拆分
2. 设计合理的服务间通信机制
3. 实现分布式事务和数据一致性
4. 建立完善的服务治理体系
5. 实现全链路监控和故障排查

## 消息队列应用

### 6. 如何设计一个高可用的消息队列系统？请详细说明消息可靠性、顺序性、幂等性保证机制 ⭐⭐⭐⭐⭐

#### 问题分析
考查候选人对消息队列核心特性的深度理解，包括可靠性保证、性能优化、故障处理等关键技术。

#### 标准答案

**消息队列架构设计：**

```mermaid
flowchart TB
    subgraph producer_layer ["生产者层"]
        A["Producer 1<br/>生产者1"]
        B["Producer 2<br/>生产者2"]
        C["Producer N<br/>生产者N"]
    end

    subgraph broker_layer ["Broker层"]
        D["Load Balancer<br/>负载均衡"]
        E["Broker 1<br/>主节点"]
        F["Broker 2<br/>从节点"]
        G["Broker 3<br/>从节点"]
    end

    subgraph storage_layer ["存储层"]
        H["Message Store<br/>消息存储"]
        I["Index Store<br/>索引存储"]
        J["Metadata Store<br/>元数据存储"]
    end

    subgraph consumer_layer ["消费者层"]
        K["Consumer Group 1<br/>消费者组1"]
        L["Consumer Group 2<br/>消费者组2"]
        M["Consumer Group N<br/>消费者组N"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    D --> F
    D --> G

    E --> H
    F --> H
    G --> H

    E --> I
    F --> I
    G --> I

    E --> J
    F --> J
    G --> J

    E --> K
    F --> L
    G --> M

    classDef producerStyle fill:#c8e6c9,stroke:#4caf50
    classDef brokerStyle fill:#e3f2fd,stroke:#2196f3
    classDef storageStyle fill:#fff3e0,stroke:#ff9800
    classDef consumerStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C producerStyle
    class D,E,F,G brokerStyle
    class H,I,J storageStyle
    class K,L,M consumerStyle
```

**详细解析：**

**1. 消息可靠性保证：**

**生产者可靠性实现：**
```java
@Service
public class ReliableMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    public void sendReliableMessage(String exchange, String routingKey, Object message) {
        // 1. 本地消息表模式
        transactionTemplate.execute(status -> {
            try {
                // 保存业务数据和消息记录
                processBusinessLogic(message);

                MessageRecord record = MessageRecord.builder()
                    .messageId(UUID.randomUUID().toString())
                    .exchange(exchange)
                    .routingKey(routingKey)
                    .payload(JsonUtils.toJson(message))
                    .status(MessageStatus.PENDING)
                    .createTime(System.currentTimeMillis())
                    .retryCount(0)
                    .build();

                messageRepository.save(record);
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Failed to save message", e);
            }
        });

        // 2. 异步发送消息
        CompletableFuture.runAsync(() -> {
            sendMessageWithRetry(exchange, routingKey, message);
        });
    }

    private void sendMessageWithRetry(String exchange, String routingKey, Object message) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 设置消息属性
                MessageProperties properties = new MessageProperties();
                properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT); // 持久化
                properties.setHeader("messageId", UUID.randomUUID().toString());
                properties.setHeader("timestamp", System.currentTimeMillis());

                // 发送消息
                rabbitTemplate.send(exchange, routingKey,
                    new Message(JsonUtils.toJson(message).getBytes(), properties));

                // 更新消息状态
                updateMessageStatus(message, MessageStatus.SENT);
                break;

            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    updateMessageStatus(message, MessageStatus.FAILED);
                    log.error("Failed to send message after {} retries", maxRetries, e);
                } else {
                    // 指数退避重试
                    try {
                        Thread.sleep(1000 * (1 << retryCount));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    // 定时任务补偿机制
    @Scheduled(fixedRate = 30000)
    public void compensateFailedMessages() {
        List<MessageRecord> failedMessages = messageRepository
            .findByStatusAndCreateTimeBefore(
                MessageStatus.PENDING,
                System.currentTimeMillis() - 300000 // 5分钟前
            );

        for (MessageRecord record : failedMessages) {
            if (record.getRetryCount() < 5) {
                sendMessageWithRetry(record.getExchange(),
                                   record.getRoutingKey(),
                                   record.getPayload());
                record.setRetryCount(record.getRetryCount() + 1);
                messageRepository.save(record);
            } else {
                record.setStatus(MessageStatus.DEAD);
                messageRepository.save(record);
            }
        }
    }
}
```

**消费者可靠性实现：**
```java
@Component
public class ReliableMessageConsumer {

    @Autowired
    private MessageProcessingService processingService;

    @Autowired
    private IdempotentService idempotentService;

    @RabbitListener(queues = "reliable.queue")
    public void handleMessage(@Payload String messageBody,
                            @Header Map<String, Object> headers,
                            Channel channel,
                            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {

        String messageId = (String) headers.get("messageId");

        try {
            // 1. 幂等性检查
            if (idempotentService.isProcessed(messageId)) {
                log.info("Message already processed: {}", messageId);
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 2. 业务处理
            processingService.processMessage(messageBody);

            // 3. 记录处理状态
            idempotentService.markAsProcessed(messageId);

            // 4. 手动确认
            channel.basicAck(deliveryTag, false);

        } catch (BusinessException e) {
            // 业务异常，不重试
            log.error("Business error processing message: {}", messageId, e);
            channel.basicNack(deliveryTag, false, false);

        } catch (Exception e) {
            // 系统异常，重试
            log.error("System error processing message: {}", messageId, e);

            int retryCount = getRetryCount(headers);
            if (retryCount < 3) {
                // 重新入队
                channel.basicNack(deliveryTag, false, true);
            } else {
                // 进入死信队列
                channel.basicNack(deliveryTag, false, false);
            }
        }
    }
}
```

**2. 消息顺序性保证：**

**顺序消息实现：**

```mermaid
flowchart LR
    subgraph ordered_messaging ["顺序消息"]
        A["Partition by Key<br/>按键分区"]
        B["Single Thread Consumer<br/>单线程消费"]
        C["Message Queue<br/>消息队列"]
        D["Sequential Processing<br/>顺序处理"]
    end

    subgraph ordering_strategies ["排序策略"]
        E["Global Ordering<br/>全局有序"]
        F["Partial Ordering<br/>局部有序"]
        G["FIFO Ordering<br/>FIFO有序"]
        H["Causal Ordering<br/>因果有序"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    classDef messagingStyle fill:#c8e6c9,stroke:#4caf50
    classDef strategyStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C,D messagingStyle
    class E,F,G,H strategyStyle
```

**顺序消息生产者：**
```java
@Service
public class OrderedMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendOrderedMessage(String orderKey, Object message) {
        // 1. 计算分区
        int partition = calculatePartition(orderKey);
        String routingKey = "ordered.queue." + partition;

        // 2. 设置消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("orderKey", orderKey);
        properties.setHeader("sequence", getNextSequence(orderKey));
        properties.setHeader("timestamp", System.currentTimeMillis());

        // 3. 发送到指定分区
        rabbitTemplate.send("ordered.exchange", routingKey,
            new Message(JsonUtils.toJson(message).getBytes(), properties));
    }

    private int calculatePartition(String orderKey) {
        // 一致性哈希确保相同key的消息进入同一分区
        return Math.abs(orderKey.hashCode()) % getPartitionCount();
    }

    private long getNextSequence(String orderKey) {
        // Redis原子递增生成序列号
        return redisTemplate.opsForValue().increment("seq:" + orderKey);
    }
}
```

**顺序消息消费者：**
```java
@Component
public class OrderedMessageConsumer {

    private final Map<String, Long> lastProcessedSequence = new ConcurrentHashMap<>();
    private final Map<String, Queue<Message>> pendingMessages = new ConcurrentHashMap<>();

    @RabbitListener(queues = "ordered.queue.0")
    public void handleOrderedMessage(@Payload String messageBody,
                                   @Header Map<String, Object> headers) {

        String orderKey = (String) headers.get("orderKey");
        Long sequence = (Long) headers.get("sequence");

        synchronized (orderKey.intern()) {
            Long lastSequence = lastProcessedSequence.get(orderKey);

            if (lastSequence == null || sequence == lastSequence + 1) {
                // 按序到达，直接处理
                processMessage(messageBody, orderKey);
                lastProcessedSequence.put(orderKey, sequence);

                // 检查是否有待处理的后续消息
                processePendingMessages(orderKey);

            } else if (sequence > lastSequence + 1) {
                // 乱序到达，暂存
                Queue<Message> pending = pendingMessages.computeIfAbsent(
                    orderKey, k -> new PriorityQueue<>(Comparator.comparing(Message::getSequence))
                );
                pending.offer(new Message(messageBody, sequence));

            } else {
                // 重复消息，忽略
                log.warn("Duplicate message received: orderKey={}, sequence={}", orderKey, sequence);
            }
        }
    }

    private void processePendingMessages(String orderKey) {
        Queue<Message> pending = pendingMessages.get(orderKey);
        if (pending == null) return;

        Long lastSequence = lastProcessedSequence.get(orderKey);

        while (!pending.isEmpty()) {
            Message nextMessage = pending.peek();
            if (nextMessage.getSequence() == lastSequence + 1) {
                pending.poll();
                processMessage(nextMessage.getBody(), orderKey);
                lastProcessedSequence.put(orderKey, nextMessage.getSequence());
                lastSequence = nextMessage.getSequence();
            } else {
                break;
            }
        }
    }
}
```

**3. 幂等性保证机制：**

**分布式幂等性实现：**
```java
@Service
public class IdempotentService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private IdempotentRecordRepository recordRepository;

    public boolean isProcessed(String messageId) {
        // 1. Redis快速检查
        String key = "idempotent:" + messageId;
        Boolean exists = redisTemplate.hasKey(key);
        if (Boolean.TRUE.equals(exists)) {
            return true;
        }

        // 2. 数据库检查
        return recordRepository.existsByMessageId(messageId);
    }

    @Transactional
    public void markAsProcessed(String messageId) {
        // 1. 数据库记录
        IdempotentRecord record = IdempotentRecord.builder()
            .messageId(messageId)
            .processTime(System.currentTimeMillis())
            .status("PROCESSED")
            .build();

        recordRepository.save(record);

        // 2. Redis缓存
        String key = "idempotent:" + messageId;
        redisTemplate.opsForValue().set(key, "1", Duration.ofHours(24));
    }

    // 基于业务键的幂等性
    public boolean processWithBusinessKey(String businessKey, Supplier<Object> processor) {
        String lockKey = "business_lock:" + businessKey;
        String requestId = UUID.randomUUID().toString();

        try {
            // 获取分布式锁
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, requestId, Duration.ofMinutes(5));

            if (!Boolean.TRUE.equals(acquired)) {
                return false; // 获取锁失败
            }

            // 检查是否已处理
            if (isBusinessKeyProcessed(businessKey)) {
                return true; // 已处理
            }

            // 执行业务逻辑
            Object result = processor.get();

            // 标记为已处理
            markBusinessKeyAsProcessed(businessKey, result);

            return true;

        } finally {
            // 释放锁
            releaseLock(lockKey, requestId);
        }
    }

    private void releaseLock(String lockKey, String requestId) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) else return 0 end";

        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class),
                            Collections.singletonList(lockKey), requestId);
    }
}
```

**最佳实践：**
1. 实现多层次的消息可靠性保证
2. 根据业务需求选择合适的顺序性策略
3. 建立完善的幂等性机制
4. 设计合理的重试和补偿机制
5. 建立监控和告警体系

## 秒杀系统设计

### 7. 设计一个秒杀系统，如何应对瞬时高并发？请详细说明限流、削峰、防刷等关键技术 ⭐⭐⭐⭐⭐

#### 问题分析
考查候选人对极端高并发场景的架构设计能力，包括流量控制、系统保护、用户体验等多方面的综合考虑。

#### 标准答案

**秒杀系统架构图：**

```mermaid
flowchart TB
    subgraph user_layer ["用户层"]
        A["Web Browser<br/>浏览器"]
        B["Mobile App<br/>移动应用"]
        C["H5 Page<br/>H5页面"]
    end

    subgraph cdn_layer ["CDN层"]
        D["Static CDN<br/>静态CDN"]
        E["Dynamic CDN<br/>动态CDN"]
        F["Edge Cache<br/>边缘缓存"]
    end

    subgraph gateway_layer ["网关层"]
        G["Rate Limiter<br/>限流器"]
        H["Anti-Bot<br/>防刷机制"]
        I["Load Balancer<br/>负载均衡"]
    end

    subgraph application_layer ["应用层"]
        J["Seckill Service<br/>秒杀服务"]
        K["Order Service<br/>订单服务"]
        L["Payment Service<br/>支付服务"]
        M["Inventory Service<br/>库存服务"]
    end

    subgraph cache_layer ["缓存层"]
        N["Redis Cluster<br/>Redis集群"]
        O["Local Cache<br/>本地缓存"]
        P["Memory Queue<br/>内存队列"]
    end

    subgraph storage_layer ["存储层"]
        Q["MySQL Master<br/>MySQL主库"]
        R["MySQL Slave<br/>MySQL从库"]
        S["Message Queue<br/>消息队列"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G

    G --> H
    H --> I
    I --> J

    J --> K
    K --> L
    L --> M

    J --> N
    K --> O
    L --> P

    J --> Q
    K --> R
    M --> S

    classDef userStyle fill:#c8e6c9,stroke:#4caf50
    classDef cdnStyle fill:#e3f2fd,stroke:#2196f3
    classDef gatewayStyle fill:#fff3e0,stroke:#ff9800
    classDef appStyle fill:#f3e5f5,stroke:#9c27b0
    classDef cacheStyle fill:#ffecb3,stroke:#ffc107
    classDef storageStyle fill:#ffcdd2,stroke:#f44336

    class A,B,C userStyle
    class D,E,F cdnStyle
    class G,H,I gatewayStyle
    class J,K,L,M appStyle
    class N,O,P cacheStyle
    class Q,R,S storageStyle
```

**详细解析：**

**1. 多层限流策略：**

**网关层限流：**
```java
@Component
public class SeckillRateLimiter {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 滑动窗口限流
    public boolean allowRequest(String userId, String seckillId) {
        String key = "rate_limit:" + seckillId + ":" + userId;
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - 60000; // 1分钟窗口

        // Lua脚本保证原子性
        String luaScript =
            "local key = KEYS[1] " +
            "local window_start = ARGV[1] " +
            "local current_time = ARGV[2] " +
            "local limit = ARGV[3] " +

            "redis.call('zremrangebyscore', key, 0, window_start) " +
            "local current_count = redis.call('zcard', key) " +

            "if current_count < tonumber(limit) then " +
            "  redis.call('zadd', key, current_time, current_time) " +
            "  redis.call('expire', key, 60) " +
            "  return 1 " +
            "else " +
            "  return 0 " +
            "end";

        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(luaScript);
        script.setResultType(Long.class);

        Long result = redisTemplate.execute(script,
            Collections.singletonList(key),
            String.valueOf(windowStart),
            String.valueOf(currentTime),
            "10" // 每分钟最多10次请求
        );

        return result != null && result == 1;
    }

    // 令牌桶限流
    public boolean acquireToken(String seckillId, int permits) {
        String key = "token_bucket:" + seckillId;

        String luaScript =
            "local key = KEYS[1] " +
            "local capacity = tonumber(ARGV[1]) " +
            "local tokens = tonumber(ARGV[2]) " +
            "local interval = tonumber(ARGV[3]) " +
            "local requested = tonumber(ARGV[4]) " +

            "local bucket = redis.call('hmget', key, 'tokens', 'last_refill') " +
            "local current_tokens = tonumber(bucket[1]) or capacity " +
            "local last_refill = tonumber(bucket[2]) or 0 " +

            "local now = redis.call('time')[1] " +
            "local elapsed = now - last_refill " +
            "local new_tokens = math.min(capacity, current_tokens + elapsed * tokens / interval) " +

            "if new_tokens >= requested then " +
            "  new_tokens = new_tokens - requested " +
            "  redis.call('hmset', key, 'tokens', new_tokens, 'last_refill', now) " +
            "  redis.call('expire', key, interval * 2) " +
            "  return 1 " +
            "else " +
            "  redis.call('hmset', key, 'tokens', new_tokens, 'last_refill', now) " +
            "  redis.call('expire', key, interval * 2) " +
            "  return 0 " +
            "end";

        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(luaScript);
        script.setResultType(Long.class);

        Long result = redisTemplate.execute(script,
            Collections.singletonList(key),
            "1000",  // 桶容量
            "100",   // 每秒补充令牌数
            "1",     // 补充间隔（秒）
            String.valueOf(permits)
        );

        return result != null && result == 1;
    }
}
```

**应用层限流：**
```java
@RestController
@RequestMapping("/seckill")
public class SeckillController {

    @Autowired
    private SeckillService seckillService;

    @Autowired
    private SeckillRateLimiter rateLimiter;

    @PostMapping("/{seckillId}/kill")
    @RateLimiter(key = "#seckillId", rate = 1000, interval = 1) // 注解限流
    public ResponseEntity<SeckillResult> seckill(
            @PathVariable String seckillId,
            @RequestParam String userId,
            HttpServletRequest request) {

        // 1. 基础验证
        if (!validateRequest(seckillId, userId, request)) {
            return ResponseEntity.badRequest()
                .body(SeckillResult.fail("请求验证失败"));
        }

        // 2. 用户限流
        if (!rateLimiter.allowRequest(userId, seckillId)) {
            return ResponseEntity.status(429)
                .body(SeckillResult.fail("请求过于频繁"));
        }

        // 3. 全局限流
        if (!rateLimiter.acquireToken(seckillId, 1)) {
            return ResponseEntity.status(503)
                .body(SeckillResult.fail("系统繁忙，请稍后重试"));
        }

        // 4. 执行秒杀
        try {
            SeckillResult result = seckillService.doSeckill(seckillId, userId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Seckill failed: seckillId={}, userId={}", seckillId, userId, e);
            return ResponseEntity.status(500)
                .body(SeckillResult.fail("秒杀失败"));
        }
    }
}
```

**2. 库存预扣和削峰：**

**Redis预扣库存：**
```java
@Service
public class SeckillInventoryService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public boolean preDeductInventory(String seckillId, String userId, int quantity) {
        String inventoryKey = "seckill_inventory:" + seckillId;
        String userKey = "seckill_user:" + seckillId + ":" + userId;

        // Lua脚本保证原子性
        String luaScript =
            "local inventory_key = KEYS[1] " +
            "local user_key = KEYS[2] " +
            "local quantity = tonumber(ARGV[1]) " +
            "local user_id = ARGV[2] " +

            "-- 检查用户是否已经参与过 " +
            "if redis.call('exists', user_key) == 1 then " +
            "  return -1 " +
            "end " +

            "-- 检查库存 " +
            "local current_inventory = tonumber(redis.call('get', inventory_key)) or 0 " +
            "if current_inventory < quantity then " +
            "  return 0 " +
            "end " +

            "-- 扣减库存 " +
            "redis.call('decrby', inventory_key, quantity) " +
            "-- 记录用户参与 " +
            "redis.call('setex', user_key, 3600, user_id) " +

            "return 1";

        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(luaScript);
        script.setResultType(Long.class);

        Long result = redisTemplate.execute(script,
            Arrays.asList(inventoryKey, userKey),
            String.valueOf(quantity),
            userId
        );

        if (result != null && result == 1) {
            // 发送异步消息处理订单
            SeckillMessage message = SeckillMessage.builder()
                .seckillId(seckillId)
                .userId(userId)
                .quantity(quantity)
                .timestamp(System.currentTimeMillis())
                .build();

            rabbitTemplate.convertAndSend("seckill.exchange", "seckill.order", message);
            return true;
        }

        return false;
    }

    // 初始化库存
    public void initInventory(String seckillId, int totalInventory) {
        String key = "seckill_inventory:" + seckillId;
        redisTemplate.opsForValue().set(key, String.valueOf(totalInventory));

        // 设置过期时间
        redisTemplate.expire(key, Duration.ofHours(24));
    }
}
```

**异步订单处理：**
```java
@Component
public class SeckillOrderProcessor {

    @Autowired
    private OrderService orderService;

    @Autowired
    private InventoryService inventoryService;

    @RabbitListener(queues = "seckill.order.queue", concurrency = "10-20")
    public void processOrder(SeckillMessage message) {
        try {
            // 1. 创建订单
            Order order = Order.builder()
                .seckillId(message.getSeckillId())
                .userId(message.getUserId())
                .quantity(message.getQuantity())
                .status(OrderStatus.PENDING)
                .createTime(System.currentTimeMillis())
                .build();

            order = orderService.createOrder(order);

            // 2. 扣减真实库存
            boolean success = inventoryService.deductInventory(
                message.getSeckillId(),
                message.getQuantity()
            );

            if (success) {
                // 3. 更新订单状态
                order.setStatus(OrderStatus.CONFIRMED);
                orderService.updateOrder(order);

                // 4. 发送支付消息
                PaymentMessage paymentMessage = PaymentMessage.builder()
                    .orderId(order.getId())
                    .amount(order.getTotalAmount())
                    .userId(message.getUserId())
                    .build();

                rabbitTemplate.convertAndSend("payment.exchange", "payment.process", paymentMessage);

            } else {
                // 库存不足，回滚Redis库存
                rollbackRedisInventory(message.getSeckillId(), message.getQuantity());

                order.setStatus(OrderStatus.FAILED);
                orderService.updateOrder(order);
            }

        } catch (Exception e) {
            log.error("Failed to process seckill order: {}", message, e);
            // 回滚操作
            rollbackRedisInventory(message.getSeckillId(), message.getQuantity());
        }
    }

    private void rollbackRedisInventory(String seckillId, int quantity) {
        String key = "seckill_inventory:" + seckillId;
        redisTemplate.opsForValue().increment(key, quantity);
    }
}
```

**3. 防刷机制：**

**多维度防刷：**

```mermaid
flowchart TB
    subgraph anti_bot_strategies ["防刷策略"]
        A["IP Frequency Limit<br/>IP频率限制"]
        B["User Behavior Analysis<br/>用户行为分析"]
        C["Device Fingerprint<br/>设备指纹"]
        D["Captcha Verification<br/>验证码验证"]
    end

    subgraph detection_methods ["检测方法"]
        E["Request Pattern<br/>请求模式"]
        F["Time Interval<br/>时间间隔"]
        G["Geographic Location<br/>地理位置"]
        H["Browser Features<br/>浏览器特征"]
    end

    subgraph response_actions ["响应动作"]
        I["Rate Limiting<br/>限流"]
        J["Account Blocking<br/>账号封禁"]
        K["IP Blacklist<br/>IP黑名单"]
        L["Challenge Response<br/>挑战响应"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef strategyStyle fill:#c8e6c9,stroke:#4caf50
    classDef detectionStyle fill:#e3f2fd,stroke:#2196f3
    classDef responseStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D strategyStyle
    class E,F,G,H detectionStyle
    class I,J,K,L responseStyle
```

**防刷检测实现：**
```java
@Service
public class AntiBotService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public boolean isBot(HttpServletRequest request, String userId) {
        // 1. IP频率检测
        if (isIpFrequencyExceeded(request.getRemoteAddr())) {
            return true;
        }

        // 2. 用户行为分析
        if (isUserBehaviorAbnormal(userId, request)) {
            return true;
        }

        // 3. 设备指纹检测
        if (isDeviceFingerprintSuspicious(request)) {
            return true;
        }

        // 4. 请求模式分析
        if (isRequestPatternAbnormal(userId, request)) {
            return true;
        }

        return false;
    }

    private boolean isIpFrequencyExceeded(String ip) {
        String key = "ip_freq:" + ip;
        String luaScript =
            "local key = KEYS[1] " +
            "local window = 60 " +
            "local limit = 100 " +
            "local current = redis.call('incr', key) " +
            "if current == 1 then " +
            "  redis.call('expire', key, window) " +
            "end " +
            "return current > limit and 1 or 0";

        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(luaScript);
        script.setResultType(Long.class);

        Long result = redisTemplate.execute(script, Collections.singletonList(key));
        return result != null && result == 1;
    }

    private boolean isUserBehaviorAbnormal(String userId, HttpServletRequest request) {
        // 分析用户行为特征
        UserBehavior behavior = UserBehavior.builder()
            .userId(userId)
            .userAgent(request.getHeader("User-Agent"))
            .referer(request.getHeader("Referer"))
            .timestamp(System.currentTimeMillis())
            .build();

        // 检查行为模式
        return behaviorAnalyzer.isAbnormal(behavior);
    }

    private boolean isDeviceFingerprintSuspicious(HttpServletRequest request) {
        DeviceFingerprint fingerprint = DeviceFingerprint.builder()
            .userAgent(request.getHeader("User-Agent"))
            .acceptLanguage(request.getHeader("Accept-Language"))
            .acceptEncoding(request.getHeader("Accept-Encoding"))
            .screenResolution(request.getHeader("X-Screen-Resolution"))
            .timezone(request.getHeader("X-Timezone"))
            .build();

        return fingerprintAnalyzer.isSuspicious(fingerprint);
    }
}
```

**验证码集成：**
```java
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

    @Autowired
    private CaptchaService captchaService;

    @GetMapping("/generate")
    public ResponseEntity<CaptchaResponse> generateCaptcha(HttpServletRequest request) {
        String sessionId = request.getSession().getId();
        CaptchaImage captcha = captchaService.generateCaptcha(sessionId);

        CaptchaResponse response = CaptchaResponse.builder()
            .captchaId(captcha.getId())
            .imageBase64(captcha.getImageBase64())
            .expireTime(captcha.getExpireTime())
            .build();

        return ResponseEntity.ok(response);
    }

    @PostMapping("/verify")
    public ResponseEntity<Boolean> verifyCaptcha(@RequestBody CaptchaVerifyRequest request) {
        boolean valid = captchaService.verifyCaptcha(
            request.getCaptchaId(),
            request.getCode()
        );

        return ResponseEntity.ok(valid);
    }
}

@Service
public class CaptchaService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public CaptchaImage generateCaptcha(String sessionId) {
        // 生成随机验证码
        String code = generateRandomCode();
        String captchaId = UUID.randomUUID().toString();

        // 生成图片
        BufferedImage image = createCaptchaImage(code);
        String imageBase64 = encodeImageToBase64(image);

        // 存储到Redis
        String key = "captcha:" + captchaId;
        redisTemplate.opsForValue().set(key, code, Duration.ofMinutes(5));

        return CaptchaImage.builder()
            .id(captchaId)
            .imageBase64(imageBase64)
            .expireTime(System.currentTimeMillis() + 300000)
            .build();
    }

    public boolean verifyCaptcha(String captchaId, String inputCode) {
        String key = "captcha:" + captchaId;
        String storedCode = redisTemplate.opsForValue().get(key);

        if (storedCode != null && storedCode.equalsIgnoreCase(inputCode)) {
            // 验证成功后删除
            redisTemplate.delete(key);
            return true;
        }

        return false;
    }
}
```

**最佳实践：**
1. 实现多层次的流量控制和限流策略
2. 使用Redis进行库存预扣和削峰填谷
3. 建立完善的防刷和反作弊机制
4. 设计异步处理流程提高系统吞吐量
5. 实现实时监控和动态调整能力

## 大型系统架构案例

### 8. 设计一个支持千万级用户的电商平台，请详细说明整体架构、技术选型、扩展性设计 ⭐⭐⭐⭐⭐

#### 问题分析
考查候选人对大型分布式系统的整体架构设计能力，包括技术选型、扩展性、可用性、一致性等多方面的综合考虑。

#### 标准答案

**电商平台整体架构图：**

```mermaid
flowchart TB
    subgraph user_access ["用户接入层"]
        A["CDN<br/>内容分发网络"]
        B["DNS<br/>域名解析"]
        C["WAF<br/>Web应用防火墙"]
    end

    subgraph gateway_layer ["网关层"]
        D["API Gateway<br/>API网关"]
        E["Load Balancer<br/>负载均衡器"]
        F["Rate Limiter<br/>限流器"]
    end

    subgraph microservices ["微服务层"]
        G["User Service<br/>用户服务"]
        H["Product Service<br/>商品服务"]
        I["Order Service<br/>订单服务"]
        J["Payment Service<br/>支付服务"]
        K["Inventory Service<br/>库存服务"]
        L["Search Service<br/>搜索服务"]
        M["Recommendation Service<br/>推荐服务"]
    end

    subgraph middleware ["中间件层"]
        N["Message Queue<br/>消息队列"]
        O["Cache Cluster<br/>缓存集群"]
        P["Config Center<br/>配置中心"]
        Q["Service Registry<br/>服务注册中心"]
    end

    subgraph data_layer ["数据层"]
        R["MySQL Cluster<br/>MySQL集群"]
        S["MongoDB<br/>文档数据库"]
        T["Elasticsearch<br/>搜索引擎"]
        U["HDFS<br/>分布式文件系统"]
    end

    subgraph monitoring ["监控层"]
        V["Prometheus<br/>监控系统"]
        W["ELK Stack<br/>日志系统"]
        X["Jaeger<br/>链路追踪"]
        Y["Grafana<br/>可视化"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    F --> M

    G --> N
    H --> O
    I --> P
    J --> Q

    G --> R
    H --> S
    L --> T
    M --> U

    G --> V
    H --> W
    I --> X
    J --> Y

    classDef accessStyle fill:#c8e6c9,stroke:#4caf50
    classDef gatewayStyle fill:#e3f2fd,stroke:#2196f3
    classDef serviceStyle fill:#fff3e0,stroke:#ff9800
    classDef middlewareStyle fill:#f3e5f5,stroke:#9c27b0
    classDef dataStyle fill:#ffecb3,stroke:#ffc107
    classDef monitorStyle fill:#ffcdd2,stroke:#f44336

    class A,B,C accessStyle
    class D,E,F gatewayStyle
    class G,H,I,J,K,L,M serviceStyle
    class N,O,P,Q middlewareStyle
    class R,S,T,U dataStyle
    class V,W,X,Y monitorStyle
```

**详细解析：**

**1. 技术选型和架构决策：**

**核心技术栈：**
```java
// 架构配置类
@Configuration
@EnableEurekaClient
@EnableFeignClients
@EnableCircuitBreaker
public class ECommerceArchitectureConfig {

    // 服务发现配置
    @Bean
    public EurekaClientConfigBean eurekaClientConfig() {
        EurekaClientConfigBean config = new EurekaClientConfigBean();
        config.setServiceUrl(Map.of("defaultZone", "http://eureka1:8761/eureka,http://eureka2:8762/eureka"));
        config.setHealthCheckEnabled(true);
        config.setLeaseRenewalIntervalInSeconds(10);
        config.setLeaseExpirationDurationInSeconds(30);
        return config;
    }

    // 负载均衡配置
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        RestTemplate template = new RestTemplate();

        // 连接池配置
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(10000);

        // 连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);

        CloseableHttpClient httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .setRetryHandler(new DefaultHttpRequestRetryHandler(3, true))
            .build();

        factory.setHttpClient(httpClient);
        template.setRequestFactory(factory);

        return template;
    }

    // 缓存配置
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());

        return builder.build();
    }

    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

**数据库分片策略：**
```java
@Configuration
public class ShardingDataSourceConfig {

    @Bean
    public DataSource dataSource() {
        // 分库分表配置
        Map<String, DataSource> dataSourceMap = createDataSourceMap();

        // 分片规则配置
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        // 用户表分片规则
        shardingRuleConfig.getTableRuleConfigs().add(getUserTableRuleConfiguration());

        // 订单表分片规则
        shardingRuleConfig.getTableRuleConfigs().add(getOrderTableRuleConfiguration());

        // 商品表分片规则
        shardingRuleConfig.getTableRuleConfigs().add(getProductTableRuleConfiguration());

        // 分库策略
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("user_id", "ds${user_id % 4}")
        );

        // 分表策略
        shardingRuleConfig.setDefaultTableShardingStrategyConfig(
            new InlineShardingStrategyConfiguration("id", "t_${id % 16}")
        );

        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new Properties());
    }

    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> dataSourceMap = new HashMap<>();

        // 创建4个数据库实例
        for (int i = 0; i < 4; i++) {
            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setJdbcUrl("jdbc:mysql://mysql-" + i + ":3306/ecommerce_" + i);
            dataSource.setUsername("root");
            dataSource.setPassword("password");
            dataSource.setMaximumPoolSize(50);
            dataSource.setMinimumIdle(10);
            dataSource.setConnectionTimeout(30000);
            dataSource.setIdleTimeout(600000);
            dataSource.setMaxLifetime(1800000);

            dataSourceMap.put("ds" + i, dataSource);
        }

        return dataSourceMap;
    }

    private TableRuleConfiguration getUserTableRuleConfiguration() {
        TableRuleConfiguration config = new TableRuleConfiguration("t_user", "ds${0..3}.t_user_${0..15}");
        config.setKeyGeneratorConfig(new KeyGeneratorConfiguration("SNOWFLAKE", "id"));
        return config;
    }

    private TableRuleConfiguration getOrderTableRuleConfiguration() {
        TableRuleConfiguration config = new TableRuleConfiguration("t_order", "ds${0..3}.t_order_${0..15}");
        config.setKeyGeneratorConfig(new KeyGeneratorConfiguration("SNOWFLAKE", "id"));
        return config;
    }
}
```

**2. 高可用架构设计：**

**多活数据中心：**

```mermaid
flowchart TB
    subgraph region_a ["Region A (主)"]
        A1["Load Balancer<br/>负载均衡器"]
        A2["App Cluster<br/>应用集群"]
        A3["MySQL Master<br/>MySQL主库"]
        A4["Redis Cluster<br/>Redis集群"]
    end

    subgraph region_b ["Region B (备)"]
        B1["Load Balancer<br/>负载均衡器"]
        B2["App Cluster<br/>应用集群"]
        B3["MySQL Slave<br/>MySQL从库"]
        B4["Redis Cluster<br/>Redis集群"]
    end

    subgraph region_c ["Region C (灾备)"]
        C1["Load Balancer<br/>负载均衡器"]
        C2["App Cluster<br/>应用集群"]
        C3["MySQL Slave<br/>MySQL从库"]
        C4["Redis Cluster<br/>Redis集群"]
    end

    subgraph global_components ["全局组件"]
        D1["Global DNS<br/>全局DNS"]
        D2["Message Queue<br/>消息队列"]
        D3["Config Center<br/>配置中心"]
        D4["Monitoring<br/>监控系统"]
    end

    A3 --> B3
    A3 --> C3
    A4 --> B4
    A4 --> C4

    D1 --> A1
    D1 --> B1
    D1 --> C1

    D2 --> A2
    D2 --> B2
    D2 --> C2

    classDef regionAStyle fill:#c8e6c9,stroke:#4caf50
    classDef regionBStyle fill:#e3f2fd,stroke:#2196f3
    classDef regionCStyle fill:#fff3e0,stroke:#ff9800
    classDef globalStyle fill:#f3e5f5,stroke:#9c27b0

    class A1,A2,A3,A4 regionAStyle
    class B1,B2,B3,B4 regionBStyle
    class C1,C2,C3,C4 regionCStyle
    class D1,D2,D3,D4 globalStyle
```

**容灾切换机制：**
```java
@Component
public class DisasterRecoveryManager {

    @Autowired
    private HealthCheckService healthCheckService;

    @Autowired
    private TrafficSwitchService trafficSwitchService;

    @Autowired
    private NotificationService notificationService;

    @Scheduled(fixedRate = 30000) // 30秒检查一次
    public void performHealthCheck() {
        List<Region> regions = Arrays.asList(
            Region.builder().name("region-a").priority(1).build(),
            Region.builder().name("region-b").priority(2).build(),
            Region.builder().name("region-c").priority(3).build()
        );

        for (Region region : regions) {
            HealthStatus status = healthCheckService.checkRegionHealth(region);

            if (status.isHealthy()) {
                if (!region.isActive()) {
                    // 区域恢复，考虑切换回来
                    considerFailback(region);
                }
            } else {
                if (region.isActive()) {
                    // 主区域故障，执行切换
                    performFailover(region, regions);
                }
            }
        }
    }

    private void performFailover(Region failedRegion, List<Region> allRegions) {
        log.error("Region {} failed, performing failover", failedRegion.getName());

        // 1. 找到下一个可用区域
        Region targetRegion = findNextAvailableRegion(allRegions, failedRegion);

        if (targetRegion != null) {
            // 2. 切换流量
            trafficSwitchService.switchTraffic(failedRegion, targetRegion);

            // 3. 更新DNS
            dnsService.updateDnsRecord(targetRegion);

            // 4. 发送告警
            notificationService.sendFailoverAlert(failedRegion, targetRegion);

            // 5. 记录切换日志
            auditService.recordFailover(failedRegion, targetRegion, System.currentTimeMillis());
        } else {
            // 所有区域都不可用，触发紧急预案
            triggerEmergencyPlan();
        }
    }

    private void considerFailback(Region recoveredRegion) {
        if (recoveredRegion.getPriority() < getCurrentActiveRegion().getPriority()) {
            // 优先级更高的区域恢复，考虑切换回来
            if (isFailbackSafe(recoveredRegion)) {
                performFailback(recoveredRegion);
            }
        }
    }

    private boolean isFailbackSafe(Region region) {
        // 检查区域稳定性
        HealthHistory history = healthCheckService.getHealthHistory(region, Duration.ofMinutes(10));
        return history.getSuccessRate() > 0.95 && history.getAvgResponseTime() < 100;
    }
}
```

**3. 性能优化策略：**

**多级缓存架构：**
```java
@Service
public class MultiLevelCacheService {

    @Autowired
    private CaffeineCache localCache;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ProductRepository productRepository;

    public Product getProduct(Long productId) {
        String cacheKey = "product:" + productId;

        // L1: 本地缓存
        Product product = localCache.get(cacheKey, Product.class);
        if (product != null) {
            return product;
        }

        // L2: Redis缓存
        product = (Product) redisTemplate.opsForValue().get(cacheKey);
        if (product != null) {
            // 回写本地缓存
            localCache.put(cacheKey, product, Duration.ofMinutes(5));
            return product;
        }

        // L3: 数据库
        product = productRepository.findById(productId).orElse(null);
        if (product != null) {
            // 异步写入缓存
            CompletableFuture.runAsync(() -> {
                redisTemplate.opsForValue().set(cacheKey, product, Duration.ofHours(1));
                localCache.put(cacheKey, product, Duration.ofMinutes(5));
            });
        }

        return product;
    }

    // 缓存预热
    @EventListener
    public void handleProductUpdated(ProductUpdatedEvent event) {
        Product product = event.getProduct();
        String cacheKey = "product:" + product.getId();

        // 更新所有级别的缓存
        localCache.put(cacheKey, product, Duration.ofMinutes(5));
        redisTemplate.opsForValue().set(cacheKey, product, Duration.ofHours(1));

        // 通知其他节点更新本地缓存
        eventPublisher.publish(new CacheInvalidationEvent(cacheKey));
    }
}
```

**数据库读写分离：**
```java
@Configuration
public class ReadWriteSplitConfig {

    @Bean
    @Primary
    public DataSource dataSource() {
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("master", masterDataSource());
        dataSourceMap.put("slave1", slave1DataSource());
        dataSourceMap.put("slave2", slave2DataSource());

        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        dynamicDataSource.setDefaultTargetDataSource(masterDataSource());

        return dynamicDataSource;
    }

    @Bean
    public DataSource masterDataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("****************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        dataSource.setMaximumPoolSize(50);
        return dataSource;
    }

    @Bean
    public DataSource slave1DataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("****************************************");
        dataSource.setUsername("readonly");
        dataSource.setPassword("password");
        dataSource.setMaximumPoolSize(30);
        return dataSource;
    }
}

@Aspect
@Component
public class DataSourceAspect {

    @Around("@annotation(readOnly)")
    public Object switchDataSource(ProceedingJoinPoint joinPoint, ReadOnly readOnly) throws Throwable {
        try {
            if (readOnly.value()) {
                // 随机选择从库
                String[] slaves = {"slave1", "slave2"};
                String selectedSlave = slaves[new Random().nextInt(slaves.length)];
                DataSourceContextHolder.setDataSource(selectedSlave);
            } else {
                DataSourceContextHolder.setDataSource("master");
            }

            return joinPoint.proceed();
        } finally {
            DataSourceContextHolder.clearDataSource();
        }
    }
}
```

**4. 扩展性设计：**

**水平扩展策略：**
```java
@Component
public class AutoScalingManager {

    @Autowired
    private MetricsCollector metricsCollector;

    @Autowired
    private KubernetesClient k8sClient;

    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkAndScale() {
        List<ServiceMetrics> serviceMetrics = metricsCollector.collectAllServiceMetrics();

        for (ServiceMetrics metrics : serviceMetrics) {
            ScalingDecision decision = makeScalingDecision(metrics);

            if (decision.shouldScale()) {
                executeScaling(metrics.getServiceName(), decision);
            }
        }
    }

    private ScalingDecision makeScalingDecision(ServiceMetrics metrics) {
        ScalingDecision decision = new ScalingDecision();

        // CPU使用率
        if (metrics.getCpuUsage() > 80) {
            decision.setScaleUp(true);
            decision.setReason("High CPU usage: " + metrics.getCpuUsage() + "%");
        } else if (metrics.getCpuUsage() < 20 && metrics.getCurrentReplicas() > 2) {
            decision.setScaleDown(true);
            decision.setReason("Low CPU usage: " + metrics.getCpuUsage() + "%");
        }

        // 内存使用率
        if (metrics.getMemoryUsage() > 85) {
            decision.setScaleUp(true);
            decision.setReason("High memory usage: " + metrics.getMemoryUsage() + "%");
        }

        // 请求队列长度
        if (metrics.getQueueLength() > 100) {
            decision.setScaleUp(true);
            decision.setReason("High queue length: " + metrics.getQueueLength());
        }

        // 响应时间
        if (metrics.getAvgResponseTime() > 1000) {
            decision.setScaleUp(true);
            decision.setReason("High response time: " + metrics.getAvgResponseTime() + "ms");
        }

        return decision;
    }

    private void executeScaling(String serviceName, ScalingDecision decision) {
        try {
            if (decision.isScaleUp()) {
                k8sClient.apps().deployments()
                    .inNamespace("ecommerce")
                    .withName(serviceName)
                    .scale(getCurrentReplicas(serviceName) + 1);

                log.info("Scaled up service: {}, reason: {}", serviceName, decision.getReason());
            } else if (decision.isScaleDown()) {
                k8sClient.apps().deployments()
                    .inNamespace("ecommerce")
                    .withName(serviceName)
                    .scale(getCurrentReplicas(serviceName) - 1);

                log.info("Scaled down service: {}, reason: {}", serviceName, decision.getReason());
            }
        } catch (Exception e) {
            log.error("Failed to scale service: {}", serviceName, e);
        }
    }
}
```

**最佳实践：**
1. 采用微服务架构实现系统解耦和独立扩展
2. 实现多级缓存和数据库分片提升性能
3. 建立多活数据中心保证高可用性
4. 设计自动扩缩容机制应对流量波动
5. 建立完善的监控和运维体系

## 总结

本文详细介绍了高并发系统架构设计的核心技术和最佳实践，涵盖了从基础并发概念到大型系统架构的完整知识体系。通过深入分析缓存架构、数据库优化、分布式系统设计、微服务架构、消息队列、秒杀系统等关键技术，为构建高性能、高可用、可扩展的分布式系统提供了全面的技术指导。

在实际项目中，需要根据具体的业务需求和技术约束，选择合适的技术方案和架构模式。同时，要注重系统的可观测性、可维护性和可演进性，确保系统能够持续稳定地为用户提供服务。
