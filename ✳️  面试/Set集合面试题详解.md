# Set集合面试题详解

> 本文档详细解析Set接口的各种实现类，包括HashSet、LinkedHashSet、TreeSet的底层原理、性能特点和使用场景。

## 目录
- [Set接口概述](#set接口概述)
- [HashSet详解](#hashset详解)
- [LinkedHashSet详解](#linkedhashset详解)
- [TreeSet详解](#treeset详解)
- [性能对比](#性能对比)

---

## Set接口概述

### 1. HashSet、LinkedHashSet、TreeSet的区别？⭐⭐⭐⭐

#### 问题分析
这是Set集合的经典对比题，考查对不同Set实现类特点的理解。

#### 标准答案

**Set实现类对比：**

```mermaid
graph TB
    subgraph "HashSet"
        A[HashSet] --> A1[基于HashMap实现]
        A1 --> A2[无序、不重复]
        A2 --> A3[O(1)查询性能]
        A3 --> A4[允许null值]
        A4 --> A5[线程不安全]
    end
    
    subgraph "LinkedHashSet"
        B[LinkedHashSet] --> B1[继承自HashSet]
        B1 --> B2[插入有序、不重复]
        B2 --> B3[O(1)查询性能]
        B3 --> B4[维护双向链表]
        B4 --> B5[内存开销较大]
    end
    
    subgraph "TreeSet"
        C[TreeSet] --> C1[基于红黑树实现]
        C1 --> C2[自然排序、不重复]
        C2 --> C3[O(log n)查询性能]
        C3 --> C4[不允许null值]
        C4 --> C5[支持范围查询]
    end
    
    style A3 fill:#c8e6c9
    style B3 fill:#c8e6c9
    style C3 fill:#fff3e0
```

#### 详细对比表

| 特性 | HashSet | LinkedHashSet | TreeSet |
|------|---------|---------------|---------|
| **底层实现** | HashMap | LinkedHashMap | 红黑树(TreeMap) |
| **元素顺序** | 无序 | 插入顺序 | 自然排序/自定义排序 |
| **查询性能** | O(1) | O(1) | O(log n) |
| **插入性能** | O(1) | O(1) | O(log n) |
| **删除性能** | O(1) | O(1) | O(log n) |
| **null值** | 允许一个 | 允许一个 | 不允许 |
| **线程安全** | 否 | 否 | 否 |
| **内存开销** | 最小 | 中等(额外链表) | 较大(树结构) |
| **适用场景** | 快速查找去重 | 保持插入顺序 | 需要排序的场景 |

#### 代码示例
```java
public class SetComparison {
    
    public void demonstrateSetDifferences() {
        // 1. HashSet - 无序
        Set<String> hashSet = new HashSet<>();
        hashSet.add("banana");
        hashSet.add("apple");
        hashSet.add("cherry");
        hashSet.add("apple");  // 重复元素被忽略
        
        System.out.println("HashSet (无序): " + hashSet);
        // 输出可能是: [banana, apple, cherry] (顺序不确定)
        
        // 2. LinkedHashSet - 插入有序
        Set<String> linkedHashSet = new LinkedHashSet<>();
        linkedHashSet.add("banana");
        linkedHashSet.add("apple");
        linkedHashSet.add("cherry");
        linkedHashSet.add("apple");  // 重复元素被忽略
        
        System.out.println("LinkedHashSet (插入有序): " + linkedHashSet);
        // 输出: [banana, apple, cherry]
        
        // 3. TreeSet - 自然排序
        Set<String> treeSet = new TreeSet<>();
        treeSet.add("banana");
        treeSet.add("apple");
        treeSet.add("cherry");
        treeSet.add("apple");  // 重复元素被忽略
        
        System.out.println("TreeSet (自然排序): " + treeSet);
        // 输出: [apple, banana, cherry]
        
        // 4. TreeSet - 自定义排序
        Set<String> customTreeSet = new TreeSet<>((a, b) -> b.compareTo(a));
        customTreeSet.add("banana");
        customTreeSet.add("apple");
        customTreeSet.add("cherry");
        
        System.out.println("TreeSet (降序): " + customTreeSet);
        // 输出: [cherry, banana, apple]
    }
    
    // 性能测试
    public void performanceTest() {
        int size = 100000;
        
        // HashSet性能测试
        long start = System.currentTimeMillis();
        Set<Integer> hashSet = new HashSet<>();
        for (int i = 0; i < size; i++) {
            hashSet.add(i);
        }
        for (int i = 0; i < size; i++) {
            hashSet.contains(i);
        }
        long hashSetTime = System.currentTimeMillis() - start;
        
        // LinkedHashSet性能测试
        start = System.currentTimeMillis();
        Set<Integer> linkedHashSet = new LinkedHashSet<>();
        for (int i = 0; i < size; i++) {
            linkedHashSet.add(i);
        }
        for (int i = 0; i < size; i++) {
            linkedHashSet.contains(i);
        }
        long linkedHashSetTime = System.currentTimeMillis() - start;
        
        // TreeSet性能测试
        start = System.currentTimeMillis();
        Set<Integer> treeSet = new TreeSet<>();
        for (int i = 0; i < size; i++) {
            treeSet.add(i);
        }
        for (int i = 0; i < size; i++) {
            treeSet.contains(i);
        }
        long treeSetTime = System.currentTimeMillis() - start;
        
        System.out.println("性能测试结果 (" + size + "个元素):");
        System.out.println("HashSet: " + hashSetTime + "ms");
        System.out.println("LinkedHashSet: " + linkedHashSetTime + "ms");
        System.out.println("TreeSet: " + treeSetTime + "ms");
    }
}
```

---

## HashSet详解

### 2. HashSet底层实现原理？⭐⭐⭐⭐

#### 问题分析
HashSet是最常用的Set实现，需要理解其基于HashMap的实现原理。

#### 标准答案

**HashSet实现原理：**

```mermaid
graph TB
    subgraph "HashSet结构"
        A[HashSet] --> B[内部HashMap]
        B --> C[Key存储元素]
        B --> D[Value固定为PRESENT]
        
        C --> E[元素1]
        C --> F[元素2]
        C --> G[元素3]
        
        D --> H[static final Object PRESENT = new Object()]
    end
    
    subgraph "操作映射"
        I[set.add(e)] --> J[map.put(e, PRESENT)]
        K[set.contains(e)] --> L[map.containsKey(e)]
        M[set.remove(e)] --> N[map.remove(e)]
        O[set.size()] --> P[map.size()]
    end
    
    style H fill:#e3f2fd
    style J fill:#c8e6c9
```

#### 源码分析
```java
public class HashSetImplementation {
    
    // HashSet核心源码分析
    public void analyzeHashSetSource() {
        System.out.println("=== HashSet源码分析 ===");
        System.out.println("""
            public class HashSet<E> extends AbstractSet<E> 
                implements Set<E>, Cloneable, java.io.Serializable {
                
                // 内部HashMap
                private transient HashMap<E,Object> map;
                
                // 虚拟值，所有元素都映射到这个值
                private static final Object PRESENT = new Object();
                
                // 构造函数
                public HashSet() {
                    map = new HashMap<>();
                }
                
                // 添加元素
                public boolean add(E e) {
                    return map.put(e, PRESENT) == null;
                }
                
                // 删除元素
                public boolean remove(Object o) {
                    return map.remove(o) == PRESENT;
                }
                
                // 包含检查
                public boolean contains(Object o) {
                    return map.containsKey(o);
                }
                
                // 大小
                public int size() {
                    return map.size();
                }
            }
            """);
    }
    
    // 模拟HashSet实现
    public static class SimpleHashSet<E> {
        private HashMap<E, Object> map;
        private static final Object PRESENT = new Object();
        
        public SimpleHashSet() {
            map = new HashMap<>();
        }
        
        public boolean add(E e) {
            return map.put(e, PRESENT) == null;
        }
        
        public boolean remove(E e) {
            return map.remove(e) == PRESENT;
        }
        
        public boolean contains(E e) {
            return map.containsKey(e);
        }
        
        public int size() {
            return map.size();
        }
        
        public void clear() {
            map.clear();
        }
        
        @Override
        public String toString() {
            return map.keySet().toString();
        }
    }
    
    // 演示自定义HashSet
    public void demonstrateCustomHashSet() {
        SimpleHashSet<String> set = new SimpleHashSet<>();
        
        System.out.println("添加元素:");
        System.out.println("add('apple'): " + set.add("apple"));     // true
        System.out.println("add('banana'): " + set.add("banana"));   // true
        System.out.println("add('apple'): " + set.add("apple"));     // false (重复)
        
        System.out.println("\n当前集合: " + set);
        System.out.println("大小: " + set.size());
        
        System.out.println("\n包含检查:");
        System.out.println("contains('apple'): " + set.contains("apple"));   // true
        System.out.println("contains('cherry'): " + set.contains("cherry")); // false
        
        System.out.println("\n删除元素:");
        System.out.println("remove('apple'): " + set.remove("apple"));       // true
        System.out.println("remove('cherry'): " + set.remove("cherry"));     // false
        
        System.out.println("\n最终集合: " + set);
    }
}
```

### 3. HashSet如何保证元素唯一性？⭐⭐⭐

#### 问题分析
HashSet去重的核心机制是基于hashCode和equals方法。

#### 标准答案

**去重机制：**

```mermaid
graph TB
    A[添加元素e] --> B[计算e.hashCode()]
    B --> C[定位HashMap数组位置]
    C --> D{该位置是否为空?}
    
    D -->|是| E[直接添加元素]
    D -->|否| F[遍历链表/红黑树]
    
    F --> G{存在元素x使得<br/>e.hashCode() == x.hashCode()<br/>且 e.equals(x)?}
    
    G -->|是| H[元素重复，不添加<br/>返回false]
    G -->|否| I[添加到链表/红黑树<br/>返回true]
    
    style E fill:#c8e6c9
    style H fill:#ffcdd2
    style I fill:#c8e6c9
```

#### 代码示例
```java
public class HashSetUniqueness {
    
    // 演示去重机制
    public void demonstrateUniqueness() {
        Set<Person> set = new HashSet<>();
        
        Person p1 = new Person("张三", 25);
        Person p2 = new Person("李四", 30);
        Person p3 = new Person("张三", 25);  // 与p1相同
        
        set.add(p1);
        set.add(p2);
        set.add(p3);  // 应该被去重
        
        System.out.println("集合大小: " + set.size());  // 应该是2
        System.out.println("集合内容: " + set);
    }
    
    // 正确实现hashCode和equals的Person类
    static class Person {
        private String name;
        private int age;
        
        public Person(String name, int age) {
            this.name = name;
            this.age = age;
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            
            Person person = (Person) obj;
            return age == person.age && Objects.equals(name, person.name);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(name, age);
        }
        
        @Override
        public String toString() {
            return "Person{name='" + name + "', age=" + age + "}";
        }
    }
    
    // 错误实现的示例
    static class BadPerson {
        private String name;
        private int age;
        
        public BadPerson(String name, int age) {
            this.name = name;
            this.age = age;
        }
        
        // ❌ 只重写了equals，没有重写hashCode
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            
            BadPerson person = (BadPerson) obj;
            return age == person.age && Objects.equals(name, person.name);
        }
        
        // hashCode使用默认实现，违反了equals-hashCode契约
        
        @Override
        public String toString() {
            return "BadPerson{name='" + name + "', age=" + age + "}";
        }
    }
    
    // 演示错误实现的后果
    public void demonstrateBadImplementation() {
        Set<BadPerson> set = new HashSet<>();
        
        BadPerson p1 = new BadPerson("张三", 25);
        BadPerson p2 = new BadPerson("张三", 25);  // 逻辑上与p1相同
        
        set.add(p1);
        set.add(p2);
        
        System.out.println("BadPerson集合大小: " + set.size());  // 可能是2，去重失败
        System.out.println("p1.equals(p2): " + p1.equals(p2));  // true
        System.out.println("p1.hashCode(): " + p1.hashCode());
        System.out.println("p2.hashCode(): " + p2.hashCode());  // 可能不同
        
        System.out.println("结论: 违反equals-hashCode契约导致去重失败");
    }
}
```

---

## LinkedHashSet详解

### 4. LinkedHashSet实现原理？⭐⭐⭐

#### 问题分析
LinkedHashSet在HashSet基础上增加了顺序维护功能，需要理解其实现机制。

#### 标准答案

**LinkedHashSet结构：**

```mermaid
graph TB
    subgraph "LinkedHashSet结构"
        A[LinkedHashSet] --> B[继承HashSet]
        B --> C[内部LinkedHashMap]

        C --> D[HashMap数组结构]
        C --> E[双向链表维护顺序]

        D --> F[hash表快速查找]
        E --> G[before/after指针]

        G --> H[插入顺序遍历]
    end

    subgraph "节点结构"
        I[LinkedHashMap.Entry] --> J[HashMap.Node字段]
        I --> K[before指针]
        I --> L[after指针]

        J --> M[hash, key, value, next]
        K --> N[前一个插入的节点]
        L --> O[后一个插入的节点]
    end

    style F fill:#c8e6c9
    style H fill:#e3f2fd
```

#### 代码示例
```java
public class LinkedHashSetDemo {

    // 演示插入顺序维护
    public void demonstrateInsertionOrder() {
        System.out.println("=== LinkedHashSet插入顺序演示 ===");

        // LinkedHashSet保持插入顺序
        Set<String> linkedSet = new LinkedHashSet<>();
        linkedSet.add("third");
        linkedSet.add("first");
        linkedSet.add("second");
        linkedSet.add("first");  // 重复元素，不会改变顺序

        System.out.println("LinkedHashSet: " + linkedSet);
        // 输出: [third, first, second]

        // 对比HashSet的无序性
        Set<String> hashSet = new HashSet<>();
        hashSet.add("third");
        hashSet.add("first");
        hashSet.add("second");

        System.out.println("HashSet: " + hashSet);
        // 输出顺序不确定: 可能是[first, second, third]

        // 遍历顺序对比
        System.out.println("\nLinkedHashSet遍历:");
        linkedSet.forEach(System.out::println);

        System.out.println("\nHashSet遍历:");
        hashSet.forEach(System.out::println);
    }

    // 性能分析
    public void performanceAnalysis() {
        int size = 100000;

        // LinkedHashSet性能测试
        long start = System.currentTimeMillis();
        Set<Integer> linkedSet = new LinkedHashSet<>();
        for (int i = 0; i < size; i++) {
            linkedSet.add(i);
        }

        // 遍历测试
        for (Integer num : linkedSet) {
            // 遍历操作
        }
        long linkedSetTime = System.currentTimeMillis() - start;

        // HashSet性能测试
        start = System.currentTimeMillis();
        Set<Integer> hashSet = new HashSet<>();
        for (int i = 0; i < size; i++) {
            hashSet.add(i);
        }

        for (Integer num : hashSet) {
            // 遍历操作
        }
        long hashSetTime = System.currentTimeMillis() - start;

        System.out.println("性能对比 (" + size + "个元素):");
        System.out.println("LinkedHashSet: " + linkedSetTime + "ms");
        System.out.println("HashSet: " + hashSetTime + "ms");
        System.out.println("性能差异: " + (linkedSetTime / (double) hashSetTime) + "倍");

        System.out.println("\n分析:");
        System.out.println("1. LinkedHashSet插入性能略低于HashSet");
        System.out.println("2. LinkedHashSet遍历性能优于HashSet");
        System.out.println("3. LinkedHashSet内存开销更大");
    }

    // 使用场景
    public void usageScenarios() {
        System.out.println("=== LinkedHashSet使用场景 ===");

        // 场景1: 去重且保持顺序
        List<String> list = Arrays.asList("apple", "banana", "apple", "cherry", "banana");
        Set<String> deduplicatedSet = new LinkedHashSet<>(list);
        System.out.println("原列表: " + list);
        System.out.println("去重后: " + deduplicatedSet);

        // 场景2: LRU缓存的基础
        Set<String> lruSet = new LinkedHashSet<String>() {
            @Override
            protected boolean removeEldestEntry(Map.Entry<String, Object> eldest) {
                return size() > 3;  // 最多保持3个元素
            }
        };

        // 注意: 上面的代码实际上不会工作，因为LinkedHashSet没有removeEldestEntry方法
        // 这里只是演示概念，实际应该使用LinkedHashMap

        // 场景3: 配置文件的键值对顺序
        Set<String> configKeys = new LinkedHashSet<>();
        configKeys.add("database.url");
        configKeys.add("database.username");
        configKeys.add("database.password");
        configKeys.add("server.port");

        System.out.println("配置项顺序: " + configKeys);
    }
}
```

---

## TreeSet详解

### 5. TreeSet实现原理？⭐⭐⭐⭐

#### 问题分析
TreeSet基于红黑树实现，提供有序的Set集合，需要理解其排序机制。

#### 标准答案

**TreeSet结构：**

```mermaid
graph TB
    subgraph "TreeSet结构"
        A[TreeSet] --> B[内部NavigableMap]
        B --> C[TreeMap实现]
        C --> D[红黑树结构]

        D --> E[自平衡二叉搜索树]
        E --> F[左子树 < 根节点 < 右子树]
        F --> G[O(log n)查询性能]
    end

    subgraph "排序方式"
        H[自然排序] --> I[元素实现Comparable]
        J[自定义排序] --> K[提供Comparator]

        I --> L[compareTo方法]
        K --> M[compare方法]
    end

    subgraph "红黑树性质"
        N[性质1] --> N1[节点是红色或黑色]
        O[性质2] --> O1[根节点是黑色]
        P[性质3] --> P1[叶子节点是黑色]
        Q[性质4] --> Q1[红色节点的子节点是黑色]
        R[性质5] --> R1[任意路径黑色节点数相同]
    end

    style G fill:#c8e6c9
    style L fill:#e3f2fd
    style M fill:#e3f2fd
```

#### 代码示例
```java
public class TreeSetDemo {

    // 基本使用演示
    public void basicUsage() {
        System.out.println("=== TreeSet基本使用 ===");

        // 自然排序
        TreeSet<Integer> numbers = new TreeSet<>();
        numbers.add(5);
        numbers.add(2);
        numbers.add(8);
        numbers.add(1);
        numbers.add(9);

        System.out.println("自然排序: " + numbers);  // [1, 2, 5, 8, 9]

        // 字符串自然排序
        TreeSet<String> strings = new TreeSet<>();
        strings.add("banana");
        strings.add("apple");
        strings.add("cherry");

        System.out.println("字符串排序: " + strings);  // [apple, banana, cherry]

        // 自定义排序
        TreeSet<String> reverseStrings = new TreeSet<>((a, b) -> b.compareTo(a));
        reverseStrings.add("banana");
        reverseStrings.add("apple");
        reverseStrings.add("cherry");

        System.out.println("降序排序: " + reverseStrings);  // [cherry, banana, apple]
    }

    // 自定义对象排序
    public void customObjectSorting() {
        System.out.println("=== 自定义对象排序 ===");

        // 方式1: 实现Comparable接口
        TreeSet<Student> students1 = new TreeSet<>();
        students1.add(new Student("张三", 85));
        students1.add(new Student("李四", 92));
        students1.add(new Student("王五", 78));

        System.out.println("按分数排序: " + students1);

        // 方式2: 提供Comparator
        TreeSet<Student> students2 = new TreeSet<>(Comparator.comparing(Student::getName));
        students2.add(new Student("张三", 85));
        students2.add(new Student("李四", 92));
        students2.add(new Student("王五", 78));

        System.out.println("按姓名排序: " + students2);

        // 方式3: 复合排序
        TreeSet<Student> students3 = new TreeSet<>(
            Comparator.comparing(Student::getScore)
                     .thenComparing(Student::getName)
        );
        students3.add(new Student("张三", 85));
        students3.add(new Student("李四", 85));  // 同分数
        students3.add(new Student("王五", 92));

        System.out.println("按分数+姓名排序: " + students3);
    }

    // Student类实现Comparable
    static class Student implements Comparable<Student> {
        private String name;
        private int score;

        public Student(String name, int score) {
            this.name = name;
            this.score = score;
        }

        @Override
        public int compareTo(Student other) {
            // 按分数降序排列
            return Integer.compare(other.score, this.score);
        }

        // getter方法
        public String getName() { return name; }
        public int getScore() { return score; }

        @Override
        public String toString() {
            return name + "(" + score + ")";
        }
    }

    // TreeSet特有方法演示
    public void demonstrateTreeSetMethods() {
        System.out.println("=== TreeSet特有方法 ===");

        TreeSet<Integer> set = new TreeSet<>();
        set.addAll(Arrays.asList(1, 3, 5, 7, 9, 11, 13, 15));

        System.out.println("原集合: " + set);

        // 范围查询
        System.out.println("first(): " + set.first());           // 1 (最小值)
        System.out.println("last(): " + set.last());             // 15 (最大值)

        System.out.println("lower(7): " + set.lower(7));         // 5 (小于7的最大值)
        System.out.println("floor(7): " + set.floor(7));         // 7 (小于等于7的最大值)
        System.out.println("ceiling(8): " + set.ceiling(8));     // 9 (大于等于8的最小值)
        System.out.println("higher(7): " + set.higher(7));       // 9 (大于7的最小值)

        // 子集操作
        System.out.println("headSet(7): " + set.headSet(7));     // [1, 3, 5]
        System.out.println("tailSet(7): " + set.tailSet(7));     // [7, 9, 11, 13, 15]
        System.out.println("subSet(5, 11): " + set.subSet(5, 11)); // [5, 7, 9]

        // 删除操作
        System.out.println("pollFirst(): " + set.pollFirst());   // 1 (删除并返回最小值)
        System.out.println("pollLast(): " + set.pollLast());     // 15 (删除并返回最大值)
        System.out.println("删除后: " + set);
    }

    // 性能分析
    public void performanceAnalysis() {
        int size = 100000;

        // TreeSet性能测试
        long start = System.currentTimeMillis();
        TreeSet<Integer> treeSet = new TreeSet<>();
        for (int i = 0; i < size; i++) {
            treeSet.add(i);
        }

        // 查询测试
        for (int i = 0; i < size; i++) {
            treeSet.contains(i);
        }
        long treeSetTime = System.currentTimeMillis() - start;

        // HashSet性能对比
        start = System.currentTimeMillis();
        HashSet<Integer> hashSet = new HashSet<>();
        for (int i = 0; i < size; i++) {
            hashSet.add(i);
        }

        for (int i = 0; i < size; i++) {
            hashSet.contains(i);
        }
        long hashSetTime = System.currentTimeMillis() - start;

        System.out.println("性能对比 (" + size + "个元素):");
        System.out.println("TreeSet: " + treeSetTime + "ms (O(log n))");
        System.out.println("HashSet: " + hashSetTime + "ms (O(1))");
        System.out.println("TreeSet比HashSet慢: " + (treeSetTime / (double) hashSetTime) + "倍");

        System.out.println("\nTreeSet优势:");
        System.out.println("1. 自动排序");
        System.out.println("2. 范围查询");
        System.out.println("3. 有序遍历");
    }
}
