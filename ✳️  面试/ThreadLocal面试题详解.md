# ThreadLocal 面试题详解

## 1. ThreadLocal 有什么用？⭐⭐⭐

### 问题分析
这是ThreadLocal的基础概念题，考查对ThreadLocal作用和使用场景的理解。

### 标准答案

**ThreadLocal的主要作用：**
- **线程隔离**：为每个线程提供独立的变量副本，避免线程间数据竞争
- **避免参数传递**：在同一线程的不同方法间共享数据，无需显式传参
- **线程安全**：每个线程访问自己的副本，天然线程安全

**核心特点：**
- 每个线程都有自己的"本地变量盒子"
- 线程间数据完全隔离，互不干扰
- 通过get()和set()方法访问线程本地数据

**典型使用场景：**
- 数据库连接管理（每个线程独立连接）
- 用户会话信息存储
- 事务管理（Spring的事务传播）
- 日期格式化工具（SimpleDateFormat线程不安全）

## 2. ThreadLocal 原理了解吗？⭐⭐⭐⭐⭐

### 问题分析
这是ThreadLocal的核心原理题，考查对底层实现机制的深度理解。

### 标准答案

**核心数据结构：**

```java
// Thread类中的成员变量
ThreadLocal.ThreadLocalMap threadLocals = null;

// ThreadLocalMap是ThreadLocal的静态内部类
static class ThreadLocalMap {
    static class Entry extends WeakReference<ThreadLocal<?>> {
        Object value;
        Entry(ThreadLocal<?> k, Object v) {
            super(k);
            value = v;
        }
    }
    private Entry[] table; // 存储数据的数组
}
```

**工作原理：**

1. **存储结构**：
   - 每个Thread对象都有一个ThreadLocalMap成员变量
   - ThreadLocalMap内部使用Entry数组存储数据
   - Entry的key是ThreadLocal对象，value是存储的值

2. **存取流程**：
   ```java
   // set方法流程
   public void set(T value) {
       Thread t = Thread.currentThread();           // 获取当前线程
       ThreadLocalMap map = getMap(t);              // 获取线程的ThreadLocalMap
       if (map != null)
           map.set(this, value);                    // 以当前ThreadLocal为key存储
       else
           createMap(t, value);                     // 首次使用创建map
   }
   
   // get方法流程
   public T get() {
       Thread t = Thread.currentThread();
       ThreadLocalMap map = getMap(t);
       if (map != null) {
           ThreadLocalMap.Entry e = map.getEntry(this);
           if (e != null)
               return (T)e.value;
       }
       return setInitialValue();                    // 返回初始值
   }
   ```

3. **哈希冲突解决**：
   - 使用开放地址法（线性探测）
   - 当发生冲突时，向后查找下一个空位置

**内存模型图解：**
```
Thread1 ──┐
          │
          ├── ThreadLocalMap
          │   ├── Entry[0]: ThreadLocal1 -> Value1
          │   ├── Entry[1]: ThreadLocal2 -> Value2
          │   └── Entry[2]: null
          │
Thread2 ──┤
          │
          └── ThreadLocalMap
              ├── Entry[0]: ThreadLocal1 -> Value3
              ├── Entry[1]: null
              └── Entry[2]: ThreadLocal3 -> Value4
```

## 3. ThreadLocal 会导致内存泄漏吗？⭐⭐⭐⭐

### 问题分析
这是ThreadLocal的经典陷阱题，考查对内存泄漏原理和预防措施的理解。

### 标准答案

**会导致内存泄漏，原因如下：**

1. **弱引用机制**：
   - Entry的key（ThreadLocal）使用WeakReference
   - 当ThreadLocal对象没有强引用时，会被GC回收
   - 但Entry的value仍然被强引用，无法回收

2. **泄漏场景**：
   ```java
   // 危险示例
   public class MemoryLeakExample {
       private static ThreadLocal<LargeObject> threadLocal = new ThreadLocal<>();
       
       public void process() {
           threadLocal.set(new LargeObject()); // 设置大对象
           // 方法结束后，如果线程不结束，LargeObject无法回收
       }
   }
   ```

3. **泄漏条件**：
   - 线程长期存活（如线程池中的线程）
   - ThreadLocal对象被回收，但value仍被引用
   - 没有主动调用remove()方法

**预防措施：**

1. **主动清理**：
   ```java
   try {
       threadLocal.set(value);
       // 业务逻辑
   } finally {
       threadLocal.remove(); // 必须清理
   }
   ```

2. **使用static final**：
   ```java
   private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();
   ```

3. **重写initialValue()**：
   ```java
   private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<String>() {
       @Override
       protected String initialValue() {
           return "default";
       }
   };
   ```

## 4. ThreadLocal 和 synchronized 的区别？⭐⭐⭐

### 标准答案

| 对比维度 | ThreadLocal | synchronized |
|---------|-------------|--------------|
| **解决思路** | 空间换时间，每个线程独立副本 | 时间换空间，串行访问共享资源 |
| **性能** | 高，无锁竞争 | 相对较低，存在锁竞争 |
| **数据共享** | 线程间数据隔离 | 线程间共享数据 |
| **使用场景** | 线程本地存储 | 保护共享资源 |
| **内存消耗** | 每个线程都有副本，内存消耗大 | 只有一份数据，内存消耗小 |

## 5. ThreadLocal 在实际项目中的应用？⭐⭐⭐⭐

### 标准答案

**1. 数据库连接管理**：
```java
public class ConnectionManager {
    private static final ThreadLocal<Connection> CONNECTION_HOLDER = new ThreadLocal<>();
    
    public static Connection getConnection() {
        Connection conn = CONNECTION_HOLDER.get();
        if (conn == null) {
            conn = DriverManager.getConnection(url, username, password);
            CONNECTION_HOLDER.set(conn);
        }
        return conn;
    }
    
    public static void closeConnection() {
        Connection conn = CONNECTION_HOLDER.get();
        if (conn != null) {
            try {
                conn.close();
            } finally {
                CONNECTION_HOLDER.remove();
            }
        }
    }
}
```

**2. 用户上下文信息**：
```java
public class UserContext {
    private static final ThreadLocal<User> USER_HOLDER = new ThreadLocal<>();
    
    public static void setUser(User user) {
        USER_HOLDER.set(user);
    }
    
    public static User getCurrentUser() {
        return USER_HOLDER.get();
    }
    
    public static void clear() {
        USER_HOLDER.remove();
    }
}
```

**3. 日期格式化工具**：
```java
public class DateUtils {
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = 
        ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    
    public static String format(Date date) {
        return DATE_FORMAT.get().format(date);
    }
    
    public static Date parse(String dateStr) throws ParseException {
        return DATE_FORMAT.get().parse(dateStr);
    }
}
```

## 6. ThreadLocal 的最佳实践？⭐⭐⭐

### 标准答案

**1. 声明为static final**：
```java
private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();
```

**2. 及时清理资源**：
```java
try {
    ThreadLocalHolder.set(value);
    // 业务逻辑
} finally {
    ThreadLocalHolder.remove(); // 防止内存泄漏
}
```

**3. 使用try-with-resources模式**：
```java
public class ThreadLocalResource implements AutoCloseable {
    private static final ThreadLocal<String> HOLDER = new ThreadLocal<>();
    
    public static ThreadLocalResource of(String value) {
        HOLDER.set(value);
        return new ThreadLocalResource();
    }
    
    public String get() {
        return HOLDER.get();
    }
    
    @Override
    public void close() {
        HOLDER.remove();
    }
}

// 使用方式
try (ThreadLocalResource resource = ThreadLocalResource.of("value")) {
    // 业务逻辑
} // 自动清理
```

**4. 避免在线程池中使用**：
- 线程池中的线程会被复用，ThreadLocal数据可能污染后续任务
- 如必须使用，确保在任务结束时清理

## 总结

ThreadLocal是Java并发编程中的重要工具，通过为每个线程提供独立的变量副本来解决线程安全问题。理解其原理、正确使用并避免内存泄漏是面试和实际开发中的关键点。记住：**用完必须remove()**！
