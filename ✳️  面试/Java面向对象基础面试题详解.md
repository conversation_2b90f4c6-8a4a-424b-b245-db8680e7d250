# Java面向对象基础面试题详解

## 1. 面向对象的三大特性是什么？⭐⭐⭐⭐

### 问题分析
这是Java面向对象编程的基础概念题，考查对OOP核心思想的理解。

### 标准答案

**面向对象三大特性：封装、继承、多态**

#### 1. 封装（Encapsulation）
**定义**：将数据和操作数据的方法绑定在一起，隐藏内部实现细节，只暴露必要的接口。

**实现方式**：
- 使用private修饰符隐藏属性
- 提供public的getter/setter方法
- 合理使用访问修饰符

```java
public class Student {
    private String name;        // 私有属性
    private int age;
    
    // 公共方法访问私有属性
    public String getName() {
        return name;
    }
    
    public void setAge(int age) {
        if (age > 0 && age < 150) {  // 数据验证
            this.age = age;
        }
    }
}
```

**优点**：
- 提高安全性，防止外部直接修改内部数据
- 便于维护，内部实现改变不影响外部调用
- 实现数据验证和业务逻辑控制

#### 2. 继承（Inheritance）
**定义**：子类可以继承父类的属性和方法，实现代码复用。

```java
// 父类
public class Animal {
    protected String name;
    
    public void eat() {
        System.out.println(name + " is eating");
    }
}

// 子类继承父类
public class Dog extends Animal {
    public void bark() {
        System.out.println(name + " is barking");
    }
    
    @Override
    public void eat() {  // 方法重写
        System.out.println(name + " is eating dog food");
    }
}
```

**特点**：
- Java只支持单继承（一个类只能继承一个父类）
- 子类可以重写父类方法
- 使用super关键字调用父类方法

#### 3. 多态（Polymorphism）
**定义**：同一个接口，不同的实现；同一个方法调用，不同的执行结果。

```java
public class PolymorphismDemo {
    public static void main(String[] args) {
        Animal animal1 = new Dog();     // 向上转型
        Animal animal2 = new Cat();
        
        animal1.eat();  // 调用Dog的eat方法
        animal2.eat();  // 调用Cat的eat方法
        
        // 运行时确定具体调用哪个方法
        makeAnimalEat(new Dog());
        makeAnimalEat(new Cat());
    }
    
    public static void makeAnimalEat(Animal animal) {
        animal.eat();  // 多态调用
    }
}
```

**实现条件**：
- 继承关系
- 方法重写
- 父类引用指向子类对象

## 2. 重载（Overload）和重写（Override）的区别？⭐⭐⭐⭐⭐

### 问题分析
这是Java中容易混淆的概念，考查对方法重载和重写机制的理解。

### 标准答案

| 对比维度 | 重载（Overload） | 重写（Override） |
|----------|------------------|------------------|
| **定义** | 同一个类中方法名相同，参数不同 | 子类重新定义父类的方法 |
| **发生位置** | 同一个类中 | 父子类之间 |
| **参数列表** | 必须不同 | 必须相同 |
| **返回类型** | 可以不同 | 必须相同（或协变返回类型） |
| **访问修饰符** | 可以不同 | 不能比父类更严格 |
| **异常** | 可以不同 | 不能抛出新的检查异常 |
| **绑定时机** | 编译时绑定（静态绑定） | 运行时绑定（动态绑定） |

**重载示例：**
```java
public class Calculator {
    // 方法重载：方法名相同，参数不同
    public int add(int a, int b) {
        return a + b;
    }
    
    public double add(double a, double b) {
        return a + b;
    }
    
    public int add(int a, int b, int c) {
        return a + b + c;
    }
    
    // 编译器根据参数类型和数量选择调用哪个方法
}
```

**重写示例：**
```java
public class Animal {
    public void makeSound() {
        System.out.println("Animal makes sound");
    }
}

public class Dog extends Animal {
    @Override  // 注解标识重写
    public void makeSound() {
        System.out.println("Dog barks");
    }
}

public class Cat extends Animal {
    @Override
    public void makeSound() {
        System.out.println("Cat meows");
    }
}
```

**重写的规则：**
1. **方法签名必须相同**：方法名、参数列表、返回类型
2. **访问权限不能更严格**：public > protected > default > private
3. **不能抛出新的检查异常**：可以抛出更少或更具体的异常
4. **final、static、private方法不能被重写**

## 3. 抽象类和接口的区别？⭐⭐⭐⭐⭐

### 问题分析
这是Java面向对象设计的核心概念，考查对抽象类和接口使用场景的理解。

### 标准答案

| 对比维度 | 抽象类（Abstract Class） | 接口（Interface） |
|----------|-------------------------|-------------------|
| **关键字** | abstract class | interface |
| **继承/实现** | extends（单继承） | implements（多实现） |
| **方法** | 可以有抽象方法和具体方法 | JDK8前只能有抽象方法，JDK8+可以有默认方法和静态方法 |
| **属性** | 可以有实例变量 | 只能有public static final常量 |
| **构造方法** | 可以有构造方法 | 不能有构造方法 |
| **访问修饰符** | 可以有各种访问修饰符 | 方法默认public，属性默认public static final |
| **设计理念** | "is-a"关系，表示继承 | "can-do"关系，表示能力 |

**抽象类示例：**
```java
public abstract class Shape {
    protected String color;  // 实例变量
    
    // 构造方法
    public Shape(String color) {
        this.color = color;
    }
    
    // 具体方法
    public void setColor(String color) {
        this.color = color;
    }
    
    // 抽象方法，子类必须实现
    public abstract double getArea();
    public abstract double getPerimeter();
}

public class Circle extends Shape {
    private double radius;
    
    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }
    
    @Override
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    @Override
    public double getPerimeter() {
        return 2 * Math.PI * radius;
    }
}
```

**接口示例：**
```java
public interface Flyable {
    // 常量（默认public static final）
    int MAX_SPEED = 1000;
    
    // 抽象方法（默认public abstract）
    void fly();
    
    // JDK8+ 默认方法
    default void land() {
        System.out.println("Landing...");
    }
    
    // JDK8+ 静态方法
    static void checkWeather() {
        System.out.println("Weather is good for flying");
    }
}

public interface Swimmable {
    void swim();
}

// 一个类可以实现多个接口
public class Duck extends Animal implements Flyable, Swimmable {
    @Override
    public void fly() {
        System.out.println("Duck is flying");
    }
    
    @Override
    public void swim() {
        System.out.println("Duck is swimming");
    }
}
```

**使用场景选择：**
- **抽象类**：当多个类有共同的属性和部分共同的方法实现时
- **接口**：当需要定义一组规范，让不相关的类都能实现时

## 4. Java中的访问修饰符有哪些？⭐⭐⭐

### 问题分析
考查对Java访问控制机制的理解，这是封装特性的具体实现。

### 标准答案

**四种访问修饰符：**

| 修饰符 | 同一个类 | 同一个包 | 不同包的子类 | 不同包的非子类 |
|--------|----------|----------|--------------|----------------|
| **private** | ✓ | ✗ | ✗ | ✗ |
| **default（包访问）** | ✓ | ✓ | ✗ | ✗ |
| **protected** | ✓ | ✓ | ✓ | ✗ |
| **public** | ✓ | ✓ | ✓ | ✓ |

**详细说明：**

1. **private（私有）**
   ```java
   public class Student {
       private String name;  // 只能在当前类中访问
       
       private void study() {  // 私有方法
           System.out.println("Studying...");
       }
   }
   ```

2. **default（包访问，默认）**
   ```java
   class PackageClass {  // 包访问类
       String packageField;  // 包访问字段
       
       void packageMethod() {  // 包访问方法
           System.out.println("Package method");
       }
   }
   ```

3. **protected（受保护）**
   ```java
   public class Parent {
       protected String protectedField;
       
       protected void protectedMethod() {
           System.out.println("Protected method");
       }
   }
   
   // 不同包的子类可以访问
   public class Child extends Parent {
       public void test() {
           protectedField = "accessible";  // 可以访问
           protectedMethod();  // 可以调用
       }
   }
   ```

4. **public（公共）**
   ```java
   public class PublicClass {
       public String publicField;
       
       public void publicMethod() {
           System.out.println("Public method");
       }
   }
   ```

**使用建议：**
- **private**：内部实现细节，外部不需要知道
- **default**：包内共享的工具类或方法
- **protected**：提供给子类使用的方法或属性
- **public**：对外提供的公共接口

## 5. static关键字的作用？⭐⭐⭐⭐

### 问题分析
考查对static关键字的理解，包括静态变量、静态方法、静态代码块等。

### 标准答案

**static关键字的作用：表示静态，属于类而不是实例**

#### 1. 静态变量（类变量）
```java
public class Counter {
    private static int count = 0;  // 静态变量，所有实例共享
    private int instanceId;        // 实例变量，每个实例独有

    public Counter() {
        count++;  // 每创建一个实例，静态变量+1
        instanceId = count;
    }

    public static int getCount() {  // 静态方法访问静态变量
        return count;
    }
}

// 使用示例
Counter c1 = new Counter();
Counter c2 = new Counter();
System.out.println(Counter.getCount());  // 输出：2
```

#### 2. 静态方法
```java
public class MathUtils {
    // 静态方法，可以直接通过类名调用
    public static int add(int a, int b) {
        return a + b;
    }

    public static double sqrt(double x) {
        return Math.sqrt(x);
    }
}

// 调用方式
int result = MathUtils.add(5, 3);  // 直接通过类名调用
```

**静态方法的限制：**
- 不能访问非静态变量和方法
- 不能使用this和super关键字
- 不能被重写（可以被隐藏）

#### 3. 静态代码块
```java
public class InitializationDemo {
    private static String staticField;
    private String instanceField;

    // 静态代码块：类加载时执行，只执行一次
    static {
        System.out.println("静态代码块执行");
        staticField = "Static initialized";
    }

    // 实例代码块：每次创建实例时执行
    {
        System.out.println("实例代码块执行");
        instanceField = "Instance initialized";
    }

    public InitializationDemo() {
        System.out.println("构造方法执行");
    }
}
```

**执行顺序：**
```
静态代码块 → 实例代码块 → 构造方法
```

#### 4. 静态内部类
```java
public class OuterClass {
    private static String staticField = "Static field";
    private String instanceField = "Instance field";

    // 静态内部类
    public static class StaticInnerClass {
        public void method() {
            System.out.println(staticField);      // 可以访问外部类的静态成员
            // System.out.println(instanceField); // 不能访问外部类的实例成员
        }
    }
}

// 使用静态内部类
OuterClass.StaticInnerClass inner = new OuterClass.StaticInnerClass();
```

## 6. final关键字的作用？⭐⭐⭐⭐

### 问题分析
考查对final关键字在不同场景下的作用和限制。

### 标准答案

**final关键字表示"最终的"，不可改变的**

#### 1. final变量（常量）
```java
public class FinalVariableDemo {
    // 静态常量：编译时确定值
    public static final String CONSTANT = "Hello World";

    // 实例常量：可以在构造方法中初始化
    private final int id;
    private final List<String> list = new ArrayList<>();

    public FinalVariableDemo(int id) {
        this.id = id;  // final变量只能赋值一次
    }

    public void test() {
        // this.id = 100;  // 编译错误：不能重新赋值

        // 注意：final只是引用不可变，对象内容可以变
        list.add("item");  // 可以修改list内容
        // list = new ArrayList<>();  // 编译错误：不能重新赋值引用
    }
}
```

#### 2. final方法
```java
public class Parent {
    // final方法不能被子类重写
    public final void finalMethod() {
        System.out.println("This method cannot be overridden");
    }

    public void normalMethod() {
        System.out.println("This method can be overridden");
    }
}

public class Child extends Parent {
    // @Override
    // public void finalMethod() {  // 编译错误：不能重写final方法
    //     System.out.println("Cannot override");
    // }

    @Override
    public void normalMethod() {  // 可以重写普通方法
        System.out.println("Overridden method");
    }
}
```

#### 3. final类
```java
// final类不能被继承
public final class FinalClass {
    public void method() {
        System.out.println("Final class method");
    }
}

// public class SubClass extends FinalClass {  // 编译错误：不能继承final类
// }

// 典型例子：String、Integer等包装类都是final类
```

**final的使用场景：**
- **常量定义**：`public static final`
- **不可变对象**：确保对象创建后不被修改
- **模板方法模式**：防止关键方法被重写
- **性能优化**：编译器可以进行优化

## 7. this和super关键字的区别？⭐⭐⭐

### 问题分析
考查对this和super关键字的理解，以及在继承关系中的使用。

### 标准答案

| 关键字 | 作用 | 使用场景 |
|--------|------|----------|
| **this** | 引用当前对象 | 访问当前类的成员、构造方法调用 |
| **super** | 引用父类对象 | 访问父类的成员、构造方法调用 |

#### this关键字的使用

**1. 区分成员变量和参数**
```java
public class Student {
    private String name;
    private int age;

    public Student(String name, int age) {
        this.name = name;  // this.name指成员变量，name指参数
        this.age = age;
    }

    public void setName(String name) {
        this.name = name;  // 区分成员变量和参数
    }
}
```

**2. 调用当前类的其他构造方法**
```java
public class Person {
    private String name;
    private int age;
    private String address;

    public Person() {
        this("Unknown", 0);  // 调用两参数构造方法
    }

    public Person(String name, int age) {
        this(name, age, "Unknown");  // 调用三参数构造方法
    }

    public Person(String name, int age, String address) {
        this.name = name;
        this.age = age;
        this.address = address;
    }
}
```

**3. 返回当前对象（链式调用）**
```java
public class Builder {
    private String name;
    private int age;

    public Builder setName(String name) {
        this.name = name;
        return this;  // 返回当前对象
    }

    public Builder setAge(int age) {
        this.age = age;
        return this;
    }

    // 链式调用
    // Builder builder = new Builder().setName("Tom").setAge(20);
}
```

#### super关键字的使用

**1. 访问父类成员**
```java
public class Animal {
    protected String name = "Animal";

    public void eat() {
        System.out.println("Animal is eating");
    }
}

public class Dog extends Animal {
    private String name = "Dog";  // 隐藏父类的name

    public void showName() {
        System.out.println("this.name: " + this.name);    // 输出：Dog
        System.out.println("super.name: " + super.name);  // 输出：Animal
    }

    @Override
    public void eat() {
        super.eat();  // 调用父类的eat方法
        System.out.println("Dog is eating dog food");
    }
}
```

**2. 调用父类构造方法**
```java
public class Vehicle {
    protected String brand;
    protected int year;

    public Vehicle(String brand, int year) {
        this.brand = brand;
        this.year = year;
    }
}

public class Car extends Vehicle {
    private int doors;

    public Car(String brand, int year, int doors) {
        super(brand, year);  // 必须是第一行，调用父类构造方法
        this.doors = doors;
    }
}
```

**注意事项：**
- `super()`调用必须是构造方法的第一行
- `this()`和`super()`不能同时出现在一个构造方法中
- 静态方法中不能使用this和super

## 8. 什么是内部类？有哪些类型？⭐⭐⭐⭐

### 问题分析
考查对Java内部类的理解，包括不同类型内部类的特点和使用场景。

### 标准答案

**内部类：定义在另一个类内部的类**

#### 1. 成员内部类（非静态内部类）
```java
public class OuterClass {
    private String outerField = "Outer field";
    private static String staticField = "Static field";

    // 成员内部类
    public class InnerClass {
        private String innerField = "Inner field";

        public void innerMethod() {
            // 可以直接访问外部类的所有成员（包括私有）
            System.out.println(outerField);
            System.out.println(staticField);
            System.out.println(OuterClass.this.outerField);  // 明确指定外部类实例
        }
    }

    public void createInner() {
        InnerClass inner = new InnerClass();  // 外部类中创建内部类实例
        inner.innerMethod();
    }
}

// 外部创建内部类实例
OuterClass outer = new OuterClass();
OuterClass.InnerClass inner = outer.new InnerClass();
```

#### 2. 静态内部类
```java
public class OuterClass {
    private String outerField = "Outer field";
    private static String staticField = "Static field";

    // 静态内部类
    public static class StaticInnerClass {
        public void method() {
            System.out.println(staticField);     // 可以访问外部类的静态成员
            // System.out.println(outerField);  // 不能访问外部类的实例成员
        }
    }
}

// 创建静态内部类实例（不需要外部类实例）
OuterClass.StaticInnerClass staticInner = new OuterClass.StaticInnerClass();
```

#### 3. 局部内部类
```java
public class OuterClass {
    public void method() {
        final String localVar = "Local variable";

        // 局部内部类
        class LocalInnerClass {
            public void localMethod() {
                System.out.println(localVar);  // 可以访问final或effectively final的局部变量
            }
        }

        LocalInnerClass localInner = new LocalInnerClass();
        localInner.localMethod();
    }
}
```

#### 4. 匿名内部类
```java
public class AnonymousClassDemo {
    public void test() {
        // 匿名内部类实现接口
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                System.out.println("Anonymous class implementing interface");
            }
        };

        // 匿名内部类继承类
        Thread thread = new Thread() {
            @Override
            public void run() {
                System.out.println("Anonymous class extending class");
            }
        };

        // Lambda表达式（JDK8+，函数式接口的简化写法）
        Runnable lambdaRunnable = () -> System.out.println("Lambda expression");
    }
}
```

**内部类的特点对比：**

| 内部类类型 | 访问外部类成员 | 创建方式 | 使用场景 |
|------------|----------------|----------|----------|
| **成员内部类** | 可以访问所有成员 | 需要外部类实例 | 与外部类关系密切的辅助类 |
| **静态内部类** | 只能访问静态成员 | 不需要外部类实例 | 独立的工具类或数据结构 |
| **局部内部类** | 可以访问外部类成员和final局部变量 | 只能在方法内使用 | 临时使用的类 |
| **匿名内部类** | 可以访问外部类成员和final局部变量 | 直接new接口或类 | 简单的回调实现 |

## 9. Java中的多态是如何实现的？⭐⭐⭐⭐⭐

### 问题分析
这是面向对象的核心概念，考查对多态实现机制的深度理解。

### 标准答案

**多态的实现机制：动态绑定（Dynamic Binding）**

#### 多态的实现条件
1. **继承关系**：子类继承父类或实现接口
2. **方法重写**：子类重写父类的方法
3. **向上转型**：父类引用指向子类对象

#### 实现原理：虚方法表（Virtual Method Table）

```java
// 多态示例
public abstract class Animal {
    public abstract void makeSound();

    public void sleep() {
        System.out.println("Animal is sleeping");
    }
}

public class Dog extends Animal {
    @Override
    public void makeSound() {
        System.out.println("Woof! Woof!");
    }

    public void wagTail() {
        System.out.println("Dog is wagging tail");
    }
}

public class Cat extends Animal {
    @Override
    public void makeSound() {
        System.out.println("Meow! Meow!");
    }
}

public class PolymorphismDemo {
    public static void main(String[] args) {
        // 向上转型
        Animal animal1 = new Dog();
        Animal animal2 = new Cat();

        // 多态调用：运行时确定调用哪个方法
        animal1.makeSound();  // 输出：Woof! Woof!
        animal2.makeSound();  // 输出：Meow! Meow!

        // 编译时类型检查
        // animal1.wagTail();  // 编译错误：Animal类没有wagTail方法

        // 向下转型（需要强制转换）
        if (animal1 instanceof Dog) {
            Dog dog = (Dog) animal1;
            dog.wagTail();  // 现在可以调用Dog特有的方法
        }
    }
}
```

#### 方法调用的绑定过程

**编译时绑定（静态绑定）：**
- static方法
- final方法
- private方法
- 构造方法

**运行时绑定（动态绑定）：**
- 普通实例方法的重写

```java
public class BindingDemo {
    public static void staticMethod() {
        System.out.println("Parent static method");
    }

    public final void finalMethod() {
        System.out.println("Parent final method");
    }

    public void instanceMethod() {
        System.out.println("Parent instance method");
    }
}

public class Child extends BindingDemo {
    // 隐藏父类静态方法（不是重写）
    public static void staticMethod() {
        System.out.println("Child static method");
    }

    // 不能重写final方法
    // public void finalMethod() { }  // 编译错误

    @Override
    public void instanceMethod() {
        System.out.println("Child instance method");
    }
}

// 测试绑定
BindingDemo obj = new Child();
obj.staticMethod();     // 输出：Parent static method（编译时绑定）
obj.finalMethod();      // 输出：Parent final method（编译时绑定）
obj.instanceMethod();   // 输出：Child instance method（运行时绑定）
```

## 10. 什么是构造方法？有什么特点？⭐⭐⭐

### 问题分析
考查对构造方法的理解，包括特点、重载、继承等。

### 标准答案

**构造方法（Constructor）：用于创建对象时初始化对象的特殊方法**

#### 构造方法的特点
1. **方法名与类名相同**
2. **没有返回类型**（连void都没有）
3. **不能被继承**
4. **不能被重写**（但可以重载）
5. **创建对象时自动调用**

#### 构造方法示例

```java
public class Student {
    private String name;
    private int age;
    private String major;

    // 默认构造方法
    public Student() {
        this.name = "Unknown";
        this.age = 0;
        this.major = "Undeclared";
    }

    // 带参数的构造方法
    public Student(String name) {
        this.name = name;
        this.age = 0;
        this.major = "Undeclared";
    }

    // 构造方法重载
    public Student(String name, int age) {
        this.name = name;
        this.age = age;
        this.major = "Undeclared";
    }

    // 全参数构造方法
    public Student(String name, int age, String major) {
        this.name = name;
        this.age = age;
        this.major = major;
    }

    // 构造方法调用其他构造方法
    public Student(String name, String major) {
        this(name, 0, major);  // 调用三参数构造方法
    }
}
```

#### 继承中的构造方法

```java
public class Person {
    protected String name;
    protected int age;

    public Person() {
        System.out.println("Person默认构造方法");
    }

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
        System.out.println("Person带参构造方法");
    }
}

public class Student extends Person {
    private String studentId;

    public Student() {
        super();  // 显式调用父类默认构造方法（可省略）
        System.out.println("Student默认构造方法");
    }

    public Student(String name, int age, String studentId) {
        super(name, age);  // 调用父类带参构造方法
        this.studentId = studentId;
        System.out.println("Student带参构造方法");
    }
}

// 创建对象时的输出：
// Person带参构造方法
// Student带参构造方法
```

#### 构造方法的执行顺序

```java
public class InitializationOrder {
    private static String staticField = initStaticField();
    private String instanceField = initInstanceField();

    static {
        System.out.println("3. 静态代码块");
    }

    {
        System.out.println("5. 实例代码块");
    }

    public InitializationOrder() {
        System.out.println("6. 构造方法");
    }

    private static String initStaticField() {
        System.out.println("1. 静态字段初始化");
        return "static";
    }

    private String initInstanceField() {
        System.out.println("4. 实例字段初始化");
        return "instance";
    }
}

// 执行顺序：
// 1. 静态字段初始化
// 2. 静态代码块
// 3. 实例字段初始化
// 4. 实例代码块
// 5. 构造方法
```

## 11. equals()和hashCode()方法的关系？⭐⭐⭐⭐⭐

### 问题分析
这是Java中的重要概念，考查对对象比较和哈希机制的理解。

### 标准答案

**equals()和hashCode()的关系：**
1. 如果两个对象equals()返回true，那么hashCode()必须返回相同的值
2. 如果两个对象hashCode()相同，equals()不一定返回true
3. 重写equals()时必须重写hashCode()

#### 默认实现的问题

```java
public class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // 没有重写equals和hashCode
}

// 测试默认实现
Person p1 = new Person("Tom", 20);
Person p2 = new Person("Tom", 20);

System.out.println(p1.equals(p2));        // false（比较引用）
System.out.println(p1.hashCode());        // 不同的哈希值
System.out.println(p2.hashCode());        // 不同的哈希值

// 在HashMap中的问题
Map<Person, String> map = new HashMap<>();
map.put(p1, "Person1");
System.out.println(map.get(p2));          // null（找不到）
```

#### 正确的重写方式

```java
public class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    @Override
    public boolean equals(Object obj) {
        // 1. 检查是否是同一个对象
        if (this == obj) return true;

        // 2. 检查是否为null
        if (obj == null) return false;

        // 3. 检查类型是否相同
        if (getClass() != obj.getClass()) return false;

        // 4. 强制转换并比较字段
        Person person = (Person) obj;
        return age == person.age &&
               Objects.equals(name, person.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age);
    }
}
```

#### 使用Objects工具类简化

```java
import java.util.Objects;

public class Person {
    private String name;
    private int age;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Person person = (Person) obj;
        return age == person.age && Objects.equals(name, person.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age);
    }
}
```

#### 在集合中的应用

```java
public class HashCodeDemo {
    public static void main(String[] args) {
        Person p1 = new Person("Tom", 20);
        Person p2 = new Person("Tom", 20);

        // HashSet去重
        Set<Person> set = new HashSet<>();
        set.add(p1);
        set.add(p2);
        System.out.println(set.size());  // 1（正确去重）

        // HashMap作为key
        Map<Person, String> map = new HashMap<>();
        map.put(p1, "Person1");
        System.out.println(map.get(p2));  // "Person1"（能正确找到）
    }
}
```

**重写规则：**
1. **自反性**：x.equals(x) 必须返回 true
2. **对称性**：x.equals(y) 和 y.equals(x) 必须返回相同结果
3. **传递性**：如果 x.equals(y) 和 y.equals(z) 都返回 true，那么 x.equals(z) 也必须返回 true
4. **一致性**：多次调用 x.equals(y) 必须返回相同结果
5. **非空性**：x.equals(null) 必须返回 false

## 12. instanceof关键字的作用？⭐⭐⭐

### 问题分析
考查对类型检查和向下转型的理解。

### 标准答案

**instanceof关键字：用于检查对象是否是特定类的实例**

#### 基本用法
```java
public class InstanceofDemo {
    public static void main(String[] args) {
        Animal animal = new Dog();

        // 检查对象类型
        if (animal instanceof Dog) {
            System.out.println("animal是Dog的实例");
            Dog dog = (Dog) animal;  // 安全的向下转型
            dog.bark();
        }

        if (animal instanceof Animal) {
            System.out.println("animal是Animal的实例");  // true
        }

        if (animal instanceof Cat) {
            System.out.println("animal是Cat的实例");     // false
        }

        // null检查
        Animal nullAnimal = null;
        System.out.println(nullAnimal instanceof Animal);  // false
    }
}
```

#### 继承关系中的instanceof
```java
public class InheritanceInstanceof {
    public static void processAnimal(Animal animal) {
        if (animal instanceof Dog) {
            Dog dog = (Dog) animal;
            dog.bark();
            dog.wagTail();
        } else if (animal instanceof Cat) {
            Cat cat = (Cat) animal;
            cat.meow();
            cat.climb();
        } else if (animal instanceof Bird) {
            Bird bird = (Bird) animal;
            bird.fly();
            bird.sing();
        }

        // 所有子类都是Animal的实例
        if (animal instanceof Animal) {
            animal.eat();  // 调用父类方法
        }
    }
}
```

#### JDK 14+ 模式匹配（Pattern Matching）
```java
// JDK 14+的新特性
public class PatternMatchingDemo {
    public static void processAnimal(Animal animal) {
        // 模式匹配，自动转型
        if (animal instanceof Dog dog) {
            dog.bark();  // 不需要手动转型
            dog.wagTail();
        } else if (animal instanceof Cat cat) {
            cat.meow();
            cat.climb();
        }
    }
}
```

## 13. Java中的包（Package）有什么作用？⭐⭐⭐

### 问题分析
考查对Java包机制的理解，包括命名空间、访问控制等。

### 标准答案

**包（Package）的作用：**
1. **命名空间管理**：避免类名冲突
2. **访问控制**：提供包级别的访问权限
3. **代码组织**：按功能模块组织代码
4. **便于维护**：相关类放在同一个包中

#### 包的声明和使用
```java
// 包声明（必须是文件的第一行非注释代码）
package com.company.project.model;

// 导入其他包的类
import java.util.List;
import java.util.ArrayList;
import java.util.Date;

// 导入静态方法
import static java.lang.Math.PI;
import static java.lang.Math.sqrt;

public class Student {
    private List<String> courses = new ArrayList<>();
    private Date enrollmentDate = new Date();

    public double calculateCircleArea(double radius) {
        return PI * radius * radius;  // 直接使用导入的静态常量
    }
}
```

#### 包的访问控制
```java
// com.company.model包中的类
package com.company.model;

public class Person {
    public String publicField;        // 所有地方都可访问
    protected String protectedField;  // 同包和子类可访问
    String packageField;              // 同包可访问
    private String privateField;      // 只有当前类可访问
}

// 同包中的其他类
package com.company.model;

public class Employee extends Person {
    public void test() {
        publicField = "public";      // 可访问
        protectedField = "protected"; // 可访问（继承关系）
        packageField = "package";     // 可访问（同包）
        // privateField = "private";  // 不可访问
    }
}

// 不同包中的类
package com.company.service;
import com.company.model.Person;

public class PersonService {
    public void test() {
        Person person = new Person();
        person.publicField = "public";      // 可访问
        // person.protectedField = "protected"; // 不可访问（不同包且非子类）
        // person.packageField = "package";     // 不可访问（不同包）
        // person.privateField = "private";     // 不可访问
    }
}
```

#### 常见的包命名规范
```java
// 公司域名倒序 + 项目名 + 模块名
com.company.projectname.module

// 示例：
com.alibaba.fastjson.parser     // 阿里巴巴的FastJSON解析器
org.springframework.boot.web    // Spring Boot的Web模块
java.util.concurrent           // JDK的并发工具包
```

## 14. 什么是Java Bean？⭐⭐⭐

### 问题分析
考查对Java Bean规范的理解，这是Java企业开发的基础概念。

### 标准答案

**Java Bean：遵循特定命名规范的Java类，主要用于封装数据**

#### Java Bean的规范
1. **类必须是public的**
2. **必须有无参构造方法**
3. **属性必须是private的**
4. **必须提供getter和setter方法**
5. **实现Serializable接口（可选但推荐）**

#### 标准Java Bean示例
```java
import java.io.Serializable;
import java.util.Date;

public class UserBean implements Serializable {
    private static final long serialVersionUID = 1L;

    // 私有属性
    private Long id;
    private String username;
    private String email;
    private Date createTime;
    private boolean active;

    // 无参构造方法
    public UserBean() {
    }

    // 有参构造方法（可选）
    public UserBean(String username, String email) {
        this.username = username;
        this.email = email;
        this.createTime = new Date();
        this.active = true;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    // boolean类型的getter方法可以用is开头
    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    // 重写toString方法（推荐）
    @Override
    public String toString() {
        return "UserBean{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", createTime=" + createTime +
                ", active=" + active +
                '}';
    }

    // 重写equals和hashCode方法（推荐）
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        UserBean userBean = (UserBean) obj;
        return Objects.equals(id, userBean.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
```

#### Java Bean的使用场景
1. **数据传输对象（DTO）**：在不同层之间传递数据
2. **实体类（Entity）**：映射数据库表结构
3. **表单绑定**：Web开发中绑定表单数据
4. **配置类**：存储配置信息
5. **序列化**：网络传输或持久化存储

#### 现代Java Bean的简化（使用Lombok）
```java
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.io.Serializable;
import java.util.Date;

@Data                    // 自动生成getter、setter、toString、equals、hashCode
@NoArgsConstructor       // 自动生成无参构造方法
@AllArgsConstructor      // 自动生成全参构造方法
public class UserBean implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String username;
    private String email;
    private Date createTime;
    private boolean active;
}
```

## 总结

Java面向对象基础是Java编程的核心，掌握以下要点：

### 🎯 核心概念
1. **三大特性**：封装、继承、多态
2. **重载vs重写**：编译时绑定vs运行时绑定
3. **抽象类vs接口**：is-a关系vs can-do关系

### 🔧 关键字理解
4. **访问修饰符**：private < default < protected < public
5. **static关键字**：属于类的静态成员
6. **final关键字**：不可变的最终修饰
7. **this/super**：当前对象vs父类对象引用

### 🏗️ 高级特性
8. **内部类**：成员、静态、局部、匿名四种类型
9. **多态实现**：动态绑定机制
10. **构造方法**：对象初始化的特殊方法

### 📦 实用工具
11. **equals/hashCode**：对象比较和哈希的黄金搭档
12. **instanceof**：类型检查和安全转型
13. **包机制**：命名空间和访问控制
14. **Java Bean**：数据封装的标准规范

记住核心原则：**封装数据、继承代码、多态行为**！

通过上面的流程图，你可以更直观地理解：
- 继承关系的类图结构
- 多态方法调用的动态绑定过程
- 对象初始化的完整顺序

这些知识点涵盖了Java面向对象编程的全部基础内容，能够帮助你在面试中展现扎实的OOP功底！
