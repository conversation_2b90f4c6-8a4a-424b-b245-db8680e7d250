# MyBatis常见面试题详解

## 目录

- [MyBatis基础](#mybatis基础)
- [MyBatis核心组件](#mybatis核心组件)
- [MyBatis映射器](#mybatis映射器)
- [MyBatis缓存机制](#mybatis缓存机制)
- [MyBatis动态SQL](#mybatis动态sql)
- [MyBatis插件机制](#mybatis插件机制)
- [MyBatis性能优化](#mybatis性能优化)
- [MyBatis Plus](#mybatis-plus)

## MyBatis基础

### 1. 什么是MyBatis？有什么优势？⭐⭐⭐⭐⭐

#### 问题分析
考查对MyBatis框架整体的理解，这是MyBatis面试的基础问题。

#### 标准答案

**MyBatis框架概述：**

```mermaid
flowchart TB
    subgraph mybatis_core ["MyBatis核心特性"]
        A["SQL映射框架<br/>SQL Mapping Framework"]
        B["半自动ORM<br/>Semi-Automatic ORM"]
        C["XML/注解配置<br/>XML/Annotation Config"]
        D["动态SQL<br/>Dynamic SQL"]
    end
    
    subgraph advantages ["核心优势"]
        E["SQL控制灵活<br/>手写SQL优化"]
        F["学习成本低<br/>接近原生JDBC"]
        G["性能优秀<br/>轻量级框架"]
        H["缓存支持<br/>一级二级缓存"]
        I["插件扩展<br/>拦截器机制"]
    end
    
    subgraph vs_others ["与其他ORM对比"]
        J["vs Hibernate<br/>更灵活的SQL控制"]
        K["vs JPA<br/>更简单的配置"]
        L["vs JDBC<br/>更少的样板代码"]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> J
    F --> K
    G --> L
    H --> I
    
    classDef coreStyle fill:#e3f2fd,stroke:#2196f3
    classDef advStyle fill:#c8e6c9,stroke:#4caf50
    classDef compareStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C,D coreStyle
    class E,F,G,H,I advStyle
    class J,K,L compareStyle
```

**MyBatis的核心特点：**

1. **半自动ORM框架**：
   - 需要手写SQL语句，提供更精确的控制
   - 自动完成结果集映射和参数绑定
   - 介于全自动ORM和原生JDBC之间

2. **SQL与Java代码分离**：
   - SQL语句写在XML文件或注解中
   - 便于SQL优化和维护
   - 支持复杂的SQL查询

3. **强大的动态SQL支持**：
   - 支持if、choose、foreach等标签
   - 根据条件动态生成SQL
   - 避免字符串拼接的复杂性

### 2. MyBatis和Hibernate有什么区别？⭐⭐⭐⭐

#### 问题分析
考查对不同ORM框架的理解和选择标准。

#### 标准答案

**MyBatis vs Hibernate详细对比：**

| 对比维度 | MyBatis | Hibernate |
|---------|---------|-----------|
| **ORM类型** | 半自动ORM | 全自动ORM |
| **SQL控制** | 手写SQL，完全控制 | HQL/Criteria，自动生成 |
| **学习成本** | 较低，接近JDBC | 较高，需要学习HQL |
| **性能优化** | 手动优化SQL | 依赖框架优化 |
| **复杂查询** | 支持复杂SQL | 复杂查询相对困难 |
| **缓存机制** | 一级、二级缓存 | 一级、二级、查询缓存 |
| **数据库移植性** | 较差，SQL依赖数据库 | 较好，HQL抽象 |
| **开发效率** | 中等 | 高（简单CRUD） |

**使用场景选择：**

```java
// MyBatis适用场景
// 1. 需要精确控制SQL的项目
@Select("SELECT u.*, p.profile_url FROM users u " +
        "LEFT JOIN profiles p ON u.id = p.user_id " +
        "WHERE u.status = #{status} AND u.create_time > #{startTime}")
List<UserWithProfile> findUsersWithProfile(@Param("status") Integer status, 
                                          @Param("startTime") Date startTime);

// 2. 复杂的统计查询
@Select("SELECT DATE(create_time) as date, COUNT(*) as count " +
        "FROM orders WHERE create_time BETWEEN #{start} AND #{end} " +
        "GROUP BY DATE(create_time) ORDER BY date")
List<OrderStatistics> getOrderStatistics(@Param("start") Date start, 
                                        @Param("end") Date end);

// Hibernate适用场景
// 1. 简单的CRUD操作
@Entity
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<Order> orders;
}

// 2. 快速开发原型
public interface UserRepository extends JpaRepository<User, Long> {
    List<User> findByStatusAndCreateTimeBetween(Integer status, Date start, Date end);
}
```

## MyBatis核心组件

### 3. MyBatis的核心组件有哪些？⭐⭐⭐⭐⭐

#### 问题分析
考查对MyBatis架构和核心组件的理解。

#### 标准答案

**MyBatis核心架构：**

```mermaid
flowchart TB
    subgraph config_layer ["配置层"]
        A["Configuration<br/>全局配置对象"]
        B["Environment<br/>环境配置"]
        C["DataSource<br/>数据源"]
        D["TransactionManager<br/>事务管理器"]
    end
    
    subgraph mapping_layer ["映射层"]
        E["MappedStatement<br/>映射语句"]
        F["ParameterMap<br/>参数映射"]
        G["ResultMap<br/>结果映射"]
        H["BoundSql<br/>绑定SQL"]
    end
    
    subgraph execution_layer ["执行层"]
        I["SqlSession<br/>SQL会话"]
        J["Executor<br/>执行器"]
        K["StatementHandler<br/>语句处理器"]
        L["ParameterHandler<br/>参数处理器"]
        M["ResultSetHandler<br/>结果集处理器"]
    end
    
    subgraph cache_layer ["缓存层"]
        N["Cache<br/>缓存接口"]
        O["PerpetualCache<br/>一级缓存"]
        P["BlockingCache<br/>二级缓存"]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    
    E --> I
    F --> J
    G --> K
    H --> L
    
    I --> N
    J --> O
    K --> P
    L --> M
    
    classDef configStyle fill:#e3f2fd,stroke:#2196f3
    classDef mappingStyle fill:#c8e6c9,stroke:#4caf50
    classDef executionStyle fill:#fff3e0,stroke:#ff9800
    classDef cacheStyle fill:#f3e5f5,stroke:#9c27b0
    
    class A,B,C,D configStyle
    class E,F,G,H mappingStyle
    class I,J,K,L,M executionStyle
    class N,O,P cacheStyle
```

**核心组件详解：**

1. **SqlSessionFactory**：
   - MyBatis的核心工厂类
   - 负责创建SqlSession实例
   - 线程安全，应用级别单例

2. **SqlSession**：
   - 执行SQL的主要接口
   - 包含所有执行SQL的方法
   - 线程不安全，方法级别使用

3. **Executor**：
   - SQL执行器，真正执行SQL的组件
   - 三种类型：SimpleExecutor、ReuseExecutor、BatchExecutor
   - 负责一级缓存和事务管理

### 4. MyBatis的工作流程是什么？⭐⭐⭐⭐⭐

#### 问题分析
考查对MyBatis完整执行流程的理解。

#### 标准答案

**MyBatis执行流程：**

```mermaid
flowchart TB
    subgraph initialization ["初始化阶段"]
        A["1. 读取配置文件<br/>mybatis-config.xml"]
        B["2. 解析配置<br/>XMLConfigBuilder"]
        C["3. 创建Configuration<br/>配置对象"]
        D["4. 构建SqlSessionFactory<br/>工厂对象"]
    end
    
    subgraph session_creation ["会话创建"]
        E["5. 创建SqlSession<br/>openSession()"]
        F["6. 创建Executor<br/>执行器"]
        G["7. 获取Mapper代理<br/>getMapper()"]
    end
    
    subgraph sql_execution ["SQL执行"]
        H["8. 调用Mapper方法<br/>invoke()"]
        I["9. 查找MappedStatement<br/>映射语句"]
        J["10. 执行SQL<br/>Executor.query()"]
        K["11. 处理结果集<br/>ResultSetHandler"]
    end
    
    subgraph cleanup ["清理阶段"]
        L["12. 返回结果<br/>Result"]
        M["13. 关闭SqlSession<br/>close()"]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
    
    classDef initStyle fill:#e3f2fd,stroke:#2196f3
    classDef sessionStyle fill:#c8e6c9,stroke:#4caf50
    classDef executionStyle fill:#fff3e0,stroke:#ff9800
    classDef cleanupStyle fill:#ffcdd2,stroke:#f44336
    
    class A,B,C,D initStyle
    class E,F,G sessionStyle
    class H,I,J,K executionStyle
    class L,M cleanupStyle
```

**详细执行流程代码示例：**

```java
// 1. 初始化MyBatis
public class MyBatisDemo {
    public static void main(String[] args) throws IOException {
        // 读取配置文件
        String resource = "mybatis-config.xml";
        InputStream inputStream = Resources.getResourceAsStream(resource);

        // 构建SqlSessionFactory
        SqlSessionFactory sqlSessionFactory =
            new SqlSessionFactoryBuilder().build(inputStream);

        // 创建SqlSession
        try (SqlSession session = sqlSessionFactory.openSession()) {
            // 获取Mapper代理
            UserMapper mapper = session.getMapper(UserMapper.class);

            // 执行SQL
            User user = mapper.selectById(1L);
            System.out.println(user);
        }
    }
}
```

## MyBatis映射器

### 5. #{}和${}有什么区别？⭐⭐⭐⭐⭐

#### 问题分析
考查MyBatis参数绑定的理解，这是常见的安全问题。

#### 标准答案

**#{}和${}对比分析：**

```mermaid
flowchart TB
    subgraph parameter_binding ["参数绑定方式"]
        A["#{} 预编译绑定<br/>PreparedStatement"]
        B["${} 字符串替换<br/>String Substitution"]
    end

    subgraph security ["安全性"]
        C["#{} 防SQL注入<br/>参数化查询"]
        D["${} 存在SQL注入风险<br/>直接字符串拼接"]
    end

    subgraph performance ["性能"]
        E["#{} 预编译缓存<br/>性能更好"]
        F["${} 每次编译<br/>性能较差"]
    end

    subgraph usage ["使用场景"]
        G["#{} 参数值绑定<br/>WHERE条件、INSERT值"]
        H["${} 动态SQL结构<br/>表名、列名、ORDER BY"]
    end

    A --> C
    B --> D

    C --> E
    D --> F

    E --> G
    F --> H

    classDef bindingStyle fill:#e3f2fd,stroke:#2196f3
    classDef securityStyle fill:#c8e6c9,stroke:#4caf50
    classDef performanceStyle fill:#fff3e0,stroke:#ff9800
    classDef usageStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B bindingStyle
    class C,D securityStyle
    class E,F performanceStyle
    class G,H usageStyle
```

**代码示例对比：**

```java
// 1. #{} 预编译参数绑定（推荐）
@Select("SELECT * FROM users WHERE id = #{id} AND name = #{name}")
User findByIdAndName(@Param("id") Long id, @Param("name") String name);

// 生成的SQL：SELECT * FROM users WHERE id = ? AND name = ?
// 参数通过PreparedStatement.setXxx()方法设置

// 2. ${} 字符串替换（谨慎使用）
@Select("SELECT * FROM users WHERE name = '${name}'")
User findByNameUnsafe(@Param("name") String name);

// 生成的SQL：SELECT * FROM users WHERE name = 'actualName'
// 直接字符串替换，存在SQL注入风险

// 3. ${} 的合理使用场景
@Select("SELECT * FROM ${tableName} WHERE id = #{id}")
User findFromTable(@Param("tableName") String tableName, @Param("id") Long id);

@Select("SELECT * FROM users ORDER BY ${orderColumn} ${orderDirection}")
List<User> findUsersOrdered(@Param("orderColumn") String orderColumn,
                           @Param("orderDirection") String orderDirection);
```

**SQL注入风险演示：**

```java
// 危险示例 - SQL注入攻击
String maliciousName = "'; DROP TABLE users; --";

// 使用 ${} 会导致：
// SELECT * FROM users WHERE name = ''; DROP TABLE users; --'
// 这将删除整个users表！

// 使用 #{} 是安全的：
// SELECT * FROM users WHERE name = ?
// 参数值：'; DROP TABLE users; --
// 只会作为字符串值查询，不会执行恶意SQL
```

### 6. MyBatis如何处理结果集映射？⭐⭐⭐⭐

#### 问题分析
考查MyBatis结果集映射机制的理解。

#### 标准答案

**结果集映射方式：**

```mermaid
flowchart TB
    subgraph mapping_types ["映射方式"]
        A["自动映射<br/>Auto Mapping<br/>字段名匹配"]
        B["ResultMap映射<br/>XML配置映射"]
        C["注解映射<br/>@Results注解"]
        D["构造器映射<br/>Constructor Mapping"]
    end

    subgraph mapping_rules ["映射规则"]
        E["驼峰命名转换<br/>mapUnderscoreToCamelCase"]
        F["类型转换<br/>TypeHandler"]
        G["嵌套映射<br/>Nested Mapping"]
        H["延迟加载<br/>Lazy Loading"]
    end

    subgraph complex_mapping ["复杂映射"]
        I["一对一映射<br/>association"]
        J["一对多映射<br/>collection"]
        K["多层嵌套<br/>Nested ResultMap"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> I

    classDef typeStyle fill:#e3f2fd,stroke:#2196f3
    classDef ruleStyle fill:#c8e6c9,stroke:#4caf50
    classDef complexStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D typeStyle
    class E,F,G,H ruleStyle
    class I,J,K complexStyle
```

**结果集映射示例：**

```xml
<!-- 1. 基础ResultMap -->
<resultMap id="userResultMap" type="User">
    <id property="id" column="user_id"/>
    <result property="userName" column="user_name"/>
    <result property="email" column="email"/>
    <result property="createTime" column="create_time"/>
</resultMap>

<!-- 2. 一对一映射 -->
<resultMap id="userWithProfileMap" type="User">
    <id property="id" column="user_id"/>
    <result property="userName" column="user_name"/>
    <association property="profile" javaType="UserProfile">
        <id property="id" column="profile_id"/>
        <result property="avatar" column="avatar"/>
        <result property="bio" column="bio"/>
    </association>
</resultMap>

<!-- 3. 一对多映射 -->
<resultMap id="userWithOrdersMap" type="User">
    <id property="id" column="user_id"/>
    <result property="userName" column="user_name"/>
    <collection property="orders" ofType="Order">
        <id property="id" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="amount" column="amount"/>
    </collection>
</resultMap>

<!-- 4. 复杂嵌套映射 -->
<resultMap id="orderWithDetailsMap" type="Order">
    <id property="id" column="order_id"/>
    <result property="orderNo" column="order_no"/>
    <association property="user" javaType="User">
        <id property="id" column="user_id"/>
        <result property="userName" column="user_name"/>
    </association>
    <collection property="orderItems" ofType="OrderItem">
        <id property="id" column="item_id"/>
        <result property="productName" column="product_name"/>
        <result property="quantity" column="quantity"/>
        <result property="price" column="price"/>
    </collection>
</resultMap>
```

## MyBatis缓存机制

### 7. MyBatis的缓存机制是怎样的？⭐⭐⭐⭐⭐

#### 问题分析
考查MyBatis缓存的理解，这是性能优化的重要知识点。

#### 标准答案

**MyBatis缓存架构：**

```mermaid
flowchart TB
    subgraph first_level ["一级缓存（SqlSession级别）"]
        A["PerpetualCache<br/>默认开启"]
        B["HashMap存储<br/>key-value结构"]
        C["SqlSession生命周期<br/>会话结束清空"]
        D["同一SqlSession<br/>相同查询复用"]
    end

    subgraph second_level ["二级缓存（Mapper级别）"]
        E["Namespace级别<br/>多个SqlSession共享"]
        F["需要手动开启<br/>配置cache标签"]
        G["序列化存储<br/>对象需实现Serializable"]
        H["事务提交后生效<br/>commit()后缓存"]
    end

    subgraph cache_flow ["缓存查询流程"]
        I["1. 查询二级缓存<br/>Namespace Cache"]
        J["2. 查询一级缓存<br/>SqlSession Cache"]
        K["3. 查询数据库<br/>Database Query"]
        L["4. 写入缓存<br/>Cache Storage"]
    end

    A --> I
    B --> J
    C --> K
    D --> L

    E --> I
    F --> J
    G --> K
    H --> L

    classDef firstStyle fill:#e3f2fd,stroke:#2196f3
    classDef secondStyle fill:#c8e6c9,stroke:#4caf50
    classDef flowStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D firstStyle
    class E,F,G,H secondStyle
    class I,J,K,L flowStyle
```

**缓存配置和使用示例：**

```xml
<!-- 1. 全局缓存配置 -->
<configuration>
    <settings>
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
    </settings>
</configuration>

<!-- 2. Mapper级别缓存配置 -->
<mapper namespace="com.example.mapper.UserMapper">
    <!-- 开启二级缓存 -->
    <cache
        eviction="LRU"           <!-- 缓存回收策略 -->
        flushInterval="60000"    <!-- 刷新间隔60秒 -->
        size="512"               <!-- 缓存对象数量 -->
        readOnly="false"/>       <!-- 是否只读 -->

    <!-- 查询语句 -->
    <select id="selectById" resultType="User" useCache="true">
        SELECT * FROM users WHERE id = #{id}
    </select>

    <!-- 更新语句会清空缓存 -->
    <update id="updateUser" flushCache="true">
        UPDATE users SET name = #{name} WHERE id = #{id}
    </update>
</mapper>
```

**缓存失效机制：**

```java
// 一级缓存失效场景
public class CacheDemo {

    public void firstLevelCacheTest() {
        SqlSession session = sqlSessionFactory.openSession();
        UserMapper mapper = session.getMapper(UserMapper.class);

        // 第一次查询，从数据库获取
        User user1 = mapper.selectById(1L);

        // 第二次查询，从一级缓存获取
        User user2 = mapper.selectById(1L);

        // 执行更新操作，一级缓存被清空
        mapper.updateUser(new User(1L, "newName"));

        // 第三次查询，重新从数据库获取
        User user3 = mapper.selectById(1L);

        session.close();
    }

    public void secondLevelCacheTest() {
        // 第一个SqlSession
        SqlSession session1 = sqlSessionFactory.openSession();
        UserMapper mapper1 = session1.getMapper(UserMapper.class);
        User user1 = mapper1.selectById(1L);
        session1.commit(); // 提交后数据进入二级缓存
        session1.close();

        // 第二个SqlSession，可以使用二级缓存
        SqlSession session2 = sqlSessionFactory.openSession();
        UserMapper mapper2 = session2.getMapper(UserMapper.class);
        User user2 = mapper2.selectById(1L); // 从二级缓存获取
        session2.close();
    }
}
```

### 8. MyBatis缓存的实现原理是什么？⭐⭐⭐⭐

#### 问题分析
考查对MyBatis缓存底层实现的深入理解。

#### 标准答案

**缓存实现原理：**

```mermaid
flowchart TB
    subgraph cache_key ["缓存Key生成"]
        A["StatementId<br/>语句ID"]
        B["Parameters<br/>参数值"]
        C["RowBounds<br/>分页信息"]
        D["SQL语句<br/>实际SQL"]
        E["Environment<br/>环境信息"]
    end

    subgraph cache_impl ["缓存实现"]
        F["Cache接口<br/>缓存抽象"]
        G["PerpetualCache<br/>基础实现"]
        H["LruCache<br/>LRU装饰器"]
        I["FifoCache<br/>FIFO装饰器"]
        J["BlockingCache<br/>阻塞装饰器"]
    end

    subgraph cache_decorator ["装饰器模式"]
        K["TransactionalCache<br/>事务缓存"]
        L["LoggingCache<br/>日志缓存"]
        M["SerializedCache<br/>序列化缓存"]
        N["SynchronizedCache<br/>同步缓存"]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    G --> L
    H --> M
    I --> N

    classDef keyStyle fill:#e3f2fd,stroke:#2196f3
    classDef implStyle fill:#c8e6c9,stroke:#4caf50
    classDef decoratorStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D,E keyStyle
    class F,G,H,I,J implStyle
    class K,L,M,N decoratorStyle
```

**缓存Key生成算法：**

```java
// CacheKey生成逻辑
public class CacheKey implements Cloneable, Serializable {

    public void update(Object object) {
        int baseHashCode = object == null ? 1 : ArrayUtil.hashCode(object);

        this.count++;
        this.checksum += baseHashCode;
        this.hashcode = this.hashcode * 37 + baseHashCode;

        this.updateList.add(object);
    }

    // 缓存Key由以下部分组成：
    // 1. MappedStatement的ID
    // 2. 分页信息（offset, limit）
    // 3. SQL语句
    // 4. 参数值列表
    // 5. Environment的ID
}

// 缓存装饰器实现
public class LruCache implements Cache {
    private final Cache delegate;
    private Map<Object, Object> keyMap;
    private Object eldestKey;

    @Override
    public Object getObject(Object key) {
        keyMap.get(key); // 更新访问顺序
        return delegate.getObject(key);
    }

    @Override
    public void putObject(Object key, Object value) {
        delegate.putObject(key, value);
        cycleKeyList(key);
    }

    private void cycleKeyList(Object key) {
        keyMap.put(key, key);
        if (eldestKey != null) {
            delegate.removeObject(eldestKey);
        }
        eldestKey = keyMap.entrySet().iterator().next().getKey();
    }
}
```

## MyBatis动态SQL

### 9. MyBatis动态SQL有哪些标签？⭐⭐⭐⭐⭐

#### 问题分析
考查MyBatis动态SQL的使用，这是实际开发中的重要技能。

#### 标准答案

**动态SQL标签体系：**

```mermaid
flowchart TB
    subgraph conditional ["条件标签"]
        A["if<br/>条件判断"]
        B["choose/when/otherwise<br/>多条件选择"]
        C["where<br/>WHERE子句"]
        D["set<br/>SET子句"]
    end

    subgraph loop ["循环标签"]
        E["foreach<br/>循环遍历"]
        F["collection属性<br/>集合类型"]
        G["item属性<br/>循环变量"]
        H["separator属性<br/>分隔符"]
    end

    subgraph text ["文本处理"]
        I["trim<br/>文本修剪"]
        J["bind<br/>变量绑定"]
        K["sql<br/>SQL片段"]
        L["include<br/>片段引用"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef conditionalStyle fill:#e3f2fd,stroke:#2196f3
    classDef loopStyle fill:#c8e6c9,stroke:#4caf50
    classDef textStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D conditionalStyle
    class E,F,G,H loopStyle
    class I,J,K,L textStyle
```

**动态SQL实战示例：**

```xml
<!-- 1. 复杂查询条件 -->
<select id="findUsers" resultType="User">
    SELECT * FROM users
    <where>
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="email != null and email != ''">
            AND email = #{email}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="createTimeStart != null">
            AND create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND create_time <= #{createTimeEnd}
        </if>
    </where>
    <if test="orderBy != null and orderBy != ''">
        ORDER BY ${orderBy}
        <if test="orderDirection != null and orderDirection != ''">
            ${orderDirection}
        </if>
    </if>
</select>

<!-- 2. choose/when/otherwise 多条件选择 -->
<select id="findUsersByCondition" resultType="User">
    SELECT * FROM users
    WHERE 1=1
    <choose>
        <when test="id != null">
            AND id = #{id}
        </when>
        <when test="name != null and name != ''">
            AND name = #{name}
        </when>
        <when test="email != null and email != ''">
            AND email = #{email}
        </when>
        <otherwise>
            AND status = 1
        </otherwise>
    </choose>
</select>

<!-- 3. foreach 批量操作 -->
<select id="findUsersByIds" resultType="User">
    SELECT * FROM users WHERE id IN
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
    </foreach>
</select>

<insert id="batchInsertUsers">
    INSERT INTO users (name, email, status) VALUES
    <foreach collection="users" item="user" separator=",">
        (#{user.name}, #{user.email}, #{user.status})
    </foreach>
</insert>

<!-- 4. 动态更新 -->
<update id="updateUserSelective">
    UPDATE users
    <set>
        <if test="name != null and name != ''">
            name = #{name},
        </if>
        <if test="email != null and email != ''">
            email = #{email},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        update_time = NOW()
    </set>
    WHERE id = #{id}
</update>

<!-- 5. SQL片段复用 -->
<sql id="userColumns">
    id, name, email, status, create_time, update_time
</sql>

<sql id="userConditions">
    <where>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </where>
</sql>

<select id="selectUsers" resultType="User">
    SELECT <include refid="userColumns"/>
    FROM users
    <include refid="userConditions"/>
</select>
```

## MyBatis插件机制

### 10. MyBatis插件机制是如何工作的？⭐⭐⭐⭐

#### 问题分析
考查MyBatis拦截器机制的理解，这是扩展MyBatis功能的重要方式。

#### 标准答案

**MyBatis插件架构：**

```mermaid
flowchart TB
    subgraph plugin_targets ["拦截目标"]
        A["Executor<br/>执行器拦截"]
        B["StatementHandler<br/>语句处理器拦截"]
        C["ParameterHandler<br/>参数处理器拦截"]
        D["ResultSetHandler<br/>结果集处理器拦截"]
    end

    subgraph plugin_flow ["拦截流程"]
        E["1. 插件注册<br/>Configuration"]
        F["2. 代理创建<br/>Plugin.wrap()"]
        G["3. 方法拦截<br/>intercept()"]
        H["4. 继续执行<br/>proceed()"]
    end

    subgraph plugin_usage ["使用场景"]
        I["SQL性能监控<br/>执行时间统计"]
        J["SQL日志记录<br/>参数和结果记录"]
        K["数据权限控制<br/>动态添加WHERE条件"]
        L["分页插件<br/>自动添加LIMIT"]
        M["数据脱敏<br/>敏感信息处理"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    I --> M

    classDef targetStyle fill:#e3f2fd,stroke:#2196f3
    classDef flowStyle fill:#c8e6c9,stroke:#4caf50
    classDef usageStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D targetStyle
    class E,F,G,H flowStyle
    class I,J,K,L,M usageStyle
```

**插件实现示例：**

```java
// 1. SQL执行时间监控插件
@Intercepts({
    @Signature(type = Executor.class, method = "query",
               args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = Executor.class, method = "update",
               args = {MappedStatement.class, Object.class})
})
public class SqlPerformanceInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        try {
            // 执行原方法
            Object result = invocation.proceed();
            return result;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录执行时间
            MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
            String sqlId = ms.getId();

            if (duration > 1000) { // 超过1秒的慢SQL
                System.out.println("慢SQL警告: " + sqlId + ", 执行时间: " + duration + "ms");
            }
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 设置插件属性
    }
}

// 2. 分页插件实现
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare",
               args = {Connection.class, Integer.class})
})
public class PageInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = MetaObject.forObject(statementHandler,
            DEFAULT_OBJECT_FACTORY, DEFAULT_OBJECT_WRAPPER_FACTORY, new DefaultReflectorFactory());

        // 获取分页参数
        BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
        Object parameterObject = boundSql.getParameterObject();

        if (parameterObject instanceof PageParam) {
            PageParam pageParam = (PageParam) parameterObject;
            String originalSql = boundSql.getSql();

            // 构造分页SQL
            String pageSql = originalSql + " LIMIT " + pageParam.getOffset() + ", " + pageParam.getSize();
            metaObject.setValue("delegate.boundSql.sql", pageSql);
        }

        return invocation.proceed();
    }
}

// 3. 数据权限控制插件
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare",
               args = {Connection.class, Integer.class})
})
public class DataPermissionInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler handler = (StatementHandler) invocation.getTarget();
        BoundSql boundSql = handler.getBoundSql();
        String originalSql = boundSql.getSql();

        // 获取当前用户信息
        Long currentUserId = getCurrentUserId();

        // 动态添加数据权限条件
        if (originalSql.toLowerCase().contains("select") &&
            originalSql.toLowerCase().contains("from users")) {
            String permissionSql = originalSql + " AND (created_by = " + currentUserId +
                                 " OR id = " + currentUserId + ")";

            // 替换SQL
            Field sqlField = BoundSql.class.getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, permissionSql);
        }

        return invocation.proceed();
    }

    private Long getCurrentUserId() {
        // 从ThreadLocal或Security Context获取当前用户ID
        return 1L;
    }
}
```

### 11. MyBatis如何处理批量操作？⭐⭐⭐⭐

#### 问题分析
考查MyBatis批量操作的实现方式和性能优化。

#### 标准答案

**批量操作方式对比：**

```mermaid
flowchart TB
    subgraph batch_types ["批量操作类型"]
        A["foreach批量<br/>单条SQL多值"]
        B["BatchExecutor<br/>JDBC批处理"]
        C["批量插入<br/>Batch Insert"]
        D["批量更新<br/>Batch Update"]
    end

    subgraph performance ["性能对比"]
        E["foreach方式<br/>SQL解析开销大"]
        F["BatchExecutor<br/>网络开销小"]
        G["单条执行<br/>网络开销大"]
        H["批量提交<br/>事务开销小"]
    end

    subgraph best_practices ["最佳实践"]
        I["大数据量<br/>使用BatchExecutor"]
        J["中等数据量<br/>使用foreach"]
        K["小数据量<br/>单条执行"]
        L["分批处理<br/>避免内存溢出"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef typeStyle fill:#e3f2fd,stroke:#2196f3
    classDef perfStyle fill:#c8e6c9,stroke:#4caf50
    classDef practiceStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D typeStyle
    class E,F,G,H perfStyle
    class I,J,K,L practiceStyle
```

**批量操作实现：**

```java
// 1. foreach批量插入
@Insert("<script>" +
        "INSERT INTO users (name, email, status) VALUES " +
        "<foreach collection='users' item='user' separator=','>" +
        "(#{user.name}, #{user.email}, #{user.status})" +
        "</foreach>" +
        "</script>")
int batchInsertUsers(@Param("users") List<User> users);

// 2. BatchExecutor批量操作
public class BatchService {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    public void batchInsertWithExecutor(List<User> users) {
        // 使用BatchExecutor
        try (SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            UserMapper mapper = session.getMapper(UserMapper.class);

            for (User user : users) {
                mapper.insertUser(user);
            }

            // 批量提交
            session.commit();
        }
    }

    // 3. 分批处理大数据量
    public void batchInsertLargeData(List<User> users) {
        int batchSize = 1000;

        for (int i = 0; i < users.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, users.size());
            List<User> batch = users.subList(i, endIndex);

            try (SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
                UserMapper mapper = session.getMapper(UserMapper.class);

                for (User user : batch) {
                    mapper.insertUser(user);
                }

                session.commit();
            }
        }
    }
}

// 4. 批量更新优化
@Update("<script>" +
        "<foreach collection='users' item='user' separator=';'>" +
        "UPDATE users SET name = #{user.name}, email = #{user.email} " +
        "WHERE id = #{user.id}" +
        "</foreach>" +
        "</script>")
int batchUpdateUsers(@Param("users") List<User> users);
```

## MyBatis性能优化

### 12. MyBatis有哪些性能优化策略？⭐⭐⭐⭐⭐

#### 问题分析
考查MyBatis性能优化的全面理解，这是高级开发者必备技能。

#### 标准答案

**MyBatis性能优化策略：**

```mermaid
flowchart TB
    subgraph sql_optimization ["SQL优化"]
        A["索引优化<br/>合理使用索引"]
        B["查询优化<br/>避免N+1问题"]
        C["分页优化<br/>LIMIT优化"]
        D["批量操作<br/>减少网络开销"]
    end

    subgraph cache_optimization ["缓存优化"]
        E["一级缓存<br/>SqlSession级别"]
        F["二级缓存<br/>Mapper级别"]
        G["第三方缓存<br/>Redis/Ehcache"]
        H["查询缓存<br/>结果集缓存"]
    end

    subgraph connection_optimization ["连接优化"]
        I["连接池配置<br/>合理的池大小"]
        J["预编译语句<br/>PreparedStatement"]
        K["批量提交<br/>减少事务开销"]
        L["懒加载<br/>按需加载"]
    end

    subgraph code_optimization ["代码优化"]
        M["ResultMap优化<br/>避免反射开销"]
        N["参数优化<br/>合理使用#{}"]
        O["SQL片段<br/>减少重复SQL"]
        P["插件优化<br/>监控慢SQL"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    I --> M
    J --> N
    K --> O
    L --> P

    classDef sqlStyle fill:#e3f2fd,stroke:#2196f3
    classDef cacheStyle fill:#c8e6c9,stroke:#4caf50
    classDef connStyle fill:#fff3e0,stroke:#ff9800
    classDef codeStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D sqlStyle
    class E,F,G,H cacheStyle
    class I,J,K,L connStyle
    class M,N,O,P codeStyle
```

**性能优化实践：**

```java
// 1. 避免N+1查询问题
// 错误方式：会产生N+1查询
@Select("SELECT * FROM users")
List<User> findAllUsers();

@Select("SELECT * FROM orders WHERE user_id = #{userId}")
List<Order> findOrdersByUserId(Long userId);

// 正确方式：使用关联查询
@Select("SELECT u.*, o.id as order_id, o.order_no, o.amount " +
        "FROM users u LEFT JOIN orders o ON u.id = o.user_id")
@Results({
    @Result(property = "id", column = "id"),
    @Result(property = "name", column = "name"),
    @Result(property = "orders", javaType = List.class,
            column = "id", many = @Many(select = "findOrdersByUserId"))
})
List<User> findUsersWithOrders();

// 2. 分页查询优化
public class PageHelper {

    // 物理分页，避免内存分页
    @Select("SELECT * FROM users WHERE status = #{status} " +
            "ORDER BY create_time DESC LIMIT #{offset}, #{size}")
    List<User> findUsersByPage(@Param("status") Integer status,
                              @Param("offset") Integer offset,
                              @Param("size") Integer size);

    // 使用覆盖索引优化大偏移量分页
    @Select("SELECT u.* FROM users u " +
            "INNER JOIN (SELECT id FROM users WHERE status = #{status} " +
            "ORDER BY create_time DESC LIMIT #{offset}, #{size}) t " +
            "ON u.id = t.id")
    List<User> findUsersByPageOptimized(@Param("status") Integer status,
                                       @Param("offset") Integer offset,
                                       @Param("size") Integer size);
}

// 3. 连接池配置优化
@Configuration
public class DataSourceConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari")
    public HikariDataSource dataSource() {
        HikariConfig config = new HikariConfig();

        // 连接池大小配置
        config.setMaximumPoolSize(20);           // 最大连接数
        config.setMinimumIdle(5);                // 最小空闲连接数
        config.setConnectionTimeout(30000);      // 连接超时时间
        config.setIdleTimeout(600000);           // 空闲超时时间
        config.setMaxLifetime(1800000);          // 连接最大生命周期

        // 性能优化配置
        config.setLeakDetectionThreshold(60000); // 连接泄漏检测
        config.setValidationTimeout(5000);       // 验证超时时间

        return new HikariDataSource(config);
    }
}

// 4. 懒加载配置
@Configuration
public class MyBatisConfig {

    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
        factory.setDataSource(dataSource);

        // 性能优化配置
        org.apache.ibatis.session.Configuration config =
            new org.apache.ibatis.session.Configuration();

        config.setLazyLoadingEnabled(true);           // 开启懒加载
        config.setAggressiveLazyLoading(false);       // 关闭积极懒加载
        config.setCacheEnabled(true);                 // 开启二级缓存
        config.setUseGeneratedKeys(true);             // 使用生成的主键
        config.setDefaultExecutorType(ExecutorType.REUSE); // 重用执行器

        factory.setConfiguration(config);
        return factory.getObject();
    }
}
```

## MyBatis Plus

### 13. MyBatis Plus有什么优势？⭐⭐⭐⭐⭐

#### 问题分析
考查对MyBatis Plus增强功能的理解，这是现代MyBatis开发的主流选择。

#### 标准答案

**MyBatis Plus核心特性：**

```mermaid
flowchart TB
    subgraph core_features ["核心特性"]
        A["无侵入<br/>只做增强不做改变"]
        B["损耗小<br/>启动即会自动注入基本CRUD"]
        C["强大的CRUD<br/>内置通用Mapper"]
        D["支持Lambda<br/>写法避免写错"]
    end

    subgraph advanced_features ["高级特性"]
        E["代码生成器<br/>自动生成Entity/Mapper/Service"]
        F["自动分页<br/>物理分页插件"]
        G["性能分析<br/>SQL性能监控"]
        H["全局拦截<br/>统一SQL处理"]
    end

    subgraph data_features ["数据处理"]
        I["逻辑删除<br/>deleted字段标记"]
        J["自动填充<br/>创建时间/更新时间"]
        K["乐观锁<br/>version字段控制"]
        L["多租户<br/>tenant_id隔离"]
    end

    subgraph query_features ["查询增强"]
        M["条件构造器<br/>QueryWrapper"]
        N["Lambda查询<br/>LambdaQueryWrapper"]
        O["动态表名<br/>@TableName"]
        P["字段策略<br/>@TableField"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    I --> M
    J --> N
    K --> O
    L --> P

    classDef coreStyle fill:#e3f2fd,stroke:#2196f3
    classDef advancedStyle fill:#c8e6c9,stroke:#4caf50
    classDef dataStyle fill:#fff3e0,stroke:#ff9800
    classDef queryStyle fill:#f3e5f5,stroke:#9c27b0

    class A,B,C,D coreStyle
    class E,F,G,H advancedStyle
    class I,J,K,L dataStyle
    class M,N,O,P queryStyle
```

**MyBatis Plus实战示例：**

```java
// 1. 实体类配置
@Data
@TableName("sys_user")
public class User {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_name")
    private String userName;

    private String email;

    @TableLogic  // 逻辑删除
    private Integer deleted;

    @Version     // 乐观锁
    private Integer version;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)  // 非数据库字段
    private String tempField;
}

// 2. Mapper接口
public interface UserMapper extends BaseMapper<User> {
    // 继承BaseMapper，自动拥有基础CRUD方法

    // 自定义SQL
    @Select("SELECT * FROM sys_user WHERE dept_id = #{deptId}")
    List<User> selectByDeptId(@Param("deptId") Long deptId);
}

// 3. Service层
@Service
public class UserService extends ServiceImpl<UserMapper, User> implements IService<User> {

    // 条件查询示例
    public List<User> findUsers(String name, Integer status) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(name), User::getUserName, name)
               .eq(status != null, User::getStatus, status)
               .orderByDesc(User::getCreateTime);

        return list(wrapper);
    }

    // 分页查询
    public IPage<User> findUsersPage(Page<User> page, String name) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(name), User::getUserName, name);

        return page(page, wrapper);
    }

    // 批量操作
    public boolean batchSaveOrUpdate(List<User> users) {
        return saveOrUpdateBatch(users);
    }
}

// 4. 配置类
@Configuration
@MapperScan("com.example.mapper")
public class MybatisPlusConfig {

    // 分页插件
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // SQL性能规范插件
        interceptor.addInnerInterceptor(new IllegalSQLInnerInterceptor());

        return interceptor;
    }

    // 自动填充处理器
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
                this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
            }
        };
    }
}
```

### 14. MyBatis Plus的条件构造器如何使用？⭐⭐⭐⭐

#### 问题分析
考查MyBatis Plus条件构造器的灵活使用。

#### 标准答案

**条件构造器体系：**

```mermaid
flowchart TB
    subgraph wrapper_types ["构造器类型"]
        A["QueryWrapper<br/>普通查询构造器"]
        B["LambdaQueryWrapper<br/>Lambda查询构造器"]
        C["UpdateWrapper<br/>更新构造器"]
        D["LambdaUpdateWrapper<br/>Lambda更新构造器"]
    end

    subgraph query_methods ["查询方法"]
        E["eq/ne<br/>等于/不等于"]
        F["gt/ge/lt/le<br/>大于/大于等于/小于/小于等于"]
        G["like/notLike<br/>模糊查询"]
        H["in/notIn<br/>范围查询"]
        I["between<br/>区间查询"]
        J["isNull/isNotNull<br/>空值判断"]
    end

    subgraph logical_methods ["逻辑方法"]
        K["and/or<br/>逻辑与/或"]
        L["nested<br/>嵌套条件"]
        M["apply<br/>拼接SQL"]
        N["last<br/>最后拼接"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> K
    F --> L
    G --> M
    H --> N
    I --> K
    J --> L

    classDef wrapperStyle fill:#e3f2fd,stroke:#2196f3
    classDef queryStyle fill:#c8e6c9,stroke:#4caf50
    classDef logicalStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D wrapperStyle
    class E,F,G,H,I,J queryStyle
    class K,L,M,N logicalStyle
```

**条件构造器使用示例：**

```java
// 1. 基础查询条件
public class UserQueryService {

    @Autowired
    private UserMapper userMapper;

    // Lambda查询（推荐）
    public List<User> findUsersByCondition(String name, Integer status, Date startTime) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        wrapper.like(StringUtils.isNotBlank(name), User::getUserName, name)
               .eq(status != null, User::getStatus, status)
               .ge(startTime != null, User::getCreateTime, startTime)
               .orderByDesc(User::getCreateTime);

        return userMapper.selectList(wrapper);
    }

    // 复杂条件查询
    public List<User> findUsersComplex(UserQueryDTO queryDTO) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        wrapper.and(StringUtils.isNotBlank(queryDTO.getKeyword()), w ->
                w.like(User::getUserName, queryDTO.getKeyword())
                 .or()
                 .like(User::getEmail, queryDTO.getKeyword())
               )
               .in(CollectionUtils.isNotEmpty(queryDTO.getStatusList()),
                   User::getStatus, queryDTO.getStatusList())
               .between(queryDTO.getStartTime() != null && queryDTO.getEndTime() != null,
                       User::getCreateTime, queryDTO.getStartTime(), queryDTO.getEndTime())
               .nested(queryDTO.getDeptId() != null, w ->
                   w.eq(User::getDeptId, queryDTO.getDeptId())
                    .or()
                    .in(User::getDeptId, getSubDeptIds(queryDTO.getDeptId()))
               );

        return userMapper.selectList(wrapper);
    }

    // 动态排序
    public List<User> findUsersWithSort(String sortField, String sortOrder) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        // 动态排序
        if ("name".equals(sortField)) {
            wrapper.orderBy(true, "asc".equals(sortOrder), User::getUserName);
        } else if ("createTime".equals(sortField)) {
            wrapper.orderBy(true, "asc".equals(sortOrder), User::getCreateTime);
        } else {
            wrapper.orderByDesc(User::getId);
        }

        return userMapper.selectList(wrapper);
    }
}

// 2. 更新构造器
public class UserUpdateService {

    @Autowired
    private UserMapper userMapper;

    // 条件更新
    public boolean updateUserStatus(List<Long> userIds, Integer status) {
        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<>();

        wrapper.set(User::getStatus, status)
               .set(User::getUpdateTime, new Date())
               .in(User::getId, userIds);

        return userMapper.update(null, wrapper) > 0;
    }

    // 复杂更新条件
    public boolean batchUpdateByCondition(Integer oldStatus, Integer newStatus, Long deptId) {
        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<>();

        wrapper.set(User::getStatus, newStatus)
               .set(User::getUpdateTime, new Date())
               .eq(User::getStatus, oldStatus)
               .eq(deptId != null, User::getDeptId, deptId);

        return userMapper.update(null, wrapper) > 0;
    }
}

// 3. 统计查询
public class UserStatisticsService {

    @Autowired
    private UserMapper userMapper;

    // 分组统计
    public List<Map<String, Object>> getUserStatistics() {
        QueryWrapper<User> wrapper = new QueryWrapper<>();

        wrapper.select("dept_id", "status", "COUNT(*) as count")
               .groupBy("dept_id", "status")
               .having("COUNT(*) > 0")
               .orderByAsc("dept_id");

        return userMapper.selectMaps(wrapper);
    }

    // 自定义SQL片段
    public List<User> findUsersWithCustomSQL(String condition) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        wrapper.apply("date_format(create_time,'%Y-%m-%d') = {0}", condition)
               .last("LIMIT 100"); // 最后拼接SQL

        return userMapper.selectList(wrapper);
    }
}
```

## 总结

本文档全面覆盖了MyBatis面试的核心知识点，包括：

### 🎯 核心内容
1. **MyBatis基础**：框架概述、与Hibernate对比、优势特点
2. **核心组件**：SqlSessionFactory、SqlSession、Executor等组件详解
3. **映射机制**：#{}与${}区别、结果集映射、参数绑定
4. **缓存机制**：一级缓存、二级缓存、缓存实现原理
5. **动态SQL**：if、choose、foreach等标签的使用
6. **插件机制**：拦截器原理、自定义插件开发
7. **性能优化**：SQL优化、缓存优化、连接池配置
8. **MyBatis Plus**：增强特性、条件构造器、代码生成

### 🔧 技术特色
- **14个核心问题**：覆盖MyBatis面试高频考点
- **15个Mermaid图表**：直观展示架构和流程
- **50+代码示例**：实用的开发案例和最佳实践
- **性能优化策略**：从SQL到架构的全方位优化

### 📊 实用价值
- **面试准备**：系统性的知识梳理和深度解析
- **技术提升**：从基础使用到高级特性的全面掌握
- **项目实战**：可直接应用的代码示例和配置
- **问题解决**：常见问题的解决方案和最佳实践

### 🌟 学习建议
1. **理论结合实践**：在实际项目中验证面试知识点
2. **深入源码**：理解MyBatis的底层实现原理
3. **性能调优**：关注SQL执行效率和系统性能
4. **新技术跟进**：掌握MyBatis Plus等增强工具

MyBatis作为Java持久层的主流框架，其灵活性和可控性使其在企业级开发中广泛应用。深入理解MyBatis的设计理念和实现机制，对于提升数据访问层的开发效率和系统性能具有重要意义。

通过本文档的学习，可以全面掌握MyBatis的核心技术，为面试和实际开发打下坚实基础。
```
