# 线程池面试题详解

## 1. 为什么要用线程池？⭐⭐⭐⭐

### 问题分析
这是线程池的基础概念题，考查对线程池核心价值的理解。

### 标准答案

**线程池的核心优势：**

1. **降低资源消耗**
   - 重复利用已创建的线程，避免频繁创建和销毁线程的开销
   - 减少内存分配和GC压力
   - 降低系统调用次数

2. **提高响应速度**
   - 任务到达时无需等待线程创建，立即执行
   - 预先创建好的线程池可以快速响应请求
   - 减少任务等待时间

3. **提高线程的可管理性**
   - 统一分配、调优和监控线程资源
   - 防止无限制创建线程导致系统崩溃
   - 提供丰富的监控指标和管理接口

4. **提供更多功能**
   - 支持定时执行、周期执行
   - 提供拒绝策略处理任务溢出
   - 支持任务优先级和分组管理

**对比直接创建线程：**
```java
// 不推荐：每次都创建新线程
new Thread(() -> {
    // 业务逻辑
}).start();

// 推荐：使用线程池
ExecutorService executor = Executors.newFixedThreadPool(10);
executor.submit(() -> {
    // 业务逻辑
});
```

## 2. 为什么不推荐使用内置线程池？⭐⭐⭐⭐⭐

### 问题分析
这是阿里巴巴开发手册中的重要规约，考查对内置线程池潜在风险的理解。

### 标准答案

**内置线程池的问题：**

1. **FixedThreadPool 和 SingleThreadExecutor**
   ```java
   // 问题代码
   ExecutorService executor = Executors.newFixedThreadPool(10);
   
   // 底层实现
   new ThreadPoolExecutor(nThreads, nThreads, 0L, TimeUnit.MILLISECONDS,
                         new LinkedBlockingQueue<Runnable>());
   ```
   - 使用无界队列 `LinkedBlockingQueue`（容量为 Integer.MAX_VALUE）
   - 任务堆积可能导致 OOM
   - 无法感知任务积压情况

2. **CachedThreadPool**
   ```java
   // 问题代码
   ExecutorService executor = Executors.newCachedThreadPool();
   
   // 底层实现
   new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS,
                         new SynchronousQueue<Runnable>());
   ```
   - 最大线程数为 Integer.MAX_VALUE
   - 高并发时可能创建大量线程导致 OOM
   - 线程创建无上限控制

3. **ScheduledThreadPool**
   ```java
   // 问题代码
   ScheduledExecutorService executor = Executors.newScheduledThreadPool(10);
   
   // 底层实现
   super(corePoolSize, Integer.MAX_VALUE, 0, NANOSECONDS,
         new DelayedWorkQueue());
   ```
   - 使用无界队列 `DelayedWorkQueue`
   - 定时任务堆积可能导致 OOM

**推荐做法：**
```java
// 手动创建线程池，明确参数
ThreadPoolExecutor executor = new ThreadPoolExecutor(
    10,                                    // 核心线程数
    20,                                    // 最大线程数
    60L,                                   // 空闲线程存活时间
    TimeUnit.SECONDS,                      // 时间单位
    new ArrayBlockingQueue<>(1000),        // 有界队列
    new ThreadFactoryBuilder()             // 自定义线程工厂
        .setNameFormat("business-pool-%d")
        .build(),
    new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
);
```

## 3. 线程池常见参数有哪些？如何解释？⭐⭐⭐⭐⭐

### 问题分析
这是线程池的核心知识点，考查对ThreadPoolExecutor构造参数的深度理解。

### 标准答案

**ThreadPoolExecutor 七大参数：**

```java
public ThreadPoolExecutor(int corePoolSize,
                         int maximumPoolSize,
                         long keepAliveTime,
                         TimeUnit unit,
                         BlockingQueue<Runnable> workQueue,
                         ThreadFactory threadFactory,
                         RejectedExecutionHandler handler)
```

**参数详解：**

1. **corePoolSize（核心线程数）**
   - 线程池中始终保持的线程数量
   - 即使线程空闲也不会被回收（除非设置allowCoreThreadTimeOut）
   - 新任务到达时，如果当前线程数 < corePoolSize，创建新线程

2. **maximumPoolSize（最大线程数）**
   - 线程池允许的最大线程数量
   - 当队列满了且当前线程数 < maximumPoolSize时，创建新线程
   - 必须 >= corePoolSize

3. **keepAliveTime（空闲线程存活时间）**
   - 非核心线程空闲时的最大存活时间
   - 超过此时间的空闲线程将被回收
   - 配合allowCoreThreadTimeOut可以回收核心线程

4. **unit（时间单位）**
   - keepAliveTime的时间单位
   - 常用：TimeUnit.SECONDS、TimeUnit.MILLISECONDS

5. **workQueue（任务队列）**
   - 存储等待执行任务的阻塞队列
   - 常用队列类型见下一题

6. **threadFactory（线程工厂）**
   - 创建新线程的工厂
   - 可以自定义线程名称、优先级、是否守护线程等

7. **handler（拒绝策略）**
   - 当线程池和队列都满时的处理策略
   - 四种内置策略见下一题

**参数配置示例：**
```java
ThreadPoolExecutor executor = new ThreadPoolExecutor(
    5,                                     // 核心线程数：5个
    10,                                    // 最大线程数：10个
    60L,                                   // 空闲时间：60秒
    TimeUnit.SECONDS,                      // 时间单位：秒
    new ArrayBlockingQueue<>(100),         // 队列容量：100
    r -> new Thread(r, "custom-thread"),   // 自定义线程工厂
    new ThreadPoolExecutor.AbortPolicy()   // 拒绝策略：抛异常
);
```

## 4. 线程池的拒绝策略有哪些？⭐⭐⭐⭐⭐

### 问题分析
考查对线程池拒绝策略的理解，以及在不同场景下的选择。

### 标准答案

**四种内置拒绝策略：**

| 策略 | 行为 | 适用场景 | 优缺点 |
|------|------|----------|--------|
| **AbortPolicy** | 抛出RejectedExecutionException异常 | 对任务丢失敏感的场景 | 优：能及时发现问题<br>缺：需要处理异常 |
| **CallerRunsPolicy** | 在调用者线程中执行被拒绝的任务 | 任务不能丢失且能容忍性能下降 | 优：不丢失任务<br>缺：影响调用者性能 |
| **DiscardPolicy** | 静默丢弃被拒绝的任务 | 任务可以丢失的场景 | 优：不影响系统运行<br>缺：任务丢失无感知 |
| **DiscardOldestPolicy** | 丢弃队列中最早的任务，然后重试 | 新任务比旧任务重要的场景 | 优：保证新任务执行<br>缺：旧任务丢失 |

**代码示例：**
```java
// 1. AbortPolicy（默认策略）
ThreadPoolExecutor executor1 = new ThreadPoolExecutor(
    1, 1, 0L, TimeUnit.MILLISECONDS,
    new ArrayBlockingQueue<>(1),
    new ThreadPoolExecutor.AbortPolicy()
);

try {
    executor1.submit(() -> Thread.sleep(1000));
    executor1.submit(() -> System.out.println("task2")); // 进入队列
    executor1.submit(() -> System.out.println("task3")); // 抛异常
} catch (RejectedExecutionException e) {
    System.out.println("任务被拒绝：" + e.getMessage());
}

// 2. CallerRunsPolicy
ThreadPoolExecutor executor2 = new ThreadPoolExecutor(
    1, 1, 0L, TimeUnit.MILLISECONDS,
    new ArrayBlockingQueue<>(1),
    new ThreadPoolExecutor.CallerRunsPolicy()
);

executor2.submit(() -> Thread.sleep(1000));
executor2.submit(() -> System.out.println("task2"));
executor2.submit(() -> System.out.println("task3")); // 在主线程执行

// 3. 自定义拒绝策略
ThreadPoolExecutor executor3 = new ThreadPoolExecutor(
    1, 1, 0L, TimeUnit.MILLISECONDS,
    new ArrayBlockingQueue<>(1),
    (r, executor) -> {
        // 记录日志
        System.out.println("任务被拒绝，当前队列大小：" + executor.getQueue().size());
        // 可以选择重试、存储到数据库等
    }
);
```

**选择建议：**
- **AbortPolicy**：适合对数据一致性要求高的场景
- **CallerRunsPolicy**：适合任务不能丢失但可以容忍性能下降的场景
- **DiscardPolicy**：适合任务可以丢失的场景（如日志记录）
- **DiscardOldestPolicy**：适合新任务优先级更高的场景

## 5. 线程池的核心线程会被回收吗？⭐⭐⭐

### 问题分析
考查对线程池生命周期管理的理解，特别是核心线程的回收机制。

### 标准答案

**默认情况下核心线程不会被回收：**

```java
ThreadPoolExecutor executor = new ThreadPoolExecutor(
    5, 10, 60L, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(100)
);

// 默认情况下，即使核心线程空闲超过keepAliveTime，也不会被回收
// 这是为了减少线程创建销毁的开销
```

**如何让核心线程也被回收：**

```java
// 方法1：设置allowCoreThreadTimeOut为true
executor.allowCoreThreadTimeOut(true);

// 方法2：在构造时设置（JDK 1.6+）
ThreadPoolExecutor executor2 = new ThreadPoolExecutor(
    5, 10, 60L, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(100),
    Executors.defaultThreadFactory(),
    new ThreadPoolExecutor.AbortPolicy()
);
executor2.allowCoreThreadTimeOut(true);
```

**核心线程回收的条件：**
1. 设置了 `allowCoreThreadTimeOut(true)`
2. 线程空闲时间超过 `keepAliveTime`
3. 当前线程数大于0（至少保留一个线程处理新任务）

**实际应用场景：**
```java
// 适合周期性使用的线程池
public class PeriodicTaskExecutor {
    private final ThreadPoolExecutor executor;

    public PeriodicTaskExecutor() {
        this.executor = new ThreadPoolExecutor(
            2, 5, 30L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50)
        );
        // 允许核心线程超时，节省资源
        this.executor.allowCoreThreadTimeOut(true);
    }
}
```

**注意事项：**
- 开启核心线程超时后，可能增加任务响应延迟
- 适合任务间隔较长、对响应时间不敏感的场景
- 需要权衡资源节省和性能损失

## 6. 线程池常用的阻塞队列有哪些？⭐⭐⭐

### 问题分析
考查对不同阻塞队列特性的理解，以及在线程池中的应用场景。

### 标准答案

**常用阻塞队列对比：**

| 队列类型 | 容量 | 特点 | 适用场景 |
|----------|------|------|----------|
| **ArrayBlockingQueue** | 有界 | 数组实现，FIFO，需要预设容量 | 内存可控的场景 |
| **LinkedBlockingQueue** | 可选有界/无界 | 链表实现，FIFO，默认无界 | 任务量不确定的场景 |
| **SynchronousQueue** | 0 | 不存储元素，直接传递 | 任务立即执行的场景 |
| **PriorityBlockingQueue** | 无界 | 优先级队列，支持排序 | 任务有优先级的场景 |
| **DelayQueue** | 无界 | 延迟队列，支持定时 | 定时任务场景 |

**详细说明：**

1. **ArrayBlockingQueue**
   ```java
   // 固定容量的有界队列
   BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(1000);
   ThreadPoolExecutor executor = new ThreadPoolExecutor(
       5, 10, 60L, TimeUnit.SECONDS, queue
   );
   ```
   - 优点：内存占用可控，防止OOM
   - 缺点：容量固定，可能限制吞吐量

2. **LinkedBlockingQueue**
   ```java
   // 默认无界，也可以设置容量
   BlockingQueue<Runnable> queue1 = new LinkedBlockingQueue<>(); // 无界
   BlockingQueue<Runnable> queue2 = new LinkedBlockingQueue<>(1000); // 有界
   ```
   - 优点：容量灵活，吞吐量高
   - 缺点：无界时可能导致OOM

3. **SynchronousQueue**
   ```java
   // 不存储元素的队列
   BlockingQueue<Runnable> queue = new SynchronousQueue<>();
   // CachedThreadPool使用此队列
   ```
   - 优点：任务直接传递，响应快
   - 缺点：需要足够的线程数

4. **PriorityBlockingQueue**
   ```java
   // 支持优先级的无界队列
   BlockingQueue<Runnable> queue = new PriorityBlockingQueue<>();
   ```
   - 优点：支持任务优先级
   - 缺点：无界，可能OOM

**选择建议：**
- **高并发场景**：ArrayBlockingQueue + 合理容量
- **任务量不确定**：LinkedBlockingQueue + 设置容量上限
- **要求快速响应**：SynchronousQueue + 足够线程数
- **任务有优先级**：PriorityBlockingQueue + 监控内存

## 7. 线程池处理任务的流程了解吗？⭐⭐⭐⭐⭐

### 问题分析
这是线程池的核心工作原理，考查对任务执行流程的深度理解。

### 标准答案

**线程池任务执行流程：**

```
提交任务 → 判断核心线程数 → 判断队列是否满 → 判断最大线程数 → 执行拒绝策略
```

**详细流程图：**
```
任务提交
    ↓
当前线程数 < corePoolSize？
    ↓ 是                    ↓ 否
创建核心线程执行        队列是否已满？
    ↓                    ↓ 否        ↓ 是
  执行任务            加入队列    当前线程数 < maximumPoolSize？
                        ↓           ↓ 是              ↓ 否
                    等待执行    创建非核心线程执行    执行拒绝策略
                                    ↓
                                执行任务
```

**源码分析：**
```java
public void execute(Runnable command) {
    if (command == null)
        throw new NullPointerException();

    int c = ctl.get();

    // 1. 如果当前线程数 < 核心线程数，创建新线程
    if (workerCountOf(c) < corePoolSize) {
        if (addWorker(command, true))
            return;
        c = ctl.get();
    }

    // 2. 如果线程池运行中且任务能加入队列
    if (isRunning(c) && workQueue.offer(command)) {
        int recheck = ctl.get();
        // 双重检查
        if (!isRunning(recheck) && remove(command))
            reject(command);
        else if (workerCountOf(recheck) == 0)
            addWorker(null, false);
    }
    // 3. 如果队列满了，尝试创建非核心线程
    else if (!addWorker(command, false))
        // 4. 创建失败，执行拒绝策略
        reject(command);
}
```

**实际示例：**
```java
ThreadPoolExecutor executor = new ThreadPoolExecutor(
    2,    // 核心线程数：2
    4,    // 最大线程数：4
    60L, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(2) // 队列容量：2
);

// 提交6个任务，观察执行流程
for (int i = 1; i <= 6; i++) {
    final int taskId = i;
    try {
        executor.execute(() -> {
            System.out.println("Task " + taskId + " executed by " +
                             Thread.currentThread().getName());
            try { Thread.sleep(2000); } catch (InterruptedException e) {}
        });
        System.out.println("Task " + taskId + " submitted");
    } catch (RejectedExecutionException e) {
        System.out.println("Task " + taskId + " rejected");
    }
}

// 执行结果分析：
// Task 1: 创建核心线程1执行
// Task 2: 创建核心线程2执行
// Task 3: 加入队列等待
// Task 4: 加入队列等待
// Task 5: 创建非核心线程3执行
// Task 6: 创建非核心线程4执行
// Task 7: 队列满，线程数达到最大值，执行拒绝策略
```

## 8. 线程池中线程异常后，销毁还是复用？⭐⭐⭐⭐

### 问题分析
考查对线程池异常处理机制的理解，这是实际开发中的重要问题。

### 标准答案

**关键结论：取决于任务提交方式**

1. **使用 execute() 提交任务**
   ```java
   executor.execute(() -> {
       throw new RuntimeException("任务异常");
   });
   ```
   - **未捕获异常**：线程终止，线程池创建新线程替代
   - **已捕获异常**：线程继续复用

2. **使用 submit() 提交任务**
   ```java
   Future<?> future = executor.submit(() -> {
       throw new RuntimeException("任务异常");
   });
   ```
   - 异常被封装在 Future 中，线程**继续复用**
   - 需要调用 `future.get()` 才能获取异常

**源码分析：**
```java
// ThreadPoolExecutor.runWorker() 方法
final void runWorker(Worker w) {
    Thread wt = Thread.currentThread();
    Runnable task = w.firstTask;
    w.firstTask = null;

    try {
        while (task != null || (task = getTask()) != null) {
            try {
                task.run(); // 执行任务
            } catch (RuntimeException x) {
                thrown = x; throw x;
            } catch (Error x) {
                thrown = x; throw x;
            } catch (Throwable x) {
                thrown = x; throw new Error(x);
            } finally {
                task = null;
            }
        }
    } finally {
        processWorkerExit(w, thrown); // 异常时销毁线程
    }
}
```

**实际测试：**
```java
public class ThreadPoolExceptionTest {
    public static void main(String[] args) throws Exception {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            2, 2, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>()
        );

        // 测试1：execute方式，未捕获异常
        System.out.println("=== execute方式测试 ===");
        executor.execute(() -> {
            System.out.println("Task1 in thread: " + Thread.currentThread().getName());
            throw new RuntimeException("Task1 异常");
        });

        Thread.sleep(100);

        executor.execute(() -> {
            System.out.println("Task2 in thread: " + Thread.currentThread().getName());
        });

        // 测试2：submit方式
        System.out.println("=== submit方式测试 ===");
        Future<?> future = executor.submit(() -> {
            System.out.println("Task3 in thread: " + Thread.currentThread().getName());
            throw new RuntimeException("Task3 异常");
        });

        try {
            future.get(); // 获取异常
        } catch (ExecutionException e) {
            System.out.println("捕获到异常: " + e.getCause().getMessage());
        }

        executor.submit(() -> {
            System.out.println("Task4 in thread: " + Thread.currentThread().getName());
        });

        executor.shutdown();
    }
}
```

**最佳实践：**
```java
// 1. 在任务内部处理异常
executor.execute(() -> {
    try {
        // 业务逻辑
        riskyOperation();
    } catch (Exception e) {
        log.error("任务执行异常", e);
        // 异常处理逻辑
    }
});

// 2. 使用submit + 异常处理
Future<?> future = executor.submit(() -> {
    // 业务逻辑
});

try {
    future.get(5, TimeUnit.SECONDS);
} catch (ExecutionException e) {
    log.error("任务执行异常", e.getCause());
} catch (TimeoutException e) {
    log.error("任务执行超时", e);
}

// 3. 自定义ThreadFactory设置异常处理器
ThreadFactory factory = r -> {
    Thread t = new Thread(r);
    t.setUncaughtExceptionHandler((thread, ex) -> {
        log.error("线程异常: " + thread.getName(), ex);
    });
    return t;
};
```

## 9. 如何设计一个能够根据任务的优先级来执行的线程池？⭐⭐⭐⭐

### 问题分析
考查对线程池扩展和自定义的能力，以及对优先级队列的理解。

### 标准答案

**核心思路：使用 PriorityBlockingQueue 作为任务队列**

**1. 定义优先级任务：**
```java
public class PriorityTask implements Runnable, Comparable<PriorityTask> {
    private final int priority;
    private final Runnable task;
    private final long createTime;

    public PriorityTask(int priority, Runnable task) {
        this.priority = priority;
        this.task = task;
        this.createTime = System.nanoTime();
    }

    @Override
    public void run() {
        task.run();
    }

    @Override
    public int compareTo(PriorityTask other) {
        // 优先级高的先执行（数字越小优先级越高）
        int result = Integer.compare(this.priority, other.priority);
        if (result == 0) {
            // 优先级相同时，按提交时间排序（FIFO）
            result = Long.compare(this.createTime, other.createTime);
        }
        return result;
    }

    // getter方法
    public int getPriority() { return priority; }
}
```

**2. 创建优先级线程池：**
```java
public class PriorityThreadPoolExecutor extends ThreadPoolExecutor {

    public PriorityThreadPoolExecutor(int corePoolSize, int maximumPoolSize,
                                    long keepAliveTime, TimeUnit unit) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit,
              new PriorityBlockingQueue<>());
    }

    // 重写submit方法，支持优先级
    public Future<?> submit(int priority, Runnable task) {
        PriorityTask priorityTask = new PriorityTask(priority, task);
        return super.submit(priorityTask);
    }

    public <T> Future<T> submit(int priority, Callable<T> task) {
        PriorityTask priorityTask = new PriorityTask(priority, () -> {
            try {
                return task.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return (Future<T>) super.submit(priorityTask);
    }
}
```

**3. 使用示例：**
```java
public class PriorityThreadPoolTest {
    public static void main(String[] args) throws InterruptedException {
        PriorityThreadPoolExecutor executor = new PriorityThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS
        );

        // 提交不同优先级的任务
        executor.submit(3, () -> System.out.println("低优先级任务"));
        executor.submit(1, () -> System.out.println("高优先级任务"));
        executor.submit(2, () -> System.out.println("中优先级任务"));
        executor.submit(1, () -> System.out.println("另一个高优先级任务"));

        // 输出顺序：高优先级任务 -> 另一个高优先级任务 -> 中优先级任务 -> 低优先级任务

        Thread.sleep(1000);
        executor.shutdown();
    }
}
```

**4. 增强版实现：**
```java
public class AdvancedPriorityThreadPool {
    private final PriorityThreadPoolExecutor executor;
    private final AtomicLong taskIdGenerator = new AtomicLong(0);

    public AdvancedPriorityThreadPool(int corePoolSize, int maximumPoolSize) {
        this.executor = new PriorityThreadPoolExecutor(
            corePoolSize, maximumPoolSize, 60L, TimeUnit.SECONDS
        );
    }

    // 支持任务分组和优先级
    public Future<?> submitTask(String group, int priority, Runnable task) {
        long taskId = taskIdGenerator.incrementAndGet();

        PriorityTask priorityTask = new PriorityTask(priority, () -> {
            System.out.println("执行任务[" + group + "-" + taskId + "], 优先级: " + priority);
            task.run();
        });

        return executor.submit(priorityTask);
    }

    // 获取队列状态
    public void printQueueStatus() {
        BlockingQueue<Runnable> queue = executor.getQueue();
        System.out.println("队列大小: " + queue.size());
        System.out.println("活跃线程数: " + executor.getActiveCount());
    }

    public void shutdown() {
        executor.shutdown();
    }
}
```

**5. 实际应用场景：**
```java
// 在Web应用中使用优先级线程池
@Service
public class TaskService {
    private final AdvancedPriorityThreadPool priorityPool;

    public TaskService() {
        this.priorityPool = new AdvancedPriorityThreadPool(5, 10);
    }

    // VIP用户任务 - 高优先级
    public void processVipTask(Runnable task) {
        priorityPool.submitTask("VIP", 1, task);
    }

    // 普通用户任务 - 中优先级
    public void processNormalTask(Runnable task) {
        priorityPool.submitTask("NORMAL", 5, task);
    }

    // 后台任务 - 低优先级
    public void processBackgroundTask(Runnable task) {
        priorityPool.submitTask("BACKGROUND", 10, task);
    }
}
```

**注意事项：**
- PriorityBlockingQueue 是无界队列，需要监控内存使用
- 优先级相同的任务按FIFO顺序执行
- 适合任务执行时间差异不大的场景
- 可以结合监控系统实时观察任务执行情况

## 10. 线程池的监控和调优？⭐⭐⭐⭐

### 问题分析
考查对线程池生产环境使用的理解，包括监控指标和性能调优。

### 标准答案

**1. 关键监控指标：**
```java
public class ThreadPoolMonitor {
    private final ThreadPoolExecutor executor;
    private final ScheduledExecutorService monitor;

    public ThreadPoolMonitor(ThreadPoolExecutor executor) {
        this.executor = executor;
        this.monitor = Executors.newScheduledThreadPool(1);
        startMonitoring();
    }

    private void startMonitoring() {
        monitor.scheduleAtFixedRate(() -> {
            System.out.println("=== 线程池监控信息 ===");
            System.out.println("核心线程数: " + executor.getCorePoolSize());
            System.out.println("最大线程数: " + executor.getMaximumPoolSize());
            System.out.println("当前线程数: " + executor.getPoolSize());
            System.out.println("活跃线程数: " + executor.getActiveCount());
            System.out.println("队列大小: " + executor.getQueue().size());
            System.out.println("已完成任务数: " + executor.getCompletedTaskCount());
            System.out.println("总任务数: " + executor.getTaskCount());

            // 计算线程池利用率
            double utilization = (double) executor.getActiveCount() / executor.getMaximumPoolSize();
            System.out.println("线程池利用率: " + String.format("%.2f%%", utilization * 100));

            // 队列使用率（如果是有界队列）
            if (executor.getQueue() instanceof ArrayBlockingQueue) {
                ArrayBlockingQueue<?> queue = (ArrayBlockingQueue<?>) executor.getQueue();
                int capacity = queue.size() + queue.remainingCapacity();
                double queueUtilization = (double) queue.size() / capacity;
                System.out.println("队列使用率: " + String.format("%.2f%%", queueUtilization * 100));
            }

            System.out.println("========================");
        }, 0, 5, TimeUnit.SECONDS);
    }
}
```

**2. 性能调优策略：**

**CPU密集型任务：**
```java
// CPU密集型：线程数 = CPU核心数 + 1
int cpuCount = Runtime.getRuntime().availableProcessors();
ThreadPoolExecutor cpuIntensivePool = new ThreadPoolExecutor(
    cpuCount + 1,           // 核心线程数
    cpuCount + 1,           // 最大线程数
    0L, TimeUnit.MILLISECONDS,
    new ArrayBlockingQueue<>(100)
);
```

**IO密集型任务：**
```java
// IO密集型：线程数 = CPU核心数 * 2
int cpuCount = Runtime.getRuntime().availableProcessors();
ThreadPoolExecutor ioIntensivePool = new ThreadPoolExecutor(
    cpuCount * 2,           // 核心线程数
    cpuCount * 2,           // 最大线程数
    60L, TimeUnit.SECONDS,
    new LinkedBlockingQueue<>(200)
);
```

**混合型任务：**
```java
// 混合型：根据实际情况调整
ThreadPoolExecutor mixedPool = new ThreadPoolExecutor(
    10,                     // 核心线程数
    20,                     // 最大线程数
    60L, TimeUnit.SECONDS,
    new ArrayBlockingQueue<>(500),
    new ThreadPoolExecutor.CallerRunsPolicy()
);
```

**3. 动态调整线程池：**
```java
public class DynamicThreadPool {
    private final ThreadPoolExecutor executor;
    private final AtomicInteger rejectCount = new AtomicInteger(0);

    public DynamicThreadPool() {
        this.executor = new ThreadPoolExecutor(
            5, 10, 60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(100),
            new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    rejectCount.incrementAndGet();
                    // 动态扩容
                    if (executor.getMaximumPoolSize() < 50) {
                        executor.setMaximumPoolSize(executor.getMaximumPoolSize() + 5);
                        executor.execute(r); // 重新提交任务
                    } else {
                        throw new RejectedExecutionException("任务被拒绝");
                    }
                }
            }
        );

        // 定期检查并调整
        startAutoTuning();
    }

    private void startAutoTuning() {
        ScheduledExecutorService tuner = Executors.newScheduledThreadPool(1);
        tuner.scheduleAtFixedRate(() -> {
            int activeCount = executor.getActiveCount();
            int corePoolSize = executor.getCorePoolSize();
            int queueSize = executor.getQueue().size();

            // 如果活跃线程数持续接近核心线程数，且队列有积压，增加核心线程数
            if (activeCount >= corePoolSize * 0.8 && queueSize > 10) {
                if (corePoolSize < 20) {
                    executor.setCorePoolSize(corePoolSize + 1);
                    System.out.println("增加核心线程数到: " + (corePoolSize + 1));
                }
            }

            // 如果活跃线程数长期较低，减少核心线程数
            if (activeCount < corePoolSize * 0.3 && queueSize == 0) {
                if (corePoolSize > 5) {
                    executor.setCorePoolSize(corePoolSize - 1);
                    System.out.println("减少核心线程数到: " + (corePoolSize - 1));
                }
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
}
```

**4. 最佳实践总结：**

```java
public class ThreadPoolBestPractices {

    // 1. 合理命名线程
    public static ThreadFactory createNamedThreadFactory(String namePrefix) {
        return new ThreadFactoryBuilder()
            .setNameFormat(namePrefix + "-%d")
            .setDaemon(false)
            .setPriority(Thread.NORM_PRIORITY)
            .setUncaughtExceptionHandler((t, e) -> {
                System.err.println("Thread " + t.getName() + " threw exception: " + e);
            })
            .build();
    }

    // 2. 优雅关闭线程池
    public static void shutdownGracefully(ExecutorService executor, long timeout, TimeUnit unit) {
        executor.shutdown(); // 停止接收新任务
        try {
            if (!executor.awaitTermination(timeout, unit)) {
                executor.shutdownNow(); // 强制关闭
                if (!executor.awaitTermination(timeout, unit)) {
                    System.err.println("线程池未能正常关闭");
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    // 3. 创建生产级线程池
    public static ThreadPoolExecutor createProductionThreadPool(String poolName) {
        return new ThreadPoolExecutor(
            10,                                    // 核心线程数
            20,                                    // 最大线程数
            60L, TimeUnit.SECONDS,                 // 空闲时间
            new ArrayBlockingQueue<>(1000),        // 有界队列
            createNamedThreadFactory(poolName),    // 命名线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }
}
```

## 总结

线程池是Java并发编程的核心工具，掌握以下要点：

1. **基础概念**：理解线程池的价值和内置线程池的问题
2. **核心参数**：熟练掌握7大参数的含义和配置
3. **工作原理**：深入理解任务执行流程和异常处理机制
4. **实际应用**：能够根据业务场景选择合适的队列和拒绝策略
5. **监控调优**：具备生产环境的监控和调优能力

记住核心原则：**合理配置、及时监控、优雅关闭**！
