# Java集合常见面试题详解

> 本文档涵盖Java集合框架的核心面试题，包括List、Set、Map、Queue等常用集合的底层实现、性能特点和使用场景。

## 目录
- [基础概念](#基础概念)
- [List集合](#list集合)
- [Queue队列](#queue队列)
- [HashMap详解](#hashmap详解)
- [Set集合](#set集合)
- [ConcurrentHashMap](#concurrenthashmap)

---

## 基础概念

### 1. List、Set、Map三者的区别？⭐⭐⭐⭐

#### 问题分析
这是Java集合框架的基础问题，考查对集合体系结构的整体理解。

#### 标准答案

**Java集合框架结构：**

```mermaid
graph TB
    subgraph one ["Collection接口"]
        A[Collection] --> B[List]
        A --> C[Set]
        A --> D[Queue]

        B --> B1["ArrayList<br/>有序、可重复<br/>支持索引访问"]
        B --> B2["LinkedList<br/>有序、可重复<br/>双向链表"]
        B --> B3["Vector<br/>有序、可重复<br/>线程安全"]

        C --> C1["HashSet<br/>无序、不重复<br/>基于HashMap"]
        C --> C2["LinkedHashSet<br/>插入有序、不重复<br/>基于LinkedHashMap"]
        C --> C3["TreeSet<br/>排序、不重复<br/>基于红黑树"]

        D --> D1["ArrayDeque<br/>双端队列<br/>数组实现"]
        D --> D2["PriorityQueue<br/>优先队列<br/>堆实现"]
    end

    subgraph two ["Map接口"]
        E[Map] --> E1["HashMap<br/>键值对、无序<br/>数组+链表+红黑树"]
        E --> E2["LinkedHashMap<br/>键值对、插入有序<br/>HashMap+双向链表"]
        E --> E3["TreeMap<br/>键值对、排序<br/>红黑树"]
        E --> E4["ConcurrentHashMap<br/>键值对、线程安全<br/>分段锁/CAS+synchronized"]
    end

    classDef list fill:#e3f2fd
    classDef set fill:#fff3e0
    classDef map fill:#e8f5e8

    class B1 list
    class C1 set
    class E1 map
```

#### 详细对比

| 特性 | List | Set | Map |
|------|------|-----|-----|
| **存储内容** | 单一元素 | 单一元素 | 键值对(Key-Value) |
| **元素重复** | 允许重复 | 不允许重复 | Key不重复，Value可重复 |
| **元素有序** | 有序(插入顺序) | 取决于实现类 | 取决于实现类 |
| **索引访问** | 支持索引访问 | 不支持索引 | 通过Key访问Value |
| **主要用途** | 存储有序的元素列表 | 存储不重复的元素集合 | 存储键值映射关系 |

#### 代码示例
```java
public class CollectionDemo {
    public static void main(String[] args) {
        // List：有序、可重复
        List<String> list = new ArrayList<>();
        list.add("apple");
        list.add("banana");
        list.add("apple");  // 允许重复
        System.out.println("List: " + list);  // [apple, banana, apple]
        System.out.println("通过索引访问: " + list.get(0));  // apple
        
        // Set：无序、不重复
        Set<String> set = new HashSet<>();
        set.add("apple");
        set.add("banana");
        set.add("apple");   // 重复元素会被忽略
        System.out.println("Set: " + set);   // [banana, apple] (顺序可能不同)
        
        // Map：键值对、Key不重复
        Map<String, Integer> map = new HashMap<>();
        map.put("apple", 5);
        map.put("banana", 3);
        map.put("apple", 8);  // Key重复，Value会被覆盖
        System.out.println("Map: " + map);   // {banana=3, apple=8}
        System.out.println("通过Key访问: " + map.get("apple"));  // 8
    }
}
```

### 2. List、Set、Map的常见实现类及底层数据结构？⭐⭐⭐⭐

#### 问题分析
考查对各种集合实现类的底层数据结构的掌握程度。

#### 标准答案

#### List实现类
```java
// 1. ArrayList - 动态数组
public class ArrayListDemo {
    /**
     * 底层数据结构：Object[] elementData
     * 特点：
     * - 支持随机访问，查询快 O(1)
     * - 插入删除慢 O(n)
     * - 线程不安全
     * - 默认初始容量10，扩容1.5倍
     */
    public void demonstrateArrayList() {
        List<String> arrayList = new ArrayList<>();
        arrayList.add("element1");
        arrayList.add("element2");
        
        // 随机访问
        String element = arrayList.get(0);  // O(1)
        
        // 中间插入需要移动元素
        arrayList.add(1, "inserted");      // O(n)
    }
}

// 2. LinkedList - 双向链表
public class LinkedListDemo {
    /**
     * 底层数据结构：双向链表 Node<E>
     * 特点：
     * - 插入删除快 O(1)
     * - 查询慢 O(n)
     * - 线程不安全
     * - 实现了Deque接口，可作为栈、队列使用
     */
    public void demonstrateLinkedList() {
        LinkedList<String> linkedList = new LinkedList<>();
        linkedList.add("element1");
        linkedList.addFirst("first");     // 头部插入 O(1)
        linkedList.addLast("last");       // 尾部插入 O(1)
        
        // 查询需要遍历
        String element = linkedList.get(1);  // O(n)
    }
}

// 3. Vector - 同步的动态数组
public class VectorDemo {
    /**
     * 底层数据结构：Object[] elementData
     * 特点：
     * - 线程安全（synchronized）
     * - 性能较差
     * - 默认扩容2倍
     * - 基本被ArrayList + Collections.synchronizedList()替代
     */
    public void demonstrateVector() {
        Vector<String> vector = new Vector<>();
        vector.add("element1");  // 线程安全的添加
    }
}
```

#### Set实现类
```java
// 1. HashSet - 基于HashMap
public class HashSetDemo {
    /**
     * 底层数据结构：HashMap<E, Object>
     * 特点：
     * - 无序、不重复
     * - 查询、插入、删除 O(1)
     * - 线程不安全
     * - 允许null值
     */
    public void demonstrateHashSet() {
        Set<String> hashSet = new HashSet<>();
        hashSet.add("apple");
        hashSet.add("banana");
        hashSet.add("apple");  // 重复元素被忽略
        
        System.out.println(hashSet.contains("apple"));  // O(1) 查询
    }
}

// 2. LinkedHashSet - 基于LinkedHashMap
public class LinkedHashSetDemo {
    /**
     * 底层数据结构：LinkedHashMap<E, Object>
     * 特点：
     * - 插入有序、不重复
     * - 维护插入顺序的双向链表
     * - 性能略低于HashSet
     */
    public void demonstrateLinkedHashSet() {
        Set<String> linkedHashSet = new LinkedHashSet<>();
        linkedHashSet.add("third");
        linkedHashSet.add("first");
        linkedHashSet.add("second");
        
        // 保持插入顺序：[third, first, second]
        System.out.println(linkedHashSet);
    }
}

// 3. TreeSet - 基于红黑树
public class TreeSetDemo {
    /**
     * 底层数据结构：红黑树（TreeMap<E, Object>）
     * 特点：
     * - 自然排序或自定义排序
     * - 查询、插入、删除 O(log n)
     * - 不允许null值
     * - 元素必须实现Comparable或提供Comparator
     */
    public void demonstrateTreeSet() {
        Set<Integer> treeSet = new TreeSet<>();
        treeSet.add(3);
        treeSet.add(1);
        treeSet.add(2);
        
        // 自动排序：[1, 2, 3]
        System.out.println(treeSet);
        
        // 自定义排序
        Set<String> customTreeSet = new TreeSet<>((a, b) -> b.compareTo(a));
        customTreeSet.add("apple");
        customTreeSet.add("banana");
        customTreeSet.add("cherry");
        
        // 降序：[cherry, banana, apple]
        System.out.println(customTreeSet);
    }
}
```

#### Map实现类
```java
// 1. HashMap - 数组+链表+红黑树
public class HashMapDemo {
    /**
     * 底层数据结构：
     * - JDK 1.7：数组 + 链表
     * - JDK 1.8+：数组 + 链表 + 红黑树
     *
     * 特点：
     * - 无序、Key不重复
     * - 查询、插入、删除平均 O(1)
     * - 线程不安全
     * - 允许null key和null value
     * - 默认初始容量16，负载因子0.75
     */
    public void demonstrateHashMap() {
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("apple", 5);
        hashMap.put("banana", 3);
        hashMap.put(null, 0);     // 允许null key
        hashMap.put("cherry", null);  // 允许null value

        System.out.println(hashMap.get("apple"));  // O(1) 查询
    }
}

// 2. LinkedHashMap - HashMap + 双向链表
public class LinkedHashMapDemo {
    /**
     * 底层数据结构：HashMap + 双向链表
     * 特点：
     * - 插入有序或访问有序
     * - 继承自HashMap
     * - 可实现LRU缓存
     */
    public void demonstrateLinkedHashMap() {
        // 插入有序
        Map<String, Integer> insertionOrder = new LinkedHashMap<>();
        insertionOrder.put("third", 3);
        insertionOrder.put("first", 1);
        insertionOrder.put("second", 2);
        // 保持插入顺序：{third=3, first=1, second=2}

        // 访问有序（LRU）
        Map<String, Integer> accessOrder = new LinkedHashMap<>(16, 0.75f, true);
        accessOrder.put("a", 1);
        accessOrder.put("b", 2);
        accessOrder.put("c", 3);
        accessOrder.get("a");  // 访问a，a移到最后
        // 访问顺序：{b=2, c=3, a=1}
    }
}

// 3. TreeMap - 红黑树
public class TreeMapDemo {
    /**
     * 底层数据结构：红黑树
     * 特点：
     * - 自然排序或自定义排序
     * - 查询、插入、删除 O(log n)
     * - 不允许null key，允许null value
     * - Key必须实现Comparable或提供Comparator
     */
    public void demonstrateTreeMap() {
        Map<Integer, String> treeMap = new TreeMap<>();
        treeMap.put(3, "three");
        treeMap.put(1, "one");
        treeMap.put(2, "two");

        // 自动按Key排序：{1=one, 2=two, 3=three}
        System.out.println(treeMap);

        // 范围查询
        System.out.println(treeMap.subMap(1, 3));  // {1=one, 2=two}
    }
}
```

### 3. 哪些集合是线程不安全的？如何解决？⭐⭐⭐⭐

#### 问题分析
考查对集合线程安全性的理解和解决方案的掌握。

#### 标准答案

**线程安全问题分析：**

```mermaid
flowchart TB
    subgraph one ["线程不安全的集合"]
        A[ArrayList] --> A1["数据丢失<br/>索引越界<br/>数据不一致"]
        B[HashMap] --> B1["死循环<br/>数据丢失<br/>数据覆盖"]
        C[HashSet] --> C1["基于HashMap<br/>同样不安全"]
        D[LinkedList] --> D1["链表结构破坏<br/>数据丢失"]
    end

    subgraph two ["解决方案"]
        E[同步包装器] --> E1["Collections.synchronizedXxx()"]
        F[并发集合] --> F1["ConcurrentHashMap<br/>CopyOnWriteArrayList<br/>ConcurrentLinkedQueue"]
        G[显式同步] --> G1["synchronized关键字<br/>ReentrantLock"]
        H[线程安全集合] --> H1["Vector<br/>Hashtable<br/>Stack"]
    end

    A1 -.-> E1
    B1 -.-> F1
    C1 -.-> G1
    D1 -.-> H1

    classDef unsafe fill:#ffcdd2,stroke:#f44336
    classDef safe fill:#c8e6c9,stroke:#4caf50
    classDef collection fill:#e3f2fd,stroke:#2196f3

    class A1 unsafe
    class B1 unsafe
    class C1 unsafe
    class D1 unsafe
    class E1 safe
    class F1 safe
    class G1 safe
    class H1 safe
```

#### 线程不安全示例
```java
public class ThreadUnsafeDemo {

    // ArrayList线程不安全示例
    public void demonstrateArrayListUnsafe() throws InterruptedException {
        List<Integer> list = new ArrayList<>();

        // 多线程并发添加元素
        Runnable task = () -> {
            for (int i = 0; i < 1000; i++) {
                list.add(i);
            }
        };

        Thread t1 = new Thread(task);
        Thread t2 = new Thread(task);

        t1.start();
        t2.start();
        t1.join();
        t2.join();

        // 期望大小2000，实际可能小于2000（数据丢失）
        System.out.println("ArrayList大小: " + list.size());

        // 可能抛出IndexOutOfBoundsException
        // 或者出现null元素
    }

    // HashMap线程不安全示例
    public void demonstrateHashMapUnsafe() throws InterruptedException {
        Map<Integer, String> map = new HashMap<>();

        Runnable task = () -> {
            for (int i = 0; i < 1000; i++) {
                map.put(i, "value" + i);
            }
        };

        Thread t1 = new Thread(task);
        Thread t2 = new Thread(task);

        t1.start();
        t2.start();
        t1.join();
        t2.join();

        // 可能出现：
        // 1. 数据丢失
        // 2. 死循环（JDK 1.7）
        // 3. 数据覆盖
        System.out.println("HashMap大小: " + map.size());
    }
}
```

#### 解决方案
```java
public class ThreadSafeSolutions {

    // 方案1：使用同步包装器
    public void useSynchronizedCollections() {
        List<String> syncList = Collections.synchronizedList(new ArrayList<>());
        Set<String> syncSet = Collections.synchronizedSet(new HashSet<>());
        Map<String, String> syncMap = Collections.synchronizedMap(new HashMap<>());

        // 注意：遍历时仍需要手动同步
        synchronized (syncList) {
            for (String item : syncList) {
                System.out.println(item);
            }
        }
    }

    // 方案2：使用并发集合
    public void useConcurrentCollections() {
        // ConcurrentHashMap：线程安全的HashMap
        Map<String, String> concurrentMap = new ConcurrentHashMap<>();

        // CopyOnWriteArrayList：读多写少场景
        List<String> copyOnWriteList = new CopyOnWriteArrayList<>();

        // ConcurrentLinkedQueue：线程安全的队列
        Queue<String> concurrentQueue = new ConcurrentLinkedQueue<>();

        // 这些集合内部已经处理了线程安全问题
        concurrentMap.put("key", "value");
        copyOnWriteList.add("item");
        concurrentQueue.offer("element");
    }

    // 方案3：使用传统线程安全集合
    public void useTraditionalSafeCollections() {
        Vector<String> vector = new Vector<>();        // 线程安全的List
        Hashtable<String, String> hashtable = new Hashtable<>();  // 线程安全的Map
        Stack<String> stack = new Stack<>();           // 线程安全的栈

        // 这些集合使用synchronized关键字保证线程安全
        // 但性能较差，不推荐使用
    }

    // 方案4：显式同步
    public void useExplicitSynchronization() {
        List<String> list = new ArrayList<>();

        // 使用synchronized关键字
        public synchronized void addToList(String item) {
            list.add(item);
        }

        // 使用ReentrantLock
        private final ReentrantLock lock = new ReentrantLock();

        public void addToListWithLock(String item) {
            lock.lock();
            try {
                list.add(item);
            } finally {
                lock.unlock();
            }
        }
    }
}
```

#### 性能对比
```java
public class PerformanceComparison {

    public void comparePerformance() {
        int iterations = 1000000;

        // ArrayList vs Vector
        long start = System.currentTimeMillis();
        List<Integer> arrayList = new ArrayList<>();
        for (int i = 0; i < iterations; i++) {
            arrayList.add(i);
        }
        long arrayListTime = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        List<Integer> vector = new Vector<>();
        for (int i = 0; i < iterations; i++) {
            vector.add(i);
        }
        long vectorTime = System.currentTimeMillis() - start;

        System.out.println("ArrayList耗时: " + arrayListTime + "ms");
        System.out.println("Vector耗时: " + vectorTime + "ms");
        System.out.println("Vector比ArrayList慢: " + (vectorTime / (double) arrayListTime) + "倍");

        // HashMap vs ConcurrentHashMap vs Hashtable
        // 类似的性能测试...
    }
}
```

---

## List集合

### 4. ArrayList和Vector的区别？⭐⭐⭐

#### 问题分析
这是经典的对比题，考查对ArrayList和Vector实现差异的理解。

#### 标准答案

#### 详细对比表

| 特性 | ArrayList | Vector |
|------|-----------|--------|
| **线程安全** | 线程不安全 | 线程安全(synchronized) |
| **性能** | 高 | 低(同步开销) |
| **扩容机制** | 1.5倍扩容 | 2倍扩容 |
| **初始容量** | 10 | 10 |
| **JDK版本** | JDK 1.2 | JDK 1.0 |
| **推荐使用** | 推荐 | 不推荐(已过时) |

#### 代码对比
```java
public class ArrayListVsVector {

    // ArrayList实现原理
    public void demonstrateArrayList() {
        List<String> arrayList = new ArrayList<>();

        // 扩容机制：newCapacity = oldCapacity + (oldCapacity >> 1)
        // 即：newCapacity = oldCapacity * 1.5
        for (int i = 0; i < 15; i++) {
            arrayList.add("item" + i);
            // 当i=10时触发扩容：10 -> 15
        }

        // 线程不安全示例
        // 多线程环境下可能出现数据丢失、索引越界等问题
    }

    // Vector实现原理
    public void demonstrateVector() {
        Vector<String> vector = new Vector<>();

        // 扩容机制：newCapacity = oldCapacity * 2
        for (int i = 0; i < 15; i++) {
            vector.add("item" + i);
            // 当i=10时触发扩容：10 -> 20
        }

        // 线程安全：所有方法都使用synchronized修饰
        // public synchronized boolean add(E e) { ... }
    }

    // 性能测试
    public void performanceTest() {
        int size = 1000000;

        // ArrayList性能测试
        long start = System.currentTimeMillis();
        List<Integer> arrayList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
        }
        long arrayListTime = System.currentTimeMillis() - start;

        // Vector性能测试
        start = System.currentTimeMillis();
        Vector<Integer> vector = new Vector<>();
        for (int i = 0; i < size; i++) {
            vector.add(i);
        }
        long vectorTime = System.currentTimeMillis() - start;

        System.out.println("ArrayList耗时: " + arrayListTime + "ms");
        System.out.println("Vector耗时: " + vectorTime + "ms");
        // Vector通常比ArrayList慢2-3倍
    }

    // 线程安全替代方案
    public void threadSafeAlternatives() {
        // 方案1：Collections.synchronizedList
        List<String> syncList = Collections.synchronizedList(new ArrayList<>());

        // 方案2：CopyOnWriteArrayList（读多写少场景）
        List<String> cowList = new CopyOnWriteArrayList<>();

        // 方案3：显式同步
        List<String> list = new ArrayList<>();
        synchronized (list) {
            list.add("item");
        }
    }
}
```

### 5. ArrayList与LinkedList的区别？⭐⭐⭐⭐

#### 问题分析
这是List接口两个主要实现类的对比，考查对不同数据结构特点的理解。

#### 标准答案

**ArrayList vs LinkedList结构对比：**

```mermaid
graph TB
    subgraph "ArrayList - 动态数组"
        A[Object[] elementData] --> A1[index 0: element1]
        A --> A2[index 1: element2]
        A --> A3[index 2: element3]
        A --> A4[index 3: element4]
        A --> A5[...]

        A6[特点:<br/>连续内存<br/>支持随机访问<br/>缓存友好]
    end

    subgraph "LinkedList - 双向链表"
        B[Node first] --> B1[prev: null<br/>item: element1<br/>next: →]
        B1 --> B2[prev: ←<br/>item: element2<br/>next: →]
        B2 --> B3[prev: ←<br/>item: element3<br/>next: →]
        B3 --> B4[prev: ←<br/>item: element4<br/>next: null]

        B5[特点:<br/>非连续内存<br/>顺序访问<br/>动态大小]
    end

    style A6 fill:#e3f2fd
    style B5 fill:#fff3e0
```

#### 详细对比

| 操作 | ArrayList | LinkedList | 说明 |
|------|-----------|------------|------|
| **随机访问** | O(1) | O(n) | ArrayList支持索引直接访问 |
| **头部插入** | O(n) | O(1) | ArrayList需要移动所有元素 |
| **尾部插入** | O(1)* | O(1) | ArrayList可能触发扩容 |
| **中间插入** | O(n) | O(n) | ArrayList移动元素，LinkedList需要遍历 |
| **删除操作** | O(n) | O(n) | 都需要先找到元素位置 |
| **内存占用** | 较少 | 较多 | LinkedList每个节点额外存储指针 |
| **缓存性能** | 好 | 差 | ArrayList内存连续，缓存命中率高 |

#### 代码示例
```java
public class ArrayListVsLinkedList {

    // 性能测试
    public void performanceComparison() {
        int size = 100000;

        List<Integer> arrayList = new ArrayList<>();
        LinkedList<Integer> linkedList = new LinkedList<>();

        // 1. 尾部添加测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
        }
        long arrayListAddTime = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        for (int i = 0; i < size; i++) {
            linkedList.add(i);
        }
        long linkedListAddTime = System.currentTimeMillis() - start;

        System.out.println("尾部添加 - ArrayList: " + arrayListAddTime + "ms");
        System.out.println("尾部添加 - LinkedList: " + linkedListAddTime + "ms");

        // 2. 随机访问测试
        Random random = new Random();
        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            int index = random.nextInt(size);
            arrayList.get(index);
        }
        long arrayListGetTime = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            int index = random.nextInt(size);
            linkedList.get(index);
        }
        long linkedListGetTime = System.currentTimeMillis() - start;

        System.out.println("随机访问 - ArrayList: " + arrayListGetTime + "ms");
        System.out.println("随机访问 - LinkedList: " + linkedListGetTime + "ms");

        // 3. 头部插入测试
        List<Integer> arrayList2 = new ArrayList<>();
        LinkedList<Integer> linkedList2 = new LinkedList<>();

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            arrayList2.add(0, i);  // 头部插入
        }
        long arrayListInsertTime = System.currentTimeMillis() - start;

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            linkedList2.addFirst(i);  // 头部插入
        }
        long linkedListInsertTime = System.currentTimeMillis() - start;

        System.out.println("头部插入 - ArrayList: " + arrayListInsertTime + "ms");
        System.out.println("头部插入 - LinkedList: " + linkedListInsertTime + "ms");
    }

    // 使用场景示例
    public void usageScenarios() {
        // ArrayList适用场景：频繁随机访问
        List<String> arrayList = new ArrayList<>();
        arrayList.add("item1");
        arrayList.add("item2");
        arrayList.add("item3");

        // 高效的随机访问
        String item = arrayList.get(1);  // O(1)

        // 高效的遍历
        for (int i = 0; i < arrayList.size(); i++) {
            System.out.println(arrayList.get(i));
        }

        // LinkedList适用场景：频繁插入删除
        LinkedList<String> linkedList = new LinkedList<>();

        // 高效的头尾操作
        linkedList.addFirst("first");    // O(1)
        linkedList.addLast("last");      // O(1)
        linkedList.removeFirst();        // O(1)
        linkedList.removeLast();         // O(1)

        // 作为栈使用
        linkedList.push("stack_item");   // 入栈
        String popped = linkedList.pop(); // 出栈

        // 作为队列使用
        linkedList.offer("queue_item");  // 入队
        String polled = linkedList.poll(); // 出队
    }

    // 内存占用分析
    public void memoryAnalysis() {
        // ArrayList内存结构
        // - Object[] elementData：存储元素的数组
        // - int size：当前元素个数
        // - 每个元素只需要一个引用的空间

        // LinkedList内存结构
        // - Node first：头节点引用
        // - Node last：尾节点引用
        // - int size：当前元素个数
        // - 每个Node包含：item引用 + prev引用 + next引用
        // - 内存开销约为ArrayList的3倍

        System.out.println("ArrayList内存开销：元素数量 × 引用大小");
        System.out.println("LinkedList内存开销：元素数量 × (引用大小 × 3 + 对象头)");
    }
}
```

### 6. ArrayList扩容机制？⭐⭐⭐

#### 问题分析
ArrayList的扩容机制是面试高频问题，考查对动态数组实现原理的理解。

#### 标准答案

**ArrayList扩容流程：**

```mermaid
graph TB
    A[添加元素] --> B{容量是否足够?}
    B -->|是| C[直接添加元素]
    B -->|否| D[触发扩容]

    D --> E[计算新容量<br/>newCapacity = oldCapacity + (oldCapacity >> 1)]
    E --> F{新容量是否超过最大值?}
    F -->|否| G[创建新数组<br/>Arrays.copyOf]
    F -->|是| H[使用最大容量<br/>Integer.MAX_VALUE - 8]

    G --> I[复制原数组元素到新数组]
    H --> I
    I --> J[更新elementData引用]
    J --> K[添加新元素]

    style D fill:#ffcdd2
    style E fill:#fff3e0
    style G fill:#c8e6c9
    style I fill:#e3f2fd
```

#### 扩容源码分析
```java
public class ArrayListGrowthMechanism {

    // 模拟ArrayList扩容过程
    public class SimpleArrayList<E> {
        private static final int DEFAULT_CAPACITY = 10;
        private static final int MAX_ARRAY_SIZE = Integer.MAX_VALUE - 8;

        private Object[] elementData;
        private int size;

        public SimpleArrayList() {
            this.elementData = new Object[DEFAULT_CAPACITY];
        }

        public boolean add(E e) {
            ensureCapacityInternal(size + 1);
            elementData[size++] = e;
            return true;
        }

        private void ensureCapacityInternal(int minCapacity) {
            if (minCapacity > elementData.length) {
                grow(minCapacity);
            }
        }

        // 核心扩容方法
        private void grow(int minCapacity) {
            int oldCapacity = elementData.length;

            // 新容量 = 旧容量 + 旧容量/2 = 旧容量 * 1.5
            int newCapacity = oldCapacity + (oldCapacity >> 1);

            // 如果新容量还是不够，直接使用所需的最小容量
            if (newCapacity < minCapacity) {
                newCapacity = minCapacity;
            }

            // 检查是否超过最大数组大小
            if (newCapacity > MAX_ARRAY_SIZE) {
                newCapacity = hugeCapacity(minCapacity);
            }

            // 创建新数组并复制元素
            elementData = Arrays.copyOf(elementData, newCapacity);

            System.out.println("扩容：" + oldCapacity + " -> " + newCapacity);
        }

        private int hugeCapacity(int minCapacity) {
            if (minCapacity < 0) {
                throw new OutOfMemoryError();
            }
            return (minCapacity > MAX_ARRAY_SIZE) ?
                Integer.MAX_VALUE : MAX_ARRAY_SIZE;
        }
    }

    // 扩容演示
    public void demonstrateGrowth() {
        SimpleArrayList<Integer> list = new SimpleArrayList<>();

        System.out.println("ArrayList扩容演示：");

        // 添加11个元素，观察扩容过程
        for (int i = 0; i < 16; i++) {
            list.add(i);
            if (i == 9) {
                System.out.println("第10个元素添加完成，即将触发扩容");
            }
        }

        // 扩容时机：
        // 初始容量：10
        // 第11个元素：10 -> 15 (10 + 10/2)
        // 第16个元素：15 -> 22 (15 + 15/2)
    }

    // 扩容性能分析
    public void performanceAnalysis() {
        int size = 1000000;

        // 测试1：不预设容量
        long start = System.currentTimeMillis();
        List<Integer> list1 = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            list1.add(i);
        }
        long time1 = System.currentTimeMillis() - start;

        // 测试2：预设容量
        start = System.currentTimeMillis();
        List<Integer> list2 = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            list2.add(i);
        }
        long time2 = System.currentTimeMillis() - start;

        System.out.println("不预设容量耗时: " + time1 + "ms");
        System.out.println("预设容量耗时: " + time2 + "ms");
        System.out.println("性能提升: " + (time1 / (double) time2) + "倍");

        // 结论：预设容量可以避免多次扩容，显著提升性能
    }

    // 扩容最佳实践
    public void bestPractices() {
        // ✅ 好的做法：预估容量
        List<String> list1 = new ArrayList<>(100);  // 预估需要100个元素

        // ✅ 好的做法：使用ensureCapacity
        List<String> list2 = new ArrayList<>();
        ((ArrayList<String>) list2).ensureCapacity(100);

        // ❌ 不好的做法：频繁扩容
        List<String> list3 = new ArrayList<>();  // 默认容量10
        for (int i = 0; i < 1000; i++) {
            list3.add("item" + i);  // 会触发多次扩容
        }

        // 扩容次数计算
        // 10 -> 15 -> 22 -> 33 -> 49 -> 73 -> 109 -> 163 -> 244 -> 366 -> 549 -> 823 -> 1234
        // 总共扩容12次，每次都需要复制数组
    }
}
```

---

## Queue队列

### 7. Queue与Deque的区别？⭐⭐⭐

#### 问题分析
考查对队列接口层次结构和功能差异的理解。

#### 标准答案

**Queue vs Deque接口对比：**

```mermaid
graph TB
    subgraph "Queue - 单端队列"
        A[Queue接口] --> A1[offer() - 入队]
        A --> A2[poll() - 出队]
        A --> A3[peek() - 查看队头]

        A4[FIFO原则<br/>先进先出<br/>只能一端进一端出]
    end

    subgraph "Deque - 双端队列"
        B[Deque接口] --> B1[addFirst/addLast<br/>offerFirst/offerLast]
        B --> B2[removeFirst/removeLast<br/>pollFirst/pollLast]
        B --> B3[getFirst/getLast<br/>peekFirst/peekLast]

        B4[双端操作<br/>可作为栈使用<br/>可作为队列使用]
    end

    subgraph "继承关系"
        C[Collection] --> D[Queue]
        D --> E[Deque]

        E --> F[ArrayDeque]
        E --> G[LinkedList]
        D --> H[PriorityQueue]
    end

    style A4 fill:#e3f2fd
    style B4 fill:#fff3e0
    style F fill:#c8e6c9
    style G fill:#c8e6c9
```

#### 接口方法对比
```java
public class QueueVsDeque {

    // Queue接口方法演示
    public void demonstrateQueue() {
        Queue<String> queue = new LinkedList<>();

        // 入队操作
        queue.offer("first");   // 推荐使用，返回boolean
        queue.add("second");    // 可能抛异常

        // 出队操作
        String polled = queue.poll();  // 推荐使用，返回null如果为空
        String removed = queue.remove(); // 可能抛异常

        // 查看队头
        String peeked = queue.peek();   // 推荐使用，返回null如果为空
        String element = queue.element(); // 可能抛异常

        System.out.println("Queue只能从一端进，另一端出");
    }

    // Deque接口方法演示
    public void demonstrateDeque() {
        Deque<String> deque = new ArrayDeque<>();

        // 双端入队
        deque.addFirst("first");    // 头部添加
        deque.addLast("last");      // 尾部添加
        deque.offerFirst("offer_first"); // 头部添加（推荐）
        deque.offerLast("offer_last");   // 尾部添加（推荐）

        // 双端出队
        String first = deque.removeFirst();  // 头部移除
        String last = deque.removeLast();    // 尾部移除
        String pollFirst = deque.pollFirst(); // 头部移除（推荐）
        String pollLast = deque.pollLast();   // 尾部移除（推荐）

        // 双端查看
        String peekFirst = deque.peekFirst(); // 查看头部
        String peekLast = deque.peekLast();   // 查看尾部

        System.out.println("Deque可以从两端进出");
    }

    // Deque作为栈使用
    public void dequeAsStack() {
        Deque<String> stack = new ArrayDeque<>();

        // 栈操作
        stack.push("bottom");    // 入栈 = addFirst()
        stack.push("middle");
        stack.push("top");

        String popped = stack.pop();  // 出栈 = removeFirst()
        String peeked = stack.peek(); // 查看栈顶 = peekFirst()

        System.out.println("Deque作为栈：LIFO（后进先出）");
        System.out.println("出栈元素: " + popped);  // top
        System.out.println("栈顶元素: " + peeked);  // middle
    }

    // Deque作为队列使用
    public void dequeAsQueue() {
        Deque<String> queue = new ArrayDeque<>();

        // 队列操作
        queue.offer("first");    // 入队 = offerLast()
        queue.offer("second");
        queue.offer("third");

        String polled = queue.poll();  // 出队 = pollFirst()
        String peeked = queue.peek();  // 查看队头 = peekFirst()

        System.out.println("Deque作为队列：FIFO（先进先出）");
        System.out.println("出队元素: " + polled);  // first
        System.out.println("队头元素: " + peeked);  // second
    }
}
```

### 8. ArrayDeque与LinkedList的区别？⭐⭐⭐

#### 问题分析
两者都实现了Deque接口，但底层数据结构不同，考查对性能差异的理解。

#### 标准答案

#### 详细对比

| 特性 | ArrayDeque | LinkedList |
|------|------------|------------|
| **底层结构** | 循环数组 | 双向链表 |
| **内存占用** | 较少 | 较多(额外指针) |
| **缓存性能** | 好(连续内存) | 差(非连续内存) |
| **随机访问** | 不支持 | 不支持 |
| **头尾操作** | O(1) | O(1) |
| **中间操作** | O(n) | O(n) |
| **null元素** | 不允许 | 允许 |
| **线程安全** | 否 | 否 |

#### 代码对比
```java
public class ArrayDequeVsLinkedList {

    // 性能测试
    public void performanceComparison() {
        int size = 1000000;

        // ArrayDeque测试
        Deque<Integer> arrayDeque = new ArrayDeque<>();
        long start = System.currentTimeMillis();

        for (int i = 0; i < size; i++) {
            arrayDeque.addLast(i);
        }
        for (int i = 0; i < size; i++) {
            arrayDeque.removeFirst();
        }
        long arrayDequeTime = System.currentTimeMillis() - start;

        // LinkedList测试
        Deque<Integer> linkedList = new LinkedList<>();
        start = System.currentTimeMillis();

        for (int i = 0; i < size; i++) {
            linkedList.addLast(i);
        }
        for (int i = 0; i < size; i++) {
            linkedList.removeFirst();
        }
        long linkedListTime = System.currentTimeMillis() - start;

        System.out.println("ArrayDeque耗时: " + arrayDequeTime + "ms");
        System.out.println("LinkedList耗时: " + linkedListTime + "ms");
        // ArrayDeque通常比LinkedList快2-3倍
    }

    // 使用场景对比
    public void usageScenarios() {
        // ArrayDeque适用场景
        Deque<String> arrayDeque = new ArrayDeque<>();

        // 1. 作为栈使用（推荐替代Stack）
        arrayDeque.push("item1");
        arrayDeque.push("item2");
        String popped = arrayDeque.pop();

        // 2. 作为队列使用（推荐替代LinkedList）
        arrayDeque.offer("queue_item1");
        arrayDeque.offer("queue_item2");
        String polled = arrayDeque.poll();

        // 3. 双端队列操作
        arrayDeque.addFirst("first");
        arrayDeque.addLast("last");

        // LinkedList适用场景
        LinkedList<String> linkedList = new LinkedList<>();

        // 1. 需要存储null元素
        linkedList.add(null);  // ArrayDeque不支持

        // 2. 需要List接口的功能
        linkedList.add(1, "inserted");  // 支持索引操作
        String element = linkedList.get(0);  // 支持随机访问（虽然效率低）

        // 3. 频繁的中间插入删除（虽然都是O(n)，但LinkedList常数较小）
    }
}
```

### 9. PriorityQueue有什么特点？⭐⭐⭐

#### 问题分析
PriorityQueue是基于堆实现的优先队列，考查对堆数据结构和优先级概念的理解。

#### 标准答案

**PriorityQueue特点：**

```mermaid
graph TB
    subgraph "PriorityQueue结构"
        A[PriorityQueue] --> B[基于二叉堆实现]
        B --> C[完全二叉树]
        C --> D[数组存储]

        D --> E[父节点: i]
        E --> F[左子节点: 2*i+1]
        E --> G[右子节点: 2*i+2]
    end

    subgraph "堆性质"
        H[小顶堆] --> H1[父节点 ≤ 子节点]
        I[大顶堆] --> I1[父节点 ≥ 子节点]

        H1 --> J[peek()返回最小值]
        I1 --> K[peek()返回最大值]
    end

    subgraph "操作复杂度"
        L[插入 offer()] --> L1[O(log n)]
        M[删除 poll()] --> M1[O(log n)]
        N[查看 peek()] --> N1[O(1)]
        O[构建堆] --> O1[O(n)]
    end

    style C fill:#e3f2fd
    style J fill:#c8e6c9
    style K fill:#fff3e0
```

#### 代码示例
```java
public class PriorityQueueDemo {

    // 基本使用
    public void basicUsage() {
        // 默认小顶堆（自然排序）
        PriorityQueue<Integer> minHeap = new PriorityQueue<>();
        minHeap.offer(5);
        minHeap.offer(2);
        minHeap.offer(8);
        minHeap.offer(1);

        System.out.println("小顶堆出队顺序：");
        while (!minHeap.isEmpty()) {
            System.out.print(minHeap.poll() + " ");  // 1 2 5 8
        }
        System.out.println();

        // 大顶堆（自定义比较器）
        PriorityQueue<Integer> maxHeap = new PriorityQueue<>((a, b) -> b - a);
        maxHeap.offer(5);
        maxHeap.offer(2);
        maxHeap.offer(8);
        maxHeap.offer(1);

        System.out.println("大顶堆出队顺序：");
        while (!maxHeap.isEmpty()) {
            System.out.print(maxHeap.poll() + " ");  // 8 5 2 1
        }
        System.out.println();
    }

    // 自定义对象优先队列
    public void customObjectPriority() {
        // 任务类
        class Task implements Comparable<Task> {
            String name;
            int priority;  // 数字越小优先级越高

            Task(String name, int priority) {
                this.name = name;
                this.priority = priority;
            }

            @Override
            public int compareTo(Task other) {
                return Integer.compare(this.priority, other.priority);
            }

            @Override
            public String toString() {
                return name + "(优先级:" + priority + ")";
            }
        }

        PriorityQueue<Task> taskQueue = new PriorityQueue<>();
        taskQueue.offer(new Task("普通任务", 3));
        taskQueue.offer(new Task("紧急任务", 1));
        taskQueue.offer(new Task("重要任务", 2));
        taskQueue.offer(new Task("低优先级任务", 4));

        System.out.println("任务执行顺序：");
        while (!taskQueue.isEmpty()) {
            System.out.println(taskQueue.poll());
        }
        // 输出：紧急任务(1) -> 重要任务(2) -> 普通任务(3) -> 低优先级任务(4)
    }

    // 实际应用场景
    public void practicalApplications() {
        // 1. Top K问题
        int[] nums = {3, 2, 1, 5, 6, 4};
        int k = 2;
        PriorityQueue<Integer> minHeap = new PriorityQueue<>();

        for (int num : nums) {
            minHeap.offer(num);
            if (minHeap.size() > k) {
                minHeap.poll();  // 保持堆大小为k
            }
        }

        System.out.println("Top " + k + " 最大元素：" + minHeap);

        // 2. 合并K个有序链表（模拟）
        PriorityQueue<int[]> pq = new PriorityQueue<>((a, b) -> a[0] - b[0]);
        pq.offer(new int[]{1, 4, 5});
        pq.offer(new int[]{1, 3, 4});
        pq.offer(new int[]{2, 6});

        System.out.println("合并有序数组：");
        while (!pq.isEmpty()) {
            int[] arr = pq.poll();
            System.out.print(arr[0] + " ");
            // 实际应用中会继续处理数组的下一个元素
        }
        System.out.println();

        // 3. 任务调度
        PriorityQueue<String> scheduler = new PriorityQueue<>();
        scheduler.offer("定时任务A");
        scheduler.offer("定时任务B");
        scheduler.offer("定时任务C");

        // 按字典序执行任务
        while (!scheduler.isEmpty()) {
            System.out.println("执行: " + scheduler.poll());
        }
    }

    // 注意事项
    public void importantNotes() {
        PriorityQueue<Integer> pq = new PriorityQueue<>();
        pq.offer(3);
        pq.offer(1);
        pq.offer(2);

        // ❌ 错误：不要依赖toString()的顺序
        System.out.println("队列内容: " + pq);  // 不保证有序输出

        // ❌ 错误：不要使用迭代器期望有序
        for (Integer num : pq) {
            System.out.print(num + " ");  // 不保证有序
        }
        System.out.println();

        // ✅ 正确：只有poll()保证按优先级顺序
        while (!pq.isEmpty()) {
            System.out.print(pq.poll() + " ");  // 保证有序：1 2 3
        }
        System.out.println();

        // 重要特性：
        // 1. 不允许null元素
        // 2. 不是线程安全的
        // 3. 元素必须实现Comparable或提供Comparator
        // 4. 只有队头元素保证是最小/最大的
    }
}
```

---

## HashMap详解

### 10. HashMap查询、删除的时间复杂度？⭐⭐⭐⭐

#### 问题分析
HashMap的时间复杂度与其底层数据结构密切相关，需要分情况讨论。

#### 标准答案

**HashMap时间复杂度分析：**

```mermaid
graph TB
    subgraph "理想情况 - 无哈希冲突"
        A[计算hash值] --> A1[O(1)]
        A1 --> A2[定位数组索引]
        A2 --> A3[O(1)]
        A3 --> A4[直接访问]
        A4 --> A5[总复杂度: O(1)]
    end

    subgraph "哈希冲突 - 链表"
        B[计算hash值] --> B1[O(1)]
        B1 --> B2[定位数组索引]
        B2 --> B3[O(1)]
        B3 --> B4[遍历链表]
        B4 --> B5[O(n)]
        B5 --> B6[总复杂度: O(n)]
    end

    subgraph "哈希冲突 - 红黑树"
        C[计算hash值] --> C1[O(1)]
        C1 --> C2[定位数组索引]
        C2 --> C3[O(1)]
        C3 --> C4[红黑树查找]
        C4 --> C5[O(log n)]
        C5 --> C6[总复杂度: O(log n)]
    end

    style A5 fill:#c8e6c9
    style B6 fill:#ffcdd2
    style C6 fill:#fff3e0
```
```
