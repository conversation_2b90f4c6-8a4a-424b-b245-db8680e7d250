# Redis常见面试题详解

## 目录
- [Redis基础](#redis基础)
- [Redis数据结构](#redis数据结构)
- [Redis线程模型](#redis线程模型)
- [Redis内存管理](#redis内存管理)
- [Redis持久化机制](#redis持久化机制)
- [Redis事务](#redis事务)
- [Redis性能优化](#redis性能优化)
- [Redis生产问题](#redis生产问题)
- [Redis集群](#redis集群)

---

## Redis基础

### 1. Redis有什么作用？为什么要用Redis/为什么要用缓存？⭐⭐⭐

#### 问题分析
这是Redis面试的入门问题，考查对Redis基本概念和使用场景的理解。

#### 标准答案

**Redis核心作用：**

```mermaid
flowchart TB
    subgraph applications ["应用场景"]
        A["缓存加速<br/>Cache<br/>提高访问速度"]
        B["会话存储<br/>Session Store<br/>分布式会话管理"]
        C["分布式锁<br/>Distributed Lock<br/>并发控制"]
        D["消息队列<br/>Message Queue<br/>异步处理"]
        E["计数器<br/>Counter<br/>统计分析"]
        F["排行榜<br/>Leaderboard<br/>实时排序"]
    end
    
    subgraph advantages ["核心优势"]
        G["高性能<br/>内存存储<br/>微秒级响应"]
        H["高并发<br/>单机10万+QPS<br/>支持集群"]
        I["数据结构丰富<br/>String/Hash/List<br/>Set/ZSet等"]
        J["持久化支持<br/>RDB/AOF<br/>数据安全"]
    end
    
    A --> G
    B --> H
    C --> I
    D --> J
    E --> G
    F --> I
    
    classDef appStyle fill:#e3f2fd,stroke:#2196f3
    classDef advStyle fill:#c8e6c9,stroke:#4caf50
    
    class A,B,C,D,E,F appStyle
    class G,H,I,J advStyle
```

#### 为什么要用缓存

**1. 性能提升**：
- 内存访问速度比磁盘快1000倍以上
- 减少数据库查询压力
- 降低响应时间，提升用户体验

**2. 高并发支持**：
- Redis单机可支持10万+QPS
- 通过集群可以线性扩展性能
- 有效应对高并发场景

**3. 减少数据库负载**：
- 缓存热点数据，减少数据库访问
- 避免数据库成为性能瓶颈
- 提高系统整体吞吐量

### 2. Redis除了做缓存，还能做什么？⭐⭐⭐⭐

#### 问题分析
考查对Redis多样化应用场景的了解，展示Redis不仅仅是缓存工具。

#### 标准答案

**Redis多元化应用场景：**

**1. 分布式锁**：
```redis
# 获取锁
SET lock_key unique_value NX EX 30

# 释放锁（Lua脚本保证原子性）
if redis.call("get", KEYS[1]) == ARGV[1] then
    return redis.call("del", KEYS[1])
else
    return 0
end
```

**2. 限流控制**：
```redis
# 滑动窗口限流
ZREMRANGEBYSCORE rate_limit:user:123 0 (current_time - window_size)
ZCARD rate_limit:user:123
ZADD rate_limit:user:123 current_time unique_id
EXPIRE rate_limit:user:123 window_size
```

**3. 排行榜系统**：
```redis
# 添加分数
ZADD leaderboard 1000 "player1"
ZADD leaderboard 1500 "player2"

# 获取排行榜
ZREVRANGE leaderboard 0 9 WITHSCORES
```

**4. 地理位置服务**：
```redis
# 添加地理位置
GEOADD locations 116.397128 39.916527 "beijing"
GEOADD locations 121.473701 31.230416 "shanghai"

# 计算距离
GEODIST locations beijing shanghai km
```

**5. 实时统计**：
```redis
# 网站UV统计（HyperLogLog）
PFADD uv:20231201 user1 user2 user3
PFCOUNT uv:20231201

# 在线用户统计（Bitmap）
SETBIT online_users 123 1  # 用户123在线
BITCOUNT online_users      # 统计在线用户数
```

### 3. Redis可以做消息队列么？⭐⭐⭐

#### 问题分析
考查对Redis消息队列功能的理解，以及与专业消息队列的对比。

#### 标准答案

**Redis消息队列实现方式：**

```mermaid
flowchart TB
    subgraph methods ["Redis消息队列实现"]
        A["List结构<br/>LPUSH + BRPOP<br/>简单队列"]
        B["Pub/Sub<br/>发布订阅<br/>广播消息"]
        C["Stream结构<br/>Redis 5.0+<br/>专业消息队列"]
        D["Sorted Set<br/>延时队列<br/>定时消息"]
    end
    
    subgraph comparison ["与专业MQ对比"]
        E["优势<br/>部署简单<br/>学习成本低"]
        F["劣势<br/>功能有限<br/>可靠性不足"]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    A --> F
    B --> F
    C --> F
    D --> F
    
    classDef methodStyle fill:#e3f2fd,stroke:#2196f3
    classDef compStyle fill:#fff3e0,stroke:#ff9800
    
    class A,B,C,D methodStyle
    class E,F compStyle
```

#### Stream消息队列示例

**生产者**：
```redis
# 添加消息到Stream
XADD mystream * field1 value1 field2 value2
```

**消费者组**：
```redis
# 创建消费者组
XGROUP CREATE mystream mygroup $ MKSTREAM

# 消费消息
XREADGROUP GROUP mygroup consumer1 COUNT 1 STREAMS mystream >
```

#### 与专业消息队列对比

| 特性 | Redis Stream | RabbitMQ | Kafka |
|------|-------------|----------|-------|
| **部署复杂度** | 简单 | 中等 | 复杂 |
| **消息持久化** | 有限支持 | 完整支持 | 完整支持 |
| **消息确认** | 基础支持 | 完整支持 | 完整支持 |
| **集群支持** | Redis Cluster | 原生集群 | 原生集群 |
| **性能** | 极高 | 高 | 极高 |
| **适用场景** | 轻量级MQ | 企业级MQ | 大数据流处理 |

### 4. 分布式缓存常见的技术选型方案有哪些？⭐⭐⭐

#### 问题分析
考查对分布式缓存技术栈的了解，主要是Redis和Memcached的对比。

#### 标准答案

**主流分布式缓存对比：**

| 特性 | Redis | Memcached |
|------|-------|-----------|
| **数据类型** | 丰富（String/Hash/List/Set/ZSet等） | 仅支持String |
| **持久化** | 支持RDB和AOF | 不支持 |
| **集群** | 原生支持Redis Cluster | 客户端分片 |
| **事务** | 支持（有限） | 不支持 |
| **Lua脚本** | 支持 | 不支持 |
| **内存使用** | 相对较高 | 更节省内存 |
| **多线程** | 6.0+支持多线程IO | 原生多线程 |
| **适用场景** | 复杂数据结构、持久化需求 | 简单缓存、极致性能 |

#### 选择建议

**选择Redis的场景**：
- 需要复杂数据结构支持
- 需要数据持久化
- 需要分布式锁、消息队列等功能
- 对数据一致性有要求

**选择Memcached的场景**：
- 纯缓存场景，数据结构简单
- 对内存使用效率要求极高
- 已有成熟的Memcached运维体系

## Redis数据结构

### 5. Redis常用的数据结构有哪些？⭐⭐⭐⭐

#### 问题分析
这是Redis的核心知识点，需要了解各种数据结构的特点和使用场景。

#### 标准答案

**Redis数据结构全景图：**

```mermaid
flowchart TB
    subgraph basic ["5种基础数据类型"]
        A["String<br/>字符串<br/>最基础的数据类型"]
        B["Hash<br/>哈希表<br/>键值对集合"]
        C["List<br/>列表<br/>有序可重复"]
        D["Set<br/>集合<br/>无序不重复"]
        E["ZSet<br/>有序集合<br/>带分数排序"]
    end
    
    subgraph special ["3种特殊数据类型"]
        F["HyperLogLog<br/>基数统计<br/>UV统计"]
        G["Bitmap<br/>位图<br/>状态统计"]
        H["Geospatial<br/>地理位置<br/>LBS应用"]
    end
    
    subgraph advanced ["高级数据类型"]
        I["Stream<br/>流<br/>消息队列"]
        J["BloomFilter<br/>布隆过滤器<br/>去重判断"]
    end
    
    classDef basicStyle fill:#e3f2fd,stroke:#2196f3
    classDef specialStyle fill:#fff3e0,stroke:#ff9800
    classDef advancedStyle fill:#f3e5f5,stroke:#9c27b0
    
    class A,B,C,D,E basicStyle
    class F,G,H specialStyle
    class I,J advancedStyle
```

#### 各数据结构详解

**1. String（字符串）**：
```redis
# 基本操作
SET key value
GET key
INCR counter        # 原子递增
SETEX key 60 value  # 设置过期时间
```

**使用场景**：缓存、计数器、分布式锁、会话存储

**2. Hash（哈希表）**：
```redis
# 基本操作
HSET user:1001 name "张三" age 25
HGET user:1001 name
HMGET user:1001 name age
HINCRBY user:1001 age 1
```

**使用场景**：对象存储、购物车、用户信息

**3. List（列表）**：
```redis
# 基本操作
LPUSH queue task1 task2
RPOP queue
LRANGE queue 0 -1
BLPOP queue 0  # 阻塞弹出
```

**使用场景**：消息队列、最新列表、栈和队列

**4. Set（集合）**：
```redis
# 基本操作
SADD tags redis nosql database
SMEMBERS tags
SINTER set1 set2    # 交集
SUNION set1 set2    # 并集
```

**使用场景**：标签系统、好友关系、去重

**5. ZSet（有序集合）**：
```redis
# 基本操作
ZADD leaderboard 100 "player1" 200 "player2"
ZRANGE leaderboard 0 -1 WITHSCORES
ZRANK leaderboard "player1"
```

**使用场景**：排行榜、延时队列、范围查询

### 6. 使用Redis统计网站UV怎么做？⭐⭐⭐⭐⭐

#### 问题分析
考查HyperLogLog数据结构的理解和实际应用场景。

#### 标准答案

**HyperLogLog统计UV原理：**

```mermaid
flowchart TB
    subgraph traditional ["传统方案"]
        A["Set集合<br/>存储所有用户ID<br/>内存消耗大"]
        B["数据库<br/>去重查询<br/>性能差"]
    end
    
    subgraph hyperloglog ["HyperLogLog方案"]
        C["概率算法<br/>基数估算<br/>误差0.81%"]
        D["固定内存<br/>12KB存储<br/>支持2^64基数"]
        E["高性能<br/>O(1)复杂度<br/>适合大数据"]
    end
    
    subgraph implementation ["实现方案"]
        F["日UV统计<br/>PFADD uv:20231201 user_id"]
        G["月UV统计<br/>PFMERGE uv:202312 uv:20231201..."]
        H["实时查询<br/>PFCOUNT uv:20231201"]
    end
    
    A --> C
    B --> D
    C --> F
    D --> G
    E --> H
    
    classDef tradStyle fill:#ffcdd2,stroke:#f44336
    classDef hllStyle fill:#c8e6c9,stroke:#4caf50
    classDef implStyle fill:#e3f2fd,stroke:#2196f3
    
    class A,B tradStyle
    class C,D,E hllStyle
    class F,G,H implStyle
```

#### 实现代码

**日常UV统计**：
```redis
# 添加用户访问记录
PFADD uv:20231201 user1 user2 user3

# 获取当日UV
PFCOUNT uv:20231201

# 合并多天数据
PFMERGE uv:week1 uv:20231201 uv:20231202 uv:20231203
```

**Java实现示例**：
```java
@Service
public class UVStatService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    // 记录用户访问
    public void recordVisit(String userId, String date) {
        String key = "uv:" + date;
        redisTemplate.opsForHyperLogLog().add(key, userId);
        // 设置过期时间
        redisTemplate.expire(key, Duration.ofDays(30));
    }
    
    // 获取UV数量
    public Long getUV(String date) {
        String key = "uv:" + date;
        return redisTemplate.opsForHyperLogLog().size(key);
    }
    
    // 获取周UV
    public Long getWeekUV(List<String> dates) {
        String[] keys = dates.stream()
            .map(date -> "uv:" + date)
            .toArray(String[]::new);
        
        String weekKey = "uv:week:" + System.currentTimeMillis();
        redisTemplate.opsForHyperLogLog().union(weekKey, keys);
        
        Long result = redisTemplate.opsForHyperLogLog().size(weekKey);
        redisTemplate.delete(weekKey); // 清理临时key
        
        return result;
    }
}
```

#### HyperLogLog优势

**1. 内存效率极高**：
- 固定使用12KB内存
- 可统计2^64个不同元素
- 比Set集合节省99%以上内存

**2. 性能优秀**：
- 添加和查询都是O(1)复杂度
- 支持高并发访问
- 适合实时统计场景

**3. 误差可控**：
- 标准误差率0.81%
- 对于UV统计精度完全够用
- 大数据量下误差更小

### 7. 使用Redis实现一个排行榜怎么做？⭐⭐⭐⭐⭐

#### 问题分析
考查ZSet（有序集合）的实际应用，这是Redis面试的高频问题。

#### 标准答案

**排行榜系统架构：**

```mermaid
flowchart TB
    subgraph operations ["排行榜操作"]
        A["更新分数<br/>ZADD leaderboard score member"]
        B["获取排名<br/>ZRANK/ZREVRANK member"]
        C["获取榜单<br/>ZRANGE/ZREVRANGE 0 9"]
        D["获取分数<br/>ZSCORE member"]
        E["范围查询<br/>ZRANGEBYSCORE min max"]
    end

    subgraph scenarios ["应用场景"]
        F["游戏排行榜<br/>积分排序"]
        G["热门文章<br/>点击量排序"]
        H["销售排行<br/>销量排序"]
        I["活跃用户<br/>活跃度排序"]
    end

    subgraph features ["核心特性"]
        J["自动排序<br/>按分数排列"]
        K["高效查询<br/>O(logN)复杂度"]
        L["范围操作<br/>支持分页"]
        M["原子操作<br/>并发安全"]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> F

    F --> J
    G --> K
    H --> L
    I --> M

    classDef opStyle fill:#e3f2fd,stroke:#2196f3
    classDef sceneStyle fill:#c8e6c9,stroke:#4caf50
    classDef featStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D,E opStyle
    class F,G,H,I sceneStyle
    class J,K,L,M featStyle
```

#### 实现示例

**基础排行榜操作**：
```redis
# 添加/更新用户分数
ZADD game_leaderboard 1000 "player1"
ZADD game_leaderboard 1500 "player2"
ZADD game_leaderboard 800 "player3"

# 增加分数
ZINCRBY game_leaderboard 100 "player1"

# 获取前10名
ZREVRANGE game_leaderboard 0 9 WITHSCORES

# 获取用户排名（从0开始）
ZREVRANK game_leaderboard "player1"

# 获取用户分数
ZSCORE game_leaderboard "player1"

# 获取指定分数范围的用户
ZRANGEBYSCORE game_leaderboard 1000 2000 WITHSCORES
```

**Java实现示例**：
```java
@Service
public class LeaderboardService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String LEADERBOARD_KEY = "game:leaderboard";

    // 更新用户分数
    public void updateScore(String userId, double score) {
        redisTemplate.opsForZSet().add(LEADERBOARD_KEY, userId, score);
    }

    // 增加用户分数
    public Double incrementScore(String userId, double delta) {
        return redisTemplate.opsForZSet().incrementScore(LEADERBOARD_KEY, userId, delta);
    }

    // 获取排行榜（前N名）
    public List<LeaderboardEntry> getTopN(int n) {
        Set<ZSetOperations.TypedTuple<String>> tuples =
            redisTemplate.opsForZSet().reverseRangeWithScores(LEADERBOARD_KEY, 0, n - 1);

        List<LeaderboardEntry> result = new ArrayList<>();
        int rank = 1;
        for (ZSetOperations.TypedTuple<String> tuple : tuples) {
            result.add(new LeaderboardEntry(rank++, tuple.getValue(), tuple.getScore()));
        }
        return result;
    }

    // 获取用户排名
    public Long getUserRank(String userId) {
        Long rank = redisTemplate.opsForZSet().reverseRank(LEADERBOARD_KEY, userId);
        return rank != null ? rank + 1 : null; // 转换为从1开始的排名
    }

    // 获取用户分数
    public Double getUserScore(String userId) {
        return redisTemplate.opsForZSet().score(LEADERBOARD_KEY, userId);
    }

    // 获取用户周围的排名
    public List<LeaderboardEntry> getUserNeighbors(String userId, int range) {
        Long rank = redisTemplate.opsForZSet().reverseRank(LEADERBOARD_KEY, userId);
        if (rank == null) return Collections.emptyList();

        long start = Math.max(0, rank - range);
        long end = rank + range;

        Set<ZSetOperations.TypedTuple<String>> tuples =
            redisTemplate.opsForZSet().reverseRangeWithScores(LEADERBOARD_KEY, start, end);

        List<LeaderboardEntry> result = new ArrayList<>();
        long currentRank = start + 1;
        for (ZSetOperations.TypedTuple<String> tuple : tuples) {
            result.add(new LeaderboardEntry(currentRank++, tuple.getValue(), tuple.getScore()));
        }
        return result;
    }
}

@Data
@AllArgsConstructor
public class LeaderboardEntry {
    private Long rank;
    private String userId;
    private Double score;
}
```

#### 高级功能实现

**1. 多维度排行榜**：
```redis
# 按不同维度创建排行榜
ZADD daily_leaderboard 1000 "player1"    # 日榜
ZADD weekly_leaderboard 5000 "player1"   # 周榜
ZADD monthly_leaderboard 20000 "player1" # 月榜
```

**2. 分页查询**：
```java
public PageResult<LeaderboardEntry> getLeaderboardPage(int page, int size) {
    long start = (page - 1) * size;
    long end = start + size - 1;

    Set<ZSetOperations.TypedTuple<String>> tuples =
        redisTemplate.opsForZSet().reverseRangeWithScores(LEADERBOARD_KEY, start, end);

    Long total = redisTemplate.opsForZSet().zCard(LEADERBOARD_KEY);

    List<LeaderboardEntry> entries = new ArrayList<>();
    long rank = start + 1;
    for (ZSetOperations.TypedTuple<String> tuple : tuples) {
        entries.add(new LeaderboardEntry(rank++, tuple.getValue(), tuple.getScore()));
    }

    return new PageResult<>(entries, total, page, size);
}
```

**3. 定时重置排行榜**：
```java
@Scheduled(cron = "0 0 0 * * MON") // 每周一重置
public void resetWeeklyLeaderboard() {
    // 备份上周数据
    String lastWeekKey = "weekly_leaderboard:" + getLastWeekDate();
    redisTemplate.rename("weekly_leaderboard", lastWeekKey);
    redisTemplate.expire(lastWeekKey, Duration.ofDays(30));

    // 创建新的周榜
    // 新的排行榜会在用户活动时自动创建
}
```

## Redis线程模型

### 8. Redis单线程模型了解吗？⭐⭐⭐⭐⭐

#### 问题分析
这是Redis架构的核心问题，需要深入理解Redis的线程模型和IO多路复用机制。

#### 标准答案

**Redis线程模型演进：**

```mermaid
flowchart TB
    subgraph redis6_before ["Redis 6.0之前"]
        A["单线程模型<br/>Single Thread<br/>主线程处理所有操作"]
        B["IO多路复用<br/>epoll/kqueue<br/>监听多个连接"]
        C["事件循环<br/>Event Loop<br/>处理网络事件"]
    end

    subgraph redis6_after ["Redis 6.0之后"]
        D["多线程IO<br/>Multi-threaded I/O<br/>网络IO多线程"]
        E["单线程执行<br/>Single-threaded Execution<br/>命令执行仍单线程"]
        F["线程池<br/>Thread Pool<br/>处理网络读写"]
    end

    subgraph advantages ["单线程优势"]
        G["无锁设计<br/>No Locks<br/>避免锁竞争"]
        H["原子操作<br/>Atomic Operations<br/>天然线程安全"]
        I["简单可靠<br/>Simple & Reliable<br/>易于调试维护"]
    end

    A --> D
    B --> F
    C --> E

    A --> G
    B --> H
    C --> I

    classDef oldStyle fill:#fff3e0,stroke:#ff9800
    classDef newStyle fill:#e3f2fd,stroke:#2196f3
    classDef advStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C oldStyle
    class D,E,F newStyle
    class G,H,I advStyle
```

#### IO多路复用原理

**传统多线程模型 vs Redis模型**：

```mermaid
flowchart TB
    subgraph traditional ["传统多线程模型"]
        A1["客户端1"] --> B1["线程1"]
        A2["客户端2"] --> B2["线程2"]
        A3["客户端3"] --> B3["线程3"]
        A4["客户端N"] --> B4["线程N"]

        B1 --> C1["阻塞IO<br/>等待数据"]
        B2 --> C2["阻塞IO<br/>等待数据"]
        B3 --> C3["阻塞IO<br/>等待数据"]
        B4 --> C4["阻塞IO<br/>等待数据"]
    end

    subgraph redis_model ["Redis IO多路复用"]
        D1["客户端1"]
        D2["客户端2"]
        D3["客户端3"]
        D4["客户端N"]

        D1 --> E["IO多路复用器<br/>epoll/select"]
        D2 --> E
        D3 --> E
        D4 --> E

        E --> F["单线程<br/>事件循环"]
        F --> G["处理就绪事件<br/>非阻塞处理"]
    end

    classDef clientStyle fill:#e3f2fd,stroke:#2196f3
    classDef threadStyle fill:#ffcdd2,stroke:#f44336
    classDef redisStyle fill:#c8e6c9,stroke:#4caf50

    class A1,A2,A3,A4,D1,D2,D3,D4 clientStyle
    class B1,B2,B3,B4,C1,C2,C3,C4 threadStyle
    class E,F,G redisStyle
```

#### 为什么Redis这么快

**1. 内存操作**：
- 所有数据存储在内存中
- 避免磁盘IO的性能瓶颈
- 内存访问速度比磁盘快1000倍以上

**2. 高效的数据结构**：
- 针对不同场景优化的数据结构
- 底层使用高效的C语言实现
- 时间复杂度大多为O(1)或O(logN)

**3. 单线程避免上下文切换**：
- 无需线程切换开销
- 避免锁竞争和同步开销
- CPU缓存友好

**4. IO多路复用**：
- 单线程处理多个连接
- 非阻塞IO，高效利用CPU
- 事件驱动模型

### 9. Redis6.0之前为什么不使用多线程？⭐⭐⭐

#### 问题分析
考查对Redis设计理念的理解，以及单线程模型的优势。

#### 标准答案

**Redis选择单线程的原因：**

**1. CPU不是瓶颈**：
- Redis的性能瓶颈通常在内存和网络IO
- CPU处理速度远超内存访问速度
- 单线程已能充分利用CPU性能

**2. 简化设计**：
- 避免复杂的锁机制
- 减少线程同步开销
- 降低系统复杂度

**3. 原子性保证**：
- 单线程天然保证操作原子性
- 无需考虑并发安全问题
- 简化事务实现

**4. 易于调试和维护**：
- 没有复杂的并发问题
- 更容易定位和解决问题
- 代码逻辑更清晰

### 10. Redis6.0之后为何引入了多线程？⭐⭐⭐⭐

#### 问题分析
考查对Redis6.0新特性的了解，以及多线程引入的背景和实现。

#### 标准答案

**Redis6.0多线程引入背景：**

**1. 网络IO成为瓶颈**：
- 随着网络带宽提升，网络IO处理成为新瓶颈
- 单线程在高并发下网络IO处理能力不足
- 需要提升网络IO处理效率

**2. 硬件发展**：
- 多核CPU普及，单线程无法充分利用多核优势
- 内存带宽和网络带宽大幅提升
- 需要更好地利用硬件资源

**Redis6.0多线程实现：**

```mermaid
flowchart TB
    subgraph main_thread ["主线程"]
        A["命令执行<br/>Command Execution<br/>保持单线程"]
        B["事件循环<br/>Event Loop<br/>协调IO线程"]
    end

    subgraph io_threads ["IO线程池"]
        C["IO线程1<br/>网络读写"]
        D["IO线程2<br/>网络读写"]
        E["IO线程3<br/>网络读写"]
        F["IO线程N<br/>网络读写"]
    end

    subgraph process ["处理流程"]
        G["1. 接收连接<br/>主线程监听"]
        H["2. 分发读任务<br/>分配给IO线程"]
        I["3. 并行读取<br/>IO线程处理"]
        J["4. 命令解析<br/>主线程执行"]
        K["5. 分发写任务<br/>分配给IO线程"]
        L["6. 并行写入<br/>IO线程处理"]
    end

    B --> C
    B --> D
    B --> E
    B --> F

    G --> H
    H --> I
    I --> J
    J --> K
    K --> L

    classDef mainStyle fill:#e3f2fd,stroke:#2196f3
    classDef ioStyle fill:#c8e6c9,stroke:#4caf50
    classDef processStyle fill:#fff3e0,stroke:#ff9800

    class A,B mainStyle
    class C,D,E,F ioStyle
    class G,H,I,J,K,L processStyle
```

#### 配置和使用

**启用多线程IO**：
```redis
# redis.conf配置
io-threads 4          # 设置IO线程数量
io-threads-do-reads yes  # 启用多线程读
```

**注意事项**：
- 命令执行仍然是单线程，保证数据一致性
- 只有网络IO使用多线程
- 适合网络IO密集型场景
- 对于CPU密集型操作提升有限

## Redis内存管理

### 11. Redis给缓存数据设置过期时间有啥用？⭐⭐⭐⭐

#### 问题分析
考查对Redis内存管理和过期机制的理解，这是缓存设计的基础问题。

#### 标准答案

**设置过期时间的作用：**

```mermaid
flowchart TB
    subgraph benefits ["过期时间的作用"]
        A["内存管理<br/>Memory Management<br/>避免内存溢出"]
        B["数据一致性<br/>Data Consistency<br/>及时更新缓存"]
        C["业务需求<br/>Business Logic<br/>会话超时等"]
        D["成本控制<br/>Cost Control<br/>减少存储成本"]
    end

    subgraph scenarios ["应用场景"]
        E["会话管理<br/>Session<br/>用户登录状态"]
        F["验证码<br/>Captcha<br/>短期有效"]
        G["热点数据<br/>Hot Data<br/>定期刷新"]
        H["临时缓存<br/>Temp Cache<br/>计算结果"]
    end

    subgraph commands ["设置方式"]
        I["EXPIRE key seconds<br/>设置秒级过期"]
        J["EXPIREAT key timestamp<br/>设置时间戳过期"]
        K["SETEX key seconds value<br/>设置值同时过期"]
        L["TTL key<br/>查看剩余时间"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef benefitStyle fill:#c8e6c9,stroke:#4caf50
    classDef sceneStyle fill:#e3f2fd,stroke:#2196f3
    classDef cmdStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D benefitStyle
    class E,F,G,H sceneStyle
    class I,J,K,L cmdStyle
```

#### 详细说明

**1. 内存管理**：
- 防止内存无限增长导致OOM
- 自动清理不再需要的数据
- 提高内存利用效率

**2. 数据一致性**：
- 强制缓存定期更新
- 避免脏数据长期存在
- 保证数据的时效性

**3. 业务场景需求**：
```redis
# 用户会话（30分钟过期）
SETEX session:user123 1800 "session_data"

# 短信验证码（5分钟过期）
SETEX sms:13800138000 300 "123456"

# 热点数据缓存（1小时过期）
SETEX hot:article:123 3600 "article_content"

# 分布式锁（10秒过期）
SET lock:order:123 "uuid" NX EX 10
```

### 12. Redis是如何判断数据是否过期的呢？⭐⭐⭐⭐

#### 问题分析
考查Redis内部过期机制的实现原理，需要了解过期字典的概念。

#### 标准答案

**Redis过期判断机制：**

```mermaid
flowchart TB
    subgraph structure ["Redis内部结构"]
        A["数据字典<br/>Dict<br/>存储键值对"]
        B["过期字典<br/>Expires Dict<br/>存储过期时间"]
        C["键空间<br/>Key Space<br/>逻辑数据库"]
    end

    subgraph process ["过期判断流程"]
        D["访问Key<br/>客户端请求"]
        E["检查过期字典<br/>查找过期时间"]
        F["比较时间<br/>当前时间 vs 过期时间"]
        G["判断结果<br/>过期/未过期"]
    end

    subgraph actions ["处理动作"]
        H["未过期<br/>返回数据"]
        I["已过期<br/>删除键值"]
        J["返回空<br/>Key不存在"]
    end

    A --> D
    B --> E
    C --> D

    D --> E
    E --> F
    F --> G

    G -->|未过期| H
    G -->|已过期| I
    I --> J

    classDef structStyle fill:#e3f2fd,stroke:#2196f3
    classDef processStyle fill:#fff3e0,stroke:#ff9800
    classDef actionStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C structStyle
    class D,E,F,G processStyle
    class H,I,J actionStyle
```

#### 实现原理

**1. 过期字典结构**：
```c
// Redis内部结构（简化）
typedef struct redisDb {
    dict *dict;     // 数据字典，存储键值对
    dict *expires;  // 过期字典，存储键的过期时间
    // ...
} redisDb;
```

**2. 过期时间存储**：
- 过期字典以键名为key，过期时间戳为value
- 过期时间使用Unix时间戳（毫秒级）
- 只有设置了过期时间的键才会在过期字典中

**3. 过期检查时机**：
- **惰性检查**：访问键时检查是否过期
- **定期检查**：后台定时扫描过期键
- **内存淘汰**：内存不足时主动清理

### 13. 过期的数据的删除策略了解么？⭐⭐⭐⭐

#### 问题分析
考查Redis过期数据清理策略，这是Redis内存管理的核心机制。

#### 标准答案

**Redis过期删除策略：**

```mermaid
flowchart TB
    subgraph strategies ["三种删除策略"]
        A["立即删除<br/>Immediate<br/>设置过期时立即删除"]
        B["惰性删除<br/>Lazy<br/>访问时检查删除"]
        C["定期删除<br/>Periodic<br/>定时扫描删除"]
    end

    subgraph comparison ["策略对比"]
        D["CPU友好度<br/>低 → 高 → 中"]
        E["内存友好度<br/>高 → 低 → 中"]
        F["实时性<br/>高 → 低 → 中"]
    end

    subgraph redis_choice ["Redis选择"]
        G["惰性删除<br/>+<br/>定期删除"]
        H["平衡CPU和内存<br/>兼顾性能和资源"]
    end

    A --> D
    B --> D
    C --> D

    A --> E
    B --> E
    C --> E

    A --> F
    B --> F
    C --> F

    B --> G
    C --> G
    G --> H

    classDef strategyStyle fill:#e3f2fd,stroke:#2196f3
    classDef compStyle fill:#fff3e0,stroke:#ff9800
    classDef choiceStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C strategyStyle
    class D,E,F compStyle
    class G,H choiceStyle
```

#### 详细实现

**1. 惰性删除（Lazy Expiration）**：
```c
// 伪代码：访问键时的过期检查
robj *lookupKeyRead(redisDb *db, robj *key) {
    robj *val;

    // 检查键是否过期
    expireIfNeeded(db, key);

    // 获取键值
    val = lookupKey(db, key);

    return val;
}

int expireIfNeeded(redisDb *db, robj *key) {
    // 获取过期时间
    mstime_t when = getExpire(db, key);

    if (when < 0) return 0; // 没有设置过期时间

    // 检查是否过期
    if (mstime() <= when) return 0; // 未过期

    // 删除过期键
    deleteKey(db, key);
    return 1;
}
```

**2. 定期删除（Active Expiration）**：
```c
// 伪代码：定期删除实现
void activeExpireCycle(int type) {
    static unsigned int current_db = 0;
    static int timelimit_exit = 0;

    int dbs_per_call = CRON_DBS_PER_CALL;
    int iteration = 0;

    // 遍历数据库
    for (int j = 0; j < dbs_per_call && timelimit_exit == 0; j++) {
        redisDb *db = server.db + (current_db % server.dbnum);
        current_db++;

        do {
            // 随机选择键进行过期检查
            for (int i = 0; i < ACTIVE_EXPIRE_CYCLE_LOOKUPS_PER_LOOP; i++) {
                dictEntry *de = dictGetRandomKey(db->expires);
                if (de == NULL) break;

                // 检查并删除过期键
                if (activeExpireCycleTryExpire(db, de, now)) {
                    expired++;
                }
            }

            iteration++;

        } while (expired > ACTIVE_EXPIRE_CYCLE_LOOKUPS_PER_LOOP/4 &&
                 iteration < 16);
    }
}
```

#### 配置参数

**定期删除相关配置**：
```redis
# redis.conf
hz 10  # 后台任务执行频率，影响定期删除频率

# 每次定期删除的时间限制
# 默认25ms，避免影响正常请求处理
```

### 14. Redis内存淘汰机制了解么？⭐⭐⭐⭐

#### 问题分析
考查Redis在内存不足时的处理策略，这是生产环境中的重要问题。

#### 标准答案

**Redis内存淘汰策略：**

```mermaid
flowchart TB
    subgraph policies ["8种淘汰策略"]
        A["noeviction<br/>不淘汰<br/>返回错误"]
        B["allkeys-lru<br/>所有键LRU<br/>最少使用"]
        C["allkeys-lfu<br/>所有键LFU<br/>最少频率"]
        D["allkeys-random<br/>所有键随机<br/>随机删除"]
        E["volatile-lru<br/>过期键LRU<br/>有过期时间的LRU"]
        F["volatile-lfu<br/>过期键LFU<br/>有过期时间的LFU"]
        G["volatile-random<br/>过期键随机<br/>有过期时间的随机"]
        H["volatile-ttl<br/>过期键TTL<br/>最早过期"]
    end

    subgraph categories ["策略分类"]
        I["不淘汰策略<br/>noeviction"]
        J["全局淘汰策略<br/>allkeys-*"]
        K["过期键淘汰策略<br/>volatile-*"]
    end

    subgraph algorithms ["淘汰算法"]
        L["LRU算法<br/>Least Recently Used<br/>最近最少使用"]
        M["LFU算法<br/>Least Frequently Used<br/>最少频率使用"]
        N["TTL算法<br/>Time To Live<br/>最早过期"]
        O["Random算法<br/>随机选择"]
    end

    A --> I
    B --> J
    C --> J
    D --> J
    E --> K
    F --> K
    G --> K
    H --> K

    B --> L
    E --> L
    C --> M
    F --> M
    H --> N
    D --> O
    G --> O

    classDef policyStyle fill:#e3f2fd,stroke:#2196f3
    classDef categoryStyle fill:#fff3e0,stroke:#ff9800
    classDef algoStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D,E,F,G,H policyStyle
    class I,J,K categoryStyle
    class L,M,N,O algoStyle
```

#### 策略详解

**1. 推荐策略选择**：

| 场景 | 推荐策略 | 原因 |
|------|----------|------|
| **通用缓存** | allkeys-lru | 符合缓存访问模式，热点数据保留 |
| **会话存储** | volatile-lru | 只淘汰有过期时间的会话数据 |
| **计数器** | allkeys-lfu | 频繁访问的计数器优先保留 |
| **临时数据** | volatile-ttl | 优先删除即将过期的数据 |

**2. 配置示例**：
```redis
# redis.conf
maxmemory 2gb                    # 设置最大内存
maxmemory-policy allkeys-lru     # 设置淘汰策略
maxmemory-samples 5              # LRU/LFU采样数量
```

**3. 监控命令**：
```redis
# 查看内存使用情况
INFO memory

# 查看当前配置
CONFIG GET maxmemory*

# 动态修改策略
CONFIG SET maxmemory-policy allkeys-lru
```

## Redis持久化机制

### 15. 怎么保证Redis挂掉之后再重启数据可以进行恢复？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis持久化机制的理解，这是Redis数据安全的核心问题。

#### 标准答案

**Redis持久化方案：**

```mermaid
flowchart TB
    subgraph persistence ["持久化方案"]
        A["RDB持久化<br/>Redis Database<br/>快照方式"]
        B["AOF持久化<br/>Append Only File<br/>日志方式"]
        C["混合持久化<br/>RDB + AOF<br/>Redis 4.0+"]
    end

    subgraph rdb_features ["RDB特性"]
        D["全量备份<br/>完整数据快照"]
        E["压缩存储<br/>文件体积小"]
        F["恢复速度快<br/>直接加载"]
        G["数据可能丢失<br/>间隔备份"]
    end

    subgraph aof_features ["AOF特性"]
        H["增量备份<br/>记录操作日志"]
        I["数据完整性好<br/>实时写入"]
        J["文件体积大<br/>记录所有操作"]
        K["恢复速度慢<br/>重放命令"]
    end

    A --> D
    A --> E
    A --> F
    A --> G

    B --> H
    B --> I
    B --> J
    B --> K

    classDef persistStyle fill:#e3f2fd,stroke:#2196f3
    classDef rdbStyle fill:#c8e6c9,stroke:#4caf50
    classDef aofStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C persistStyle
    class D,E,F,G rdbStyle
    class H,I,J,K aofStyle
```

### 16. 什么是RDB持久化？⭐⭐⭐⭐⭐

#### 问题分析
考查RDB持久化机制的原理、配置和使用场景。

#### 标准答案

**RDB持久化原理：**

```mermaid
flowchart TB
    subgraph trigger ["触发方式"]
        A["手动触发<br/>SAVE/BGSAVE"]
        B["自动触发<br/>配置条件满足"]
        C["关闭触发<br/>SHUTDOWN"]
    end

    subgraph process ["生成流程"]
        D["fork子进程<br/>Copy-on-Write"]
        E["遍历数据<br/>序列化写入"]
        F["原子替换<br/>重命名文件"]
        G["清理临时文件<br/>完成备份"]
    end

    subgraph advantages ["优势"]
        H["性能影响小<br/>子进程处理"]
        I["文件紧凑<br/>压缩存储"]
        J["恢复速度快<br/>直接加载"]
        K["适合备份<br/>定期快照"]
    end

    subgraph disadvantages ["劣势"]
        L["数据丢失风险<br/>间隔备份"]
        M["fork开销<br/>内存翻倍"]
        N["阻塞风险<br/>数据量大时"]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G

    D --> H
    E --> I
    F --> J
    G --> K

    D --> L
    E --> M
    F --> N

    classDef triggerStyle fill:#e3f2fd,stroke:#2196f3
    classDef processStyle fill:#fff3e0,stroke:#ff9800
    classDef advStyle fill:#c8e6c9,stroke:#4caf50
    classDef disadvStyle fill:#ffcdd2,stroke:#f44336

    class A,B,C triggerStyle
    class D,E,F,G processStyle
    class H,I,J,K advStyle
    class L,M,N disadvStyle
```

#### RDB配置

**自动触发配置**：
```redis
# redis.conf
save 900 1      # 900秒内至少1个key变化
save 300 10     # 300秒内至少10个key变化
save 60 10000   # 60秒内至少10000个key变化

# RDB文件配置
dbfilename dump.rdb           # RDB文件名
dir /var/lib/redis           # 文件保存目录
rdbcompression yes           # 启用压缩
rdbchecksum yes             # 启用校验和
```

**手动触发命令**：
```redis
# 阻塞式保存（生产环境慎用）
SAVE

# 非阻塞式保存（推荐）
BGSAVE

# 查看最后一次保存时间
LASTSAVE
```

### 17. 什么是AOF持久化？⭐⭐⭐⭐⭐

#### 问题分析
考查AOF持久化机制的原理、配置和重写机制。

#### 标准答案

**AOF持久化流程：**

```mermaid
flowchart TB
    subgraph write_process ["AOF写入流程"]
        A["执行命令<br/>Command Execution"]
        B["写入AOF缓冲<br/>AOF Buffer"]
        C["同步策略<br/>Sync Policy"]
        D["写入磁盘<br/>Disk Write"]
    end

    subgraph sync_policies ["同步策略"]
        E["always<br/>每个命令同步<br/>最安全，性能差"]
        F["everysec<br/>每秒同步<br/>平衡安全性和性能"]
        G["no<br/>操作系统控制<br/>性能好，安全性差"]
    end

    subgraph rewrite ["AOF重写"]
        H["触发条件<br/>文件大小阈值"]
        I["fork子进程<br/>后台重写"]
        J["生成新AOF<br/>压缩命令"]
        K["原子替换<br/>切换文件"]
    end

    A --> B
    B --> C
    C --> D

    C --> E
    C --> F
    C --> G

    H --> I
    I --> J
    J --> K

    classDef processStyle fill:#e3f2fd,stroke:#2196f3
    classDef policyStyle fill:#fff3e0,stroke:#ff9800
    classDef rewriteStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D processStyle
    class E,F,G policyStyle
    class H,I,J,K rewriteStyle
```

#### AOF配置

**基础配置**：
```redis
# redis.conf
appendonly yes                    # 启用AOF
appendfilename "appendonly.aof"   # AOF文件名
appendfsync everysec             # 同步策略

# AOF重写配置
auto-aof-rewrite-percentage 100  # 文件增长100%时重写
auto-aof-rewrite-min-size 64mb   # 最小64MB时才重写
```

**手动重写命令**：
```redis
# 手动触发AOF重写
BGREWRITEAOF

# 查看AOF状态
INFO persistence
```

### 18. Redis 4.0对于持久化机制做了什么优化？⭐⭐⭐⭐

#### 问题分析
考查Redis混合持久化的理解，这是Redis 4.0的重要特性。

#### 标准答案

**混合持久化机制：**

```mermaid
flowchart TB
    subgraph traditional ["传统方案"]
        A["纯RDB<br/>数据丢失风险大"]
        B["纯AOF<br/>恢复速度慢"]
    end

    subgraph hybrid ["混合持久化"]
        C["RDB快照<br/>作为AOF前缀"]
        D["AOF日志<br/>记录增量变化"]
        E["结合优势<br/>快速恢复+数据完整"]
    end

    subgraph process ["工作流程"]
        F["AOF重写时<br/>生成RDB快照"]
        G["追加AOF日志<br/>记录后续操作"]
        H["恢复时<br/>先加载RDB再重放AOF"]
    end

    A --> C
    B --> D
    C --> E
    D --> E

    F --> G
    G --> H

    classDef tradStyle fill:#ffcdd2,stroke:#f44336
    classDef hybridStyle fill:#c8e6c9,stroke:#4caf50
    classDef processStyle fill:#e3f2fd,stroke:#2196f3

    class A,B tradStyle
    class C,D,E hybridStyle
    class F,G,H processStyle
```

#### 配置和使用

**启用混合持久化**：
```redis
# redis.conf
aof-use-rdb-preamble yes  # 启用混合持久化（默认开启）
```

**文件结构**：
```
appendonly.aof 文件结构：
[RDB格式的数据] + [AOF格式的增量数据]
```

**优势对比**：

| 特性 | 纯RDB | 纯AOF | 混合持久化 |
|------|-------|-------|------------|
| **恢复速度** | 快 | 慢 | 快 |
| **数据完整性** | 差 | 好 | 好 |
| **文件大小** | 小 | 大 | 中等 |
| **CPU开销** | 低 | 高 | 中等 |

## Redis事务

### 19. 如何使用Redis事务？⭐⭐⭐⭐

#### 问题分析
考查Redis事务的基本使用和相关命令。

#### 标准答案

**Redis事务命令：**

```mermaid
flowchart TB
    subgraph commands ["事务命令"]
        A["MULTI<br/>开始事务<br/>进入事务状态"]
        B["命令入队<br/>Command Queue<br/>暂存命令"]
        C["EXEC<br/>执行事务<br/>批量执行命令"]
        D["DISCARD<br/>取消事务<br/>清空命令队列"]
        E["WATCH<br/>监视键<br/>乐观锁机制"]
    end

    subgraph process ["执行流程"]
        F["1. MULTI开始"]
        G["2. 命令入队"]
        H["3. EXEC执行"]
        I["4. 返回结果"]
    end

    subgraph features ["事务特性"]
        J["原子性<br/>批量执行"]
        K["隔离性<br/>串行执行"]
        L["一致性<br/>部分支持"]
        M["持久性<br/>依赖持久化"]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> F

    F --> J
    G --> K
    H --> L
    I --> M

    classDef cmdStyle fill:#e3f2fd,stroke:#2196f3
    classDef processStyle fill:#fff3e0,stroke:#ff9800
    classDef featureStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D,E cmdStyle
    class F,G,H,I processStyle
    class J,K,L,M featureStyle
```

#### 使用示例

**基本事务**：
```redis
# 开始事务
MULTI

# 命令入队
SET key1 "value1"
SET key2 "value2"
INCR counter

# 执行事务
EXEC
```

**带监视的事务**：
```redis
# 监视键
WATCH balance:user1 balance:user2

# 开始事务
MULTI

# 转账操作
DECRBY balance:user1 100
INCRBY balance:user2 100

# 执行事务（如果监视的键被修改，事务会失败）
EXEC
```

**Java实现示例**：
```java
@Service
public class TransactionService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 转账操作
    public boolean transfer(String fromUser, String toUser, int amount) {
        String fromKey = "balance:" + fromUser;
        String toKey = "balance:" + toUser;

        return redisTemplate.execute(new SessionCallback<Boolean>() {
            @Override
            public Boolean execute(RedisOperations operations) throws DataAccessException {
                // 监视账户余额
                operations.watch(fromKey);

                // 检查余额
                String balanceStr = (String) operations.opsForValue().get(fromKey);
                int balance = Integer.parseInt(balanceStr != null ? balanceStr : "0");

                if (balance < amount) {
                    operations.unwatch();
                    return false; // 余额不足
                }

                // 开始事务
                operations.multi();

                // 转账操作
                operations.opsForValue().decrement(fromKey, amount);
                operations.opsForValue().increment(toKey, amount);

                // 执行事务
                List<Object> results = operations.exec();

                return results != null && !results.isEmpty();
            }
        });
    }
}
```

### 20. Redis事务支持原子性吗？⭐⭐⭐⭐

#### 问题分析
考查对Redis事务ACID特性的理解，特别是原子性的限制。

#### 标准答案

**Redis事务原子性分析：**

```mermaid
flowchart TB
    subgraph scenarios ["不同场景"]
        A["语法错误<br/>Syntax Error<br/>整个事务不执行"]
        B["运行时错误<br/>Runtime Error<br/>部分命令执行"]
        C["正常执行<br/>Normal Execution<br/>所有命令执行"]
    end

    subgraph atomicity ["原子性表现"]
        D["完全原子<br/>语法错误时"]
        E["非原子<br/>运行时错误"]
        F["完全原子<br/>正常情况"]
    end

    subgraph comparison ["与传统数据库对比"]
        G["传统数据库<br/>支持回滚<br/>严格原子性"]
        H["Redis<br/>不支持回滚<br/>有限原子性"]
    end

    A --> D
    B --> E
    C --> F

    D --> G
    E --> H
    F --> G

    classDef sceneStyle fill:#e3f2fd,stroke:#2196f3
    classDef atomicStyle fill:#fff3e0,stroke:#ff9800
    classDef compStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C sceneStyle
    class D,E,F atomicStyle
    class G,H compStyle
```

#### 详细说明

**1. 语法错误场景**：
```redis
MULTI
SET key1 value1
INVALID_COMMAND  # 语法错误
SET key2 value2
EXEC
# 结果：整个事务不执行，保持原子性
```

**2. 运行时错误场景**：
```redis
MULTI
SET key1 value1
INCR key1        # 运行时错误（对字符串执行INCR）
SET key2 value2
EXEC
# 结果：SET key1和SET key2执行成功，INCR失败
# 不满足原子性！
```

**3. Redis不支持回滚的原因**：
- 简化设计，提高性能
- Redis命令失败通常是编程错误
- 生产环境中应该避免这类错误

### 21. Redis事务还有什么缺陷？⭐⭐⭐⭐

#### 问题分析
考查对Redis事务局限性的全面理解。

#### 标准答案

**Redis事务的主要缺陷：**

**1. 网络开销大**：
```redis
# 每个命令都需要网络交互
MULTI          # 网络请求1
SET key1 val1  # 网络请求2
SET key2 val2  # 网络请求3
INCR counter   # 网络请求4
EXEC           # 网络请求5
# 总共5次网络往返
```

**2. 不支持条件执行**：
```redis
# 无法在事务中根据中间结果做判断
MULTI
GET balance
# 无法在这里判断余额是否足够
DECRBY balance 100
EXEC
```

**3. 有限的原子性**：
- 运行时错误不会回滚
- 部分命令可能执行成功

**4. 无法嵌套**：
- 不支持事务嵌套
- 事务内不能再开启事务

### 22. 如何解决Redis事务的缺陷？⭐⭐⭐⭐

#### 问题分析
考查Redis事务替代方案的理解，主要是Lua脚本。

#### 标准答案

**解决方案对比：**

```mermaid
flowchart TB
    subgraph solutions ["解决方案"]
        A["Lua脚本<br/>Script Execution<br/>原子执行"]
        B["Redis Functions<br/>Redis 7.0+<br/>函数库"]
        C["Pipeline<br/>批量执行<br/>减少网络开销"]
    end

    subgraph lua_advantages ["Lua脚本优势"]
        D["真正原子性<br/>单个命令执行"]
        E["条件逻辑<br/>支持if/else"]
        F["减少网络开销<br/>一次网络请求"]
        G["服务器端执行<br/>减少数据传输"]
    end

    subgraph implementation ["实现方式"]
        H["EVAL命令<br/>直接执行脚本"]
        I["EVALSHA命令<br/>执行缓存脚本"]
        J["SCRIPT LOAD<br/>预加载脚本"]
    end

    A --> D
    A --> E
    A --> F
    A --> G

    A --> H
    A --> I
    A --> J

    classDef solutionStyle fill:#e3f2fd,stroke:#2196f3
    classDef advStyle fill:#c8e6c9,stroke:#4caf50
    classDef implStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C solutionStyle
    class D,E,F,G advStyle
    class H,I,J implStyle
```

#### Lua脚本示例

**转账脚本**：
```lua
-- transfer.lua
local from_key = KEYS[1]
local to_key = KEYS[2]
local amount = tonumber(ARGV[1])

-- 获取余额
local balance = tonumber(redis.call('GET', from_key) or 0)

-- 检查余额
if balance < amount then
    return {err = "Insufficient balance"}
end

-- 执行转账
redis.call('DECRBY', from_key, amount)
redis.call('INCRBY', to_key, amount)

return {ok = "Transfer successful"}
```

**Java调用示例**：
```java
@Service
public class LuaTransactionService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TRANSFER_SCRIPT =
        "local from_key = KEYS[1] " +
        "local to_key = KEYS[2] " +
        "local amount = tonumber(ARGV[1]) " +
        "local balance = tonumber(redis.call('GET', from_key) or 0) " +
        "if balance < amount then " +
        "    return {err = 'Insufficient balance'} " +
        "end " +
        "redis.call('DECRBY', from_key, amount) " +
        "redis.call('INCRBY', to_key, amount) " +
        "return {ok = 'Transfer successful'}";

    public boolean transfer(String fromUser, String toUser, int amount) {
        List<String> keys = Arrays.asList(
            "balance:" + fromUser,
            "balance:" + toUser
        );

        Object result = redisTemplate.execute(
            RedisScript.of(TRANSFER_SCRIPT, Object.class),
            keys,
            String.valueOf(amount)
        );

        return result != null && result.toString().contains("successful");
    }
}
```

## Redis性能优化

### 23. 什么是bigkey？有什么危害？⭐⭐⭐

#### 问题分析
考查对Redis性能问题的理解，bigkey是常见的性能杀手。

#### 标准答案

**BigKey定义和危害：**

```mermaid
flowchart TB
    subgraph definition ["BigKey定义"]
        A["String类型<br/>值大于10KB"]
        B["Hash类型<br/>元素超过5000个"]
        C["List类型<br/>元素超过5000个"]
        D["Set类型<br/>元素超过5000个"]
        E["ZSet类型<br/>元素超过5000个"]
    end

    subgraph dangers ["危害影响"]
        F["内存占用大<br/>影响其他数据"]
        G["网络传输慢<br/>阻塞其他请求"]
        H["序列化耗时<br/>CPU使用率高"]
        I["过期删除慢<br/>可能阻塞服务"]
        J["主从同步慢<br/>影响复制性能"]
    end

    subgraph scenarios ["常见场景"]
        K["大JSON对象<br/>用户详细信息"]
        L["大列表<br/>消息队列积压"]
        M["大集合<br/>好友关系"]
        N["大排行榜<br/>全服排名"]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    G --> L
    H --> M
    I --> N

    classDef defStyle fill:#e3f2fd,stroke:#2196f3
    classDef dangerStyle fill:#ffcdd2,stroke:#f44336
    classDef sceneStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C,D,E defStyle
    class F,G,H,I,J dangerStyle
    class K,L,M,N sceneStyle
```

### 24. 如何发现bigkey？⭐⭐⭐

#### 问题分析
考查bigkey的检测方法和工具使用。

#### 标准答案

**BigKey检测方法：**

**1. Redis自带工具**：
```bash
# 使用redis-cli扫描bigkey
redis-cli --bigkeys

# 指定数据库
redis-cli -n 1 --bigkeys

# 扫描特定模式
redis-cli --bigkeys -i 0.1  # 每100ms扫描一次，减少影响
```

**2. 内存分析**：
```redis
# 分析key的内存使用
MEMORY USAGE key_name

# 获取key的详细信息
DEBUG OBJECT key_name
```

**3. 自定义脚本检测**：
```python
import redis

def find_big_keys(redis_client, threshold=1024*1024):  # 1MB阈值
    big_keys = []

    for key in redis_client.scan_iter():
        try:
            memory_usage = redis_client.memory_usage(key)
            if memory_usage and memory_usage > threshold:
                key_type = redis_client.type(key).decode()
                big_keys.append({
                    'key': key.decode(),
                    'type': key_type,
                    'memory': memory_usage
                })
        except Exception as e:
            print(f"Error checking key {key}: {e}")

    return sorted(big_keys, key=lambda x: x['memory'], reverse=True)
```

**4. 监控告警**：
```java
@Component
public class BigKeyMonitor {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void monitorBigKeys() {
        // 扫描可能的bigkey模式
        Set<String> keys = redisTemplate.keys("user:*");

        for (String key : keys) {
            Long memoryUsage = redisTemplate.execute(
                (RedisCallback<Long>) connection ->
                    connection.memoryUsage(key.getBytes())
            );

            if (memoryUsage != null && memoryUsage > 1024 * 1024) { // 1MB
                // 发送告警
                alertBigKey(key, memoryUsage);
            }
        }
    }

    private void alertBigKey(String key, Long memoryUsage) {
        log.warn("BigKey detected: {} with memory usage: {} bytes",
                 key, memoryUsage);
        // 发送告警通知
    }
}
```

### 25. 如何避免大量key集中过期？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis过期策略优化，这是生产环境的重要问题。

#### 标准答案

**集中过期问题和解决方案：**

```mermaid
flowchart TB
    subgraph problem ["集中过期问题"]
        A["大量key同时过期<br/>定期删除压力大"]
        B["CPU使用率飙升<br/>影响正常请求"]
        C["响应时间增加<br/>用户体验下降"]
    end

    subgraph solutions ["解决方案"]
        D["随机过期时间<br/>Random Expiration"]
        E["分批过期<br/>Batch Expiration"]
        F["惰性删除<br/>Lazy Free"]
        G["业务层优化<br/>Business Logic"]
    end

    subgraph implementation ["实现方式"]
        H["过期时间加随机值<br/>TTL + Random"]
        I["使用UNLINK命令<br/>异步删除"]
        J["分散设置时间<br/>Stagger Setting"]
        K["监控和告警<br/>Monitor & Alert"]
    end

    A --> D
    B --> E
    C --> F

    D --> H
    E --> I
    F --> J
    G --> K

    classDef problemStyle fill:#ffcdd2,stroke:#f44336
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50
    classDef implStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C problemStyle
    class D,E,F,G solutionStyle
    class H,I,J,K implStyle
```

#### 解决方案实现

**1. 随机过期时间**：
```java
@Service
public class CacheService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 设置带随机偏移的过期时间
    public void setWithRandomExpire(String key, String value, int baseSeconds) {
        // 基础时间 + 随机偏移（±20%）
        int randomOffset = (int) (baseSeconds * 0.2 * Math.random());
        int finalExpire = baseSeconds + randomOffset;

        redisTemplate.opsForValue().set(key, value, Duration.ofSeconds(finalExpire));
    }

    // 批量设置缓存，分散过期时间
    public void setBatchWithScatteredExpire(Map<String, String> data, int baseSeconds) {
        data.forEach((key, value) -> {
            // 每个key的过期时间都不同
            int randomExpire = baseSeconds + (int) (Math.random() * 3600); // 1小时内随机
            redisTemplate.opsForValue().set(key, value, Duration.ofSeconds(randomExpire));
        });
    }
}
```

**2. 启用lazy-free**：
```redis
# redis.conf
lazyfree-lazy-eviction yes        # 淘汰时使用lazy free
lazyfree-lazy-expire yes          # 过期时使用lazy free
lazyfree-lazy-server-del yes      # DEL命令使用lazy free
```

**3. 使用UNLINK命令**：
```java
// 使用UNLINK代替DEL，异步删除大key
public void deleteBigKey(String key) {
    redisTemplate.execute((RedisCallback<Void>) connection -> {
        connection.unlink(key.getBytes());
        return null;
    });
}
```

**4. 监控过期事件**：
```java
@Component
public class ExpireEventMonitor {

    private final AtomicLong expireCount = new AtomicLong(0);

    @EventListener
    public void handleExpireEvent(RedisKeyExpiredEvent event) {
        long count = expireCount.incrementAndGet();

        // 每分钟重置计数器
        if (count % 1000 == 0) {
            log.info("Expire events in last period: {}", count);

            // 如果过期事件过多，发送告警
            if (count > 10000) {
                alertHighExpireRate(count);
            }
        }
    }
}
```

## Redis生产问题

### 26. 什么是缓存穿透？怎么解决？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis生产环境中的经典问题，缓存穿透是高频面试题。

#### 标准答案

**缓存穿透问题分析：**

```mermaid
flowchart TB
    subgraph problem ["缓存穿透问题"]
        A["恶意请求<br/>查询不存在的数据"]
        B["缓存未命中<br/>Redis中没有数据"]
        C["数据库查询<br/>数据库中也没有"]
        D["大量请求<br/>直接打到数据库"]
    end

    subgraph solutions ["解决方案"]
        E["缓存空值<br/>Cache Null Values"]
        F["布隆过滤器<br/>Bloom Filter"]
        G["参数校验<br/>Parameter Validation"]
        H["接口限流<br/>Rate Limiting"]
    end

    subgraph implementation ["实现方式"]
        I["设置空值缓存<br/>短期过期"]
        J["预加载布隆过滤器<br/>判断数据存在性"]
        K["业务层校验<br/>过滤无效请求"]
        L["限制请求频率<br/>防止恶意攻击"]
    end

    A --> B
    B --> C
    C --> D

    E --> I
    F --> J
    G --> K
    H --> L

    classDef problemStyle fill:#ffcdd2,stroke:#f44336
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50
    classDef implStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C,D problemStyle
    class E,F,G,H solutionStyle
    class I,J,K,L implStyle
```

#### 解决方案实现

**1. 缓存空值方案**：
```java
@Service
public class UserService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private UserRepository userRepository;

    private static final String NULL_VALUE = "NULL";
    private static final int NULL_CACHE_TTL = 300; // 5分钟

    public User getUserById(Long userId) {
        String key = "user:" + userId;
        String cached = redisTemplate.opsForValue().get(key);

        // 缓存命中
        if (cached != null) {
            if (NULL_VALUE.equals(cached)) {
                return null; // 空值缓存
            }
            return JSON.parseObject(cached, User.class);
        }

        // 查询数据库
        User user = userRepository.findById(userId);

        if (user != null) {
            // 缓存正常数据
            redisTemplate.opsForValue().set(key, JSON.toJSONString(user),
                Duration.ofHours(1));
        } else {
            // 缓存空值，防止缓存穿透
            redisTemplate.opsForValue().set(key, NULL_VALUE,
                Duration.ofSeconds(NULL_CACHE_TTL));
        }

        return user;
    }
}
```

**2. 布隆过滤器方案**：
```java
@Component
public class BloomFilterService {

    private BloomFilter<String> bloomFilter;

    @PostConstruct
    public void init() {
        // 创建布隆过滤器，预期100万数据，误判率0.01%
        bloomFilter = BloomFilter.create(
            Funnels.stringFunnel(Charset.defaultCharset()),
            1000000,
            0.0001
        );

        // 预加载所有存在的用户ID
        loadExistingUserIds();
    }

    private void loadExistingUserIds() {
        // 从数据库加载所有用户ID
        List<Long> userIds = userRepository.findAllUserIds();
        for (Long userId : userIds) {
            bloomFilter.put("user:" + userId);
        }
    }

    public boolean mightContain(String key) {
        return bloomFilter.mightContain(key);
    }

    public void addKey(String key) {
        bloomFilter.put(key);
    }
}

@Service
public class UserServiceWithBloom {

    @Autowired
    private BloomFilterService bloomFilterService;

    public User getUserById(Long userId) {
        String key = "user:" + userId;

        // 先检查布隆过滤器
        if (!bloomFilterService.mightContain(key)) {
            return null; // 数据肯定不存在
        }

        // 后续正常缓存逻辑
        return getUserFromCacheOrDB(userId);
    }
}
```

### 27. 什么是缓存雪崩？怎么解决？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis高可用性问题，缓存雪崩是生产环境的严重问题。

#### 标准答案

**缓存雪崩问题分析：**

```mermaid
flowchart TB
    subgraph causes ["雪崩原因"]
        A["Redis服务宕机<br/>整个缓存不可用"]
        B["大量key同时过期<br/>集中失效"]
        C["缓存预热不足<br/>冷启动问题"]
    end

    subgraph effects ["影响后果"]
        D["数据库压力激增<br/>可能导致宕机"]
        E["响应时间急剧增加<br/>用户体验差"]
        F["系统可用性下降<br/>服务不稳定"]
    end

    subgraph solutions ["解决方案"]
        G["Redis高可用<br/>主从+哨兵/集群"]
        H["过期时间随机化<br/>避免集中过期"]
        I["多级缓存<br/>本地+分布式"]
        J["熔断降级<br/>保护数据库"]
        K["缓存预热<br/>提前加载热点数据"]
    end

    A --> D
    B --> E
    C --> F

    G --> A
    H --> B
    I --> A
    J --> D
    K --> C

    classDef causeStyle fill:#ffcdd2,stroke:#f44336
    classDef effectStyle fill:#fff3e0,stroke:#ff9800
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C causeStyle
    class D,E,F effectStyle
    class G,H,I,J,K solutionStyle
```

#### 解决方案实现

**1. Redis高可用架构**：
```yaml
# Redis Sentinel配置
sentinel monitor mymaster 127.0.0.1 6379 2
sentinel down-after-milliseconds mymaster 30000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 180000
```

**2. 多级缓存实现**：
```java
@Service
public class MultiLevelCacheService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 本地缓存
    private final Cache<String, Object> localCache = Caffeine.newBuilder()
        .maximumSize(10000)
        .expireAfterWrite(Duration.ofMinutes(5))
        .build();

    public <T> T get(String key, Class<T> type, Supplier<T> dataLoader) {
        // 1. 先查本地缓存
        T result = (T) localCache.getIfPresent(key);
        if (result != null) {
            return result;
        }

        // 2. 查Redis缓存
        try {
            String cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                result = JSON.parseObject(cached, type);
                localCache.put(key, result);
                return result;
            }
        } catch (Exception e) {
            log.warn("Redis查询失败，降级到数据库: {}", e.getMessage());
        }

        // 3. 查数据库
        result = dataLoader.get();
        if (result != null) {
            // 更新缓存
            localCache.put(key, result);
            try {
                redisTemplate.opsForValue().set(key, JSON.toJSONString(result),
                    Duration.ofHours(1));
            } catch (Exception e) {
                log.warn("Redis写入失败: {}", e.getMessage());
            }
        }

        return result;
    }
}
```

**3. 熔断降级机制**：
```java
@Component
public class CacheCircuitBreaker {

    private final CircuitBreaker circuitBreaker;

    public CacheCircuitBreaker() {
        this.circuitBreaker = CircuitBreaker.ofDefaults("redis");
        circuitBreaker.getEventPublisher()
            .onStateTransition(event ->
                log.info("Circuit breaker state transition: {}", event));
    }

    public <T> T executeWithFallback(Supplier<T> cacheOperation,
                                   Supplier<T> fallbackOperation) {
        return circuitBreaker.executeSupplier(() -> {
            try {
                return cacheOperation.get();
            } catch (Exception e) {
                log.warn("Cache operation failed: {}", e.getMessage());
                throw e;
            }
        }).recover(throwable -> {
            log.info("Circuit breaker open, using fallback");
            return fallbackOperation.get();
        });
    }
}
```

### 28. 如何保证缓存和数据库数据的一致性？⭐⭐⭐⭐⭐

#### 问题分析
考查缓存一致性策略，这是分布式系统的核心问题。

#### 标准答案

**缓存一致性策略：**

```mermaid
flowchart TB
    subgraph strategies ["一致性策略"]
        A["Cache Aside<br/>旁路缓存<br/>应用管理缓存"]
        B["Read Through<br/>读穿透<br/>缓存代理读"]
        C["Write Through<br/>写穿透<br/>缓存代理写"]
        D["Write Behind<br/>写回<br/>异步写入"]
    end

    subgraph patterns ["更新模式"]
        E["先更新数据库<br/>再删除缓存"]
        F["先删除缓存<br/>再更新数据库"]
        G["双删策略<br/>删除-更新-删除"]
        H["延时双删<br/>异步延时删除"]
    end

    subgraph solutions ["解决方案"]
        I["分布式锁<br/>保证原子性"]
        J["消息队列<br/>异步处理"]
        K["Canal监听<br/>binlog同步"]
        L["版本号机制<br/>乐观锁"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> L

    classDef strategyStyle fill:#e3f2fd,stroke:#2196f3
    classDef patternStyle fill:#fff3e0,stroke:#ff9800
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D strategyStyle
    class E,F,G,H patternStyle
    class I,J,K,L solutionStyle
```

#### 实现方案

**1. Cache Aside模式（推荐）**：
```java
@Service
public class UserCacheService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private UserRepository userRepository;

    // 读操作：先查缓存，缓存未命中再查数据库
    public User getUser(Long userId) {
        String key = "user:" + userId;
        String cached = redisTemplate.opsForValue().get(key);

        if (cached != null) {
            return JSON.parseObject(cached, User.class);
        }

        User user = userRepository.findById(userId);
        if (user != null) {
            redisTemplate.opsForValue().set(key, JSON.toJSONString(user),
                Duration.ofHours(1));
        }

        return user;
    }

    // 写操作：先更新数据库，再删除缓存
    @Transactional
    public void updateUser(User user) {
        // 1. 更新数据库
        userRepository.save(user);

        // 2. 删除缓存
        String key = "user:" + user.getId();
        redisTemplate.delete(key);
    }
}
```

**2. 延时双删策略**：
```java
@Service
public class DelayedDoubleDeleteService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TaskExecutor taskExecutor;

    @Transactional
    public void updateUserWithDelayedDelete(User user) {
        String key = "user:" + user.getId();

        // 1. 先删除缓存
        redisTemplate.delete(key);

        // 2. 更新数据库
        userRepository.save(user);

        // 3. 延时删除缓存（异步）
        taskExecutor.execute(() -> {
            try {
                Thread.sleep(1000); // 延时1秒
                redisTemplate.delete(key);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
}
```

**3. 基于Canal的数据同步**：
```java
@Component
public class CanalCacheSync {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @EventListener
    public void handleDatabaseChange(CanalEntry.Entry entry) {
        if (entry.getEntryType() == CanalEntry.EntryType.ROWDATA) {
            CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());

            for (CanalEntry.RowData rowData : rowChange.getRowDatasList()) {
                if ("user".equals(entry.getHeader().getTableName())) {
                    handleUserTableChange(rowData, rowChange.getEventType());
                }
            }
        }
    }

    private void handleUserTableChange(CanalEntry.RowData rowData,
                                     CanalEntry.EventType eventType) {
        String userId = null;

        // 获取用户ID
        for (CanalEntry.Column column : rowData.getAfterColumnsList()) {
            if ("id".equals(column.getName())) {
                userId = column.getValue();
                break;
            }
        }

        if (userId != null) {
            String key = "user:" + userId;

            switch (eventType) {
                case UPDATE:
                case DELETE:
                    // 删除缓存
                    redisTemplate.delete(key);
                    break;
                default:
                    break;
            }
        }
    }
}
```

## Redis集群

### 29. 如何保证Redis服务高可用？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis高可用架构设计，这是生产环境的核心问题。

#### 标准答案

**Redis高可用方案：**

```mermaid
flowchart TB
    subgraph solutions ["高可用方案"]
        A["主从复制<br/>Master-Slave<br/>数据备份"]
        B["哨兵模式<br/>Sentinel<br/>自动故障转移"]
        C["集群模式<br/>Cluster<br/>分布式存储"]
    end

    subgraph sentinel ["哨兵架构"]
        D["哨兵节点<br/>监控主从状态"]
        E["主节点<br/>处理写请求"]
        F["从节点<br/>处理读请求"]
        G["故障转移<br/>自动切换主节点"]
    end

    subgraph cluster ["集群架构"]
        H["数据分片<br/>16384个槽位"]
        I["节点通信<br/>Gossip协议"]
        J["故障检测<br/>节点健康监控"]
        K["自动迁移<br/>槽位重新分配"]
    end

    A --> D
    B --> E
    C --> F

    B --> G
    C --> H
    C --> I
    C --> J
    C --> K

    classDef solutionStyle fill:#e3f2fd,stroke:#2196f3
    classDef sentinelStyle fill:#c8e6c9,stroke:#4caf50
    classDef clusterStyle fill:#fff3e0,stroke:#ff9800

    class A,B,C solutionStyle
    class D,E,F,G sentinelStyle
    class H,I,J,K clusterStyle
```

### 30. Sentinel（哨兵）有什么作用？⭐⭐⭐⭐

#### 问题分析
考查Redis Sentinel的工作原理和配置。

#### 标准答案

**Sentinel核心功能：**

```mermaid
flowchart TB
    subgraph functions ["哨兵功能"]
        A["监控<br/>Monitoring<br/>检查主从状态"]
        B["通知<br/>Notification<br/>故障事件通知"]
        C["故障转移<br/>Failover<br/>自动主从切换"]
        D["配置提供<br/>Configuration<br/>客户端服务发现"]
    end

    subgraph process ["故障转移流程"]
        E["检测主节点下线<br/>主观下线"]
        F["多哨兵确认<br/>客观下线"]
        G["选举领导者<br/>Leader Election"]
        H["执行故障转移<br/>Promote Slave"]
        I["更新配置<br/>Notify Clients"]
    end

    subgraph deployment ["部署架构"]
        J["奇数个哨兵<br/>避免脑裂"]
        K["分布式部署<br/>不同机器"]
        L["独立进程<br/>与Redis分离"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> F
    F --> G
    G --> H
    H --> I

    J --> K
    K --> L

    classDef funcStyle fill:#e3f2fd,stroke:#2196f3
    classDef processStyle fill:#fff3e0,stroke:#ff9800
    classDef deployStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D funcStyle
    class E,F,G,H,I processStyle
    class J,K,L deployStyle
```

#### Sentinel配置示例

**哨兵配置文件**：
```bash
# sentinel.conf
port 26379
sentinel monitor mymaster 127.0.0.1 6379 2
sentinel down-after-milliseconds mymaster 30000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 180000

# 可选配置
sentinel auth-pass mymaster yourpassword
sentinel notification-script mymaster /var/redis/notify.sh
```

**Java客户端配置**：
```java
@Configuration
public class RedisConfig {

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisSentinelConfiguration sentinelConfig =
            new RedisSentinelConfiguration()
                .master("mymaster")
                .sentinel("127.0.0.1", 26379)
                .sentinel("127.0.0.1", 26380)
                .sentinel("127.0.0.1", 26381);

        return new LettuceConnectionFactory(sentinelConfig);
    }

    @Bean
    public RedisTemplate<String, String> redisTemplate() {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory());
        template.setDefaultSerializer(new StringRedisSerializer());
        return template;
    }
}
```

### 31. Redis缓存的数据量太大怎么办？⭐⭐⭐⭐⭐

#### 问题分析
考查Redis Cluster的理解和数据分片策略。

#### 标准答案

**大数据量解决方案：**

```mermaid
flowchart TB
    subgraph problems ["大数据量问题"]
        A["内存不足<br/>单机内存限制"]
        B["性能瓶颈<br/>单点压力大"]
        C["可用性风险<br/>单点故障"]
    end

    subgraph solutions ["解决方案"]
        D["Redis Cluster<br/>分布式集群"]
        E["数据分片<br/>Hash Slot"]
        F["水平扩展<br/>增加节点"]
        G["读写分离<br/>主从架构"]
    end

    subgraph cluster_features ["集群特性"]
        H["16384个槽位<br/>数据分片"]
        I["无中心架构<br/>去中心化"]
        J["自动故障转移<br/>高可用"]
        K["在线扩容<br/>动态调整"]
    end

    A --> D
    B --> E
    C --> F

    D --> H
    E --> I
    F --> J
    G --> K

    classDef problemStyle fill:#ffcdd2,stroke:#f44336
    classDef solutionStyle fill:#c8e6c9,stroke:#4caf50
    classDef featureStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C problemStyle
    class D,E,F,G solutionStyle
    class H,I,J,K featureStyle
```

### 32. Redis Cluster虚拟槽分区有什么优点？⭐⭐⭐⭐

#### 问题分析
考查Redis Cluster的槽位机制和优势。

#### 标准答案

**虚拟槽分区优势：**

```mermaid
flowchart TB
    subgraph traditional ["传统分片方式"]
        A["一致性哈希<br/>Consistent Hash<br/>节点变化影响大"]
        B["范围分片<br/>Range Partition<br/>数据分布不均"]
        C["哈希取模<br/>Hash Mod<br/>扩容需要迁移"]
    end

    subgraph virtual_slot ["虚拟槽分区"]
        D["固定槽位数<br/>16384个槽"]
        E["槽位映射<br/>Slot Mapping<br/>解耦数据和节点"]
        F["灵活迁移<br/>按槽位迁移"]
    end

    subgraph advantages ["核心优势"]
        G["扩展性好<br/>易于扩容缩容"]
        H["负载均衡<br/>槽位平均分配"]
        I["故障恢复快<br/>槽位重新分配"]
        J["运维简单<br/>槽位管理"]
    end

    A --> D
    B --> E
    C --> F

    D --> G
    E --> H
    F --> I
    F --> J

    classDef tradStyle fill:#ffcdd2,stroke:#f44336
    classDef slotStyle fill:#c8e6c9,stroke:#4caf50
    classDef advStyle fill:#e3f2fd,stroke:#2196f3

    class A,B,C tradStyle
    class D,E,F slotStyle
    class G,H,I,J advStyle
```

#### 槽位分配算法

**CRC16算法**：
```java
public class RedisClusterSlot {

    private static final int SLOT_COUNT = 16384;

    // 计算key对应的槽位
    public static int calculateSlot(String key) {
        // 处理hash tag
        int start = key.indexOf('{');
        if (start != -1) {
            int end = key.indexOf('}', start + 1);
            if (end != -1 && end != start + 1) {
                key = key.substring(start + 1, end);
            }
        }

        return CRC16.crc16(key.getBytes()) % SLOT_COUNT;
    }

    // 槽位分配示例
    public static void distributeSlots(List<String> nodes) {
        int slotsPerNode = SLOT_COUNT / nodes.size();
        int remainder = SLOT_COUNT % nodes.size();

        int currentSlot = 0;
        for (int i = 0; i < nodes.size(); i++) {
            int nodeSlots = slotsPerNode + (i < remainder ? 1 : 0);
            System.out.printf("Node %s: slots %d-%d%n",
                nodes.get(i), currentSlot, currentSlot + nodeSlots - 1);
            currentSlot += nodeSlots;
        }
    }
}
```

### 33. Redis Cluster中的各个节点是如何实现数据一致性的？⭐⭐⭐⭐

#### 问题分析
考查Redis Cluster的通信机制和一致性保证。

#### 标准答案

**Gossip协议实现一致性：**

```mermaid
flowchart TB
    subgraph gossip ["Gossip协议"]
        A["节点发现<br/>Node Discovery<br/>自动发现新节点"]
        B["状态同步<br/>State Sync<br/>同步集群状态"]
        C["故障检测<br/>Failure Detection<br/>检测节点故障"]
        D["配置传播<br/>Config Propagation<br/>传播配置变更"]
    end

    subgraph message_types ["消息类型"]
        E["PING消息<br/>心跳检测"]
        F["PONG消息<br/>响应确认"]
        G["MEET消息<br/>节点加入"]
        H["FAIL消息<br/>故障通知"]
    end

    subgraph consistency ["一致性保证"]
        I["最终一致性<br/>Eventually Consistent"]
        J["分区容错<br/>Partition Tolerance"]
        K["可用性优先<br/>Availability First"]
    end

    A --> E
    B --> F
    C --> G
    D --> H

    E --> I
    F --> J
    G --> K
    H --> I

    classDef gossipStyle fill:#e3f2fd,stroke:#2196f3
    classDef msgStyle fill:#fff3e0,stroke:#ff9800
    classDef consistStyle fill:#c8e6c9,stroke:#4caf50

    class A,B,C,D gossipStyle
    class E,F,G,H msgStyle
    class I,J,K consistStyle
```

#### 集群管理命令

**集群操作命令**：
```bash
# 创建集群
redis-cli --cluster create 127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 \
  127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005 --cluster-replicas 1

# 查看集群状态
redis-cli -c -p 7000 cluster nodes
redis-cli -c -p 7000 cluster info

# 添加节点
redis-cli --cluster add-node 127.0.0.1:7006 127.0.0.1:7000

# 重新分片
redis-cli --cluster reshard 127.0.0.1:7000

# 删除节点
redis-cli --cluster del-node 127.0.0.1:7000 <node-id>
```

## 总结

本文档涵盖了Redis面试的核心知识点，包括：

1. **Redis基础**：作用、应用场景、技术选型
2. **数据结构**：5种基础类型、3种特殊类型、使用场景
3. **线程模型**：单线程模型、IO多路复用、6.0多线程
4. **内存管理**：过期机制、删除策略、淘汰策略
5. **持久化**：RDB、AOF、混合持久化
6. **事务机制**：事务命令、原子性、Lua脚本
7. **性能优化**：bigkey处理、过期优化、内存碎片
8. **生产问题**：缓存穿透、缓存雪崩、数据一致性
9. **集群架构**：主从复制、哨兵模式、集群分片

掌握这些知识点，能够应对大部分Redis相关的面试问题。在实际面试中，建议：

- **结合项目经验**：用具体案例说明Redis的使用
- **深入原理**：不仅知道怎么用，还要知道为什么
- **关注生产问题**：重点准备缓存穿透、雪崩等问题
- **了解最新特性**：关注Redis新版本的特性和改进

Redis作为现代互联网架构的核心组件，其重要性不言而喻。深入理解Redis的原理和最佳实践，对于提升系统性能和稳定性具有重要意义。
```
