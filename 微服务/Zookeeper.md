[https://mp.weixin.qq.com/s/W6QgmFTpXQ8EL-dVvLWsyg](https://mp.weixin.qq.com/s/W6QgmFTpXQ8EL-dVvLWsyg)
# 1. 是什么

```basic
ZooKeeper 是一个开源的分布式协调框架，它的定位是为分布式应用提供一致性服务，是整个大数据体系的管理员。ZooKeeper 会封装好复杂易出错的关键服务，将高效、稳定、易用的服务提供给用户使用。
ZooKeeper = 文件系统(树) + 监听通知机制。
```

# 2. 文件系统
## 2.1 数据结构

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611408389237-3e098e41-b9cd-4fdf-b2b9-9b3f53240284.png#align=left&display=inline&height=251&originHeight=251&originWidth=442&size=0&status=done&style=none&width=442)

```basic
树
1. 每个节点znode, 可以有子节点, 也可以存数据, 数据上限1M
```

## 2.2 节点类型

- 持久化  客户端与zk断开连接, 节点仍然存在不会被删
- 临时 客户端与zk断开连接, 会删除节点, 
- 顺序 给节点名称进行顺序编号


```
1. 持久化目录节点 PERSISTENT
2. 持久化顺序编号目录节点 PERSISTENT_SEQUENTIAL
3. 临时目录节点 EPHEMERAL
4. 临时顺序编号目录节点 EPHEMERAL_SEQUENTIAL
```

# 3. 监听通知机制 - Watcher 监听机制
给节点绑定监听事件, 可以监听**节点数据变化, 节点删除, 子节点状态变化**等
可实现分布式锁, 集群管理

## 3.1 Watcher特性

```basic
当数据发生变化的时候，Zookeeper  会产生一个 Watcher 事件，并且会发送到客户端。
但是客户端只会收到一次通知。
如果后续这个节点再次发生变化，那么之前设置 Watcher 的客户端不会再次收到消息。
（Watcher 是一次性的操作）。
可以通过循环监听去达到永久监听效果。
```

## 3.2 三个过程

```basic
1. 客户端注册 Watcher，注册 watcher 有 3 种方式，getData、exists、getChildren。
2. 服务器处理 Watcher
3. 客户端回调 Watcher 客户端。
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611408389203-e815a1a1-7a1a-4640-9fd1-12c37c6d08af.png#align=left&display=inline&height=352&originHeight=352&originWidth=993&size=0&status=done&style=none&width=993)
## 3.3 监听流程

```basic
1. 首先创建一个Main线程
2. 在main线程创建zk客户端 zkClient, 这时再创建两个线程, 一个是负责网络连接通信(connect), 一个负责监听(listener)
3. 通过connect线程将注册的监听事件发送给zk
4. 在zk的注册监听器列表, 把注册的监听事件添加到列表中
5. zk监听到数据或路径发生变化, 就把消息发生给listener线程
6. listener线程内部调用process()线程

每次变化都要向zk注册监听事件, 一次性的, 可以循环遍历注册
```

## 4. 特点

```basic
1. 集群: 一主(Leader)多从(follower), Leader会同步数据到follower
2. 高可用性: 半数以上节点存活, 集群就正常
3. 全局数据一致: 每个server数据一致
4. 更新请求顺序进行：同一个client按顺序请求server
5. 数据更新原子性: 
6. 实时性：在一定时间范围内，Client能读到最新数据。
7. 设计模式: 观察者模式, 它负责管理跟存储大家都关心的数据，然后接受观察者的注册，数据发生变化zk会通知在zk上注册的观察者做出反应。
8. Zookeeper是一个分布式协调系统，满足CP性，跟SpringCloud中的Eureka满足AP不一样。
```

# 5. CAP

```basic
指的是在一个分布式系统中，
一致性（Consistency）、
可用性（Availability）、
分区容错性（Partition tolerance）。

CAP 原则指的是，这三个要素最多只能同时实现两点，不可能三者兼顾。
```

# 6. 常用命令

```
create /path value
get /path
set /path value version   版本
delete /path   没有子节点才能删除, 需先删除子节点
stat /path watch 监听/path
get /path watcher  获取/path监听
```

![](https://cdn.nlark.com/yuque/0/2021/png/684952/1611408389149-8c01a7ea-00c7-4412-9e9b-d183e45edc2b.png#align=left&display=inline&height=480&originHeight=480&originWidth=821&size=0&status=done&style=none&width=821)
# 7. 集群
## 7.1 如何搭建

# 8. 用法
## 8.1 分布式锁

```basic
创建一个临时节点, 只有拿到节点才能操作数据, 没拿到的线程等待,
可能引发羊群效应
1000个请求并发, 第一个用完, 后面999个同时并发向zk请求获取锁,
解决:
先创建临时节点, 后续请求创建临时顺序节点, 编号最小的先获取锁, 用完删除
```


```java
public ResponseData zookpTest() {
        String lockName = zkProperties.getLockPath() + IdUtil.randomUUID();
        log.info("============={} 线程访问开始=======lockName:{}", Thread.currentThread().getName(), lockName);
        //TODO 获取分布式锁
        InterProcessSemaphoreMutex lock = new InterProcessSemaphoreMutex(curatorFramework, lockName);
        try {
			//获取锁资源
            boolean flag = lock.acquire(10, TimeUnit.HOURS);
            if (flag) {
				log.info("线程:{}，获取到了锁", Thread.currentThread().getName());
                //TODO 获得锁之后可以进行相应的处理  睡一会
                // 业务逻辑处理
                Thread.sleep(500);
                log.info("======获得锁后进行相应的操作======" + Thread.currentThread().getName());
            }
        } catch (Exception e) {
			log.info("错误信息：{}", e.getMessage());
        } finally {
			try {
                lock.release();
                // 有子节点 不能删除
				// curatorFramework.delete().forPath(lockName);
                log.info("=========lockName:{}======={}释放了锁", lockName, Thread.currentThread().getName());
            } catch (Exception e) {
				log.info("错误信息：{}", e.getMessage());
            }
        }
		return ResponseData.successWithData(lockName);
    }
```

## 8.2 负载均衡

```basic
1. 多个服务注册
2. 客户端获取中间件地址集合
3. 从集合中随机选一个服务执行任务

dubbo注册中心
```

## 8.3命名服务
## 8.4 分布式协调/通知
## 8.5 集群管理

```basic
1. 动态上下线
   有一个节点 /Configuration, 集群中机器server1启动在 /Configuration下创建临时节点/Configuration/server1, server2创建临时节点/Configuration/server2, server1和server2都watch父节点/Configuration, 父节点的数据或子节点发生变化就会通知server1和server2
2. Leader选举
   动态Master选举. 利用临时顺序节点, 每个请求都能成功, 取序号最小的为master

集群节点要奇数个, 3个 5个
```

# 9. Leader选举
## 节点4种状态

```bash
1. LOOKING: 正在寻找Leader, 需要进行Leader选举
2. FOLLOWING: 跟随Leader, 处理客户端的非事务请求，转发事务请求给 Leader 服务器，参与事务请求 Proposal(提议) 的投            票，参与 Leader 选举投票。
3. LEADING: 领导者, 事务请求的唯一调度和处理者，保证集群事务处理的顺序性，集群内部个服务器的调度者(管理follower,数据同步)。
4. OBSERVING: 观察者状态。3.0 版本以后引入的一个服务器角色，在不影响集群事务处理能力的基础上提升集群的非事务处理能力，处理客户端的非事务请求，转发事务请求给 Leader 服务器，不参与任何形式的投票。
```

.....
# 10. 可视化工具

```
https://github.com/xin497668869/zookeeper-visualizer
```


