# 1. 授权码模式（authorization code）
# 2. 简化模式（implicit）
# 3. 密码模式（resource owner password credentials）

```java
127.0.0.1:8080/oauth/token?client_id=admin&client_secret=123&grant_type=password&username=admin&password=123
参数: 
client_id:admin
client_secret:123
grant_type:password
username:admin
password:123
返回: 
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MTE2MjUzNDMsInVzZXJfbmFtZSI6ImFkbWluIiwiYXV0aG9yaXRpZXMiOlsiYnRuOm1vbml0b3I6b25saW5lOmtpY2tvdXQiLCJwYWdlOm1vbml0b3I6b25saW5lIiwiYnRuOm1vbml0b3I6b25saW5lOnF1ZXJ5IiwiYnRuOnRlc3Q6aW5zZXJ0IiwicGFnZTp0ZXN0IiwiYnRuOnRlc3Q6cXVlcnkiLCJidG46d3M6c2VuZEFsbCIsImJ0bjp0ZXN0OnNlbmQiXSwianRpIjoiajg0d1FNek5iV1lPenYwZjNQaUpFWEgxa0pZIiwiY2xpZW50X2lkIjoiYWRtaW4iLCJzY29wZSI6WyJBTEwiXX0.rU8HaIGTRQhgz1M_bCErJ5mmPD-WvHtsA4SB349Ez3E",
    "token_type": "bearer",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJhZG1pbiIsInNjb3BlIjpbIkFMTCJdLCJhdGkiOiJqODR3UU16TmJXWU96djBmM1BpSkVYSDFrSlkiLCJleHAiOjE2MTE2MjY1NDMsImF1dGhvcml0aWVzIjpbImJ0bjptb25pdG9yOm9ubGluZTpraWNrb3V0IiwicGFnZTptb25pdG9yOm9ubGluZSIsImJ0bjptb25pdG9yOm9ubGluZTpxdWVyeSIsImJ0bjp0ZXN0Omluc2VydCIsInBhZ2U6dGVzdCIsImJ0bjp0ZXN0OnF1ZXJ5IiwiYnRuOndzOnNlbmRBbGwiLCJidG46dGVzdDpzZW5kIl0sImp0aSI6IkZEanhmbHBQbUg5MkprYWpYOWRQVXBfQkoxMCIsImNsaWVudF9pZCI6ImFkbWluIn0.w-yUdk-huaRHZZmak99oj8p0vVCiP65NUAzzRFgQOmI",
    "expires_in": 1199,
    "scope": "ALL",
    "jti": "j84wQMzNbWYOzv0f3PiJEXH1kJY"
}
```
# 4. 客户端模式（client credentials）

```java
127.0.0.1:8080/oauth/token?client_id=admin&client_secret=123&grant_type=client_credentials
参数: 
client_id:admin
client_secret:123
grant_type:client_credentials
返回: 
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJBTEwiXSwiZXhwIjoxNjExNjI1MzY5LCJhdXRob3JpdGllcyI6WyIxIiwiMiJdLCJqdGkiOiJESmpyM2xaZWR3WHV4cDNvb2w5YV9wWE0xRGciLCJjbGllbnRfaWQiOiJhZG1pbiJ9.TxQXQkj613D-8Pqxne7z95IkPbj3GupcKeSNSIwq9Uo",
    "token_type": "bearer",
    "expires_in": 1199,
    "scope": "ALL",
    "jti": "DJjr3lZedwXuxp3ool9a_pXM1Dg"
}
```
# 5. 刷新token模式 (refresh_token)

```java

127.0.0.1:8080/oauth/token?client_id=admin&client_secret=123&grant_type=refresh_token&refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJhZG1pbiIsInNjb3BlIjpbIkFMTCJdLCJhdGkiOiI2NFRHMzZEdWx1WHo0M05VU1ZYMnZHZXl0VE0iLCJleHAiOjE2MTE2MjY5MTEsImF1dGhvcml0aWVzIjpbImJ0bjptb25pdG9yOm9ubGluZTpraWNrb3V0IiwicGFnZTptb25pdG9yOm9ubGluZSIsImJ0bjptb25pdG9yOm9ubGluZTpxdWVyeSIsImJ0bjp0ZXN0Omluc2VydCIsInBhZ2U6dGVzdCIsImJ0bjp0ZXN0OnF1ZXJ5IiwiYnRuOndzOnNlbmRBbGwiLCJidG46dGVzdDpzZW5kIl0sImp0aSI6ImxQRi0yWWVHU0FjUEJ5Wm5ZS19JZmJaNzFIRSIsImNsaWVudF9pZCI6ImFkbWluIn0.uIWQIvDwzOv7eX6Hfq_O70e6gGRr8gmmDe2-g658iao

参数:
client_id:admin
client_secret:123
grant_type:refresh_token
refresh_token:token值

返回: 
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MTE2MjU5MDIsInVzZXJfbmFtZSI6ImFkbWluIiwiYXV0aG9yaXRpZXMiOlsiYnRuOm1vbml0b3I6b25saW5lOmtpY2tvdXQiLCJwYWdlOm1vbml0b3I6b25saW5lIiwiYnRuOm1vbml0b3I6b25saW5lOnF1ZXJ5IiwiYnRuOnRlc3Q6aW5zZXJ0IiwicGFnZTp0ZXN0IiwiYnRuOnRlc3Q6cXVlcnkiLCJidG46d3M6c2VuZEFsbCIsImJ0bjp0ZXN0OnNlbmQiXSwianRpIjoidE5WdklrSzg0emxXQjFkYzc2SUFVY1J0VjRvIiwiY2xpZW50X2lkIjoiYWRtaW4iLCJzY29wZSI6WyJBTEwiXX0.uFeIi9VGWI6HpSYfugkWsdOpN7h-F75L3ylzzkgg1lo",
    "token_type": "bearer",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJhZG1pbiIsInNjb3BlIjpbIkFMTCJdLCJhdGkiOiJ0TlZ2SWtLODR6bFdCMWRjNzZJQVVjUnRWNG8iLCJleHAiOjE2MTE2MjY5MTEsImF1dGhvcml0aWVzIjpbImJ0bjptb25pdG9yOm9ubGluZTpraWNrb3V0IiwicGFnZTptb25pdG9yOm9ubGluZSIsImJ0bjptb25pdG9yOm9ubGluZTpxdWVyeSIsImJ0bjp0ZXN0Omluc2VydCIsInBhZ2U6dGVzdCIsImJ0bjp0ZXN0OnF1ZXJ5IiwiYnRuOndzOnNlbmRBbGwiLCJidG46dGVzdDpzZW5kIl0sImp0aSI6ImxQRi0yWWVHU0FjUEJ5Wm5ZS19JZmJaNzFIRSIsImNsaWVudF9pZCI6ImFkbWluIn0.wNhlqJME98gVrj0tUDBzfuQ09JNfBMrHhCfR2Rwun9Y",
    "expires_in": 1199,
    "scope": "ALL",
    "jti": "tNVvIkK84zlWB1dc76IAUcRtV4o"
}
```

# 6. 其他接口
## 6.1 检查token接口

```java
检查token 把token转为json
127.0.0.1:8080/oauth/check_token?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJBTEwiXSwiZXhwIjoxNjExNjI1MzY5LCJhdXRob3JpdGllcyI6WyIxIiwiMiJdLCJqdGkiOiJESmpyM2xaZWR3WHV4cDNvb2w5YV9wWE0xRGciLCJjbGllbnRfaWQiOiJhZG1pbiJ9.TxQXQkj613D-8Pqxne7z95IkPbj3GupcKeSNSIwq9Uo

客户端凭证 结果:
{
    "scope": [
        "ALL"
    ],
    "active": true,
    "exp": 1611625369,
    "authorities": [
        "1",
        "2"
    ],
    "jti": "DJjr3lZedwXuxp3ool9a_pXM1Dg",
    "client_id": "admin"
}
密码 结果:
{
    "user_name": "admin",
    "scope": [
        "ALL"
    ],
    "active": true,
    "exp": 1611625711,
    "authorities": [
        "btn:monitor:online:kickout",
        "page:monitor:online",
        "btn:monitor:online:query",
        "btn:test:insert",
        "page:test",
        "btn:test:query",
        "btn:ws:sendAll",
        "btn:test:send"
    ],
    "jti": "64TG36DuluXz43NUSVX2vGeytTM",
    "client_id": "admin"
}
```


