```shell

# 导入系统
wsl --import Ubuntu-24.04 "C:\wsl\ubuntu" "C:\Users\<USER>\Downloads\ubuntu-noble-wsl-amd64-24.04lts.rootfs.tar.gz"


```

```shell
# 查看所有可安装的linux发行版本
wsl --list --online

# 安装debian系统 
# wsl --install <Distribution>
wsl --install -d Debian

# 查看已安装的linux子系统 
# wsl -l -v
wsl --list --verbose

# 设置默认linux发行版 
# wsl --set-default <Distribution>
wsl --set-default Debian

#进入debian系统 wsl --distribution <Distribution> --user <UserName>
wsl -d Debian
```

