## HTTPS

```nginx 
    server {
        listen       10445 ssl;
        
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
        ssl_certificate /home/<USER>/ssl/sg.crt;
        ssl_certificate_key /home/<USER>/ssl/sg.key;
        
        add_header Set-Cookie "JSESSIONID=$cookie_JSESSIONID; Secure; HttpOnly; SameSite=Strict" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always; 
        
        # 屏蔽恶意爬虫 & User-Agent
        if ($http_user_agent ~* (curl|wget|python|sqlmap|scanner)) {
           return 403;
        }
        
        # 接口文档地址禁止访问
        location ~* /(.*actuator.*|.*doc\.html.*|.*v3/api-docs.*) {
            return 403;
        }
    }
```

## 过滤 Referer（防盗链 & 防垃圾流量）

```nginx
location /images/ {
    valid_referers none blocked example.com;
    if ($invalid_referer) {
        return 403;
    }
}
```

## 跨域配置

```nginx 
    map $http_origin $cors_allow_origin {
        default "";
        "http://localhost:5173" "$http_origin";
        'http://**********:10242' "$http_origin";
        #"http://example.com" "$http_origin";
    }

    location ^~ /rcsp {
        proxy_pass  http://rcsp-ms-gateway-server/rcsp;
        proxy_next_upstream error timeout http_500 http_503 http_403 http_404 http_429;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        send_timeout 300s;
        proxy_read_timeout 300s;

        # 隐藏后端跨域配置
        proxy_hide_header Access-Control-Allow-Origin;
        add_header Access-Control-Allow-Origin $cors_allow_origin always;
        add_header Access-Control-Allow-Headers "Accept,Accept-Encoding,Accept-Language,Connection,Content-Length,Content-Type,Host,Origin,Referer,User-Agent,timezone,x-access-token";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Credentials true;
        if ($request_method = 'OPTIONS') {
            return 200;
        }
    }

```

## 使用 GeoIP2 限制特定国家/地区访问

> 如果你想阻止特定国家或地区访问，可以用 ngx_http_geoip2_module 进行 IP 过滤。
> 首先安装 GeoIP2 数据库：
> ✅ 防止特定国家或地区的恶意访问

```bash
wget -O /etc/nginx/geoip2.mmdb https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-Country&license_key=YOUR_LICENSE_KEY&suffix=tar.gz

```

```nginx
http {
    geoip2 /etc/nginx/geoip2.mmdb {
        auto_reload 5m;
        $geoip2_data_country_code country iso_code;
    }

    server {
        listen 80;
        server_name example.com;

        if ($geoip2_data_country_code = "CN") {
            return 403;
        }

        location / {
            root /var/www/html;
        }
    }
}
```

## 使用 Ratelimit 阻止暴力攻击

> 针对登录接口、API 等敏感操作，添加请求速率限制：
> ✅ 防止暴力破解、恶意 API 滥用

> IP 每秒最多 5 个请求，防止恶意爬虫、API 滥用、暴力破解等攻击。
> 使用 burst=10 允许瞬时流量突增，防止误伤正常用户。
> nodelay 让超出 burst 的请求直接返回 503，而不会排队等待。

```nginx
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=5r/s;

server {
    listen 80;
    server_name example.com;

    location /login {
        limit_req zone=api_limit burst=10 nodelay;
        proxy_pass http://backend;
    }
}
```

## 限制单 IP 并发连接数

> 防止单个 IP 并发请求过多，导致服务器崩溃。

> limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
> 创建名为 conn_limit 的限流区域，存储每个 IP 的并发连接数。
> limit_conn conn_limit 10;
> 每个 IP 同时最多只能建立 10 个连接，超出后会返回 503。

```nginx
http {
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
    
    server {
        listen 80;
        server_name example.com;

        location / {
            limit_conn conn_limit 10; # 限制单个 IP 最大同时连接数为 10
        }
    }
}
```